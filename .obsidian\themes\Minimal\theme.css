/* ---------------------------------------------------------------------------

Minimal Theme by @kepano

User interface replacement for Obsidian.

Designed to be used with the Minimal Theme Settings 
plugin and the Hider plugin.

Sponsor my work:
https://www.buymeacoffee.com/kepano

Readme:
https://github.com/kepano/obsidian-minimal

-----------------------------------------------------------------------------

MIT License

Copyright (c) 2020-2024 Steph Ango (@kepano)

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in 
all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.

*/
body{--font-editor-theme:-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,Inter,Ubuntu,sans-serif;--font-editor:var(--font-editor-override),var(--font-text-override),var(--font-editor-theme)}body{--blockquote-style:normal;--blockquote-color:var(--text-muted);--blockquote-border-thickness:1px;--blockquote-border-color:var(--quote-opening-modifier);--embed-block-shadow-hover:none;--font-ui-smaller:11px;--normal-weight:400;--inline-title-margin-bottom:1rem;--h1-size:1.125em;--h2-size:1.05em;--h3-size:1em;--h4-size:0.90em;--h5-size:0.85em;--h6-size:0.85em;--h1-weight:600;--h2-weight:600;--h3-weight:500;--h4-weight:500;--h5-weight:500;--h6-weight:400;--h1-variant:normal;--h2-variant:normal;--h3-variant:normal;--h4-variant:normal;--h5-variant:small-caps;--h6-variant:small-caps;--h1-style:normal;--h2-style:normal;--h3-style:normal;--h4-style:normal;--h5-style:normal;--h6-style:normal;--line-width:40rem;--line-height:1.5;--line-height-normal:var(--line-height);--max-width:88%;--max-col-width:18em;--icon-muted:0.5;--nested-padding:1.1em;--folding-offset:32px;--list-edit-offset:0.5em;--list-indent:2em;--list-spacing:0.075em;--input-height:32px;--header-height:40px;--metadata-label-width:9rem;--metadata-label-font-size:var(--font-adaptive-small);--metadata-input-font-size:var(--font-adaptive-small);--mobile-left-sidebar-width:280pt;--mobile-right-sidebar-width:240pt;--top-left-padding-y:0px;--image-muted:0.7;--image-radius:4px;--heading-spacing:2em;--p-spacing:1.75rem;--border-width:1px;--table-border-width:var(--border-width);--table-selection:var(--text-selection);--table-selection-border-color:var(--text-accent);--table-selection-border-width:0px;--table-selection-border-radius:0px;--table-drag-handle-background-active:var(--text-selection);--table-drag-handle-color-active:var(--text-accent);--table-add-button-border-width:0px;--file-margins:var(--size-4-2) var(--size-4-12)}.mod-macos{--top-left-padding-y:24px}.is-phone{--metadata-label-font-size:var(--font-adaptive-smaller);--metadata-input-font-size:var(--font-adaptive-smaller)}@media only screen and (-webkit-min-device-pixel-ratio:2),only screen and (min-device-pixel-ratio:2){.is-phone{--border-width:0.75px}}body{--base-h:0;--base-s:0%;--base-l:96%;--accent-h:201;--accent-s:17%;--accent-l:50%}.theme-dark,.theme-light{--color-red-rgb:208,66,85;--color-orange-rgb:213,118,63;--color-yellow-rgb:229,181,103;--color-green-rgb:168,195,115;--color-cyan-rgb:115,187,178;--color-blue-rgb:108,153,187;--color-purple-rgb:158,134,200;--color-pink-rgb:176,82,121;--color-red:#d04255;--color-orange:#d5763f;--color-yellow:#e5b567;--color-green:#a8c373;--color-cyan:#73bbb2;--color-blue:#6c99bb;--color-purple:#9e86c8;--color-pink:#b05279}.theme-light,.theme-light.minimal-default-light,body .excalidraw{--bg1:white;--bg2:hsl( var(--base-h), var(--base-s), var(--base-l) );--bg3:hsla( var(--base-h), var(--base-s), calc(var(--base-l) - 50%), 0.12 );--ui1:hsl( var(--base-h), var(--base-s), calc(var(--base-l) - 6%) );--ui2:hsl( var(--base-h), var(--base-s), calc(var(--base-l) - 12%) );--ui3:hsl( var(--base-h), var(--base-s), calc(var(--base-l) - 20%) );--tx1:hsl( var(--base-h), var(--base-s), calc(var(--base-l) - 90%) );--tx2:hsl( var(--base-h), calc(var(--base-s) - 20%), calc(var(--base-l) - 50%) );--tx3:hsl( var(--base-h), calc(var(--base-s) - 10%), calc(var(--base-l) - 25%) );--tx4:hsl( var(--base-h), calc(var(--base-s) - 10%), calc(var(--base-l) - 60%) );--ax1:hsl( var(--accent-h), var(--accent-s), var(--accent-l) );--ax2:hsl( var(--accent-h), var(--accent-s), calc(var(--accent-l) - 8%) );--ax3:hsl( var(--accent-h), var(--accent-s), calc(var(--accent-l) + 6%) );--hl1:hsla( var(--accent-h), 50%, calc(var(--base-l) - 20%), 30% );--hl2:rgba(255, 225, 0, 0.5);--sp1:white}.excalidraw.theme--dark,.theme-dark,.theme-dark.minimal-default-dark,.theme-light.minimal-light-contrast .titlebar,.theme-light.minimal-light-contrast.minimal-status-off .status-bar{--accent-l:60%;--base-l:15%;--bg1:hsl( var(--base-h), var(--base-s), var(--base-l) );--bg2:hsl( var(--base-h), var(--base-s), calc(var(--base-l) - 2%) );--bg3:hsla( var(--base-h), var(--base-s), calc(var(--base-l) + 40%), 0.12 );--ui1:hsl( var(--base-h), var(--base-s), calc(var(--base-l) + 6%) );--ui2:hsl( var(--base-h), var(--base-s), calc(var(--base-l) + 12%) );--ui3:hsl( var(--base-h), var(--base-s), calc(var(--base-l) + 20%) );--tx1:hsl( var(--base-h), calc(var(--base-s) - 10%), calc(var(--base-l) + 67%) );--tx2:hsl( var(--base-h), calc(var(--base-s) - 20%), calc(var(--base-l) + 45%) );--tx3:hsl( var(--base-h), calc(var(--base-s) - 10%), calc(var(--base-l) + 20%) );--tx4:hsl( var(--base-h), calc(var(--base-s) - 10%), calc(var(--base-l) + 50%) );--ax1:hsl( var(--accent-h), var(--accent-s), var(--accent-l) );--ax2:hsl( var(--accent-h), var(--accent-s), calc(var(--accent-l) + 8%) );--ax3:hsl( var(--accent-h), var(--accent-s), calc(var(--accent-l) - 5%) );--hl1:hsla( var(--accent-h), 50%, 40%, 30% );--hl2:rgba(255, 177, 80, 0.3);--sp1:white}.theme-light.minimal-light-white{--background-primary:white;--background-secondary:white;--background-secondary-alt:white;--ribbon-background:white;--titlebar-background:white;--bg1:white}.theme-dark.minimal-dark-black{--base-d:0%;--titlebar-background:black;--background-primary:black;--background-secondary:black;--background-secondary-alt:black;--ribbon-background:black;--background-modifier-hover:hsl( var(--base-h), var(--base-s), calc(var(--base-d) + 10%));--tx1:hsl( var(--base-h), var(--base-s), calc(var(--base-d) + 75%) );--tx2:hsl( var(--base-h), var(--base-s), calc(var(--base-d) + 50%) );--tx3:hsl( var(--base-h), var(--base-s), calc(var(--base-d) + 25%) );--ui1:hsl( var(--base-h), var(--base-s), calc(var(--base-d) + 12%) );--ui2:hsl( var(--base-h), var(--base-s), calc(var(--base-d) + 20%) );--ui3:hsl( var(--base-h), var(--base-s), calc(var(--base-d) + 30%) )}.theme-light{--mono100:black;--mono0:white}.theme-dark{--mono100:white;--mono0:black}.theme-dark,.theme-light,.theme-light.minimal-light-contrast .titlebar,.theme-light.minimal-light-contrast.is-mobile .workspace-drawer.mod-left,.theme-light.minimal-light-contrast.minimal-status-off .status-bar{--background-modifier-accent:var(--ax3);--background-modifier-border-focus:var(--ui3);--background-modifier-border-hover:var(--ui2);--background-modifier-border:var(--ui1);--mobile-sidebar-background:var(--bg1);--background-modifier-form-field-highlighted:var(--bg1);--background-modifier-form-field:var(--bg1);--background-modifier-success:var(--color-green);--background-modifier-hover:var(--bg3);--background-modifier-active-hover:var(--bg3);--background-primary:var(--bg1);--background-primary-alt:var(--bg2);--background-secondary:var(--bg2);--background-secondary-alt:var(--bg1);--background-table-rows:var(--bg2);--checkbox-color:var(--ax3);--code-normal:var(--tx1);--divider-color:var(--ui1);--frame-divider-color:var(--ui1);--icon-color-active:var(--tx1);--icon-color-focused:var(--tx1);--icon-color-hover:var(--tx2);--icon-color:var(--tx2);--icon-hex:var(--mono0);--interactive-accent-hover:var(--ax1);--interactive-accent:var(--ax3);--interactive-hover:var(--ui1);--list-marker-color:var(--tx3);--nav-item-background-active:var(--bg3);--nav-item-background-hover:var(--bg3);--nav-item-color:var(--tx2);--nav-item-color-active:var(--tx1);--nav-item-color-hover:var(--tx1);--nav-item-color-selected:var(--tx1);--nav-collapse-icon-color:var(--tx2);--nav-collapse-icon-color-collapsed:var(--tx2);--nav-indentation-guide-color:var(--ui1);--prompt-border-color:var(--ui3);--quote-opening-modifier:var(--ui2);--ribbon-background:var(--bg2);--scrollbar-active-thumb-bg:var(--ui3);--scrollbar-bg:transparent;--scrollbar-thumb-bg:var(--ui1);--search-result-background:var(--bg1);--tab-text-color-focused-active:var(--tx1);--tab-outline-color:var(--ui1);--text-accent-hover:var(--ax2);--text-accent:var(--ax1);--text-blockquote:var(--tx2);--text-bold:var(--tx1);--text-code:var(--tx4);--text-error:var(--color-red);--text-faint:var(--tx3);--text-highlight-bg:var(--hl2);--text-italic:var(--tx1);--text-muted:var(--tx2);--text-normal:var(--tx1);--text-on-accent:var(--sp1);--text-selection:var(--hl1);--text-formatting:var(--tx3);--title-color-inactive:var(--tx2);--title-color:var(--tx1);--titlebar-background:var(--bg2);--titlebar-background-focused:var(--bg2);--titlebar-text-color-focused:var(--tx1);--vault-profile-color:var(--tx1);--vault-profile-color-hover:var(--tx1);--workspace-background-translucent:hsla(var(--base-h),var(--base-s), var(--base-l), 0.7)}.theme-dark .view-actions,.theme-light .view-actions{--icon-color-active:var(--ax1)}.theme-light.minimal-light-contrast{--workspace-background-translucent:rgba(0,0,0,0.6)}.theme-light.minimal-light-contrast .theme-dark{--tab-container-background:var(--bg2);--ribbon-background-collapsed:var(--bg2)}.theme-light{--interactive-normal:var(--bg1);--interactive-accent-rgb:220,220,220;--active-line-bg:rgba(0,0,0,0.035);--background-modifier-cover:hsla(var(--base-h),calc(var(--base-s) - 70%),calc(var(--base-l) - 20%),0.5);--text-highlight-bg-active:rgba(0, 0, 0, 0.1);--background-modifier-error:rgba(255,0,0,0.14);--background-modifier-error-hover:rgba(255,0,0,0.08);--shadow-color:rgba(0, 0, 0, 0.1);--btn-shadow-color:rgba(0, 0, 0, 0.05)}.theme-dark{--interactive-normal:var(--bg3);--interactive-accent-rgb:66,66,66;--active-line-bg:rgba(255,255,255,0.04);--background-modifier-cover:hsla(var(--base-h),var(--base-s), calc(var(--base-l) - 12%), 0.5);--text-highlight-bg-active:rgba(255, 255, 255, 0.1);--background-modifier-error:rgba(255,20,20,0.12);--background-modifier-error-hover:rgba(255,20,20,0.18);--background-modifier-box-shadow:rgba(0, 0, 0, 0.3);--shadow-color:rgba(0, 0, 0, 0.3);--btn-shadow-color:rgba(0, 0, 0, 0.2);--modal-border-color:var(--ui2)}.theme-light.minimal-light-white{--background-table-rows:var(--bg2)}.theme-light.minimal-light-tonal{--background-primary:var(--bg2);--background-primary-alt:var(--bg3);--background-table-rows:var(--bg3)}.theme-dark.minimal-dark-tonal{--ribbon-background:var(--bg1);--background-secondary:var(--bg1);--background-table-rows:var(--bg3)}.theme-dark.minimal-dark-black{--background-primary-alt:var(--bg3);--background-table-rows:var(--bg3);--modal-border:var(--ui2);--active-line-bg:rgba(255,255,255,0.085);--background-modifier-form-field:var(--bg3);--background-modifier-cover:hsla(var(--base-h),var(--base-s),calc(var(--base-d) + 8%),0.9);--background-modifier-box-shadow:rgba(0, 0, 0, 1)}body{--font-adaptive-normal:var(--font-text-size,var(--editor-font-size));--font-adaptive-small:calc(var(--font-ui-small) * 1.07);--font-adaptive-smaller:var(--font-ui-small);--font-adaptive-smallest:var(--font-ui-smaller);--line-width-wide:calc(var(--line-width) + 12.5%);--font-code:calc(var(--font-adaptive-normal) * 0.9);--table-text-size:calc(var(--font-adaptive-normal) * 0.875)}.minimal-dev-block-width .mod-root .workspace-leaf-content:after{display:flex;align-items:flex-end;content:" pane ";font-size:12px;color:gray;font-family:var(--font-monospace);width:100%;max-width:100%;height:100vh;top:0;z-index:999;position:fixed;pointer-events:none}.minimal-dev-block-width.minimal-readable .mod-root .view-header:after{display:flex;align-items:flex-end;color:green;font-size:12px;font-family:var(--font-monospace);content:" ";width:var(--folding-offset);height:100vh;border-left:1px solid green;border-right:1px solid green;background-color:rgba(0,128,0,.1);top:0;left:max(50% - var(--line-width)/2 - 1px,50% - var(--max-width)/2 - 1px);z-index:999;position:fixed;pointer-events:none}.minimal-dev-block-width.minimal-readable-off .mod-root .view-header:after{display:flex;align-items:flex-end;color:green;font-size:12px;font-family:var(--font-monospace);content:" ";width:var(--folding-offset);height:100vh;border-left:1px solid green;border-right:1px solid green;background-color:rgba(0,128,0,.1);top:0;left:calc(50% - var(--max-width)/ 2 - 1px);z-index:999;position:fixed;pointer-events:none}.minimal-dev-block-width .mod-root .view-content:before{display:flex;align-items:flex-end;content:" max ";font-size:12px;color:red;width:var(--max-width);height:100vh;border-left:1px solid red;border-right:1px solid red;top:0;left:50%;transform:translate(-50%,0);z-index:999;position:fixed;pointer-events:none}.minimal-dev-block-width.minimal-readable .mod-root .workspace-leaf-content:before{display:flex;align-items:flex-end;content:" wide ";font-size:12px;color:orange;font-family:var(--font-monospace);width:var(--line-width-wide);max-width:var(--max-width);height:100vh;border-left:1px solid orange;border-right:1px solid orange;background-color:rgba(255,165,0,.05);top:0;left:50%;transform:translate(-50%,0);z-index:999;position:fixed;pointer-events:none}.minimal-dev-block-width.minimal-readable .mod-root .view-content:after{display:flex;align-items:flex-end;color:#00f;font-size:12px;font-family:var(--font-monospace);content:" normal";width:var(--line-width);max-width:var(--max-width);height:100vh;border-left:1px solid #00f;border-right:1px solid #00f;background-color:rgba(0,0,255,.08);top:0;left:50%;transform:translate(-50%,0);z-index:999;position:fixed;pointer-events:none}.CodeMirror-wrap>div>textarea{opacity:0}.markdown-source-view.mod-cm6 hr{border-width:2px}.cm-editor .cm-content{padding-top:.5em}.markdown-source-view{color:var(--text-normal)}.markdown-source-view.mod-cm6 .cm-sizer{display:block}.markdown-source-view.mod-cm6 .cm-scroller{padding-inline-end:0;padding-inline-start:0}.cm-s-obsidian .cm-line.HyperMD-header{padding-top:calc(var(--p-spacing)/2)}.markdown-rendered .mod-header+div>*{margin-block-start:0}body :not(.canvas-node) .markdown-source-view.mod-cm6 .cm-gutters{position:absolute!important;z-index:0;margin-inline-end:0}body :not(.canvas-node) .markdown-source-view.mod-cm6 .cm-gutters .cm-gutter:before{content:"";height:100%;top:-100%;width:100%;position:absolute;z-index:1;background-color:var(--gutter-background);min-width:var(--folding-offset)}body :not(.canvas-node) .markdown-source-view.mod-cm6.is-rtl .cm-gutters{right:0}body{--line-number-color:var(--text-faint);--line-number-color-active:var(--text-muted)}.markdown-source-view.mod-cm6 .cm-gutters{color:var(--line-number-color)!important}.markdown-source-view.mod-cm6 .cm-editor .cm-gutterElement.cm-active .cm-heading-marker,.markdown-source-view.mod-cm6 .cm-editor .cm-lineNumbers .cm-gutterElement.cm-active{color:var(--line-number-color-active)}.cm-editor .cm-lineNumbers{background-color:var(--gutter-background)}.cm-editor .cm-lineNumbers .cm-gutterElement{min-width:var(--folding-offset);padding-inline-end:.5em}.is-rtl .cm-editor .cm-lineNumbers .cm-gutterElement{text-align:left}@media(max-width:400pt){.cm-editor .cm-lineNumbers .cm-gutterElement{padding-inline-end:4px;padding-inline-start:8px}}.cm-editor .cm-gutterElement.cm-active .cm-heading-marker,.cm-editor .cm-lineNumbers .cm-gutterElement.cm-active{color:var(--text-muted)}.markdown-source-view.mod-cm6 .edit-block-button{cursor:var(--cursor);color:var(--text-faint);background-color:var(--background-primary);top:0;opacity:0;transition:opacity .2s;padding:4px 4px 4px 9px}.markdown-source-view.mod-cm6 .edit-block-button svg{margin:0!important}.markdown-source-view.mod-cm6.is-live-preview.is-readable-line-width .cm-embed-block>.edit-block-button{width:30px!important;padding-inline-start:7px!important}.is-live-preview:not(.is-readable-line-width) .cm-embed-block>.edit-block-button{padding-inline-start:0px!important;margin-inline-start:0!important;padding:4px}.markdown-source-view.mod-cm6 .edit-block-button:hover{background-color:var(--background-primary);color:var(--text-muted)}.markdown-source-view.mod-cm6 .edit-block-button svg{opacity:1}.markdown-source-view.mod-cm6 .edit-block-button:hover svg{opacity:1}.markdown-source-view.mod-cm6 .cm-embed-block{padding:0;border:0;border-radius:0}.markdown-source-view.mod-cm6 .cm-embed-block:hover{border:0}.metadata-container{--input-height:2rem}body.metadata-heading-off .metadata-properties-heading{display:none}.metadata-add-property-off .mod-root .metadata-add-button{display:none}.metadata-dividers{--metadata-divider-width:1px;--metadata-gap:0px}.metadata-icons-off .workspace-leaf-content[data-type=all-properties] .tree-item-inner{margin-inline-start:-16px}.metadata-icons-off .workspace-leaf-content[data-type=all-properties] .tree-item-icon{display:none}.metadata-icons-off .metadata-property-icon{display:none}figure{margin-inline-start:0;margin-inline-end:0}.markdown-preview-view .mod-highlighted{transition:background-color .3s ease;background-color:var(--text-selection);color:inherit}.inline-title{padding-top:16px}.mod-macos.hider-frameless .workspace-ribbon{border:none}.is-tablet.hider-ribbon{--ribbon-width:0px}.is-tablet.hider-ribbon .side-dock-ribbon{display:none}.hider-ribbon .workspace-ribbon{padding:0}:root{--hider-ribbon-display:none;--ribbon-animation-duration:0.1s}.ribbon-bottom-left-hover-vertical:not(.is-mobile),.ribbon-bottom-left-hover:not(.is-mobile){--hider-ribbon-display:flex}body.ribbon-vertical-expand:not(.is-mobile){--ribbon-width:0px}body.ribbon-vertical-expand:not(.is-mobile) .workspace-ribbon.mod-left{width:10px;flex-basis:10px;opacity:0;position:fixed;height:100%;top:0;bottom:0;left:0;z-index:10;transition:all var(--ribbon-animation-duration) linear .6s}body.ribbon-vertical-expand:not(.is-mobile) .workspace-ribbon.mod-left .side-dock-actions{transition:opacity var(--ribbon-animation-duration) linear .3s}body.ribbon-vertical-expand:not(.is-mobile) .workspace-ribbon.mod-left:hover{width:44px;opacity:1;flex-basis:44px;transition:opacity var(--ribbon-animation-duration) linear .1s}body.ribbon-vertical-expand:not(.is-mobile) .workspace-ribbon.mod-left:hover .side-dock-actions{opacity:1;transition:opacity calc(var(--ribbon-animation-duration)*2) linear .2s}body.ribbon-vertical-expand:not(.is-mobile).labeled-nav .workspace-ribbon.mod-left~.mod-left-split .workspace-tab-header-container{margin-left:0;transition:all var(--ribbon-animation-duration) linear .6s}body.ribbon-vertical-expand:not(.is-mobile).labeled-nav .workspace-ribbon.mod-left:hover~.mod-left-split .workspace-tab-header-container{margin-left:44px;transition:all var(--ribbon-animation-duration) linear}body.ribbon-vertical-expand:not(.is-mobile) .workspace-ribbon.mod-left~.mod-left-split .workspace-tab-container{padding-left:0;transition:all var(--ribbon-animation-duration) linear .6s}body.ribbon-vertical-expand:not(.is-mobile) .workspace-ribbon.mod-left~.mod-left-split .workspace-sidedock-vault-profile{transition:all var(--ribbon-animation-duration) linear .6s}body.ribbon-vertical-expand:not(.is-mobile) .workspace-ribbon.mod-left:hover~.mod-left-split .workspace-tab-container{padding-left:44px;transition:all var(--ribbon-animation-duration) linear}body.ribbon-vertical-expand:not(.is-mobile) .workspace-ribbon.mod-left:hover~.mod-left-split .workspace-sidedock-vault-profile{padding-left:52px;transition:all var(--ribbon-animation-duration) linear}.hider-ribbon .workspace-ribbon.mod-left:before,.ribbon-bottom-left-hover .workspace-ribbon.mod-left:before,.ribbon-bottom-left-hover-vertical .workspace-ribbon.mod-left:before{opacity:0}.hider-ribbon .workspace-ribbon-collapse-btn,.ribbon-bottom-left-hover .workspace-ribbon-collapse-btn,.ribbon-bottom-left-hover-vertical .workspace-ribbon-collapse-btn{display:none}.hider-ribbon .workspace-ribbon.mod-right,.ribbon-bottom-left-hover .workspace-ribbon.mod-right,.ribbon-bottom-left-hover-vertical .workspace-ribbon.mod-right{pointer-events:none}.hider-ribbon .workspace-ribbon.mod-left,.ribbon-bottom-left-hover .workspace-ribbon.mod-left,.ribbon-bottom-left-hover-vertical .workspace-ribbon.mod-left{overflow:visible;border-top:var(--border-width) solid var(--background-modifier-border)!important;border-right:var(--border-width) solid var(--background-modifier-border)!important;border-top-right-radius:var(--radius-m);padding:0;position:absolute;border-right:0px;margin:0;width:auto;height:44px;flex-basis:0;bottom:0;top:auto;background:var(--background-secondary);display:var(--hider-ribbon-display)!important;flex-direction:row;z-index:17;opacity:0;transition:opacity calc(var(--ribbon-animation-duration)*2) ease-in-out;filter:drop-shadow(2px 10px 30px rgba(0, 0, 0, .2));gap:0}.hider-ribbon .side-dock-actions,.hider-ribbon .side-dock-settings,.ribbon-bottom-left-hover .side-dock-actions,.ribbon-bottom-left-hover .side-dock-settings,.ribbon-bottom-left-hover-vertical .side-dock-actions,.ribbon-bottom-left-hover-vertical .side-dock-settings{flex-direction:row;display:var(--hider-ribbon-display);background:rgba(0,0,0,0);margin:0;position:relative;gap:var(--size-2-2)}.hider-ribbon .side-dock-actions,.ribbon-bottom-left-hover .side-dock-actions,.ribbon-bottom-left-hover-vertical .side-dock-actions{padding:6px 6px 6px 8px}.hider-ribbon .side-dock-settings:empty,.ribbon-bottom-left-hover .side-dock-settings:empty,.ribbon-bottom-left-hover-vertical .side-dock-settings:empty{display:none}.hider-ribbon .workspace-ribbon.mod-left .side-dock-ribbon-action,.ribbon-bottom-left-hover .workspace-ribbon.mod-left .side-dock-ribbon-action,.ribbon-bottom-left-hover-vertical .workspace-ribbon.mod-left .side-dock-ribbon-action{display:var(--hider-ribbon-display)}.hider-ribbon .workspace-ribbon.mod-left:hover,.ribbon-bottom-left-hover .workspace-ribbon.mod-left:hover,.ribbon-bottom-left-hover-vertical .workspace-ribbon.mod-left:hover{opacity:1;transition:opacity .25s ease-in-out}.hider-ribbon .workspace-ribbon.mod-left .workspace-ribbon-collapse-btn,.ribbon-bottom-left-hover .workspace-ribbon.mod-left .workspace-ribbon-collapse-btn,.ribbon-bottom-left-hover-vertical .workspace-ribbon.mod-left .workspace-ribbon-collapse-btn{opacity:0}.hider-ribbon .workspace-split.mod-left-split,.ribbon-bottom-left-hover .workspace-split.mod-left-split,.ribbon-bottom-left-hover-vertical .workspace-split.mod-left-split{margin:0}.hider-ribbon .workspace-leaf-content .item-list,.ribbon-bottom-left-hover .workspace-leaf-content .item-list,.ribbon-bottom-left-hover-vertical .workspace-leaf-content .item-list{padding-bottom:40px}.ribbon-bottom-left-hover-vertical .workspace-ribbon.mod-left{height:auto}.ribbon-bottom-left-hover-vertical .side-dock-actions{flex-direction:column;padding:8px 6px}.minimal-status-off .status-bar{--status-bar-position:static;--status-bar-radius:0;--status-bar-border-width:1px 0 0 0;--status-bar-background:var(--background-secondary);--status-bar-border-color:var(--ui1)}body:not(.minimal-status-off) .status-bar{background-color:var(--background-primary);--status-bar-border-width:0}.status-bar{transition:color .2s linear;color:var(--text-faint);font-size:var(--font-adaptive-smallest)}.status-bar .sync-status-icon.mod-success,.status-bar .sync-status-icon.mod-working{color:var(--text-faint)}.status-bar:hover,.status-bar:hover .sync-status-icon.mod-success,.status-bar:hover .sync-status-icon.mod-working{color:var(--text-muted);transition:color .2s linear}.status-bar .plugin-sync:hover .sync-status-icon.mod-success,.status-bar .plugin-sync:hover .sync-status-icon.mod-working{color:var(--text-normal)}.status-bar .status-bar-item{cursor:var(--cursor)!important}.status-bar .status-bar-item.cMenu-statusbar-button:hover,.status-bar .status-bar-item.mod-clickable:hover,.status-bar .status-bar-item.plugin-editor-status:hover,.status-bar .status-bar-item.plugin-sync:hover{text-align:center;background-color:var(--background-modifier-hover)!important}.tab-stack-top-flipped{--tab-stacked-text-transform:rotate(180deg);--tab-stacked-text-align:right}.tab-stack-center{--tab-stacked-text-align:center}.tab-stack-center-flipped{--tab-stacked-text-transform:rotate(180deg);--tab-stacked-text-align:center}.tab-stack-bottom{--tab-stacked-text-transform:rotate(180deg)}.tab-stack-bottom-flipped{--tab-stacked-text-align:right}.view-header-title,.view-header-title-parent{text-overflow:ellipsis}.view-header-title-container:not(.mod-at-end):after{display:none}body:not(.is-mobile) .view-actions .view-action:last-child{margin-left:-1px}.minimal-focus-mode .workspace-ribbon:not(.is-collapsed)~.mod-root .view-header:hover .view-actions,.mod-right.is-collapsed~.mod-root .view-header:hover .view-actions,.view-action.is-active:hover,.workspace-ribbon.mod-left.is-collapsed~.mod-root .view-header:hover .view-actions,body:not(.minimal-focus-mode) .workspace-ribbon:not(.is-collapsed)~.mod-root .view-actions{opacity:1;transition:opacity .25s ease-in-out}.view-header-title-container{opacity:0;transition:opacity .1s ease-in-out}.view-header-title-container:focus-within{opacity:1;transition:opacity .1s ease-in-out}.view-header:hover .view-header-title-container,.workspace-tab-header-container:hover+.workspace-tab-container .view-header-title-container{opacity:1;transition:opacity .1s ease-in-out}.is-phone .view-header-title-container,.minimal-tab-title-visible .view-header-title-container{opacity:1}.minimal-tab-title-hidden .view-header-title-container{opacity:0}.minimal-tab-title-hidden .view-header-title-container:focus-within{opacity:1;transition:opacity .1s ease-in-out}.minimal-tab-title-hidden .view-header:hover .view-header-title-container,.minimal-tab-title-hidden .workspace-tab-header-container:hover+.workspace-tab-container .view-header-title-container{opacity:0}body.window-title-off .titlebar-text{display:none}.titlebar-button-container.mod-right{background-color:rgba(0,0,0,0)!important}.is-hidden-frameless.theme-dark:not(.minimal-dark-black):not(.colorful-frame),.is-hidden-frameless.theme-light:not(.minimal-light-tonal):not(.colorful-frame):not(.minimal-light-white){--titlebar-background:var(--bg1)}.is-hidden-frameless.theme-dark:not(.minimal-dark-black):not(.colorful-frame).is-focused .sidebar-toggle-button.mod-right,.is-hidden-frameless.theme-dark:not(.minimal-dark-black):not(.colorful-frame).is-focused .workspace-ribbon.mod-left.is-collapsed,.is-hidden-frameless.theme-dark:not(.minimal-dark-black):not(.colorful-frame).is-focused .workspace-tabs.mod-top,.is-hidden-frameless.theme-light:not(.minimal-light-tonal):not(.colorful-frame):not(.minimal-light-white).is-focused .sidebar-toggle-button.mod-right,.is-hidden-frameless.theme-light:not(.minimal-light-tonal):not(.colorful-frame):not(.minimal-light-white).is-focused .workspace-ribbon.mod-left.is-collapsed,.is-hidden-frameless.theme-light:not(.minimal-light-tonal):not(.colorful-frame):not(.minimal-light-white).is-focused .workspace-tabs.mod-top{--titlebar-background-focused:var(--bg1)}.is-hidden-frameless.theme-dark:not(.minimal-dark-black):not(.colorful-frame):not(.minimal-dark-tonal):not(.minimal-light-white) .workspace-ribbon.mod-left:not(.is-collapsed),.is-hidden-frameless.theme-light:not(.minimal-light-tonal):not(.colorful-frame):not(.minimal-light-white):not(.minimal-dark-tonal):not(.minimal-light-white) .workspace-ribbon.mod-left:not(.is-collapsed){--titlebar-background:var(--bg2)}.mod-macos.is-hidden-frameless:not(.is-popout-window) .sidebar-toggle-button.mod-right{right:0;padding-right:var(--size-4-2)}body.is-focused{--titlebar-background-focused:var(--background-secondary)}.is-hidden-frameless:not(.colorful-frame) .mod-left-split .mod-top .workspace-tab-header-container{--tab-container-background:var(--background-secondary)}.mod-root .workspace-tab-header-status-icon{color:var(--text-muted)}.is-collapsed .workspace-sidedock-vault-profile{opacity:0}body:not(.is-mobile).hide-help .workspace-drawer-vault-actions .clickable-icon:first-child{display:none}body:not(.is-mobile).hide-settings .workspace-drawer-vault-actions .clickable-icon:last-child{display:none}body:not(.is-mobile).hide-help.hide-settings .workspace-drawer-vault-actions{display:none!important}body:not(.is-grabbing):not(.is-fullscreen).labeled-nav.is-hidden-frameless.vault-profile-top .mod-left-split .mod-top .workspace-tab-header-container{-webkit-app-region:no-drag}body:not(.is-grabbing):not(.is-fullscreen).labeled-nav.is-hidden-frameless.vault-profile-top .mod-left-split .mod-top .workspace-tab-header-container:before{position:absolute;top:0;content:"";height:var(--header-height);width:100%;-webkit-app-region:drag}body:not(.is-mobile):not(.labeled-nav).vault-profile-top .workspace-split.mod-left-split .mod-top .workspace-tab-container{margin-top:calc(var(--header-height) + 8px)}body:not(.is-mobile):not(.labeled-nav).vault-profile-top .workspace-split.mod-left-split .workspace-sidedock-vault-profile{-webkit-app-region:no-drag;position:absolute;top:var(--header-height);z-index:6;width:100%;border-top:0;border-bottom:1px solid var(--background-modifier-border)}body:not(.is-mobile):not(.labeled-nav).vault-profile-top .workspace-split.mod-left-split .workspace-sidedock-vault-profile .workspace-drawer-vault-switcher{padding-left:var(--size-4-2)}body:not(.is-mobile).labeled-nav.vault-profile-top .workspace-split.mod-left-split .workspace-sidedock-vault-profile{-webkit-app-region:no-drag;position:absolute;top:var(--labeled-nav-top-margin);z-index:6;width:100%;background-color:rgba(0,0,0,0);border-top:0;border-bottom:1px solid var(--background-modifier-border)}body:not(.is-mobile).labeled-nav.vault-profile-top .workspace-split.mod-left-split .workspace-sidedock-vault-profile .workspace-drawer-vault-switcher{padding-left:var(--size-4-2)}.vault-profile-top .workspace-tab-header-container-inner{--labeled-nav-top-margin:84px}.modal button:not(.mod-warning),.modal.mod-settings button:not(.mod-cta):not(.mod-warning),.modal.mod-settings button:not(.mod-warning){white-space:nowrap;transition:background-color .2s ease-out,border-color .2s ease-out}button.mod-warning{border:1px solid var(--background-modifier-error);color:var(--text-error);box-shadow:0 1px 1px 0 var(--btn-shadow-color);transition:background-color .2s ease-out}button.mod-warning:hover{border:1px solid var(--background-modifier-error);color:var(--text-error);box-shadow:0 2px 3px 0 var(--btn-shadow-color);transition:background-color .2s ease-out}.document-replace,.document-search{max-width:100%;padding:0}.document-search-container{margin:0 auto;max-width:var(--max-width);width:var(--line-width)}.modal-button-container .mod-checkbox{--checkbox-radius:4px}.modal-container.mod-confirmation .modal{width:480px;min-width:0}.popover{--file-margins:var(--size-4-6) var(--size-4-6) var(--size-4-6)}.minimal-line-nums .popover .markdown-source-view{--file-margins:var(--size-4-4) var(--size-4-6) var(--size-4-6) var(--size-4-12)}.minimal-line-nums .popover .markdown-source-view.is-rtl{--file-margins:var(--size-4-4) var(--size-4-12) var(--size-4-6) var(--size-4-6)}body{--progress-outline:var(--background-modifier-border);--progress-complete:var(--text-accent)}.markdown-preview-view progress,.markdown-rendered progress,.markdown-source-view.is-live-preview progress{width:220px}.markdown-preview-view progress[value]::-webkit-progress-bar,.markdown-rendered progress[value]::-webkit-progress-bar,.markdown-source-view.is-live-preview progress[value]::-webkit-progress-bar{box-shadow:inset 0 0 0 var(--border-width) var(--progress-outline)}.markdown-preview-view progress[value^="1"]::-webkit-progress-value,.markdown-preview-view progress[value^="2"]::-webkit-progress-value,.markdown-preview-view progress[value^="3"]::-webkit-progress-value,.markdown-rendered progress[value^="1"]::-webkit-progress-value,.markdown-rendered progress[value^="2"]::-webkit-progress-value,.markdown-rendered progress[value^="3"]::-webkit-progress-value,.markdown-source-view.is-live-preview progress[value^="1"]::-webkit-progress-value,.markdown-source-view.is-live-preview progress[value^="2"]::-webkit-progress-value,.markdown-source-view.is-live-preview progress[value^="3"]::-webkit-progress-value{background-color:var(--color-red)}.markdown-preview-view progress[value^="4"]::-webkit-progress-value,.markdown-preview-view progress[value^="5"]::-webkit-progress-value,.markdown-rendered progress[value^="4"]::-webkit-progress-value,.markdown-rendered progress[value^="5"]::-webkit-progress-value,.markdown-source-view.is-live-preview progress[value^="4"]::-webkit-progress-value,.markdown-source-view.is-live-preview progress[value^="5"]::-webkit-progress-value{background-color:var(--color-orange)}.markdown-preview-view progress[value^="6"]::-webkit-progress-value,.markdown-preview-view progress[value^="7"]::-webkit-progress-value,.markdown-rendered progress[value^="6"]::-webkit-progress-value,.markdown-rendered progress[value^="7"]::-webkit-progress-value,.markdown-source-view.is-live-preview progress[value^="6"]::-webkit-progress-value,.markdown-source-view.is-live-preview progress[value^="7"]::-webkit-progress-value{background-color:var(--color-yellow)}.markdown-preview-view progress[value^="8"]::-webkit-progress-value,.markdown-preview-view progress[value^="9"]::-webkit-progress-value,.markdown-rendered progress[value^="8"]::-webkit-progress-value,.markdown-rendered progress[value^="9"]::-webkit-progress-value,.markdown-source-view.is-live-preview progress[value^="8"]::-webkit-progress-value,.markdown-source-view.is-live-preview progress[value^="9"]::-webkit-progress-value{background-color:var(--color-green)}.markdown-preview-view progress[value="1"]::-webkit-progress-value,.markdown-preview-view progress[value="100"]::-webkit-progress-value,.markdown-rendered progress[value="1"]::-webkit-progress-value,.markdown-rendered progress[value="100"]::-webkit-progress-value,.markdown-source-view.is-live-preview progress[value="1"]::-webkit-progress-value,.markdown-source-view.is-live-preview progress[value="100"]::-webkit-progress-value{background-color:var(--progress-complete)}.markdown-preview-view progress[value="0"]::-webkit-progress-value,.markdown-preview-view progress[value="2"]::-webkit-progress-value,.markdown-preview-view progress[value="3"]::-webkit-progress-value,.markdown-preview-view progress[value="4"]::-webkit-progress-value,.markdown-preview-view progress[value="5"]::-webkit-progress-value,.markdown-preview-view progress[value="6"]::-webkit-progress-value,.markdown-preview-view progress[value="7"]::-webkit-progress-value,.markdown-preview-view progress[value="8"]::-webkit-progress-value,.markdown-preview-view progress[value="9"]::-webkit-progress-value,.markdown-rendered progress[value="0"]::-webkit-progress-value,.markdown-rendered progress[value="2"]::-webkit-progress-value,.markdown-rendered progress[value="3"]::-webkit-progress-value,.markdown-rendered progress[value="4"]::-webkit-progress-value,.markdown-rendered progress[value="5"]::-webkit-progress-value,.markdown-rendered progress[value="6"]::-webkit-progress-value,.markdown-rendered progress[value="7"]::-webkit-progress-value,.markdown-rendered progress[value="8"]::-webkit-progress-value,.markdown-rendered progress[value="9"]::-webkit-progress-value,.markdown-source-view.is-live-preview progress[value="0"]::-webkit-progress-value,.markdown-source-view.is-live-preview progress[value="2"]::-webkit-progress-value,.markdown-source-view.is-live-preview progress[value="3"]::-webkit-progress-value,.markdown-source-view.is-live-preview progress[value="4"]::-webkit-progress-value,.markdown-source-view.is-live-preview progress[value="5"]::-webkit-progress-value,.markdown-source-view.is-live-preview progress[value="6"]::-webkit-progress-value,.markdown-source-view.is-live-preview progress[value="7"]::-webkit-progress-value,.markdown-source-view.is-live-preview progress[value="8"]::-webkit-progress-value,.markdown-source-view.is-live-preview progress[value="9"]::-webkit-progress-value{background-color:var(--color-red)}body:not(.hider-scrollbars).styled-scrollbars ::-webkit-scrollbar,body:not(.native-scrollbars) ::-webkit-scrollbar{width:11px;background-color:rgba(0,0,0,0)}body:not(.hider-scrollbars).styled-scrollbars ::-webkit-scrollbar:horizontal,body:not(.native-scrollbars) ::-webkit-scrollbar:horizontal{height:11px}body:not(.hider-scrollbars).styled-scrollbars ::-webkit-scrollbar-corner,body:not(.native-scrollbars) ::-webkit-scrollbar-corner{background-color:rgba(0,0,0,0)}body:not(.hider-scrollbars).styled-scrollbars ::-webkit-scrollbar-track,body:not(.native-scrollbars) ::-webkit-scrollbar-track{background-color:rgba(0,0,0,0)}body:not(.hider-scrollbars).styled-scrollbars ::-webkit-scrollbar-thumb,body:not(.native-scrollbars) ::-webkit-scrollbar-thumb{background-clip:padding-box;border-radius:20px;border:3px solid transparent;background-color:var(--background-modifier-border);border-width:3px 3px 3px 3px;min-height:45px}body:not(.hider-scrollbars).styled-scrollbars .mod-left-split .workspace-tabs ::-webkit-scrollbar-thumb:hover,body:not(.hider-scrollbars).styled-scrollbars .modal .vertical-tab-header::-webkit-scrollbar-thumb:hover,body:not(.hider-scrollbars).styled-scrollbars ::-webkit-scrollbar-thumb:hover,body:not(.native-scrollbars) .mod-left-split .workspace-tabs ::-webkit-scrollbar-thumb:hover,body:not(.native-scrollbars) .modal .vertical-tab-header::-webkit-scrollbar-thumb:hover,body:not(.native-scrollbars) ::-webkit-scrollbar-thumb:hover{background-color:var(--background-modifier-border-hover)}body:not(.hider-scrollbars).styled-scrollbars .mod-left-split .workspace-tabs ::-webkit-scrollbar-thumb:active,body:not(.hider-scrollbars).styled-scrollbars .modal .vertical-tab-header::-webkit-scrollbar-thumb:active,body:not(.hider-scrollbars).styled-scrollbars ::-webkit-scrollbar-thumb:active,body:not(.native-scrollbars) .mod-left-split .workspace-tabs ::-webkit-scrollbar-thumb:active,body:not(.native-scrollbars) .modal .vertical-tab-header::-webkit-scrollbar-thumb:active,body:not(.native-scrollbars) ::-webkit-scrollbar-thumb:active{background-color:var(--background-modifier-border-focus)}.tooltip{transition:none;animation:none}.tooltip.mod-left,.tooltip.mod-right{animation:none}.tooltip.mod-error{color:var(--text-error)}.markdown-preview-view blockquote{padding-inline-start:var(--nested-padding);font-size:var(--blockquote-size)}.markdown-source-view.mod-cm6 .HyperMD-quote,.markdown-source-view.mod-cm6.is-live-preview .HyperMD-quote{font-size:var(--blockquote-size)}.is-live-preview .cm-hmd-indent-in-quote{color:var(--text-faint)}.is-live-preview.is-readable-line-width>.cm-callout .callout{max-width:var(--max-width);margin:0 auto}.callouts-outlined .callout .callout-title{background-color:var(--background-primary);margin-top:-24px;z-index:200;width:fit-content;padding:0 .5em;margin-left:-.75em;letter-spacing:.05em;font-variant-caps:all-small-caps}.callouts-outlined .callout{overflow:visible;--callout-border-width:1px;--callout-border-opacity:0.5;--callout-title-size:0.8em;--callout-blend-mode:normal;background-color:rgba(0,0,0,0)}.callouts-outlined .cm-embed-block.cm-callout{padding-top:12px}.callouts-outlined .callout-content .callout{margin-top:18px}body{--checkbox-radius:50%;--checkbox-top:2px;--checkbox-left:0px;--checkbox-margin:0px 6px 0px -2em}.checkbox-square{--checkbox-size:calc(var(--font-text-size) * 0.85);--checkbox-radius:4px;--checkbox-top:1px;--checkbox-left:0px;--checkbox-margin:0px 8px 0px -2em}body.minimal-strike-lists{--checklist-done-decoration:line-through}body:not(.minimal-strike-lists){--checklist-done-decoration:none;--checklist-done-color:var(--text-normal)}.markdown-preview-section>.contains-task-list{padding-bottom:.5em}.mod-cm6 .HyperMD-task-line[data-task] .cm-formatting-list-ol~.task-list-label .task-list-item-checkbox{margin:1px}.markdown-preview-view .task-list-item-checkbox{position:relative;top:var(--checkbox-top);left:var(--checkbox-left)}.markdown-preview-view ul>li.task-list-item{text-indent:0}.minimal-code-scroll{--code-white-space:pre}.minimal-code-scroll .HyperMD-codeblock.HyperMD-codeblock-bg{overflow-y:scroll;white-space:pre}.minimal-code-scroll .cm-hmd-codeblock{white-space:pre!important}@media print{.print{--code-background:#eee!important}}body{--embed-max-height:none;--embed-decoration-style:solid;--embed-decoration-color:var(--background-modifier-border-hover)}.embed-strict{--embed-background:transparent;--embed-border-start:0;--embed-border-left:0;--embed-padding:0}.embed-strict .markdown-embed-content{--folding-offset:0px}.embed-strict .internal-embed .markdown-embed,.embed-strict .markdown-preview-view .markdown-embed,.embed-strict.markdown-preview-view .markdown-embed{padding:0}.embed-strict .internal-embed .markdown-embed .markdown-embed-title,.embed-strict .markdown-embed-title{display:none}.embed-strict .internal-embed:not([src*="#^"]) .markdown-embed-link{width:24px;opacity:0}.embed-underline .internal-embed:not(.pdf-embed){text-decoration-line:underline;text-decoration-style:var(--embed-decoration-style);text-decoration-color:var(--embed-decoration-color)}.embed-hide-title .markdown-embed-title{display:none}.contextual-typography .embed-strict .internal-embed .markdown-preview-view .markdown-preview-sizer>div,.embed-strict.contextual-typography .internal-embed .markdown-preview-view .markdown-preview-sizer>div{margin:0;width:100%}.markdown-embed .markdown-preview-view .markdown-preview-sizer{padding-bottom:0!important}.markdown-preview-view.is-readable-line-width .markdown-embed .markdown-preview-sizer,.markdown-preview-view.markdown-embed .markdown-preview-sizer{max-width:100%;width:100%;min-height:0!important;padding-bottom:0!important}.markdown-embed .markdown-preview-section div:last-child p,.markdown-embed .markdown-preview-section div:last-child ul{margin-block-end:2px}.markdown-preview-view .markdown-embed{margin-top:var(--nested-padding);padding:0 calc(var(--nested-padding)/2) 0 var(--nested-padding)}.internal-embed:not([src*="#^"]) .markdown-embed-link{right:0;width:100%}.file-embed-link,.markdown-embed-link{top:0;right:0;text-align:right;justify-content:flex-end}.file-embed-link svg,.markdown-embed-link svg{width:16px;height:16px}.markdown-embed .file-embed-link,.markdown-embed .markdown-embed-link{opacity:.6;transition:opacity .1s linear}.markdown-embed .file-embed-link:hover,.markdown-embed .markdown-embed-link:hover{opacity:1}.markdown-embed .file-embed-link:hover:hover,.markdown-embed .markdown-embed-link:hover:hover{background-color:rgba(0,0,0,0);--icon-color:var(--text-accent)}.file-embed-link:hover,.markdown-embed-link:hover{color:var(--text-muted)}.markdown-embed .markdown-preview-view{padding:0}.internal-embed .markdown-embed{border:0;border-left:1px solid var(--quote-opening-modifier);border-radius:0}a[href*="obsidian://search"]{background-image:url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100' width='17' height='17' class='search'><path fill='black' stroke='black' stroke-width='2' d='M42,6C23.2,6,8,21.2,8,40s15.2,34,34,34c7.4,0,14.3-2.4,19.9-6.4l26.3,26.3l5.6-5.6l-26-26.1c5.1-6,8.2-13.7,8.2-22.1 C76,21.2,60.8,6,42,6z M42,10c16.6,0,30,13.4,30,30S58.6,70,42,70S12,56.6,12,40S25.4,10,42,10z'></path></svg>")}.theme-dark a[href*="obsidian://search"]{background-image:url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100' width='17' height='17' class='search'><path fill='white' stroke='white' stroke-width='2' d='M42,6C23.2,6,8,21.2,8,40s15.2,34,34,34c7.4,0,14.3-2.4,19.9-6.4l26.3,26.3l5.6-5.6l-26-26.1c5.1-6,8.2-13.7,8.2-22.1 C76,21.2,60.8,6,42,6z M42,10c16.6,0,30,13.4,30,30S58.6,70,42,70S12,56.6,12,40S25.4,10,42,10z'></path></svg>")}.plain-external-links .external-link{background-image:none;padding-right:0}body{--adaptive-list-edit-offset:var(--list-edit-offset)}.is-rtl{--adaptive-list-edit-offset:calc(var(--list-edit-offset)*-1)}.markdown-preview-view ol>li,.markdown-preview-view ul>li,.markdown-source-view ol>li,.markdown-source-view ul>li,.mod-cm6 .HyperMD-list-line.cm-line{padding-top:var(--list-spacing);padding-bottom:var(--list-spacing)}.is-mobile ul>li:not(.task-list-item)::marker{font-size:.8em}.is-mobile .workspace-leaf-content:not([data-type=search]) .workspace-leaf-content[data-type=markdown] .nav-buttons-container{border-bottom:none;padding-top:5px}.is-mobile .mod-root .workspace-leaf-content[data-type=markdown] .search-input-container{width:calc(100% - 160px)}.embedded-backlinks .backlink-pane>.tree-item-self,.embedded-backlinks .backlink-pane>.tree-item-self:hover{text-transform:none;color:var(--text-normal);font-size:var(--font-adaptive-normal);font-weight:500;letter-spacing:unset}body{--pdf-dark-opacity:1}.theme-dark:not(.pdf-shadows-on),.theme-light:not(.pdf-shadows-on){--pdf-shadow:none;--pdf-thumbnail-shadow:none}.theme-dark:not(.pdf-shadows-on) .pdf-viewer .page,.theme-light:not(.pdf-shadows-on) .pdf-viewer .page{border:0}.theme-dark:not(.pdf-shadows-on) .pdf-sidebar-container .thumbnailSelectionRing,.theme-light:not(.pdf-shadows-on) .pdf-sidebar-container .thumbnailSelectionRing{padding:0}.theme-dark:not(.pdf-shadows-on) .pdf-sidebar-container .thumbnail::after,.theme-light:not(.pdf-shadows-on) .pdf-sidebar-container .thumbnail::after{right:var(--size-4-2);bottom:var(--size-4-2)}.theme-dark{--pdf-thumbnail-shadow:0 0 1px 0 rgba(0,0,0,0.6);--pdf-shadow:0 0 1px 0 rgba(0,0,0,0.6)}.theme-dark .pdf-viewer .canvasWrapper{opacity:var(--pdf-dark-opacity)}.theme-dark.pdf-invert-dark .workspace-leaf-content[data-type=pdf] .pdf-viewer .canvasWrapper{filter:invert(1) hue-rotate(180deg);mix-blend-mode:screen}.theme-light.pdf-blend-light .workspace-leaf-content[data-type=pdf] .pdf-viewer .canvasWrapper{mix-blend-mode:multiply}body{--table-header-border-width:0;--table-column-first-border-width:0;--table-column-last-border-width:0;--table-row-last-border-width:0;--table-edge-cell-padding-first:0;--table-edge-cell-padding-last:0;--table-cell-padding:4px 10px;--table-header-size:var(--table-text-size)}.markdown-source-view.mod-cm6 table{border-collapse:collapse}.markdown-rendered th{--table-header-size:var(--table-text-size)}.markdown-preview-view table,.markdown-source-view.mod-cm6 table{border:var(--border-width) solid var(--border-color);border-collapse:collapse}.markdown-preview-view td,.markdown-preview-view th,.markdown-source-view.mod-cm6 td,.markdown-source-view.mod-cm6 th{padding:var(--table-cell-padding)}.markdown-preview-view td:first-child,.markdown-preview-view th:first-child,.markdown-source-view.mod-cm6 td:first-child,.markdown-source-view.mod-cm6 th:first-child{padding-inline-start:var(--table-edge-cell-padding-first)}.markdown-preview-view td:first-child .table-cell-wrapper,.markdown-preview-view th:first-child .table-cell-wrapper,.markdown-source-view.mod-cm6 td:first-child .table-cell-wrapper,.markdown-source-view.mod-cm6 th:first-child .table-cell-wrapper{padding-inline-start:0}.markdown-preview-view td:last-child,.markdown-preview-view th:last-child,.markdown-source-view.mod-cm6 td:last-child,.markdown-source-view.mod-cm6 th:last-child{padding-inline-end:var(--table-edge-cell-padding-last)}.markdown-preview-view td:last-child .table-cell-wrapper,.markdown-preview-view th:last-child .table-cell-wrapper,.markdown-source-view.mod-cm6 td:last-child .table-cell-wrapper,.markdown-source-view.mod-cm6 th:last-child .table-cell-wrapper{padding-inline-end:0}.markdown-source-view.mod-cm6 .cm-table-widget .table-cell-wrapper{padding:var(--table-cell-padding)}.markdown-reading-view table{--p-spacing:0.5rem}.cm-embed-block.cm-table-widget.markdown-rendered{padding:var(--table-drag-padding);overscroll-behavior-x:none}.is-mobile .cm-embed-block.cm-table-widget.markdown-rendered{padding-bottom:40px}.markdown-preview-view th,.markdown-source-view.mod-cm6 .dataview.table-view-table thead.table-view-thead tr th,.table-view-table>thead>tr>th{padding:var(--table-cell-padding)}.markdown-preview-view th:first-child,.markdown-source-view.mod-cm6 .dataview.table-view-table thead.table-view-thead tr th:first-child,.table-view-table>thead>tr>th:first-child{padding-inline-start:var(--table-edge-cell-padding-first)}.markdown-preview-view th:last-child,.markdown-source-view.mod-cm6 .dataview.table-view-table thead.table-view-thead tr th:last-child,.table-view-table>thead>tr>th:last-child{padding-inline-end:var(--table-edge-cell-padding-last)}.cm-hmd-table-sep-dummy,.cm-s-obsidian .HyperMD-table-row span.cm-hmd-table-sep{color:var(--text-faint);font-weight:400}body.minimal-unstyled-tags{--tag-background:transparent;--tag-background-hover:transparent;--tag-border-width:0px;--tag-padding-x:0;--tag-padding-y:0;--tag-size:inherit;--tag-color-hover:var(--text-accent-hover)}body.minimal-unstyled-tags.is-mobile.theme-dark{--tag-background:transparent}body:not(.minimal-unstyled-tags){--tag-size:0.8em;--tag-padding-y:0.2em;--tag-background:transparent;--tag-background-hover:transparent;--tag-color:var(--text-muted);--tag-border-width:1px;--tag-border-color:var(--background-modifier-border);--tag-border-color-hover:var(--background-modifier-border-hover);--tag-color-hover:var(--text-normal)}body.is-mobile.theme-dark{--tag-background:transparent}h1,h2,h3,h4{letter-spacing:-.02em}body,button,input{font-family:var(--font-interface)}.cm-s-obsidian span.cm-error{color:var(--color-red)}.markdown-preview-view,.popover,.workspace-leaf-content[data-type=markdown]{font-family:var(--font-text)}.markdown-preview-view,.view-content>.cm-s-obsidian,.view-content>.markdown-source-view.mod-cm6.is-live-preview>.cm-scroller,body{font-size:var(--font-adaptive-normal);font-weight:var(--normal-weight)}.view-content>.cm-s-obsidian,.view-content>.markdown-source-view,.view-content>.markdown-source-view.mod-cm6 .cm-scroller{font-family:var(--font-editor)}.cm-formatting:not(.cm-formatting-code-block):not(.cm-formatting-hashtag){color:var(--text-formatting)}.hide-markdown .is-live-preview .cm-formatting.cm-formatting-code.cm-inline-code,.hide-markdown .is-live-preview .cm-formatting.cm-formatting-em,.hide-markdown .is-live-preview .cm-formatting.cm-formatting-highlight,.hide-markdown .is-live-preview .cm-formatting.cm-formatting-link,.hide-markdown .is-live-preview .cm-formatting.cm-formatting-strikethrough,.hide-markdown .is-live-preview .cm-formatting.cm-formatting-strong{display:none}.hide-markdown .is-live-preview .cm-formatting-quote{opacity:0}.hide-markdown .is-live-preview .cm-formatting-link,.hide-markdown .is-live-preview .cm-formatting:has(+.cm-header),.hide-markdown .is-live-preview .cm-hmd-internal-link.cm-link-has-alias,.hide-markdown .is-live-preview .cm-link-alias-pipe{display:none}.active-line-on .workspace-leaf-content[data-type=markdown] .cm-line.cm-active,.active-line-on .workspace-leaf-content[data-type=markdown] .markdown-source-view.mod-cm6.is-live-preview .HyperMD-quote.cm-active{background-color:var(--active-line-bg);box-shadow:-25vw 0 var(--active-line-bg),25vw 0 var(--active-line-bg)}.disable-animations{--ribbon-animation-duration:0ms;--focus-animation-duration:0ms}.disable-animations .mod-sidedock{transition-duration:0s!important}.fast-animations{--ribbon-animation-duration:0.05s;--focus-animation-duration:0.05s}.fast-animations .mod-sidedock{transition-duration:70ms!important}body{--content-margin:auto;--content-margin-start:max( calc(50% - var(--line-width)/2), calc(50% - var(--max-width)/2) );--content-line-width:min(var(--line-width), var(--max-width))}.markdown-preview-view .markdown-preview-sizer.markdown-preview-sizer{max-width:100%;margin-inline:auto;width:100%}.markdown-source-view.mod-cm6.is-readable-line-width .cm-content,.markdown-source-view.mod-cm6.is-readable-line-width .cm-sizer{max-width:100%;width:100%}.markdown-preview-view.is-readable-line-width .markdown-preview-sizer>div,.markdown-source-view.mod-cm6.is-readable-line-width .cm-contentContainer.cm-contentContainer>.cm-content>div,.markdown-source-view.mod-cm6.is-readable-line-width .cm-sizer>.embedded-backlinks,.markdown-source-view.mod-cm6.is-readable-line-width .cm-sizer>.inline-title,.markdown-source-view.mod-cm6.is-readable-line-width .cm-sizer>.metadata-container{max-width:var(--max-width);width:var(--line-width);margin-inline:var(--content-margin)!important}.markdown-source-view.mod-cm6.is-readable-line-width .cm-contentContainer.cm-contentContainer>.cm-content>:not(div){max-width:var(--content-line-width);margin-inline-start:var(--content-margin-start)!important}.is-readable-line-width{--file-margins:1rem 0 0 0}.is-mobile .markdown-preview-view{--folding-offset:0}.minimal-line-nums .workspace-leaf-content[data-type=markdown]{--file-margins:var(--size-4-8) var(--size-4-8) var(--size-4-8) 48px}.minimal-line-nums .workspace-leaf-content[data-type=markdown].is-rtl{--file-margins:var(--size-4-8) 48px var(--size-4-8) var(--size-4-8)}.minimal-line-nums .workspace-leaf-content[data-type=markdown] .is-readable-line-width{--file-margins:1rem 0 0 var(--folding-offset)}.minimal-line-nums .workspace-leaf-content[data-type=markdown] .is-readable-line-width.is-rtl{--file-margins:1rem var(--folding-offset) 0 0}.minimal-line-nums .mod-left-split .markdown-preview-view,.minimal-line-nums .mod-left-split .markdown-source-view.mod-cm6 .cm-scroller,.minimal-line-nums .mod-right-split .markdown-preview-view,.minimal-line-nums .mod-right-split .markdown-source-view.mod-cm6 .cm-scroller{--file-margins:var(--size-4-5) var(--size-4-5) var(--size-4-5) 48px}.view-content .reader-mode-content.is-readable-line-width .markdown-preview-sizer{max-width:var(--max-width);width:var(--line-width)}.markdown-preview-view .inline-embed{--max-width:100%}body{--container-table-max-width:var(--max-width);--table-max-width:none;--table-width:auto;--table-margin:inherit;--table-wrapper-width:fit-content;--container-dataview-table-width:var(--line-width);--container-img-width:var(--line-width);--container-img-max-width:var(--max-width);--img-max-width:100%;--img-width:auto;--img-margin-start:var(--content-margin-start);--img-line-width:var(--content-line-width);--container-chart-width:var(--line-width);--container-chart-max-width:var(--max-width);--chart-max-width:none;--chart-width:auto;--container-map-width:var(--line-width);--container-map-max-width:var(--max-width);--map-max-width:none;--map-width:auto;--container-iframe-width:var(--line-width);--container-iframe-max-width:var(--max-width);--iframe-max-width:none;--iframe-width:auto}body .wide{--line-width:var(--line-width-wide);--container-table-width:var(--line-width-wide);--container-dataview-table-width:var(--line-width-wide);--container-img-width:var(--line-width-wide);--container-iframe-width:var(--line-width-wide);--container-map-width:var(--line-width-wide);--container-chart-width:var(--line-width-wide)}body .max{--line-width:var(--max-width);--container-table-width:var(--max-width);--container-dataview-table-width:var(--max-width);--container-img-width:var(--max-width);--container-iframe-width:var(--max-width);--container-map-width:var(--max-width);--container-chart-width:var(--max-width)}table.dataview{--table-min-width:min(var(--line-width),var(--max-width))}.cards table.dataview{--table-width:100%;--table-min-width:none}body{--table-drag-space:16px;--container-table-margin:calc(var(--content-margin-start) - var(--table-drag-space));--container-table-width:calc(var(--line-width) + var(--table-drag-space)*2);--table-drag-padding:var(--table-drag-space)}.is-mobile{--table-drag-space:16px;--container-table-max-width:calc(100% - var(--container-table-margin))}.maximize-tables-auto{--container-table-max-width:100%;--container-table-width:100%;--container-dataview-table-width:100%;--container-table-margin:0;--table-drag-padding:var(--table-drag-space) 0;--table-max-width:100%;--table-margin:var(--content-margin-start) auto;--table-width:auto}.maximize-tables-auto .cards{--container-table-max-width:var(--max-width)}.maximize-tables-auto .cards .block-language-dataview{--table-margin:auto}.maximize-tables{--container-table-max-width:100%;--container-table-width:100%;--container-table-margin:0;--table-drag-padding:var(--table-drag-space) 0;--table-min-width:min(var(--line-width), var(--max-width));--table-max-width:100%;--table-margin:auto;--table-width:auto;--table-edge-cell-padding-first:8px;--table-edge-cell-padding-last:8px;--table-wrapper-width:auto}.table-100,.table-max,.table-wide{--table-max-width:100%;--table-width:100%}.table-wide{--container-table-width:var(--line-width-wide);--container-dataview-table-width:var(--line-width-wide);--container-table-margin:auto;--table-edge-cell-padding-first:0px}.table-max{--container-table-width:var(--max-width);--container-table-max-width:calc(var(--max-width) + var(--table-drag-space)*2);--container-dataview-table-width:var(--max-width);--container-table-margin:auto;--table-edge-cell-padding-first:0px;--table-margin:0}.table-100{--container-table-width:100%;--container-dataview-table-width:100%;--container-table-max-width:100%;--container-table-margin:0;--table-edge-cell-padding-first:16px;--table-edge-cell-padding-last:16px;--table-margin:0;--table-drag-padding:var(--table-drag-space) 0;--table-wrapper-width:min(fit-content, 100%)}.table-100 .dataview.list-view-ul{max-width:var(--max-width);width:var(--line-width);margin-inline:auto}.table-100 .table-col-btn{display:none!important}.img-100,.img-max,.img-wide{--img-max-width:100%;--img-width:100%}.img-wide{--container-img-width:var(--line-width-wide);--img-line-width:var(--line-width-wide);--img-margin-start:calc(50% - var(--line-width-wide)/2)}.img-max{--container-img-width:var(--max-width);--img-line-width:var(--max-width);--img-margin-start:calc(50% - var(--max-width)/2)}.img-100{--container-img-width:100%;--container-img-max-width:100%;--img-line-width:100%;--img-margin-start:0}.map-100,.map-max,.map-wide{--map-max-width:100%;--map-width:100%}.map-wide{--container-map-width:var(--line-width-wide)}.map-max{--container-map-width:var(--max-width)}.map-100{--container-map-width:100%;--container-map-max-width:100%}.chart-100,.chart-max,.chart-wide{--chart-max-width:100%;--chart-width:100%}.chart-wide{--container-chart-width:var(--line-width-wide)}.chart-max{--container-chart-width:var(--max-width)}.chart-100{--container-chart-width:100%;--container-chart-max-width:100%}.iframe-100,.iframe-max,.iframe-wide{--iframe-max-width:100%;--iframe-width:100%}.iframe-wide{--container-iframe-width:var(--line-width-wide)}.iframe-max{--container-iframe-width:var(--max-width)}.iframe-100{--container-iframe-width:100%;--container-iframe-max-width:100%}.markdown-preview-view.is-readable-line-width .markdown-preview-sizer .cm-table-widget,.markdown-preview-view.is-readable-line-width .markdown-preview-sizer>div:has(>table),.markdown-source-view.mod-cm6.is-readable-line-width .cm-contentContainer.cm-contentContainer>.cm-content .cm-table-widget,.markdown-source-view.mod-cm6.is-readable-line-width .cm-contentContainer.cm-contentContainer>.cm-content>div:has(>table){width:var(--container-table-width);max-width:var(--container-table-max-width);margin-inline:var(--container-table-margin)!important;padding-inline-start:var(--table-drag-padding)}.markdown-preview-view.is-readable-line-width .markdown-preview-sizer .table-wrapper,.markdown-source-view.mod-cm6.is-readable-line-width .cm-contentContainer.cm-contentContainer>.cm-content .table-wrapper{width:var(--table-wrapper-width)}.markdown-preview-view.is-readable-line-width .markdown-preview-sizer>div:has(>.block-language-dataview table),.markdown-preview-view.is-readable-line-width .markdown-preview-sizer>div:has(>.block-language-dataviewjs table),.markdown-source-view.mod-cm6.is-readable-line-width .cm-contentContainer.cm-contentContainer>.cm-content>div:has(>.block-language-dataview table),.markdown-source-view.mod-cm6.is-readable-line-width .cm-contentContainer.cm-contentContainer>.cm-content>div:has(>.block-language-dataviewjs table){width:var(--container-dataview-table-width);max-width:var(--container-table-max-width)}.markdown-preview-view.is-readable-line-width .markdown-preview-sizer table,.markdown-source-view.mod-cm6.is-readable-line-width .cm-contentContainer.cm-contentContainer>.cm-content table{width:var(--table-width);max-width:var(--table-max-width);margin-inline:var(--table-margin);min-width:var(--table-min-width)}.markdown-preview-view.is-readable-line-width .markdown-preview-sizer .block-language-dataviewjs>:is(p,h1,h2,h3,h4,h5,h6),.markdown-source-view.mod-cm6.is-readable-line-width .cm-contentContainer.cm-contentContainer>.cm-content .block-language-dataviewjs>:is(p,h1,h2,h3,h4,h5,h6){width:var(--line-width);margin-inline:var(--content-margin)}.markdown-preview-view.is-readable-line-width .markdown-preview-sizer .block-language-dataviewjs>.dataview-error,.markdown-source-view.mod-cm6.is-readable-line-width .cm-contentContainer.cm-contentContainer>.cm-content .block-language-dataviewjs>.dataview-error{margin:0 auto;width:var(--content-line-width)}.markdown-preview-view.is-readable-line-width .markdown-preview-sizer .dataview.dataview-error-box,.markdown-source-view.mod-cm6.is-readable-line-width .cm-contentContainer.cm-contentContainer>.cm-content .dataview.dataview-error-box{margin-inline:var(--table-margin)}.markdown-preview-view.is-readable-line-width .markdown-preview-sizer>.image-embed,.markdown-source-view.mod-cm6.is-readable-line-width .cm-contentContainer.cm-contentContainer>.cm-content>.image-embed{padding-top:.25rem;padding-bottom:.25rem}.markdown-preview-view.is-readable-line-width .markdown-preview-sizer>.image-embed,.markdown-preview-view.is-readable-line-width .markdown-preview-sizer>div:has(.image-embed),.markdown-source-view.mod-cm6.is-readable-line-width .cm-contentContainer.cm-contentContainer>.cm-content>.image-embed,.markdown-source-view.mod-cm6.is-readable-line-width .cm-contentContainer.cm-contentContainer>.cm-content>div:has(.image-embed){width:var(--container-img-width);max-width:var(--container-img-max-width)}.markdown-preview-view.is-readable-line-width .markdown-preview-sizer>.image-embed img,.markdown-preview-view.is-readable-line-width .markdown-preview-sizer>div:has(.image-embed) img,.markdown-source-view.mod-cm6.is-readable-line-width .cm-contentContainer.cm-contentContainer>.cm-content>.image-embed img,.markdown-source-view.mod-cm6.is-readable-line-width .cm-contentContainer.cm-contentContainer>.cm-content>div:has(.image-embed) img{max-width:var(--img-max-width)}.markdown-preview-view.is-readable-line-width .markdown-preview-sizer>img,.markdown-source-view.mod-cm6.is-readable-line-width .cm-contentContainer.cm-contentContainer>.cm-content>img{max-width:var(--img-line-width);margin-inline-start:var(--img-margin-start)!important}.markdown-preview-view.is-readable-line-width .markdown-preview-sizer div:has(>.block-language-chart),.markdown-preview-view.is-readable-line-width .markdown-preview-sizer div:has(>.block-language-dataviewjs canvas),.markdown-source-view.mod-cm6.is-readable-line-width .cm-contentContainer.cm-contentContainer>.cm-content div:has(>.block-language-chart),.markdown-source-view.mod-cm6.is-readable-line-width .cm-contentContainer.cm-contentContainer>.cm-content div:has(>.block-language-dataviewjs canvas){width:var(--container-chart-width);max-width:var(--container-chart-max-width)}.markdown-preview-view.is-readable-line-width .markdown-preview-sizer div:has(>.block-language-chart) canvas,.markdown-preview-view.is-readable-line-width .markdown-preview-sizer div:has(>.block-language-dataviewjs canvas) canvas,.markdown-source-view.mod-cm6.is-readable-line-width .cm-contentContainer.cm-contentContainer>.cm-content div:has(>.block-language-chart) canvas,.markdown-source-view.mod-cm6.is-readable-line-width .cm-contentContainer.cm-contentContainer>.cm-content div:has(>.block-language-dataviewjs canvas) canvas{max-width:var(--map-chart-width)}.markdown-preview-view.is-readable-line-width .markdown-preview-sizer div:has(>.block-language-leaflet),.markdown-source-view.mod-cm6.is-readable-line-width .cm-contentContainer.cm-contentContainer>.cm-content div:has(>.block-language-leaflet){width:var(--container-map-width);max-width:var(--container-map-max-width)}.markdown-preview-view.is-readable-line-width .markdown-preview-sizer div:has(>.block-language-leaflet) iframe,.markdown-source-view.mod-cm6.is-readable-line-width .cm-contentContainer.cm-contentContainer>.cm-content div:has(>.block-language-leaflet) iframe{max-width:var(--map-max-width)}.markdown-preview-view.is-readable-line-width .markdown-preview-sizer div:has(>.cm-html-embed),.markdown-preview-view.is-readable-line-width .markdown-preview-sizer>div:has(>iframe),.markdown-source-view.mod-cm6.is-readable-line-width .cm-contentContainer.cm-contentContainer>.cm-content div:has(>.cm-html-embed),.markdown-source-view.mod-cm6.is-readable-line-width .cm-contentContainer.cm-contentContainer>.cm-content>div:has(>iframe){width:var(--container-iframe-width);max-width:var(--container-iframe-max-width)}.markdown-preview-view.is-readable-line-width .markdown-preview-sizer div:has(>.cm-html-embed) iframe,.markdown-preview-view.is-readable-line-width .markdown-preview-sizer>div:has(>iframe) iframe,.markdown-source-view.mod-cm6.is-readable-line-width .cm-contentContainer.cm-contentContainer>.cm-content div:has(>.cm-html-embed) iframe,.markdown-source-view.mod-cm6.is-readable-line-width .cm-contentContainer.cm-contentContainer>.cm-content>div:has(>iframe) iframe{max-width:var(--iframe-max-width)}.borders-none{--divider-width:0px;--tab-outline-width:0px}body:is(.borders-none) .mod-root .workspace-tab-header-container:is(div,:hover){--tab-outline-width:0px}body{--cards-min-width:180px;--cards-max-width:1fr;--cards-mobile-width:120px;--cards-image-height:400px;--cards-padding:1.2em;--cards-image-fit:contain;--cards-background:transparent;--cards-background-hover:transparent;--cards-border-width:1px;--cards-aspect-ratio:auto;--cards-columns:repeat(auto-fit, minmax(var(--cards-min-width), var(--cards-max-width)))}@media(max-width:400pt){body{--cards-min-width:var(--cards-mobile-width)}}.cards.table-100 table.dataview tbody,.table-100 .cards table.dataview tbody{padding:.25rem .75rem}.cards table.dataview{--table-width:100%;--table-edge-cell-padding-first:calc(var(--cards-padding)/2);--table-edge-cell-padding-last:calc(var(--cards-padding)/2);--table-cell-padding:calc(var(--cards-padding)/3) calc(var(--cards-padding)/2);line-height:1.3}.cards table.dataview tbody{clear:both;padding:.5rem 0;display:grid;grid-template-columns:var(--cards-columns);grid-column-gap:.75rem;grid-row-gap:.75rem}.cards table.dataview>tbody>tr{background-color:var(--cards-background);border:var(--cards-border-width) solid var(--background-modifier-border);display:flex;flex-direction:column;margin:0;padding:0 0 calc(var(--cards-padding)/3) 0;border-radius:6px;overflow:hidden;transition:box-shadow .15s linear;max-width:var(--cards-max-width);height:auto}.cards table.dataview>tbody>tr:hover{background-color:var(--cards-background-hover)!important;border:var(--cards-border-width) solid var(--background-modifier-border-hover);box-shadow:0 4px 6px 0 rgba(0,0,0,.05),0 1px 3px 1px rgba(0,0,0,.025);transition:box-shadow .15s linear}.cards table.dataview tbody>tr>td:first-child{font-weight:var(--bold-weight);border:none}.cards table.dataview tbody>tr>td:first-child a{display:block}.cards table.dataview tbody>tr>td:last-child{border:none}.cards table.dataview tbody>tr>td:not(:first-child){font-size:calc(var(--table-text-size)*.9);color:var(--text-muted)}.cards table.dataview tbody>tr>td>*{padding:calc(var(--cards-padding)/3) 0}.cards table.dataview tbody>tr>td:not(:last-child):not(:first-child){padding:4px 0;border-bottom:1px solid var(--background-modifier-border);width:calc(100% - var(--cards-padding));margin:0 calc(var(--cards-padding)/2)}.cards table.dataview tbody>tr>td a{text-decoration:none}.cards table.dataview tbody>tr>td>button{width:100%;margin:calc(var(--cards-padding)/2) 0}.cards table.dataview tbody>tr>td:last-child>button{margin-bottom:calc(var(--cards-padding)/6)}.cards table.dataview tbody>tr>td>ul{width:100%;padding:.25em 0!important;margin:0 auto!important}.cards table.dataview tbody>tr>td:has(img){padding:0!important;background-color:var(--background-secondary);display:block;margin:0;width:100%}.cards table.dataview tbody>tr>td img{aspect-ratio:var(--cards-aspect-ratio);width:100%;object-fit:var(--cards-image-fit);max-height:var(--cards-image-height);background-color:var(--background-secondary);vertical-align:bottom}.markdown-source-view.mod-cm6.cards .dataview.table-view-table>tbody>tr>td,.trim-cols .cards table.dataview tbody>tr>td{white-space:normal}.links-int-on .cards table{--link-decoration:none}.markdown-source-view.mod-cm6.cards .edit-block-button{top:-1px;right:28px;opacity:1}.cards.table-100 table.dataview thead>tr,.table-100 .cards table.dataview thead>tr{right:.75rem}.cards.table-100 table.dataview thead:before,.table-100 .cards table.dataview thead:before{margin-right:.75rem}.cards table.dataview thead{user-select:none;width:180px;display:block;float:right;position:relative;text-align:right;height:24px;padding-bottom:0}.cards table.dataview thead:hover:after{background-color:var(--background-modifier-hover)}.cards table.dataview thead:hover:before{background-color:var(--text-muted)}.cards table.dataview thead:after,.cards table.dataview thead:before{content:"";position:absolute;right:0;top:0;width:10px;height:16px;cursor:var(--cursor);text-align:right;padding:var(--size-4-1) var(--size-4-2);margin-bottom:2px;border-radius:var(--radius-s);font-weight:500;font-size:var(--font-adaptive-small)}.cards table.dataview thead:before{background-color:var(--text-faint);-webkit-mask-repeat:no-repeat;-webkit-mask-size:16px;-webkit-mask-position:center center;-webkit-mask-image:url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 100 100"><path fill="currentColor" d="M49.792 33.125l-5.892 5.892L33.333 28.45V83.333H25V28.45L14.438 39.017L8.542 33.125L29.167 12.5l20.625 20.625zm41.667 33.75L70.833 87.5l-20.625 -20.625l5.892 -5.892l10.571 10.567L66.667 16.667h8.333v54.883l10.567 -10.567l5.892 5.892z"></path></svg>')}.cards table.dataview thead>tr{top:-1px;position:absolute;display:none;z-index:9;border:1px solid var(--background-modifier-border-hover);background-color:var(--background-secondary);box-shadow:var(--shadow-s);padding:6px;border-radius:var(--radius-m);flex-direction:column;margin:24px 0 0 0;width:100%}.cards table.dataview thead:hover>tr{display:flex;height:auto}.cards table.dataview thead>tr>th{display:block;padding:3px 30px 3px 6px!important;border-radius:var(--radius-s);width:100%;font-weight:400;color:var(--text-normal);cursor:var(--cursor);border:none;font-size:var(--font-ui-small)}.cards table.dataview thead>tr>th[sortable-style=sortable-asc],.cards table.dataview thead>tr>th[sortable-style=sortable-desc]{color:var(--text-normal)}.cards table.dataview thead>tr>th:hover{color:var(--text-normal);background-color:var(--background-modifier-hover)}.list-cards.markdown-preview-view .list-bullet,.list-cards.markdown-preview-view .list-collapse-indicator,.list-cards.markdown-preview-view.markdown-rendered.show-indentation-guide li>ul::before{display:none}.list-cards.markdown-preview-view div>ul{display:grid;gap:.75rem;grid-template-columns:var(--cards-columns);padding:0;line-height:var(--line-height-tight)}.list-cards.markdown-preview-view div>ul .contains-task-list{padding-inline-start:calc(var(--cards-padding)*1.5)}.list-cards.markdown-preview-view div>ul>li{background-color:var(--cards-background);padding:calc(var(--cards-padding)/2);border-radius:var(--radius-s);border:var(--cards-border-width) solid var(--background-modifier-border);overflow:hidden;margin-inline-start:0}.list-cards.markdown-preview-view div>ul .image-embed{padding:0;display:block;background-color:var(--background-secondary);border-radius:var(--image-radius)}.list-cards.markdown-preview-view div>ul .image-embed img{aspect-ratio:var(--cards-aspect-ratio);object-fit:var(--cards-image-fit);max-height:var(--cards-image-height);background-color:var(--background-secondary);vertical-align:bottom}.list-cards.markdown-preview-view div>ul>li>a{--link-decoration:none;--link-external-decoration:none;font-weight:var(--bold-weight)}.list-cards.markdown-preview-view div ul>li:hover{border-color:var(--background-modifier-border-hover)}.list-cards.markdown-preview-view div ul ul{display:block;width:100%;color:var(--text-muted);font-size:var(--font-smallest);margin:calc(var(--cards-padding)/-4) 0;padding:calc(var(--cards-padding)/2) 0}.list-cards.markdown-preview-view div ul ul ul{padding-bottom:calc(var(--cards-padding)/4)}.list-cards.markdown-preview-view div ul ul>li{display:block;margin-inline-start:0}.cards.cards-16-9,.list-cards.cards-16-9{--cards-aspect-ratio:16/9}.cards.cards-1-1,.list-cards.cards-1-1{--cards-aspect-ratio:1/1}.cards.cards-2-1,.list-cards.cards-2-1{--cards-aspect-ratio:2/1}.cards.cards-2-3,.list-cards.cards-2-3{--cards-aspect-ratio:2/3}.cards.cards-cols-1,.list-cards.cards-cols-1{--cards-columns:repeat(1, minmax(0, 1fr))}.cards.cards-cols-2,.list-cards.cards-cols-2{--cards-columns:repeat(2, minmax(0, 1fr))}.cards.cards-cover,.list-cards.cards-cover{--cards-image-fit:cover}.cards.cards-align-bottom table.dataview tbody>tr>td:last-child,.list-cards.cards-align-bottom table.dataview tbody>tr>td:last-child{margin-top:auto}@media(max-width:400pt){.cards table.dataview tbody>tr>td:not(:first-child){font-size:80%}}@media(min-width:400pt){.cards-cols-3{--cards-columns:repeat(3, minmax(0, 1fr))}.cards-cols-4{--cards-columns:repeat(4, minmax(0, 1fr))}.cards-cols-5{--cards-columns:repeat(5, minmax(0, 1fr))}.cards-cols-6{--cards-columns:repeat(6, minmax(0, 1fr))}.cards-cols-7{--cards-columns:repeat(7, minmax(0, 1fr))}.cards-cols-8{--cards-columns:repeat(8, minmax(0, 1fr))}}.cm-formatting.cm-formatting-task.cm-property{font-family:var(--font-monospace)}input[data-task="!"]:checked,input[data-task="*"]:checked,input[data-task="-"]:checked,input[data-task="<"]:checked,input[data-task=">"]:checked,input[data-task=I]:checked,input[data-task=b]:checked,input[data-task=c]:checked,input[data-task=d]:checked,input[data-task=f]:checked,input[data-task=k]:checked,input[data-task=l]:checked,input[data-task=p]:checked,input[data-task=u]:checked,input[data-task=w]:checked,li[data-task="!"]>input:checked,li[data-task="!"]>p>input:checked,li[data-task="*"]>input:checked,li[data-task="*"]>p>input:checked,li[data-task="-"]>input:checked,li[data-task="-"]>p>input:checked,li[data-task="<"]>input:checked,li[data-task="<"]>p>input:checked,li[data-task=">"]>input:checked,li[data-task=">"]>p>input:checked,li[data-task=I]>input:checked,li[data-task=I]>p>input:checked,li[data-task=b]>input:checked,li[data-task=b]>p>input:checked,li[data-task=c]>input:checked,li[data-task=c]>p>input:checked,li[data-task=d]>input:checked,li[data-task=d]>p>input:checked,li[data-task=f]>input:checked,li[data-task=f]>p>input:checked,li[data-task=k]>input:checked,li[data-task=k]>p>input:checked,li[data-task=l]>input:checked,li[data-task=l]>p>input:checked,li[data-task=p]>input:checked,li[data-task=p]>p>input:checked,li[data-task=u]>input:checked,li[data-task=u]>p>input:checked,li[data-task=w]>input:checked,li[data-task=w]>p>input:checked{--checkbox-marker-color:transparent;border:none;border-radius:0;background-image:none;background-color:currentColor;-webkit-mask-size:var(--checkbox-icon);-webkit-mask-position:50% 50%}input[data-task=">"]:checked,li[data-task=">"]>input:checked,li[data-task=">"]>p>input:checked{color:var(--text-faint);transform:rotate(90deg);-webkit-mask-position:50% 100%;-webkit-mask-image:url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' class='h-5 w-5' viewBox='0 0 20 20' fill='currentColor'%3E%3Cpath d='M10.894 2.553a1 1 0 00-1.788 0l-7 14a1 1 0 001.169 1.409l5-1.429A1 1 0 009 15.571V11a1 1 0 112 0v4.571a1 1 0 00.725.962l5 1.428a1 1 0 001.17-1.408l-7-14z' /%3E%3C/svg%3E")}input[data-task="<"]:checked,li[data-task="<"]>input:checked,li[data-task="<"]>p>input:checked{color:var(--text-faint);-webkit-mask-image:url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' class='h-5 w-5' viewBox='0 0 20 20' fill='currentColor'%3E%3Cpath fill-rule='evenodd' d='M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z' clip-rule='evenodd' /%3E%3C/svg%3E");-webkit-mask-image:url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' class='h-5 w-5' viewBox='0 0 20 20' fill='currentColor'%3E%3Cpath fill-rule='evenodd' d='M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z' clip-rule='evenodd' /%3E%3C/svg%3E")}input[data-task="?"]:checked,li[data-task="?"]>input:checked,li[data-task="?"]>p>input:checked{--checkbox-marker-color:transparent;background-color:var(--color-yellow);border-color:var(--color-yellow);background-position:50% 50%;background-size:200% 90%;background-image:url('data:image/svg+xml,%3Csvg xmlns="http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg" width="20" height="20" preserveAspectRatio="xMidYMid meet" viewBox="0 0 16 16"%3E%3Cpath fill="white" fill-rule="evenodd" d="M4.475 5.458c-.284 0-.514-.237-.47-.517C4.28 3.24 5.576 2 7.825 2c2.25 0 3.767 1.36 3.767 3.215c0 1.344-.665 2.288-1.79 2.973c-1.1.659-1.414 1.118-1.414 2.01v.03a.5.5 0 0 1-.5.5h-.77a.5.5 0 0 1-.5-.495l-.003-.2c-.043-1.221.477-2.001 1.645-2.712c1.03-.632 1.397-1.135 1.397-2.028c0-.979-.758-1.698-1.926-1.698c-1.009 0-1.71.529-1.938 1.402c-.066.254-.278.461-.54.461h-.777ZM7.496 14c.622 0 1.095-.474 1.095-1.09c0-.618-.473-1.092-1.095-1.092c-.606 0-1.087.474-1.087 1.091S6.89 14 7.496 14Z"%2F%3E%3C%2Fsvg%3E')}.theme-dark input[data-task="?"]:checked,.theme-dark li[data-task="?"]>input:checked,.theme-dark li[data-task="?"]>p>input:checked{background-image:url('data:image/svg+xml,%3Csvg xmlns="http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg" width="20" height="20" preserveAspectRatio="xMidYMid meet" viewBox="0 0 16 16"%3E%3Cpath fill="black" fill-opacity="0.8" fill-rule="evenodd" d="M4.475 5.458c-.284 0-.514-.237-.47-.517C4.28 3.24 5.576 2 7.825 2c2.25 0 3.767 1.36 3.767 3.215c0 1.344-.665 2.288-1.79 2.973c-1.1.659-1.414 1.118-1.414 2.01v.03a.5.5 0 0 1-.5.5h-.77a.5.5 0 0 1-.5-.495l-.003-.2c-.043-1.221.477-2.001 1.645-2.712c1.03-.632 1.397-1.135 1.397-2.028c0-.979-.758-1.698-1.926-1.698c-1.009 0-1.71.529-1.938 1.402c-.066.254-.278.461-.54.461h-.777ZM7.496 14c.622 0 1.095-.474 1.095-1.09c0-.618-.473-1.092-1.095-1.092c-.606 0-1.087.474-1.087 1.091S6.89 14 7.496 14Z"%2F%3E%3C%2Fsvg%3E')}input[data-task="/"]:checked,li[data-task="/"]>input:checked,li[data-task="/"]>p>input:checked{background-image:none;background-color:rgba(0,0,0,0);position:relative;overflow:hidden}input[data-task="/"]:checked:after,li[data-task="/"]>input:checked:after,li[data-task="/"]>p>input:checked:after{top:0;left:0;content:" ";display:block;position:absolute;background-color:var(--background-modifier-accent);width:calc(50% - .5px);height:100%;-webkit-mask-image:none}input[data-task="!"]:checked,li[data-task="!"]>input:checked,li[data-task="!"]>p>input:checked{color:var(--color-orange);-webkit-mask-image:url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' class='h-5 w-5' viewBox='0 0 20 20' fill='currentColor'%3E%3Cpath fill-rule='evenodd' d='M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z' clip-rule='evenodd' /%3E%3C/svg%3E")}input[data-task='"']:checked,input[data-task=“]:checked,li[data-task='"']>input:checked,li[data-task='"']>p>input:checked,li[data-task=“]>input:checked,li[data-task=“]>p>input:checked{--checkbox-marker-color:transparent;background-position:50% 50%;background-color:var(--color-cyan);border-color:var(--color-cyan);background-size:75%;background-repeat:no-repeat;background-image:url('data:image/svg+xml,%3Csvg xmlns="http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg" width="20" height="20" preserveAspectRatio="xMidYMid meet" viewBox="0 0 24 24"%3E%3Cpath fill="white" d="M6.5 10c-.223 0-.437.034-.65.065c.069-.232.14-.468.254-.68c.114-.308.292-.575.469-.844c.148-.291.409-.488.601-.737c.201-.242.475-.403.692-.604c.213-.21.492-.315.714-.463c.232-.133.434-.28.65-.35l.539-.222l.474-.197l-.485-1.938l-.597.144c-.191.048-.424.104-.689.171c-.271.05-.56.187-.882.312c-.318.142-.686.238-1.028.466c-.344.218-.741.4-1.091.692c-.339.301-.748.562-1.05.945c-.33.358-.656.734-.909 1.162c-.293.408-.492.856-.702 1.299c-.19.443-.343.896-.468 1.336c-.237.882-.343 1.72-.384 2.437c-.034.718-.014 1.315.028 1.747c.015.204.043.402.063.539l.025.168l.026-.006A4.5 4.5 0 1 0 6.5 10zm11 0c-.223 0-.437.034-.65.065c.069-.232.14-.468.254-.68c.114-.308.292-.575.469-.844c.148-.291.409-.488.601-.737c.201-.242.475-.403.692-.604c.213-.21.492-.315.714-.463c.232-.133.434-.28.65-.35l.539-.222l.474-.197l-.485-1.938l-.597.144c-.191.048-.424.104-.689.171c-.271.05-.56.187-.882.312c-.317.143-.686.238-1.028.467c-.344.218-.741.4-1.091.692c-.339.301-.748.562-1.05.944c-.33.358-.656.734-.909 1.162c-.293.408-.492.856-.702 1.299c-.19.443-.343.896-.468 1.336c-.237.882-.343 1.72-.384 2.437c-.034.718-.014 1.315.028 1.747c.015.204.043.402.063.539l.025.168l.026-.006A4.5 4.5 0 1 0 17.5 10z"%2F%3E%3C%2Fsvg%3E')}.theme-dark input[data-task='"']:checked,.theme-dark input[data-task=“]:checked,.theme-dark li[data-task='"']>input:checked,.theme-dark li[data-task='"']>p>input:checked,.theme-dark li[data-task=“]>input:checked,.theme-dark li[data-task=“]>p>input:checked{background-image:url('data:image/svg+xml,%3Csvg xmlns="http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg" width="20" height="20" preserveAspectRatio="xMidYMid meet" viewBox="0 0 24 24"%3E%3Cpath fill="black" fill-opacity="0.7" d="M6.5 10c-.223 0-.437.034-.65.065c.069-.232.14-.468.254-.68c.114-.308.292-.575.469-.844c.148-.291.409-.488.601-.737c.201-.242.475-.403.692-.604c.213-.21.492-.315.714-.463c.232-.133.434-.28.65-.35l.539-.222l.474-.197l-.485-1.938l-.597.144c-.191.048-.424.104-.689.171c-.271.05-.56.187-.882.312c-.318.142-.686.238-1.028.466c-.344.218-.741.4-1.091.692c-.339.301-.748.562-1.05.945c-.33.358-.656.734-.909 1.162c-.293.408-.492.856-.702 1.299c-.19.443-.343.896-.468 1.336c-.237.882-.343 1.72-.384 2.437c-.034.718-.014 1.315.028 1.747c.015.204.043.402.063.539l.025.168l.026-.006A4.5 4.5 0 1 0 6.5 10zm11 0c-.223 0-.437.034-.65.065c.069-.232.14-.468.254-.68c.114-.308.292-.575.469-.844c.148-.291.409-.488.601-.737c.201-.242.475-.403.692-.604c.213-.21.492-.315.714-.463c.232-.133.434-.28.65-.35l.539-.222l.474-.197l-.485-1.938l-.597.144c-.191.048-.424.104-.689.171c-.271.05-.56.187-.882.312c-.317.143-.686.238-1.028.467c-.344.218-.741.4-1.091.692c-.339.301-.748.562-1.05.944c-.33.358-.656.734-.909 1.162c-.293.408-.492.856-.702 1.299c-.19.443-.343.896-.468 1.336c-.237.882-.343 1.72-.384 2.437c-.034.718-.014 1.315.028 1.747c.015.204.043.402.063.539l.025.168l.026-.006A4.5 4.5 0 1 0 17.5 10z"%2F%3E%3C%2Fsvg%3E')}input[data-task="-"]:checked,li[data-task="-"]>input:checked,li[data-task="-"]>p>input:checked{color:var(--text-faint);-webkit-mask-image:url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' class='h-5 w-5' viewBox='0 0 20 20' fill='currentColor'%3E%3Cpath fill-rule='evenodd' d='M3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z' clip-rule='evenodd' /%3E%3C/svg%3E")}body:not(.tasks) .markdown-preview-view ul li[data-task="-"].task-list-item.is-checked,body:not(.tasks) .markdown-source-view.mod-cm6 .HyperMD-task-line[data-task]:is([data-task="-"]),body:not(.tasks) li[data-task="-"].task-list-item.is-checked{color:var(--text-faint);text-decoration:line-through solid var(--text-faint) 1px}input[data-task="*"]:checked,li[data-task="*"]>input:checked,li[data-task="*"]>p>input:checked{color:var(--color-yellow);-webkit-mask-image:url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' class='h-5 w-5' viewBox='0 0 20 20' fill='currentColor'%3E%3Cpath d='M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z' /%3E%3C/svg%3E")}input[data-task=l]:checked,li[data-task=l]>input:checked,li[data-task=l]>p>input:checked{color:var(--color-red);-webkit-mask-image:url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' class='h-5 w-5' viewBox='0 0 20 20' fill='currentColor'%3E%3Cpath fill-rule='evenodd' d='M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z' clip-rule='evenodd' /%3E%3C/svg%3E")}input[data-task=i]:checked,li[data-task=i]>input:checked,li[data-task=i]>p>input:checked{--checkbox-marker-color:transparent;background-color:var(--color-blue);border-color:var(--color-blue);background-position:50%;background-size:100%;background-image:url('data:image/svg+xml,%3Csvg xmlns="http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg" width="20" height="20" preserveAspectRatio="xMidYMid meet" viewBox="0 0 512 512"%3E%3Cpath fill="none" stroke="white" stroke-linecap="round" stroke-linejoin="round" stroke-width="40" d="M196 220h64v172"%2F%3E%3Cpath fill="none" stroke="white" stroke-linecap="round" stroke-miterlimit="10" stroke-width="40" d="M187 396h138"%2F%3E%3Cpath fill="white" d="M256 160a32 32 0 1 1 32-32a32 32 0 0 1-32 32Z"%2F%3E%3C%2Fsvg%3E')}.theme-dark input[data-task=i]:checked,.theme-dark li[data-task=i]>input:checked,.theme-dark li[data-task=i]>p>input:checked{background-image:url('data:image/svg+xml,%3Csvg xmlns="http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg" width="20" height="20" preserveAspectRatio="xMidYMid meet" viewBox="0 0 512 512"%3E%3Cpath fill="none" stroke="black" stroke-opacity="0.8" stroke-linecap="round" stroke-linejoin="round" stroke-width="40" d="M196 220h64v172"%2F%3E%3Cpath fill="none" stroke="black" stroke-opacity="0.8" stroke-linecap="round" stroke-miterlimit="10" stroke-width="40" d="M187 396h138"%2F%3E%3Cpath fill="black" fill-opacity="0.8" d="M256 160a32 32 0 1 1 32-32a32 32 0 0 1-32 32Z"%2F%3E%3C%2Fsvg%3E')}input[data-task=S]:checked,li[data-task=S]>input:checked,li[data-task=S]>p>input:checked{--checkbox-marker-color:transparent;border-color:var(--color-green);background-color:var(--color-green);background-size:100%;background-image:url('data:image/svg+xml,%3Csvg xmlns="http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg" width="20" height="20" preserveAspectRatio="xMidYMid meet" viewBox="0 0 48 48"%3E%3Cpath fill="white" fill-rule="evenodd" d="M26 8a2 2 0 1 0-4 0v2a8 8 0 1 0 0 16v8a4.002 4.002 0 0 1-3.773-2.666a2 2 0 0 0-3.771 1.332A8.003 8.003 0 0 0 22 38v2a2 2 0 1 0 4 0v-2a8 8 0 1 0 0-16v-8a4.002 4.002 0 0 1 3.773 2.666a2 2 0 0 0 3.771-1.332A8.003 8.003 0 0 0 26 10V8Zm-4 6a4 4 0 0 0 0 8v-8Zm4 12v8a4 4 0 0 0 0-8Z" clip-rule="evenodd"%2F%3E%3C%2Fsvg%3E')}.theme-dark input[data-task=S]:checked,.theme-dark li[data-task=S]>input:checked,.theme-dark li[data-task=S]>p>input:checked{background-image:url('data:image/svg+xml,%3Csvg xmlns="http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg" width="20" height="20" preserveAspectRatio="xMidYMid meet" viewBox="0 0 48 48"%3E%3Cpath fill-opacity="0.8" fill="black" fill-rule="evenodd" d="M26 8a2 2 0 1 0-4 0v2a8 8 0 1 0 0 16v8a4.002 4.002 0 0 1-3.773-2.666a2 2 0 0 0-3.771 1.332A8.003 8.003 0 0 0 22 38v2a2 2 0 1 0 4 0v-2a8 8 0 1 0 0-16v-8a4.002 4.002 0 0 1 3.773 2.666a2 2 0 0 0 3.771-1.332A8.003 8.003 0 0 0 26 10V8Zm-4 6a4 4 0 0 0 0 8v-8Zm4 12v8a4 4 0 0 0 0-8Z" clip-rule="evenodd"%2F%3E%3C%2Fsvg%3E')}input[data-task=I]:checked,li[data-task=I]>input:checked,li[data-task=I]>p>input:checked{color:var(--color-yellow);-webkit-mask-image:url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' class='h-5 w-5' viewBox='0 0 20 20' fill='currentColor'%3E%3Cpath d='M11 3a1 1 0 10-2 0v1a1 1 0 102 0V3zM15.657 5.757a1 1 0 00-1.414-1.414l-.707.707a1 1 0 001.414 1.414l.707-.707zM18 10a1 1 0 01-1 1h-1a1 1 0 110-2h1a1 1 0 011 1zM5.05 6.464A1 1 0 106.464 5.05l-.707-.707a1 1 0 00-1.414 1.414l.707.707zM5 10a1 1 0 01-1 1H3a1 1 0 110-2h1a1 1 0 011 1zM8 16v-1h4v1a2 2 0 11-4 0zM12 14c.015-.34.208-.646.477-.859a4 4 0 10-4.954 0c.27.213.462.519.476.859h4.002z' /%3E%3C/svg%3E")}input[data-task=f]:checked,li[data-task=f]>input:checked,li[data-task=f]>p>input:checked{color:var(--color-red);-webkit-mask-image:url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' class='h-5 w-5' viewBox='0 0 20 20' fill='currentColor'%3E%3Cpath fill-rule='evenodd' d='M12.395 2.553a1 1 0 00-1.45-.385c-.345.23-.614.558-.822.88-.214.33-.403.713-.57 1.116-.334.804-.614 1.768-.84 2.734a31.365 31.365 0 00-.613 3.58 2.64 2.64 0 01-.945-1.067c-.328-.68-.398-1.534-.398-2.654A1 1 0 005.05 6.05 6.981 6.981 0 003 11a7 7 0 1011.95-4.95c-.592-.591-.98-.985-1.348-1.467-.363-.476-.724-1.063-1.207-2.03zM12.12 15.12A3 3 0 017 13s.879.5 2.5.5c0-1 .5-4 1.25-4.5.5 1 .786 1.293 1.371 1.879A2.99 2.99 0 0113 13a2.99 2.99 0 01-.879 2.121z' clip-rule='evenodd' /%3E%3C/svg%3E")}input[data-task=k]:checked,li[data-task=k]>input:checked,li[data-task=k]>p>input:checked{color:var(--color-yellow);-webkit-mask-image:url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' class='h-5 w-5' viewBox='0 0 20 20' fill='currentColor'%3E%3Cpath fill-rule='evenodd' d='M18 8a6 6 0 01-7.743 5.743L10 14l-1 1-1 1H6v2H2v-4l4.257-4.257A6 6 0 1118 8zm-6-4a1 1 0 100 2 2 2 0 012 2 1 1 0 102 0 4 4 0 00-4-4z' clip-rule='evenodd' /%3E%3C/svg%3E")}input[data-task=u]:checked,li[data-task=u]>input:checked,li[data-task=u]>p>input:checked{color:var(--color-green);-webkit-mask-image:url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' class='h-5 w-5' viewBox='0 0 20 20' fill='currentColor'%3E%3Cpath fill-rule='evenodd' d='M12 7a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0V8.414l-4.293 4.293a1 1 0 01-1.414 0L8 10.414l-4.293 4.293a1 1 0 01-1.414-1.414l5-5a1 1 0 011.414 0L11 10.586 14.586 7H12z' clip-rule='evenodd' /%3E%3C/svg%3E")}input[data-task=d]:checked,li[data-task=d]>input:checked,li[data-task=d]>p>input:checked{color:var(--color-red);-webkit-mask-image:url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' class='h-5 w-5' viewBox='0 0 20 20' fill='currentColor'%3E%3Cpath fill-rule='evenodd' d='M12 13a1 1 0 100 2h5a1 1 0 001-1V9a1 1 0 10-2 0v2.586l-4.293-4.293a1 1 0 00-1.414 0L8 9.586 3.707 5.293a1 1 0 00-1.414 1.414l5 5a1 1 0 001.414 0L11 9.414 14.586 13H12z' clip-rule='evenodd' /%3E%3C/svg%3E")}input[data-task=w]:checked,li[data-task=w]>input:checked,li[data-task=w]>p>input:checked{color:var(--color-purple);-webkit-mask-image:url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' class='h-5 w-5' viewBox='0 0 20 20' fill='currentColor'%3E%3Cpath fill-rule='evenodd' d='M6 3a1 1 0 011-1h.01a1 1 0 010 2H7a1 1 0 01-1-1zm2 3a1 1 0 00-2 0v1a2 2 0 00-2 2v1a2 2 0 00-2 2v.683a3.7 3.7 0 011.055.485 1.704 1.704 0 001.89 0 3.704 3.704 0 014.11 0 1.704 1.704 0 001.89 0 3.704 3.704 0 014.11 0 1.704 1.704 0 001.89 0A3.7 3.7 0 0118 12.683V12a2 2 0 00-2-2V9a2 2 0 00-2-2V6a1 1 0 10-2 0v1h-1V6a1 1 0 10-2 0v1H8V6zm10 8.868a3.704 3.704 0 01-4.055-.036 1.704 1.704 0 00-1.89 0 3.704 3.704 0 01-4.11 0 1.704 1.704 0 00-1.89 0A3.704 3.704 0 012 14.868V17a1 1 0 001 1h14a1 1 0 001-1v-2.132zM9 3a1 1 0 011-1h.01a1 1 0 110 2H10a1 1 0 01-1-1zm3 0a1 1 0 011-1h.01a1 1 0 110 2H13a1 1 0 01-1-1z' clip-rule='evenodd' /%3E%3C/svg%3E")}input[data-task=p]:checked,li[data-task=p]>input:checked,li[data-task=p]>p>input:checked{color:var(--color-green);-webkit-mask-image:url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' class='h-5 w-5' viewBox='0 0 20 20' fill='currentColor'%3E%3Cpath d='M2 10.5a1.5 1.5 0 113 0v6a1.5 1.5 0 01-3 0v-6zM6 10.333v5.43a2 2 0 001.106 1.79l.05.025A4 4 0 008.943 18h5.416a2 2 0 001.962-1.608l1.2-6A2 2 0 0015.56 8H12V4a2 2 0 00-2-2 1 1 0 00-1 1v.667a4 4 0 01-.8 2.4L6.8 7.933a4 4 0 00-.8 2.4z' /%3E%3C/svg%3E")}input[data-task=c]:checked,li[data-task=c]>input:checked,li[data-task=c]>p>input:checked{color:var(--color-orange);-webkit-mask-image:url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' class='h-5 w-5' viewBox='0 0 20 20' fill='currentColor'%3E%3Cpath d='M18 9.5a1.5 1.5 0 11-3 0v-6a1.5 1.5 0 013 0v6zM14 9.667v-5.43a2 2 0 00-1.105-1.79l-.05-.025A4 4 0 0011.055 2H5.64a2 2 0 00-1.962 1.608l-1.2 6A2 2 0 004.44 12H8v4a2 2 0 002 2 1 1 0 001-1v-.667a4 4 0 01.8-2.4l1.4-1.866a4 4 0 00.8-2.4z' /%3E%3C/svg%3E")}input[data-task=b]:checked,li[data-task=b]>input:checked,li[data-task=b]>p>input:checked{color:var(--color-orange);-webkit-mask-image:url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' class='h-5 w-5' viewBox='0 0 20 20' fill='currentColor'%3E%3Cpath d='M5 4a2 2 0 012-2h6a2 2 0 012 2v14l-5-2.5L5 18V4z' /%3E%3C/svg%3E")}.colorful-active .nav-files-container{--nav-item-background-active:var(--interactive-accent);--nav-item-color-active:var(--text-on-accent)}.colorful-active .nav-files-container .is-active .nav-file-tag{color:var(--text-on-accent);opacity:.6}.colorful-active .tree-item-self.is-being-renamed:focus-within{--nav-item-color-active:var(--text-normal)}.colorful-active #calendar-container .active,.colorful-active #calendar-container .active.today,.colorful-active #calendar-container .active:hover,.colorful-active #calendar-container .day:active{background-color:var(--interactive-accent);color:var(--text-on-accent)}.colorful-active #calendar-container .active .dot,.colorful-active #calendar-container .day:active .dot,.colorful-active #calendar-container .today.active .dot{fill:var(--text-on-accent)}body:not(.colorful-active) .horizontal-tab-nav-item.is-active,body:not(.colorful-active) .vertical-tab-nav-item.is-active{background-color:var(--bg3);color:var(--text-normal)}body{--frame-background:hsl( var(--frame-background-h), var(--frame-background-s), var(--frame-background-l));--frame-icon-color:var(--frame-muted-color)}.theme-light{--frame-background-h:var(--accent-h);--frame-background-s:var(--accent-s);--frame-background-l:calc(var(--accent-l) + 30%);--frame-outline-color:hsla( var(--frame-background-h), var(--frame-background-s), calc(var(--frame-background-l) - 6.5%), 1 );--frame-muted-color:hsl( var(--frame-background-h), calc(var(--frame-background-s) - 10%), calc(var(--frame-background-l) - 35%))}.theme-dark{--frame-background-h:var(--accent-h);--frame-background-s:var(--accent-s);--frame-background-l:calc(var(--accent-l) - 25%);--frame-outline-color:hsla( var(--frame-background-h), calc(var(--frame-background-s) - 2%), calc(var(--frame-background-l) + 6.5%), 1 );--frame-muted-color:hsl( var(--frame-background-h), calc(var(--frame-background-s) - 10%), calc(var(--frame-background-l) + 25%))}.colorful-frame.theme-dark{--tab-outline-width:0px}.colorful-frame,.colorful-frame.is-focused{--frame-divider-color:var(--frame-outline-color);--titlebar-background:var(--frame-background);--titlebar-background-focused:var(--frame-background);--titlebar-text-color:var(--frame-muted-color);--minimal-tab-text-color:var(--frame-muted-color)}.colorful-frame .workspace-tabs:not(.mod-stacked),.colorful-frame.is-focused .workspace-tabs:not(.mod-stacked){--tab-text-color:var(--minimal-tab-text-color);--tab-text-color-focused:var(--minimal-tab-text-color)}.colorful-frame .mod-top .workspace-tab-header-container,.colorful-frame .titlebar,.colorful-frame .workspace-ribbon.mod-left:before,.colorful-frame.is-focused .mod-top .workspace-tab-header-container,.colorful-frame.is-focused .titlebar,.colorful-frame.is-focused .workspace-ribbon.mod-left:before{--tab-outline-color:var(--frame-outline-color);--tab-divider-color:var(--frame-outline-color)}.colorful-frame .mod-root .workspace-tab-header .workspace-tab-header-inner-icon,.colorful-frame.is-focused .mod-root .workspace-tab-header .workspace-tab-header-inner-icon{--icon-color:var(--minimal-tab-text-color-active);--icon-color-hover:var(--minimal-tab-text-color-active);--icon-color-active:var(--minimal-tab-text-color-active);--icon-color-focused:var(--minimal-tab-text-color-active)}.colorful-frame .mod-left-split .mod-top .workspace-tab-header,.colorful-frame .mod-right-split .mod-top .workspace-tab-header,.colorful-frame .sidebar-toggle-button,.colorful-frame .workspace-tab-header-new-tab,.colorful-frame .workspace-tab-header-tab-list,.colorful-frame .workspace-tab-header:not(.is-active),.colorful-frame.is-focused .mod-left-split .mod-top .workspace-tab-header,.colorful-frame.is-focused .mod-right-split .mod-top .workspace-tab-header,.colorful-frame.is-focused .sidebar-toggle-button,.colorful-frame.is-focused .workspace-tab-header-new-tab,.colorful-frame.is-focused .workspace-tab-header-tab-list,.colorful-frame.is-focused .workspace-tab-header:not(.is-active){--background-modifier-hover:var(--frame-outline-color);--icon-color:var(--frame-icon-color);--icon-color-hover:var(--frame-icon-color);--icon-color-active:var(--frame-icon-color);--icon-color-focused:var(--frame-icon-color);--icon-color-focus:var(--frame-icon-color)}.colorful-frame .mod-left-split .mod-top .workspace-tab-header.is-active .workspace-tab-header-inner-icon,.colorful-frame .mod-right-split .mod-top .workspace-tab-header.is-active .workspace-tab-header-inner-icon,.colorful-frame.is-focused .mod-left-split .mod-top .workspace-tab-header.is-active .workspace-tab-header-inner-icon,.colorful-frame.is-focused .mod-right-split .mod-top .workspace-tab-header.is-active .workspace-tab-header-inner-icon{color:var(--frame-icon-color)}.workspace-leaf-resize-handle{transition:none}.colorful-frame.is-hidden-frameless:not(.minimal-focus-mode) .workspace-split.mod-left-split>.workspace-leaf-resize-handle,.colorful-frame.is-hidden-frameless:not(.minimal-focus-mode) .workspace-split.mod-right-split>.workspace-leaf-resize-handle,.colorful-frame.is-hidden-frameless:not(.minimal-focus-mode) .workspace-split.mod-vertical>*>.workspace-leaf-resize-handle{-webkit-app-region:no-drag;border:0;z-index:15}.colorful-frame.is-hidden-frameless:not(.minimal-focus-mode) .workspace-split.mod-left-split>.workspace-leaf-resize-handle:after,.colorful-frame.is-hidden-frameless:not(.minimal-focus-mode) .workspace-split.mod-right-split>.workspace-leaf-resize-handle:after,.colorful-frame.is-hidden-frameless:not(.minimal-focus-mode) .workspace-split.mod-vertical>*>.workspace-leaf-resize-handle:after{content:"";height:100%;width:1px;background:linear-gradient(180deg,var(--frame-outline-color) var(--header-height),var(--divider-color) var(--header-height));top:0;position:absolute}.colorful-frame.is-hidden-frameless:not(.minimal-focus-mode) .workspace-split.mod-left-split>.workspace-leaf-resize-handle:hover:after,.colorful-frame.is-hidden-frameless:not(.minimal-focus-mode) .workspace-split.mod-right-split>.workspace-leaf-resize-handle:hover:after,.colorful-frame.is-hidden-frameless:not(.minimal-focus-mode) .workspace-split.mod-vertical>*>.workspace-leaf-resize-handle:hover:after{background:var(--divider-color-hover)}.colorful-frame.is-hidden-frameless:not(.minimal-focus-mode) .workspace-split.mod-right-split>.workspace-leaf-resize-handle:after{left:0}.colorful-frame.is-hidden-frameless:not(.minimal-focus-mode) .workspace-split.mod-left-split>.workspace-leaf-resize-handle:after,.colorful-frame.is-hidden-frameless:not(.minimal-focus-mode) .workspace-split.mod-vertical>*>.workspace-leaf-resize-handle:after{right:0}body.colorful-headings{--h1-color:var(--color-red);--h2-color:var(--color-orange);--h3-color:var(--color-yellow);--h4-color:var(--color-green);--h5-color:var(--color-blue);--h6-color:var(--color-purple)}body.colorful-headings .modal{--h1-color:var(--text-normal);--h2-color:var(--text-normal);--h3-color:var(--text-normal);--h4-color:var(--text-normal);--h5-color:var(--text-normal);--h6-color:var(--text-normal)}.is-mobile .tree-item-self .collapse-icon{width:20px}body:not(.minimal-icons-off) svg.calendar-day,body:not(.minimal-icons-off) svg.excalidraw-icon,body:not(.minimal-icons-off) svg.globe,body:not(.minimal-icons-off) svg.longform,body:not(.minimal-icons-off) svg.obsidian-leaflet-plugin-icon-map{background-color:currentColor}body:not(.minimal-icons-off) svg.excalidraw-icon path{display:none}body:not(.minimal-icons-off) svg.bar-graph{-webkit-mask-image:url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><path fill="black" d="M20 7h-4V4c0-1.103-.897-2-2-2h-4c-1.103 0-2 .897-2 2v5H4c-1.103 0-2 .897-2 2v9a1 1 0 0 0 1 1h18a1 1 0 0 0 1-1V9c0-1.103-.897-2-2-2zM4 11h4v8H4v-8zm6-1V4h4v15h-4v-9zm10 9h-4V9h4v10z"></path></svg>')}body:not(.minimal-icons-off) svg.excalidraw-icon{-webkit-mask-image:url('data:image/svg+xml;utf8,<svg  xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><g  stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"%3E%3Cpath d="M12 19l7-7l3 3l-7 7l-3-3z"/%3E%3Cpath d="M18 13l-1.5-7.5L2 2l3.5 14.5L13 18l5-5z"/%3E%3Cpath d="M2 2l7.586 7.586"/%3E%3Ccircle cx="11" cy="11" r="2"/%3E%3C/g%3E%3C/svg%3E%0A')}body:not(.minimal-icons-off) svg.globe{-webkit-mask-image:url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' class='h-6 w-6' fill='none' viewBox='0 0 24 24' stroke='currentColor'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z' /%3E%3C/svg%3E")}body:not(.minimal-icons-off) svg.obsidian-leaflet-plugin-icon-map{-webkit-mask-image:url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' class='h-6 w-6' fill='none' viewBox='0 0 24 24' stroke='currentColor'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7' /%3E%3C/svg%3E")}body:not(.minimal-icons-off) .workspace-tab-header[data-type=dictionary-view] svg.quote-glyph{-webkit-mask-image:url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' class='h-6 w-6' fill='none' viewBox='0 0 24 24' stroke='currentColor'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253' /%3E%3C/svg%3E")}body:not(.minimal-icons-off) svg.calendar-day{-webkit-mask-image:url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 100 100"><path fill="black" d="M29.167 45.833H37.5V54.167H29.167zM29.167 62.5H37.5V70.833H29.167zM45.833 45.833H54.167V54.167H45.833zM45.833 62.5H54.167V70.833H45.833zM62.5 45.833H70.833V54.167H62.5zM62.5 62.5H70.833V70.833H62.5z M20.833 91.667h58.333c4.596 0 8.333 -3.738 8.333 -8.333V33.333V25c0 -4.596 -3.738 -8.333 -8.333 -8.333h-8.333V8.333h-8.333v8.333H37.5V8.333H29.167v8.333H20.833C16.238 16.667 12.5 20.404 12.5 25v8.333v50C12.5 87.929 16.238 91.667 20.833 91.667zM79.167 33.333l0.004 50H20.833V33.333H79.167z"></path></svg>')}body:not(.minimal-icons-off) svg.longform{-webkit-mask-image:url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><path fill="black" d="M21 4H7a2 2 0 1 0 0 4h14v13a1 1 0 0 1-1 1H7a4 4 0 0 1-4-4V6a4 4 0 0 1 4-4h13a1 1 0 0 1 1 1v1zM5 18a2 2 0 0 0 2 2h12V10H7a3.982 3.982 0 0 1-2-.535V18zM20 7H7a1 1 0 1 1 0-2h13v2z"></path></svg>')}.workspace-ribbon.mod-left{border-left:0;transition:none}:root{--focus-animation-duration:0.1s}.minimal-focus-mode.is-translucent .workspace-ribbon.mod-left.is-collapsed,.minimal-focus-mode.is-translucent .workspace-ribbon.mod-left.is-collapsed:before{background-color:var(--background-primary)!important}.minimal-focus-mode .workspace-ribbon.mod-left{transition:background-color 0s linear 0s}.minimal-focus-mode .workspace-ribbon.mod-left.is-collapsed{border-color:transparent;background-color:var(--background-primary)}.minimal-focus-mode .workspace-ribbon.mod-left.is-collapsed:before{background-color:var(--background-primary);border-color:transparent}.minimal-focus-mode .workspace-ribbon.mod-left.is-collapsed .side-dock-actions,.minimal-focus-mode .workspace-ribbon.mod-left.is-collapsed .side-dock-settings{opacity:0;transition:opacity var(--focus-animation-duration) ease-in-out .1s}.minimal-focus-mode .workspace-ribbon.mod-left.is-collapsed:hover .side-dock-actions,.minimal-focus-mode .workspace-ribbon.mod-left.is-collapsed:hover .side-dock-settings{opacity:1;transition:opacity var(--focus-animation-duration) ease-in-out .1s}.minimal-focus-mode.borders-title .workspace-ribbon.mod-left.is-collapsed{border-right:none}.minimal-focus-mode .mod-root .sidebar-toggle-button.mod-right{opacity:0;transition:opacity var(--focus-animation-duration) ease-in-out .5s}.minimal-focus-mode:not(.minimal-status-off) .status-bar{opacity:0;transition:opacity var(--focus-animation-duration) ease-in-out}.minimal-focus-mode .status-bar:hover{opacity:1;transition:opacity var(--focus-animation-duration) ease-in-out}.minimal-focus-mode .mod-root .workspace-tabs{position:relative}.minimal-focus-mode .mod-root .workspace-tabs:before:hover{background-color:#00f}.minimal-focus-mode .mod-root .workspace-tab-header-container{height:0;transition:all var(--focus-animation-duration) linear .6s;--tab-outline-width:0px}.minimal-focus-mode .mod-root .workspace-tab-header-container .workspace-tab-header-container-inner,.minimal-focus-mode .mod-root .workspace-tab-header-container .workspace-tab-header-new-tab,.minimal-focus-mode .mod-root .workspace-tab-header-container .workspace-tab-header-tab-list{opacity:0;transition:all var(--focus-animation-duration) linear .6s}.minimal-focus-mode .mod-root .workspace-tab-header-container .workspace-tab-header-spacer:before{width:100%;content:" ";background-color:rgba(0,0,0,0);height:15px;position:absolute;z-index:100;top:0;left:0}.minimal-focus-mode .mod-root .workspace-tab-header-container .workspace-tab-header-container-inner .workspace-tab-header.is-active,.minimal-focus-mode .mod-root .workspace-tab-header-container .workspace-tab-header-container-inner .workspace-tab-header.is-active::after,.minimal-focus-mode .mod-root .workspace-tab-header-container .workspace-tab-header-container-inner .workspace-tab-header.is-active::before{transition:all var(--focus-animation-duration) linear .6s}.minimal-focus-mode .mod-root .workspace-tab-header-container:hover{height:var(--header-height);--tab-outline-width:1px;transition:all var(--focus-animation-duration) linear .05s}.minimal-focus-mode .mod-root .workspace-tab-header-container:hover .sidebar-toggle-button.mod-right,.minimal-focus-mode .mod-root .workspace-tab-header-container:hover .workspace-tab-header-container-inner,.minimal-focus-mode .mod-root .workspace-tab-header-container:hover .workspace-tab-header-new-tab,.minimal-focus-mode .mod-root .workspace-tab-header-container:hover .workspace-tab-header-tab-list{opacity:1;transition:all var(--focus-animation-duration) linear .05s}.minimal-focus-mode .mod-root .workspace-tab-header-container:hover .workspace-tab-header-container-inner .workspace-tab-header.is-active,.minimal-focus-mode .mod-root .workspace-tab-header-container:hover .workspace-tab-header-container-inner .workspace-tab-header.is-active::after,.minimal-focus-mode .mod-root .workspace-tab-header-container:hover .workspace-tab-header-container-inner .workspace-tab-header.is-active::before{transition:all var(--focus-animation-duration) linear .05s}.minimal-focus-mode.mod-macos:not(.is-fullscreen) .workspace:not(.is-left-sidedock-open) .mod-root .workspace-tabs.mod-stacked .workspace-tab-container .workspace-tab-header-inner{padding-top:30px}body.show-view-header .app-container .workspace-split.mod-root>.workspace-leaf .view-header{transition:height var(--focus-animation-duration) linear .1s}body.minimal-focus-mode.show-view-header .mod-top-left-space .view-header{padding-left:var(--frame-left-space)}body.minimal-focus-mode.show-view-header .mod-root .workspace-leaf .view-header{height:0;transition:all var(--focus-animation-duration) linear .5s}body.minimal-focus-mode.show-view-header .view-header::after{width:100%;content:" ";background-color:rgba(0,0,0,0);height:40px;position:absolute;z-index:-9;top:0}body.minimal-focus-mode.show-view-header .view-actions,body.minimal-focus-mode.show-view-header .view-header-nav-buttons,body.minimal-focus-mode.show-view-header .view-header-title-container{opacity:0;transition:all var(--focus-animation-duration) linear .5s}body.minimal-focus-mode.show-view-header .mod-root .workspace-leaf .view-header:focus-within,body.minimal-focus-mode.show-view-header .mod-root .workspace-leaf .view-header:hover,body.minimal-focus-mode.show-view-header .mod-root .workspace-tab-header-container:hover~.workspace-tab-container .view-header{height:calc(var(--header-height) + 2px);transition:all var(--focus-animation-duration) linear .1s}body.minimal-focus-mode.show-view-header .mod-root .workspace-tab-header-container:hover~.workspace-tab-container .view-header .view-actions,body.minimal-focus-mode.show-view-header .mod-root .workspace-tab-header-container:hover~.workspace-tab-container .view-header .view-header-nav-buttons,body.minimal-focus-mode.show-view-header .mod-root .workspace-tab-header-container:hover~.workspace-tab-container .view-header .view-header-title-container,body.minimal-focus-mode.show-view-header .view-header:focus-within .view-actions,body.minimal-focus-mode.show-view-header .view-header:focus-within .view-header-nav-buttons,body.minimal-focus-mode.show-view-header .view-header:focus-within .view-header-title-container,body.minimal-focus-mode.show-view-header .view-header:hover .view-actions,body.minimal-focus-mode.show-view-header .view-header:hover .view-header-nav-buttons,body.minimal-focus-mode.show-view-header .view-header:hover .view-header-title-container{opacity:1;transition:all var(--focus-animation-duration) linear .1s}body.minimal-focus-mode.show-view-header .view-content{height:100%}.full-width-media{--iframe-width:100%}.full-width-media .markdown-preview-view .external-embed,.full-width-media .markdown-preview-view .image-embed img:not(.link-favicon):not(.emoji):not([width]),.full-width-media .markdown-preview-view audio,.full-width-media .markdown-preview-view img:not(.link-favicon):not(.emoji):not([width]),.full-width-media .markdown-preview-view p:has(.external-embed),.full-width-media .markdown-preview-view video,.full-width-media .markdown-source-view .external-embed,.full-width-media .markdown-source-view .image-embed img:not(.link-favicon):not(.emoji):not([width]),.full-width-media .markdown-source-view audio,.full-width-media .markdown-source-view img:not(.link-favicon):not(.emoji):not([width]),.full-width-media .markdown-source-view p:has(.external-embed),.full-width-media .markdown-source-view video{width:100%}.markdown-rendered img:not(.emoji),.markdown-rendered video,.markdown-source-view img:not(.emoji),.markdown-source-view video{border-radius:var(--image-radius)}.tabular{font-variant-numeric:tabular-nums}.table-small table:not(.calendar){--table-text-size:85%}.table-tiny table:not(.calendar){--table-text-size:75%}.row-hover{--table-edge-cell-padding-first:8px;--table-edge-cell-padding-last:8px;--table-row-background-hover:var(--hl1);--table-row-alt-background-hover:var(--hl1)}.row-alt{--table-row-alt-background:var(--background-table-rows);--table-row-alt-background-hover:var(--background-table-rows);--table-edge-cell-padding-first:8px;--table-edge-cell-padding-last:8px}.col-alt .markdown-rendered:not(.cards){--table-column-alt-background:var(--background-table-rows)}.table-tabular table:not(.calendar){font-variant-numeric:tabular-nums}.table-center .markdown-preview-view .markdown-preview-sizer table,.table-center .markdown-source-view.mod-cm6 .table-wrapper,.table-center.markdown-preview-view .markdown-preview-sizer table,.table-center.markdown-source-view.mod-cm6 .table-wrapper{margin:0 auto}.table-lines{--table-border-width:var(--border-width);--table-header-border-width:var(--border-width);--table-column-first-border-width:var(--border-width);--table-column-last-border-width:var(--border-width);--table-row-last-border-width:var(--border-width);--table-edge-cell-padding:8px;--table-edge-cell-padding-first:8px;--table-edge-cell-padding-last:8px;--table-add-button-border-width:1px}.table-nowrap{--table-white-space:nowrap}.table-nowrap-first table tbody>tr>td:first-child,.table-nowrap-first table thead>tr>th:first-child{--table-white-space:nowrap}.table-nowrap .table-wrap,.trim-cols{--table-white-space:normal}.table-numbers{--table-numbers-padding-right:0.5em}.table-numbers table:not(.calendar){counter-reset:section}.table-numbers table:not(.calendar)>thead>tr>th:first-child{white-space:nowrap}.table-numbers table:not(.calendar)>thead>tr>th:first-child::before{content:" ";padding-right:var(--table-numbers-padding-right);display:inline-block;min-width:2em}.table-numbers table:not(.calendar)>thead>tr>th:first-child .cm-s-obsidian,.table-numbers table:not(.calendar)>thead>tr>th:first-child .table-cell-wrapper{display:inline-block;min-width:10px}.table-numbers table:not(.calendar).table-editor>tbody>tr>td:first-child .table-cell-wrapper,.table-numbers table:not(.calendar):not(.table-editor)>tbody>tr>td:first-child{white-space:nowrap}.table-numbers table:not(.calendar).table-editor>tbody>tr>td:first-child .table-cell-wrapper::before,.table-numbers table:not(.calendar):not(.table-editor)>tbody>tr>td:first-child::before{counter-increment:section;content:counter(section) " ";text-align:center;padding-right:var(--table-numbers-padding-right);display:inline-block;min-width:2em;color:var(--text-faint);font-variant-numeric:tabular-nums}.table-numbers table:not(.calendar).table-editor>tbody>tr>td:first-child .table-cell-wrapper .cm-s-obsidian,.table-numbers table:not(.calendar):not(.table-editor)>tbody>tr>td:first-child .cm-s-obsidian{display:inline-block;min-width:10px}.table-numbers .table-editor{--table-numbers-padding-right:0}.row-lines-off{--table-row-last-border-width:0}.row-lines-off .table-view-table>tbody>tr>td,.row-lines-off table:not(.calendar) tbody>tr:last-child>td,.row-lines-off table:not(.calendar) tbody>tr>td{border-bottom:none}.row-lines:not(.table-lines) .markdown-preview-view:not(.cards),.row-lines:not(.table-lines) .markdown-source-view:not(.cards){--table-row-last-border-width:0px}.row-lines:not(.table-lines) .markdown-preview-view:not(.cards) .table-view-table>tbody>tr:not(:last-child)>td,.row-lines:not(.table-lines) .markdown-preview-view:not(.cards) table:not(.calendar) tbody>tr:not(:last-child)>td,.row-lines:not(.table-lines) .markdown-source-view:not(.cards) .table-view-table>tbody>tr:not(:last-child)>td,.row-lines:not(.table-lines) .markdown-source-view:not(.cards) table:not(.calendar) tbody>tr:not(:last-child)>td{border-bottom:var(--table-border-width) solid var(--table-border-color)}.col-lines .table-view-table thead>tr>th:not(:last-child),.col-lines .table-view-table>tbody>tr>td:not(:last-child),.col-lines table:not(.calendar) tbody>tr>td:not(:last-child){border-right:var(--table-border-width) solid var(--background-modifier-border)}:root{--image-mix:normal}.image-blend-light{--image-mix:multiply}.theme-dark .markdown-preview-view img,.theme-dark .markdown-source-view img{opacity:var(--image-muted);transition:opacity .25s linear}@media print{body{--image-muted:1}}.theme-dark .markdown-preview-view img:hover,.theme-dark .markdown-source-view img:hover,.theme-dark .print-preview img{opacity:1;transition:opacity .25s linear}.theme-light img{mix-blend-mode:var(--image-mix)}div[src$="#invert"],div[src$="#multiply"]{background-color:var(--background-primary)}.theme-dark div[src$="#invert"] img,.theme-dark img[src$="#invert"],.theme-dark span[src$="#invert"] img{filter:invert(1) hue-rotate(180deg);mix-blend-mode:screen}.theme-dark div[src$="#multiply"] img,.theme-dark img[src$="#multiply"],.theme-dark span[src$="#multiply"] img{mix-blend-mode:screen}.theme-light div[src$="#multiply"] img,.theme-light img[src$="#multiply"],.theme-light span[src$="#multiply"] img{mix-blend-mode:multiply}.theme-light div[src$="#invertW"] img,.theme-light img[src$="#invertW"],.theme-light span[src$=invertW] img{filter:invert(1) hue-rotate(180deg)}img[src$="#circle"]:not(.emoji),span[src$="#circle"] img:not(.emoji),span[src$="#round"] img:not(.emoji){border-radius:50%;aspect-ratio:1/1}div[src$="#outline"] img,img[src$="#outline"],span[src$="#outline"] img{border:1px solid var(--ui1)}img[src$="#interface"],span[src$="#interface"] img{border:1px solid var(--ui1);box-shadow:0 .5px .9px rgba(0,0,0,.021),0 1.3px 2.5px rgba(0,0,0,.03),0 3px 6px rgba(0,0,0,.039),0 10px 20px rgba(0,0,0,.06);margin-top:10px;margin-bottom:15px;border-radius:var(--radius-m)}body{--image-grid-fit:cover;--image-grid-background:transparent;--img-grid-gap:0.5rem}@media(max-width:400pt){body{--img-grid-gap:0.25rem}}.img-grid-ratio{--image-grid-fit:contain}.img-grid .image-embed.is-loaded{line-height:0}.img-grid .image-embed.is-loaded img{background-color:var(--image-grid-background)}.img-grid .image-embed.is-loaded img:active{background-color:rgba(0,0,0,0)}.img-grid .markdown-preview-section>div:has(img) .image-embed~br,.img-grid .markdown-preview-section>div:has(img) img~br,.img-grid .markdown-preview-section>div:has(img) p:empty{display:none}.img-grid .markdown-preview-section div:has(>.image-embed~.image-embed),.img-grid .markdown-preview-section div:has(>img~img),.img-grid .markdown-preview-section p:has(>.image-embed~.image-embed),.img-grid .markdown-preview-section p:has(>.image-embed~img),.img-grid .markdown-preview-section p:has(>img~.image-embed),.img-grid .markdown-preview-section p:has(>img~img){display:grid;margin-block-start:var(--img-grid-gap);margin-block-end:var(--img-grid-gap);grid-column-gap:var(--img-grid-gap);grid-row-gap:0;grid-template-columns:repeat(auto-fit,minmax(0,1fr))}.img-grid .markdown-preview-section div:has(>.image-embed~.image-embed)>img,.img-grid .markdown-preview-section div:has(>img~img)>img,.img-grid .markdown-preview-section p:has(>.image-embed~.image-embed)>img,.img-grid .markdown-preview-section p:has(>.image-embed~img)>img,.img-grid .markdown-preview-section p:has(>img~.image-embed)>img,.img-grid .markdown-preview-section p:has(>img~img)>img{object-fit:var(--image-grid-fit);align-self:stretch}.img-grid .markdown-preview-section div:has(>.image-embed~.image-embed)>.internal-embed img,.img-grid .markdown-preview-section div:has(>img~img)>.internal-embed img,.img-grid .markdown-preview-section p:has(>.image-embed~.image-embed)>.internal-embed img,.img-grid .markdown-preview-section p:has(>.image-embed~img)>.internal-embed img,.img-grid .markdown-preview-section p:has(>img~.image-embed)>.internal-embed img,.img-grid .markdown-preview-section p:has(>img~img)>.internal-embed img{object-fit:var(--image-grid-fit);height:100%;align-self:center}.img-grid .markdown-preview-section>div:has(img)>p{display:grid;margin-block-start:var(--img-grid-gap);margin-block-end:var(--img-grid-gap);grid-column-gap:var(--img-grid-gap);grid-row-gap:0;grid-template-columns:repeat(auto-fit,minmax(0,1fr))}.img-grid .markdown-preview-section>div:has(img)>p>br{display:none}body:not(.zoom-off):not(.is-mobile) .workspace-leaf-content[data-type=markdown] .view-content div:not(.canvas-node-content) img{cursor:zoom-in}body:not(.zoom-off):not(.is-mobile) .workspace-leaf-content[data-type=markdown] .view-content img:active{cursor:zoom-out;max-width:100%;z-index:900}body:not(.zoom-off):not(.is-mobile) .workspace-leaf-content[data-type=markdown] .view-content .markdown-preview-view img[referrerpolicy=no-referrer]:active,body:not(.zoom-off):not(.is-mobile) .workspace-leaf-content[data-type=markdown] .view-content .markdown-source-view.mod-cm6 .cm-content>img[contenteditable=false]:active{background-color:var(--background-primary)}body:not(.zoom-off):not(.is-mobile) .workspace-leaf-content[data-type=markdown] .view-content .image-embed:not(.canvas-node-content):active,body:not(.zoom-off):not(.is-mobile) .workspace-leaf-content[data-type=markdown] .view-content .markdown-preview-view img[referrerpolicy=no-referrer]:active,body:not(.zoom-off):not(.is-mobile) .workspace-leaf-content[data-type=markdown] .view-content .markdown-source-view.mod-cm6 .cm-content>img[contenteditable=false]:active{--container-img-width:100%;--container-img-max-width:100%;aspect-ratio:unset;cursor:zoom-out;display:block;z-index:200;position:fixed;max-height:calc(100% + 1px);max-width:100%;height:calc(100% + 1px);width:100%;object-fit:contain;margin:-.5px auto 0!important;text-align:center;padding:0;left:0;right:0;bottom:0}body:not(.zoom-off):not(.is-mobile) .workspace-leaf-content[data-type=markdown] .view-content .image-embed:not(.canvas-node-content):active:after{background-color:var(--background-primary);opacity:.9;content:" ";height:calc(100% + 1px);width:100%;position:fixed;left:0;right:1px;z-index:0}body:not(.zoom-off):not(.is-mobile) .workspace-leaf-content[data-type=markdown] .view-content .image-embed:not(.canvas-node-content):active img{aspect-ratio:unset;z-index:99;padding:0;margin:0 auto;width:calc(100% - 20px);max-height:95vh;object-fit:contain;left:0;right:0;bottom:0;position:absolute;opacity:1}body:not(.zoom-off):not(.is-mobile) .workspace-leaf-content[data-type=markdown] .view-content .markdown-source-view.mod-cm6 .cm-content>[contenteditable=false]:has(.image-embed:not(.canvas-node-content):active){contain:unset!important}.labeled-nav.is-fullscreen:not(.colorful-frame),.labeled-nav.mod-windows{--labeled-nav-top-margin:0}.labeled-nav{--labeled-nav-top-margin:var(--header-height)}.labeled-nav.is-translucent .mod-left-split .mod-top .workspace-tab-header-container .workspace-tab-header-container-inner{background-color:rgba(0,0,0,0)}.labeled-nav.is-hidden-frameless:not(.is-fullscreen) .mod-left-split .workspace-tabs.mod-top-left-space .workspace-tab-header-container{padding-left:0}.labeled-nav.mod-macos .mod-left-split .mod-top .workspace-tab-header-container:before,.labeled-nav.mod-macos.is-hidden-frameless:not(.is-fullscreen) .mod-left-split .mod-top .workspace-tab-header-container:before{-webkit-app-region:drag;position:absolute;width:calc(100% - var(--divider-width));height:calc(var(--header-height) - var(--tab-outline-width));border-bottom:0 solid var(--tab-outline-color)}.labeled-nav.mod-macos.is-hidden-frameless:not(.is-fullscreen) .workspace-ribbon.mod-left:not(.is-collapsed){border:none;--tab-outline-width:0px}.labeled-nav.colorful-frame.is-hidden-frameless:not(.is-fullscreen) .mod-left-split .mod-top .workspace-tab-header-container:before,.labeled-nav.mod-macos:not(.hider-ribbon) .mod-left-split .mod-top .workspace-tab-header-container:before,.labeled-nav:not(.is-hidden-frameless) .mod-left-split .mod-top .workspace-tab-header-container:before{border-bottom:var(--tab-outline-width) solid var(--tab-outline-color)}.labeled-nav.colorful-frame.is-hidden-frameless:not(.is-fullscreen) .workspace-ribbon.mod-left:not(.is-collapsed),.labeled-nav.mod-macos:not(.hider-ribbon) .workspace-ribbon.mod-left:not(.is-collapsed),.labeled-nav:not(.is-hidden-frameless) .workspace-ribbon.mod-left:not(.is-collapsed){--tab-outline-width:1px}.labeled-nav:not(.is-hidden-frameless) .mod-left-split .mod-top .workspace-tab-header-container:before{position:absolute;top:0;content:" "}.labeled-nav.hider-ribbon.mod-macos.is-hidden-frameless:not(.is-fullscreen):not(.is-popout-window) .mod-left-split:not(.is-sidedock-collapsed) .workspace-tabs.mod-top-left-space .workspace-tab-header-container{padding-left:0}.labeled-nav .mod-left-split .mod-top .workspace-tab-header-spacer{display:none}.labeled-nav .mod-left-split .mod-top .workspace-tab-header-inner-title{display:inline-block;font-weight:500;font-size:var(--font-adaptive-smaller)}.labeled-nav .mod-left-split .mod-top .workspace-tab-header-container{position:relative;flex-direction:column-reverse!important;height:auto;width:100%}.labeled-nav .mod-left-split .mod-top .workspace-tab-header-container .sidebar-toggle-button.mod-left{position:absolute;justify-content:flex-end;padding-right:var(--size-4-2);top:0;right:0}.labeled-nav .mod-left-split .mod-top .workspace-tab-header-container .workspace-tab-header-container-inner{padding:var(--size-4-2) var(--size-4-2);margin-top:var(--labeled-nav-top-margin);flex-direction:column!important;background-color:var(--background-secondary)}.labeled-nav .mod-left-split .mod-top .workspace-tab-header-container .workspace-tab-container-inner{flex-grow:1;gap:0;padding:var(--size-4-2) var(--size-4-3)}.labeled-nav .mod-left-split .mod-top .workspace-tab-header{--icon-color:var(--text-muted);--tab-text-color:var(--text-muted);--tab-text-color-focused:var(--text-muted);padding:0;margin-bottom:2px;border:none;height:auto}.labeled-nav .mod-left-split .mod-top .workspace-tab-header.is-active:not(:hover){background-color:rgba(0,0,0,0)}.labeled-nav .mod-left-split .mod-top .workspace-tab-header.is-active,.labeled-nav .mod-left-split .mod-top .workspace-tab-header:hover{opacity:1;--tab-text-color-active:var(--text-normal);--tab-text-color-focused:var(--text-normal);--tab-text-color-focused-active:var(--text-normal);--tab-text-color-focused-active-current:var(--text-normal);--icon-color:var(--text-normal)}.labeled-nav .mod-left-split .mod-top .workspace-tab-header .workspace-tab-header-inner{gap:var(--size-2-3);padding:var(--size-4-1) var(--size-4-2);box-shadow:none;border:none}.labeled-nav .mod-left-split .mod-top .workspace-tab-header.has-active-menu:hover,.labeled-nav .mod-left-split .mod-top .workspace-tab-header.is-active:hover{background-color:rgba(0,0,0,0)}.labeled-nav .mod-left-split .mod-top .workspace-tab-header.is-active:hover .workspace-tab-header-inner,.labeled-nav .mod-left-split .mod-top .workspace-tab-header:not(.is-active):hover .workspace-tab-header-inner{background-color:var(--nav-item-background-hover)}.labeled-nav .mod-left-split .mod-top .workspace-tab-header.is-active .workspace-tab-header-inner-icon,.labeled-nav .mod-left-split .mod-top .workspace-tab-header:hover .workspace-tab-header-inner-icon{color:var(--icon-color-active)}.labeled-nav .mod-left-split .mod-top .workspace-tab-header-container{border:none;padding:0}body:not(.links-int-on){--link-decoration:none}body:not(.links-ext-on){--link-external-decoration:none}body:not(.sidebar-color) .mod-right-split{--background-secondary:var(--background-primary)}body:not(.sidebar-color) .mod-right-split :not(.mod-top) .workspace-tab-header-container{--tab-container-background:var(--background-primary)}.theme-dark,.theme-light{--minimal-tab-text-color:var(--tx2);--minimal-tab-text-color-active:var(--tx1)}.workspace-tabs:not(.mod-stacked){--tab-text-color:var(--minimal-tab-text-color);--tab-text-color-focused:var(--minimal-tab-text-color);--tab-text-color-active:var(--minimal-tab-text-color-active);--tab-text-color-focused-active:var(--minimal-tab-text-color-active);--tab-text-color-focused-active-current:var(--minimal-tab-text-color-active)}.tabs-plain-square .mod-root{--tab-curve:0;--tab-radius:0;--tab-radius-active:0}.tabs-plain-square .mod-root .workspace-tab-header-container{padding-left:0;padding-right:0}.tabs-plain-square .mod-root .workspace-tab-header-container-inner{margin-top:-1px;margin-left:-15px}.tabs-plain-square .mod-root .workspace-tab-header{padding:0}.tabs-plain-square .mod-root .workspace-tab-header-inner{padding:0 8px}.tabs-square .mod-root{--tab-curve:0;--tab-radius:0;--tab-radius-active:0}.tabs-underline .mod-root{--tab-curve:0;--tab-radius:0;--tab-radius-active:0;--tab-outline-width:0px;--tab-background-active:transparent}.tabs-underline .mod-root .workspace-tab-header-container{border-bottom:1px solid var(--divider-color)}.tabs-underline .mod-root .workspace-tab-header{border-bottom:2px solid transparent}.tabs-underline .mod-root .workspace-tab-header:hover{border-bottom:2px solid var(--ui2)}.tabs-underline .mod-root .workspace-tab-header:hover .workspace-tab-header-inner{background-color:rgba(0,0,0,0)}.tabs-underline .mod-root .workspace-tab-header.is-active{border-bottom:2px solid var(--ax3)}.tabs-underline .mod-root .workspace-tab-header-inner:hover{background-color:rgba(0,0,0,0)}body:not(.sidebar-tabs-underline):not(.sidebar-tabs-index):not(.sidebar-tabs-square) .workspace>.workspace-split:not(.mod-root) .workspace-tabs:not(.mod-top) .workspace-tab-header-container{--tab-outline-width:0}.tabs-modern.colorful-frame .mod-root .mod-top.workspace-tabs:not(.mod-stacked){--tab-background:var(--frame-outline-color);--tab-outline-width:1px}.tabs-modern.colorful-frame .mod-root .mod-top.workspace-tabs:not(.mod-stacked) .workspace-tab-header.is-active .workspace-tab-header-inner-close-button,.tabs-modern.colorful-frame .mod-root .mod-top.workspace-tabs:not(.mod-stacked) .workspace-tab-header:hover .workspace-tab-header-inner-close-button{color:var(--minimal-tab-text-color-active)}.tabs-modern.minimal-focus-mode .mod-root .workspace-tab-header-container:hover{--tab-outline-width:0px}.tabs-modern .mod-root{--tab-container-background:var(--background-primary)}.tabs-modern .mod-root .workspace-tabs:not(.mod-stacked){--tab-background:var(--background-modifier-hover);--tab-height:calc(var(--header-height) - 14px);--tab-outline-width:0px}.tabs-modern .mod-root .workspace-tabs:not(.mod-stacked) .workspace-tab-header-inner::after,.tabs-modern .mod-root .workspace-tabs:not(.mod-stacked) .workspace-tab-header::after,.tabs-modern .mod-root .workspace-tabs:not(.mod-stacked) .workspace-tab-header::before{display:none}.tabs-modern .mod-root .workspace-tabs:not(.mod-stacked) .workspace-tab-header-container-inner{align-items:center;margin:0;padding:2px var(--size-4-2) 0 var(--size-4-1)}.tabs-modern .mod-root .workspace-tabs:not(.mod-stacked) .workspace-tab-header-inner-title{text-overflow:ellipsis;-webkit-mask-image:none}.tabs-modern .mod-root .workspace-tabs:not(.mod-stacked) .workspace-tab-header{background:rgba(0,0,0,0);border-radius:5px;border:none;box-shadow:none;height:var(--tab-height);margin-left:var(--size-4-1);padding:0}.tabs-modern .mod-root .workspace-tabs:not(.mod-stacked) .workspace-tab-header.is-active .workspace-tab-header-inner-title{color:var(--tab-text-color-active)}.tabs-modern .mod-root .workspace-tabs:not(.mod-stacked) .workspace-tab-header.is-active.mod-active,.tabs-modern .mod-root .workspace-tabs:not(.mod-stacked) .workspace-tab-header:hover{opacity:1;background-color:var(--tab-background)}.tabs-modern .mod-root .workspace-tabs:not(.mod-stacked) .workspace-tab-header-new-tab{margin-inline-end:0}.tabs-modern .mod-root .workspace-tabs:not(.mod-stacked) .workspace-tab-header-inner{padding:0 var(--size-4-1) 0 var(--size-4-2);border:1px solid transparent}.tabs-modern .mod-root .workspace-tabs:not(.mod-stacked) .workspace-tab-header:not(.is-active):hover .workspace-tab-header-inner{background-color:rgba(0,0,0,0)}.tabs-modern .mod-root .workspace-tabs:not(.mod-stacked) .workspace-tab-header.is-active:not(.mod-active) .workspace-tab-header-inner,.tabs-modern .mod-root .workspace-tabs:not(.mod-stacked) .workspace-tab-header:not(:hover):not(.mod-active) .workspace-tab-header-inner{border:1px solid var(--tab-outline-color)}.tab-names-on .workspace-split:not(.mod-root) .workspace-tab-header-container-inner{--sidebar-tab-text-display:static}.tab-names-on .workspace-split:not(.mod-root) .workspace-tab-header-container-inner .workspace-tab-header-inner-title{font-weight:500}.tab-names-on .workspace-split:not(.mod-root) .workspace-tab-header-container-inner .workspace-tab-header-inner{gap:var(--size-2-3)}.tab-names-single .workspace>.workspace-split:not(.mod-root) .workspace-tab-header-container-inner .workspace-tab-header:only-child{--sidebar-tab-text-display:static;background-color:rgba(0,0,0,0)}.tab-names-single .workspace>.workspace-split:not(.mod-root) .workspace-tab-header-container-inner .workspace-tab-header:only-child .workspace-tab-header-inner-title{font-weight:500}.tab-names-single .workspace>.workspace-split:not(.mod-root) .workspace-tab-header-container-inner .workspace-tab-header:only-child .workspace-tab-header-inner{gap:var(--size-2-3)}.tabs-modern.sidebar-tabs-default .mod-right-split,.tabs-modern.sidebar-tabs-wide .mod-right-split{--tab-outline-width:0}.sidebar-tabs-underline .mod-right-split .workspace-tab-header-spacer,.sidebar-tabs-underline.labeled-nav .mod-left-split .workspace-tabs:not(.mod-top) .workspace-tab-header-spacer,.sidebar-tabs-underline:not(.labeled-nav) .mod-left-split .workspace-tab-header-spacer{display:none}.sidebar-tabs-underline .mod-right-split .workspace-tab-header-container,.sidebar-tabs-underline.labeled-nav .mod-left-split .workspace-tabs:not(.mod-top) .workspace-tab-header-container,.sidebar-tabs-underline:not(.labeled-nav) .mod-left-split .workspace-tab-header-container{padding-right:0}.sidebar-tabs-underline .mod-right-split .workspace-tab-header-container-inner,.sidebar-tabs-underline.labeled-nav .mod-left-split .workspace-tabs:not(.mod-top) .workspace-tab-header-container-inner,.sidebar-tabs-underline:not(.labeled-nav) .mod-left-split .workspace-tab-header-container-inner{padding:0;margin:0;flex-grow:1;gap:0}.sidebar-tabs-underline .mod-right-split .workspace-tab-header-container .workspace-tab-header,.sidebar-tabs-underline.labeled-nav .mod-left-split .workspace-tabs:not(.mod-top) .workspace-tab-header-container .workspace-tab-header,.sidebar-tabs-underline:not(.labeled-nav) .mod-left-split .workspace-tab-header-container .workspace-tab-header{flex-grow:1;border-radius:0;max-width:100px}.sidebar-tabs-underline .mod-right-split .workspace-tab-header-container .workspace-tab-header.is-active,.sidebar-tabs-underline .mod-right-split .workspace-tab-header-container .workspace-tab-header:hover,.sidebar-tabs-underline.labeled-nav .mod-left-split .workspace-tabs:not(.mod-top) .workspace-tab-header-container .workspace-tab-header.is-active,.sidebar-tabs-underline.labeled-nav .mod-left-split .workspace-tabs:not(.mod-top) .workspace-tab-header-container .workspace-tab-header:hover,.sidebar-tabs-underline:not(.labeled-nav) .mod-left-split .workspace-tab-header-container .workspace-tab-header.is-active,.sidebar-tabs-underline:not(.labeled-nav) .mod-left-split .workspace-tab-header-container .workspace-tab-header:hover{background-color:rgba(0,0,0,0)}.sidebar-tabs-underline .mod-right-split .workspace-tab-header-container .workspace-tab-header.is-active .workspace-tab-header-inner,.sidebar-tabs-underline .mod-right-split .workspace-tab-header-container .workspace-tab-header:hover .workspace-tab-header-inner,.sidebar-tabs-underline.labeled-nav .mod-left-split .workspace-tabs:not(.mod-top) .workspace-tab-header-container .workspace-tab-header.is-active .workspace-tab-header-inner,.sidebar-tabs-underline.labeled-nav .mod-left-split .workspace-tabs:not(.mod-top) .workspace-tab-header-container .workspace-tab-header:hover .workspace-tab-header-inner,.sidebar-tabs-underline:not(.labeled-nav) .mod-left-split .workspace-tab-header-container .workspace-tab-header.is-active .workspace-tab-header-inner,.sidebar-tabs-underline:not(.labeled-nav) .mod-left-split .workspace-tab-header-container .workspace-tab-header:hover .workspace-tab-header-inner{background-color:rgba(0,0,0,0)}.sidebar-tabs-underline .mod-right-split .workspace-tab-header-container .workspace-tab-header .workspace-tab-header-inner,.sidebar-tabs-underline.labeled-nav .mod-left-split .workspace-tabs:not(.mod-top) .workspace-tab-header-container .workspace-tab-header .workspace-tab-header-inner,.sidebar-tabs-underline:not(.labeled-nav) .mod-left-split .workspace-tab-header-container .workspace-tab-header .workspace-tab-header-inner{border-bottom:2px solid transparent;border-radius:0}.sidebar-tabs-underline .mod-right-split .workspace-tab-header-container .workspace-tab-header .workspace-tab-header-inner:hover,.sidebar-tabs-underline.labeled-nav .mod-left-split .workspace-tabs:not(.mod-top) .workspace-tab-header-container .workspace-tab-header .workspace-tab-header-inner:hover,.sidebar-tabs-underline:not(.labeled-nav) .mod-left-split .workspace-tab-header-container .workspace-tab-header .workspace-tab-header-inner:hover{border-color:var(--ui2)}.sidebar-tabs-underline .mod-right-split .workspace-tab-header-container .workspace-tab-header.is-active .workspace-tab-header-inner,.sidebar-tabs-underline.labeled-nav .mod-left-split .workspace-tabs:not(.mod-top) .workspace-tab-header-container .workspace-tab-header.is-active .workspace-tab-header-inner,.sidebar-tabs-underline:not(.labeled-nav) .mod-left-split .workspace-tab-header-container .workspace-tab-header.is-active .workspace-tab-header-inner{border-color:var(--ax3);padding-top:1px}.sidebar-tabs-square .mod-left-split,.sidebar-tabs-square .mod-right-split{--tab-radius:0px}.sidebar-tabs-plain-square .mod-left-split,.sidebar-tabs-plain-square .mod-right-split{--tab-radius:0px}.sidebar-tabs-plain-square.labeled-nav .mod-left-split .workspace-tabs:not(.mod-top),.sidebar-tabs-plain-square:not(.labeled-nav) .mod-left-split{--tab-background-active:var(--background-secondary)}.sidebar-tabs-plain-square .mod-right-split .workspace-tab-header-container,.sidebar-tabs-plain-square.labeled-nav .mod-left-split .workspace-tabs:not(.mod-top) .workspace-tab-header-container,.sidebar-tabs-plain-square:not(.labeled-nav) .mod-left-split .workspace-tab-header-container{padding-left:0}.sidebar-tabs-plain-square .mod-right-split .workspace-tab-header-container-inner,.sidebar-tabs-plain-square.labeled-nav .mod-left-split .workspace-tabs:not(.mod-top) .workspace-tab-header-container-inner,.sidebar-tabs-plain-square:not(.labeled-nav) .mod-left-split .workspace-tab-header-container-inner{padding:0;margin:0 0 calc(var(--tab-outline-width)*-1);flex-grow:1;gap:0}.sidebar-tabs-plain-square .mod-right-split .workspace-tab-header,.sidebar-tabs-plain-square.labeled-nav .mod-left-split .workspace-tabs:not(.mod-top) .workspace-tab-header,.sidebar-tabs-plain-square:not(.labeled-nav) .mod-left-split .workspace-tab-header{flex-grow:1;max-width:100px;border-radius:var(--tab-radius) var(--tab-radius) 0 0}.sidebar-tabs-plain-square .mod-right-split .workspace-tab-header.is-active,.sidebar-tabs-plain-square.labeled-nav .mod-left-split .workspace-tabs:not(.mod-top) .workspace-tab-header.is-active,.sidebar-tabs-plain-square:not(.labeled-nav) .mod-left-split .workspace-tab-header.is-active{box-shadow:0 0 0 var(--tab-outline-width) var(--tab-outline-color);color:var(--tab-text-color-active);background-color:var(--tab-background-active)}.sidebar-tabs-index.labeled-nav .mod-left-split .workspace-tabs:not(.mod-top),.sidebar-tabs-index:not(.labeled-nav) .mod-left-split,.sidebar-tabs-square.labeled-nav .mod-left-split .workspace-tabs:not(.mod-top),.sidebar-tabs-square:not(.labeled-nav) .mod-left-split{--tab-background-active:var(--background-secondary)}.sidebar-tabs-index .mod-right-split .workspace-tab-header-container-inner,.sidebar-tabs-index.labeled-nav .mod-left-split .workspace-tabs:not(.mod-top) .workspace-tab-header-container-inner,.sidebar-tabs-index:not(.labeled-nav) .mod-left-split .workspace-tab-header-container-inner,.sidebar-tabs-square .mod-right-split .workspace-tab-header-container-inner,.sidebar-tabs-square.labeled-nav .mod-left-split .workspace-tabs:not(.mod-top) .workspace-tab-header-container-inner,.sidebar-tabs-square:not(.labeled-nav) .mod-left-split .workspace-tab-header-container-inner{padding:1px var(--size-4-2) 0;margin:6px 0 calc(var(--tab-outline-width)*-1);flex-grow:1}.sidebar-tabs-index .mod-right-split .workspace-tab-header,.sidebar-tabs-index.labeled-nav .mod-left-split .workspace-tabs:not(.mod-top) .workspace-tab-header,.sidebar-tabs-index:not(.labeled-nav) .mod-left-split .workspace-tab-header,.sidebar-tabs-square .mod-right-split .workspace-tab-header,.sidebar-tabs-square.labeled-nav .mod-left-split .workspace-tabs:not(.mod-top) .workspace-tab-header,.sidebar-tabs-square:not(.labeled-nav) .mod-left-split .workspace-tab-header{flex-grow:1;max-width:100px;border-radius:var(--tab-radius) var(--tab-radius) 0 0}.sidebar-tabs-index .mod-right-split .workspace-tab-header.is-active,.sidebar-tabs-index.labeled-nav .mod-left-split .workspace-tabs:not(.mod-top) .workspace-tab-header.is-active,.sidebar-tabs-index:not(.labeled-nav) .mod-left-split .workspace-tab-header.is-active,.sidebar-tabs-square .mod-right-split .workspace-tab-header.is-active,.sidebar-tabs-square.labeled-nav .mod-left-split .workspace-tabs:not(.mod-top) .workspace-tab-header.is-active,.sidebar-tabs-square:not(.labeled-nav) .mod-left-split .workspace-tab-header.is-active{box-shadow:0 0 0 var(--tab-outline-width) var(--tab-outline-color);color:var(--tab-text-color-active);background-color:var(--tab-background-active)}.sidebar-tabs-wide .mod-right-split .workspace-tab-header-container-inner,.sidebar-tabs-wide.labeled-nav .mod-left-split .workspace-tabs:not(.mod-top) .workspace-tab-header-container-inner,.sidebar-tabs-wide:not(.labeled-nav) .mod-left-split .workspace-tab-header-container-inner{flex-grow:1;border:1px solid var(--tab-outline-color);padding:3px;margin:6px 8px 6px;border-radius:4px}.sidebar-tabs-wide .mod-right-split .workspace-tab-header,.sidebar-tabs-wide.labeled-nav .mod-left-split .workspace-tabs:not(.mod-top) .workspace-tab-header,.sidebar-tabs-wide:not(.labeled-nav) .mod-left-split .workspace-tab-header{flex-grow:1}.sidebar-tabs-wide .mod-right-split .workspace-tab-header.is-active,.sidebar-tabs-wide.labeled-nav .mod-left-split .workspace-tabs:not(.mod-top) .workspace-tab-header.is-active,.sidebar-tabs-wide:not(.labeled-nav) .mod-left-split .workspace-tab-header.is-active{border-color:transparent}.sidebar-tabs-wide .mod-right-split .workspace-tab-header-container,.sidebar-tabs-wide.labeled-nav .mod-left-split .workspace-tabs:not(.mod-top) .workspace-tab-header-container,.sidebar-tabs-wide:not(.labeled-nav) .mod-left-split .workspace-tab-header-container{padding-right:0}.sidebar-tabs-wide .mod-right-split .workspace-tab-header-spacer,.sidebar-tabs-wide.labeled-nav .mod-left-split .workspace-tabs:not(.mod-top) .workspace-tab-header-spacer,.sidebar-tabs-wide:not(.labeled-nav) .mod-left-split .workspace-tab-header-spacer{display:none}.full-file-names{--nav-item-white-space:normal}body:not(.full-file-names){--nav-item-white-space:nowrap}body:not(.full-file-names) .tree-item-self{white-space:nowrap}body:not(.full-file-names) .tree-item-inner{text-overflow:ellipsis;overflow:hidden}.theme-dark,.theme-light{--h1l:var(--ui1);--h2l:var(--ui1);--h3l:var(--ui1);--h4l:var(--ui1);--h5l:var(--ui1);--h6l:var(--ui1)}.h1-l .markdown-reading-view h1:not(.embedded-note-title),.h1-l .mod-cm6 .cm-editor .HyperMD-header-1{border-bottom:1px solid var(--h1l);padding-bottom:.4em;margin-block-end:.6em}.h2-l .markdown-reading-view h2,.h2-l .mod-cm6 .cm-editor .HyperMD-header-2{border-bottom:1px solid var(--h2l);padding-bottom:.4em;margin-block-end:.6em}.h3-l .markdown-reading-view h3,.h3-l .mod-cm6 .cm-editor .HyperMD-header-3{border-bottom:1px solid var(--h3l);padding-bottom:.4em;margin-block-end:.6em}.h4-l .markdown-reading-view h4,.h4-l .mod-cm6 .cm-editor .HyperMD-header-4{border-bottom:1px solid var(--h4l);padding-bottom:.4em;margin-block-end:.6em}.h5-l .markdown-reading-view h5,.h5-l .mod-cm6 .cm-editor .HyperMD-header-5{border-bottom:1px solid var(--h5l);padding-bottom:.4em;margin-block-end:.6em}.h6-l .markdown-reading-view h6,.h6-l .mod-cm6 .cm-editor .HyperMD-header-6{border-bottom:1px solid var(--h6l);padding-bottom:.4em;margin-block-end:.6em}.is-tablet .workspace-drawer{padding-top:0}.is-tablet .workspace-drawer:not(.is-pinned){margin:30px 16px 0;height:calc(100vh - 48px);border-radius:15px;border:none}.is-tablet .workspace-drawer-ribbon{background-color:var(--background-primary);border-right:1px solid var(--background-modifier-border)}.is-tablet .workspace-drawer-header,.is-tablet .workspace-drawer.is-pinned .workspace-drawer-header{padding-top:var(--size-4-4)}.is-tablet .workspace-drawer-header-icon{margin-inline-start:0}.is-mobile{--font-bold:600;--font-ui-medium:var(--font-adaptive-small);--interactive-normal:var(--background-modifier-hover);--background-modifier-form-field:var(--background-secondary);--background-modifier-form-field-highlighted:var(--background-secondary)}.is-mobile .markdown-source-view.mod-cm6 .cm-gutters{margin-left:0}.is-mobile .workspace-drawer.mod-left.is-pinned{width:var(--mobile-left-sidebar-width);min-width:150pt}.is-mobile .workspace-drawer.mod-right.is-pinned{width:var(--mobile-right-sidebar-width);min-width:150pt}.backlink-pane>.tree-item-self,.backlink-pane>.tree-item-self:hover,.outgoing-link-pane>.tree-item-self,.outgoing-link-pane>.tree-item-self:hover{color:var(--text-muted);text-transform:uppercase;letter-spacing:.05em;font-size:var(--font-adaptive-smallest);font-weight:500}body{--canvas-dot-pattern:var(--background-modifier-border-hover)}.canvas-node-label{font-size:var(--font-adaptive-small)}.canvas-edges :not(.is-themed) path.canvas-display-path{stroke:var(--background-modifier-border-focus)}.canvas-edges :not(.is-themed) polyline.canvas-path-end{stroke:var(--background-modifier-border-focus);fill:var(--background-modifier-border-focus)}.canvas-node-container{border:1.5px solid var(--background-modifier-border-focus)}.node-insert-event.mod-inside-iframe{--max-width:100%;--folding-offset:0px}.node-insert-event.mod-inside-iframe .cm-editor .cm-content{padding-top:0}.hider-file-nav-header:not(.labeled-nav) .nav-files-container{padding-top:var(--size-4-3)}.is-mobile .nav-folder.mod-root>.nav-folder-title .nav-folder-title-content{display:none}body:not(.is-mobile) .nav-folder.mod-root>.nav-folder-title .nav-folder-title-content{font-weight:500;text-transform:uppercase;letter-spacing:.05em;color:var(--text-muted);font-size:var(--font-adaptive-smallest)}.nav-buttons-container{justify-content:flex-start}.nav-file-tag{padding-top:.2em;background-color:rgba(0,0,0,0);color:var(--text-faint)}.nav-file .is-active .nav-file-tag,.nav-file:hover .nav-file-tag{color:var(--text-muted)}input.prompt-input,input.prompt-input:focus,input.prompt-input:focus-visible,input.prompt-input:hover{border-color:rgba(var(--mono-rgb-100),.05)}.is-mobile .mod-publish .modal-content{display:unset;padding:10px 10px 10px;margin-bottom:120px;overflow-x:hidden}.is-mobile .mod-publish .button-container,.is-mobile .modal.mod-publish .modal-button-container{padding:10px 15px 30px;margin-left:0;left:0}.is-mobile .modal.mod-publish .modal-title{padding:10px 20px;margin:0 -10px;border-bottom:1px solid var(--background-modifier-border)}.is-mobile .publish-site-settings-container{margin-right:0;padding:0}.is-mobile .modal.mod-publish .modal-content .publish-sections-container{margin-right:0;padding-right:0}@media(max-width:400pt){.is-mobile .publish-changes-info,.is-mobile .publish-section-header{flex-wrap:wrap;border:none}.is-mobile .publish-changes-info .publish-changes-add-linked-btn{flex-basis:100%;margin-top:10px}.is-mobile .publish-section-header-text{flex-basis:100%;margin-bottom:10px;margin-left:20px;margin-top:-8px}.is-mobile .publish-section{background:var(--background-secondary);border-radius:10px;padding:12px 12px 1px}.is-mobile .publish-changes-switch-site{flex-grow:0;margin-right:10px}}.release-notes-view .cm-scroller.is-readable-line-width{width:var(--line-width);max-width:var(--max-width);margin:0 auto}.search-results-info{border-bottom:none}.workspace-leaf-content[data-type=sync] .tree-item.nav-folder .nav-folder-title{color:var(--text-muted);text-transform:uppercase;letter-spacing:.05em;font-size:var(--font-adaptive-smallest);font-weight:500;margin-bottom:4px}.workspace-leaf-content[data-type=sync] .tree-item.nav-folder .nav-folder-title:hover{color:var(--text-normal)}.workspace-leaf-content[data-type=sync] .tree-item.nav-folder.is-collapsed .nav-folder-title{color:var(--text-faint)}.workspace-leaf-content[data-type=sync] .tree-item.nav-folder.is-collapsed .nav-folder-title:hover{color:var(--text-muted)}.obsidian-banner.solid{border-bottom:var(--divider-width) solid var(--divider-color)}.contextual-typography .markdown-preview-view div.has-banner-icon.obsidian-banner-wrapper{overflow:visible}.theme-dark .markdown-preview-view img.emoji{opacity:1}body.theme-dark .button-default,body.theme-light .button-default{border:none;box-shadow:none;height:var(--input-height);background:var(--background-modifier-hover);color:var(--text-normal);font-size:revert;font-weight:500;transform:none;transition:all .1s linear;padding:0 20px}body.theme-dark .button-default:hover,body.theme-light .button-default:hover{border:none;background:var(--background-modifier-border-hover);box-shadow:none;transform:none;transition:all .1s linear}body.theme-dark .button-default:active,body.theme-dark .button-default:focus,body.theme-light .button-default:active,body.theme-light .button-default:focus{box-shadow:none}body .button-default.blue{background-color:var(--color-blue)!important}.button-default.red{background-color:var(--color-red)!important}.button-default.green{background-color:var(--color-green)!important}.button-default.yellow{background-color:var(--color-yellow)!important}.button-default.purple{background-color:var(--color-purple)!important}.workspace-leaf-content[data-type=calendar] .view-content{padding:5px 0 0 0}.mod-root #calendar-container{width:var(--line-width);max-width:var(--max-width);margin:0 auto;padding:0}body{--calendar-dot-active:var(--text-faint);--calendar-dot-today:var(--text-accent)}#calendar-container{padding:0 var(--size-4-4) var(--size-4-1);--color-background-day-empty:var(--background-secondary-alt);--color-background-day-active:var(--background-modifier-hover);--color-background-day-hover:var(--background-modifier-hover);--color-dot:var(--text-faint);--calendar-text-active:inherit;--color-text-title:var(--text-normal);--color-text-heading:var(--text-muted);--color-text-day:var(--text-normal);--color-text-today:var(--text-normal);--color-arrow:var(--text-faint);--color-background-day-empty:transparent}#calendar-container .table{border-collapse:separate;table-layout:fixed}#calendar-container h2{font-weight:400;font-size:var(--h2)}#calendar-container .arrow{cursor:var(--cursor);width:22px;border-radius:4px;padding:3px 7px}#calendar-container .arrow svg{width:12px;height:12px;color:var(--text-faint);opacity:.7}#calendar-container .arrow:hover{fill:var(--text-muted);color:var(--text-muted);background-color:var(--background-modifier-hover)}#calendar-container .arrow:hover svg{color:var(--text-muted);opacity:1}#calendar-container tr th{padding:2px 0 4px;font-weight:500;letter-spacing:.1em;font-size:var(--font-adaptive-smallest)}#calendar-container tr th:first-child{padding-left:0!important}#calendar-container tr td{padding:2px 0 0 0;border-radius:var(--radius-m);cursor:var(--cursor);border:1px solid transparent;transition:none}#calendar-container tr td:first-child{padding-left:0!important}#calendar-container .nav{padding:0;margin:var(--size-4-2) var(--size-4-1)}#calendar-container .dot{margin:0}#calendar-container .month,#calendar-container .title,#calendar-container .year{font-size:calc(var(--font-adaptive-small) + 2px);font-weight:400;color:var(--text-normal)}#calendar-container .today,#calendar-container .today.active{color:var(--text-accent);font-weight:600}#calendar-container .today .dot,#calendar-container .today.active .dot{fill:var(--calendar-dot-today)}#calendar-container .active .task{stroke:var(--text-faint)}#calendar-container .active{color:var(--text-normal)}#calendar-container .reset-button{text-transform:none;letter-spacing:0;font-size:var(--font-adaptive-smaller);font-weight:500;color:var(--text-muted);border-radius:4px;margin:0;padding:2px 8px}#calendar-container .reset-button:hover{color:var(--text-normal);background-color:var(--background-modifier-hover)}#calendar-container .day,#calendar-container .reset-button,#calendar-container .week-num{cursor:var(--cursor)}#calendar-container .day.adjacent-month{color:var(--text-faint);opacity:1}#calendar-container .day{padding:2px 4px 4px;transition:none}#calendar-container .day,#calendar-container .week-num{font-size:calc(var(--font-adaptive-smaller) + 5%)}#calendar-container .active,#calendar-container .active.today,#calendar-container .day:hover,#calendar-container .week-num:hover{background-color:var(--color-background-day-active);color:var(--calendar-text-active);transition:none}#calendar-container .active .dot{fill:var(--calendar-dot-active)}#calendar-container .active .task{stroke:var(--text-faint)}.block-language-chart canvas,.block-language-dataviewjs canvas{margin:1em 0}.theme-dark,.theme-light{--chart-color-1:var(--color-blue);--chart-color-2:var(--color-red);--chart-color-3:var(--color-yellow);--chart-color-4:var(--color-green);--chart-color-5:var(--color-orange);--chart-color-6:var(--color-purple);--chart-color-7:var(--color-cyan);--chart-color-8:var(--color-pink)}.checklist-plugin-main .group .classic,.checklist-plugin-main .group .compact,.checklist-plugin-main .group .page,.checklist-plugin-main .group svg{cursor:var(--cursor)}.workspace .view-content .checklist-plugin-main{padding:10px 10px 15px 15px;--todoList-togglePadding--compact:2px;--todoList-listItemMargin--compact:2px}.checklist-plugin-main .title{font-weight:400;color:var(--text-muted);font-size:var(--font-adaptive-small)}.checklist-plugin-main .group svg{fill:var(--text-faint)}.checklist-plugin-main .group svg:hover{fill:var(--text-normal)}.checklist-plugin-main .group .title:hover{color:var(--text-normal)}.checklist-plugin-main .group:not(:last-child){border-bottom:1px solid var(--background-modifier-border)}.checklist-plugin-main .group{padding:0 0 2px 0}.checklist-plugin-main .group .classic:last-child,.checklist-plugin-main .group .compact:last-child{margin-bottom:10px}.checklist-plugin-main .group .classic,.checklist-plugin-main .group .compact{font-size:var(--font-adaptive-small)}.checklist-plugin-main .group .classic,.checklist-plugin-main .group .compact{background:rgba(0,0,0,0);border-radius:0;margin:1px auto;padding:0}.checklist-plugin-main .group .classic .content{padding:0}.checklist-plugin-main .group .classic:hover,.checklist-plugin-main .group .compact:hover{background:rgba(0,0,0,0)}.markdown-preview-view.checklist-plugin-main ul>li:not(.task-list-item)::before{display:none}.checklist-plugin-main .group .compact>.toggle .checked{background:var(--text-accent);top:-1px;left:-1px;height:18px;width:18px}.checklist-plugin-main .compact .toggle:hover{opacity:1!important}.checklist-plugin-main .group .count{font-size:var(--font-adaptive-smaller);padding:0;background:rgba(0,0,0,0);font-weight:400;color:var(--text-faint)}.checklist-plugin-main .group .group-header:hover .count{color:var(--text-muted)}.checklist-plugin-main .group .checkbox{border:1px solid var(--background-modifier-border-hover);min-height:18px;min-width:18px;height:18px;width:18px}.checklist-plugin-main .group .checkbox:hover{border:1px solid var(--background-modifier-border-focus)}.checklist-plugin-main button:active,.checklist-plugin-main button:focus,.checklist-plugin-main button:hover{box-shadow:none!important}.checklist-plugin-main button.collapse{padding:0}body:not(.is-mobile) .checklist-plugin-main button.collapse svg{width:18px;height:18px}.is-mobile .checklist-plugin-main .group-header .title{flex-grow:1;flex-shrink:0}.is-mobile .checklist-plugin-main button{width:auto}.is-mobile .checklist-plugin-main.markdown-preview-view ul{padding-inline-start:0}.is-mobile .workspace .view-content .checklist-plugin-main{padding-bottom:50px}body #cMenuModalBar{box-shadow:0 2px 20px var(--shadow-color)}body #cMenuModalBar .cMenuCommandItem{cursor:var(--cursor)}body #cMenuModalBar button.cMenuCommandItem:hover{background-color:var(--background-modifier-hover)}.MiniSettings-statusbar-button{padding-top:0;padding-bottom:0}.MySnippets-statusbar-menu .menu-item .MS-OpenSnippet{height:auto;border:none;background:rgba(0,0,0,0);box-shadow:none;width:auto;padding:4px 6px;margin-left:0}.MySnippets-statusbar-menu .menu-item .MS-OpenSnippet svg path{fill:var(--text-muted)}.MySnippets-statusbar-menu .menu-item .MS-OpenSnippet:hover{background-color:var(--background-modifier-hover)}.dataview-inline-lists .markdown-preview-view .dataview-ul,.dataview-inline-lists .markdown-source-view .dataview-ul{--list-spacing:0}.dataview-inline-lists .markdown-preview-view .dataview-ol li:not(:last-child):after,.dataview-inline-lists .markdown-preview-view .dataview-ul li:not(:last-child):after,.dataview-inline-lists .markdown-source-view .dataview-ol li:not(:last-child):after,.dataview-inline-lists .markdown-source-view .dataview-ul li:not(:last-child):after{content:", "}.dataview-inline-lists .markdown-preview-view ul.dataview-ol>li::before,.dataview-inline-lists .markdown-preview-view ul.dataview-ul>li::before,.dataview-inline-lists .markdown-source-view ul.dataview-ol>li::before,.dataview-inline-lists .markdown-source-view ul.dataview-ul>li::before{display:none}.dataview-inline-lists .markdown-preview-view .dataview-ol li,.dataview-inline-lists .markdown-preview-view .dataview-ul li,.dataview-inline-lists .markdown-source-view .dataview-ol li,.dataview-inline-lists .markdown-source-view .dataview-ul li{display:inline-block;padding-inline-end:.25em;margin-inline-start:0}.markdown-rendered table.dataview{margin-block-start:0;margin-block-end:0}.markdown-rendered table.dataview .dataview-result-list-li{margin-inline-start:0}.markdown-preview-view .table-view-table>thead>tr>th,body .table-view-table>thead>tr>th{font-weight:400;font-size:var(--table-text-size);color:var(--text-muted);border-bottom:var(--table-border-width) solid var(--table-border-color);cursor:var(--cursor)}table.dataview ul.dataview-ul{list-style:none;padding-inline-start:0;margin-block-start:0em!important;margin-block-end:0em!important}.markdown-preview-view:not(.cards) .table-view-table>tbody>tr>td,.markdown-source-view.mod-cm6:not(.cards) .table-view-table>tbody>tr>td{max-width:var(--max-col-width)}body .dataview.small-text{color:var(--text-faint)}body:not(.row-hover) .dataview.task-list-basic-item:hover,body:not(.row-hover) .dataview.task-list-item:hover,body:not(.row-hover) .table-view-table>tbody>tr:hover{background-color:rgba(0,0,0,0)!important;box-shadow:none}body.row-hover .dataview.task-list-basic-item:hover,body.row-hover .dataview.task-list-item:hover,body.row-hover .table-view-table>tbody>tr:hover{background-color:var(--table-row-background-hover)}body .dataview-error{background-color:rgba(0,0,0,0)}.dataview.dataview-error,.markdown-source-view.mod-cm6 .cm-content .dataview.dataview-error{color:var(--text-muted)}body div.dataview-error-box{min-height:0;border:none;background-color:rgba(0,0,0,0);font-size:var(--table-text-size);border-radius:var(--radius-m);padding:15px 0;justify-content:flex-start}body div.dataview-error-box p{margin-block-start:0;margin-block-end:0;color:var(--text-faint)}table.dataview:has(+.dataview-error-box){display:none}.trim-cols .markdown-preview-view .table-view-table>tbody>tr>td,.trim-cols .markdown-source-view.mod-cm6 .table-view-table>tbody>tr>td,.trim-cols .markdown-source-view.mod-cm6 .table-view-table>thead>tr>th{white-space:nowrap;text-overflow:ellipsis;overflow:hidden}ul .dataview .task-list-basic-item:hover,ul .dataview .task-list-item:hover{background-color:rgba(0,0,0,0);box-shadow:none}body .dataview.result-group{padding-left:0}body .dataview .inline-field-standalone-value,body .dataview.inline-field-key,body .dataview.inline-field-value{font-family:var(--font-text);font-size:calc(var(--font-adaptive-normal) - 2px);background:rgba(0,0,0,0);color:var(--text-muted)}body .dataview.inline-field-key{padding:0}body .dataview .inline-field-standalone-value{padding:0}body .dataview.inline-field-key::after{margin-left:3px;content:"|";color:var(--background-modifier-border)}body .dataview.inline-field-value{padding:0 1px 0 3px}.markdown-preview-view .block-language-dataview table.calendar th{border:none;cursor:default;background-image:none}.markdown-preview-view .block-language-dataview table.calendar .day{font-size:var(--font-adaptive-small)}.database-plugin__navbar,.database-plugin__scroll-container,.database-plugin__table{width:100%}.dbfolder-table-container{--font-adaptive-normal:var(--table-text-size);--font-size-text:12px}.database-plugin__cell_size_wide .database-plugin__td{padding:.15rem}.database-plugin__table{border-spacing:0!important}.MuiAppBar-root{background-color:rgba(0,0,0,0)!important}.workspace-leaf-content .view-content.dictionary-view-content{padding:0}div[data-type=dictionary-view] .contents{padding-bottom:2rem}div[data-type=dictionary-view] .results>.container{background-color:rgba(0,0,0,0);margin-top:0;max-width:none;padding:0 10px}div[data-type=dictionary-view] .error,div[data-type=dictionary-view] .errorDescription{text-align:left;font-size:var(--font-adaptive-small);padding:10px 12px 0;margin:0}div[data-type=dictionary-view] .results>.container h3{text-transform:uppercase;letter-spacing:.05em;color:var(--text-muted);font-size:var(--font-adaptive-smallest);font-weight:500;padding:5px 7px 0 2px;margin-bottom:6px}div[data-type=dictionary-view] .container .main{border-radius:0;background-color:rgba(0,0,0,0);font-size:var(--font-adaptive-smaller);line-height:1.3;color:var(--text-muted);padding:5px 0 0}div[data-type=dictionary-view] .main .definition{padding:10px;border:1px solid var(--background-modifier-border);border-radius:5px;margin:10px 0 5px;background-color:var(--background-primary)}div[data-type=dictionary-view] .main .definition:last-child{border:1px solid var(--background-modifier-border)}div[data-type=dictionary-view] .main .synonyms{padding:10px 0 0}div[data-type=dictionary-view] .main .synonyms p{margin:0}div[data-type=dictionary-view] .main .definition>blockquote{margin:0}div[data-type=dictionary-view] .main .label{color:var(--text-normal);margin-bottom:2px;font-size:var(--font-adaptive-smaller);font-weight:500}div[data-type=dictionary-view] .main .mark{color:var(--text-normal);background-color:var(--text-selection);box-shadow:none}div[data-type=dictionary-view] .main>.opener{font-size:var(--font-adaptive-small);color:var(--text-normal);padding-left:5px}body .excalidraw,body .excalidraw.theme--dark{--color-primary-light:var(--text-selection);--color-primary:var(--interactive-accent);--color-primary-darker:var(--interactive-accent-hover);--color-primary-darkest:var(--interactive-accent-hover);--ui-font:var(--font-interface);--island-bg-color:var(--background-secondary);--icon-fill-color:var(--text-normal);--button-hover:var(--background-modifier-hover);--button-gray-1:var(--background-modifier-hover);--button-gray-2:var(--background-modifier-hover);--focus-highlight-color:var(--background-modifier-border-focus);--default-bg-color:var(--background-primary);--default-border-color:var(--background-modifier-border);--input-border-color:var(--background-modifier-border);--link-color:var(--text-accent);--overlay-bg-color:rgba(255, 255, 255, 0.88);--text-primary-color:var(--text-normal)}.git-view-body .opener{text-transform:uppercase;letter-spacing:.05em;font-size:var(--font-adaptive-smallest);font-weight:500;padding:5px 7px 5px 10px;margin-bottom:6px}.git-view-body .file-view .opener{text-transform:none;letter-spacing:normal;font-size:var(--font-adaptive-smallest);font-weight:400;padding:initial;margin-bottom:0}.git-view-body .file-view .opener .collapse-icon{display:flex!important;margin-left:-7px}.git-view-body{margin-top:6px}.git-view-body .file-view{margin-left:4px}.git-view-body .file-view main:hover{color:var(--text-normal)}.git-view-body .file-view .tools .type{display:none!important}.git-view-body .file-view .tools{opacity:0;transition:opacity .1s}.git-view-body .file-view main:hover>.tools{opacity:1}.git-view-body .staged{margin-bottom:12px}.git-view-body .opener.open{color:var(--text-normal)}div[data-type=git-view] .search-input-container{margin-left:0;width:100%}.git-view-body .opener .collapse-icon{display:none!important}.git-view-body main{background-color:var(--background-primary)!important;width:initial!important}.git-view-body .file-view>main:not(.topLevel){margin-left:7px}div[data-type=git-view] .commit-msg{min-height:2.5em!important;height:2.5em!important;padding:6.5px 8px!important}div[data-type=git-view] .search-input-clear-button{bottom:5.5px}.hider-vault .nav-folder.mod-root>.nav-folder-title{height:4px}.popover.hover-editor{--folding-offset:10px}.theme-dark,.theme-light{--he-title-bar-inactive-bg:var(--background-secondary);--he-title-bar-inactive-pinned-bg:var(--background-secondary);--he-title-bar-active-pinned-bg:var(--background-secondary);--he-title-bar-active-bg:var(--background-secondary);--he-title-bar-inactive-fg:var(--text-muted);--he-title-bar-active-fg:var(--text-normal);--he-title-bar-font-size:14px}.theme-light{--popover-shadow:0px 2.7px 3.1px rgba(0, 0, 0, 0.032),0px 5.9px 8.7px rgba(0, 0, 0, 0.052),0px 10.4px 18.1px rgba(0, 0, 0, 0.071),0px 20px 40px rgba(0, 0, 0, 0.11)}.theme-dark{--popover-shadow:0px 2.7px 3.1px rgba(0, 0, 0, 0.081),0px 5.9px 8.7px rgba(0, 0, 0, 0.131),0px 10.4px 18.1px rgba(0, 0, 0, 0.18),0px 20px 40px rgba(0, 0, 0, 0.28)}.popover.hover-editor:not(.snap-to-viewport){--max-width:92%}.popover.hover-editor:not(.snap-to-viewport) .markdown-preview-view,.popover.hover-editor:not(.snap-to-viewport) .markdown-source-view .cm-content{font-size:90%}body .popover.hover-editor:not(.is-loaded){box-shadow:var(--popover-shadow)}body .popover.hover-editor:not(.is-loaded) .markdown-preview-view{padding:15px 0 0 0}body .popover.hover-editor:not(.is-loaded) .view-content{height:100%;background-color:var(--background-primary)}body .popover.hover-editor:not(.is-loaded) .view-actions{height:auto}body .popover.hover-editor:not(.is-loaded) .popover-content{border:1px solid var(--background-modifier-border-hover)}body .popover.hover-editor:not(.is-loaded) .popover-titlebar{padding:0 4px}body .popover.hover-editor:not(.is-loaded) .popover-titlebar .popover-title{padding-left:4px;letter-spacing:-.02em;font-weight:var(--title-weight)}body .popover.hover-editor:not(.is-loaded) .markdown-embed{height:auto;font-size:unset;line-height:unset}body .popover.hover-editor:not(.is-loaded) .markdown-embed .markdown-preview-view{padding:0}body .popover.hover-editor:not(.is-loaded).show-navbar .popover-titlebar{border-bottom:var(--border-width) solid var(--background-modifier-border)}body .popover.hover-editor:not(.is-loaded) .popover-action,body .popover.hover-editor:not(.is-loaded) .popover-header-icon{cursor:var(--cursor);margin:4px 0;padding:4px 3px;border-radius:var(--radius-m);color:var(--icon-color)}body .popover.hover-editor:not(.is-loaded) .popover-action.mod-pin-popover,body .popover.hover-editor:not(.is-loaded) .popover-header-icon.mod-pin-popover{padding:4px 2px}body .popover.hover-editor:not(.is-loaded) .popover-action svg,body .popover.hover-editor:not(.is-loaded) .popover-header-icon svg{opacity:var(--icon-muted)}body .popover.hover-editor:not(.is-loaded) .popover-action:hover,body .popover.hover-editor:not(.is-loaded) .popover-header-icon:hover{background-color:var(--background-modifier-hover);color:var(--icon-color-hover)}body .popover.hover-editor:not(.is-loaded) .popover-action:hover svg,body .popover.hover-editor:not(.is-loaded) .popover-header-icon:hover svg{opacity:1;transition:opacity .1s ease-in-out}body .popover.hover-editor:not(.is-loaded) .popover-action.is-active,body .popover.hover-editor:not(.is-loaded) .popover-header-icon.is-active{color:var(--icon-color)}body.minimal-dark-black.theme-dark,body.minimal-dark-tonal.theme-dark,body.minimal-light-tonal.theme-light,body.minimal-light-white.theme-light,body.theme-dark{--kanban-border:0px}body:not(.is-mobile) .kanban-plugin__grow-wrap>textarea:focus{box-shadow:none}body:not(.minimal-icons-off) .kanban-plugin svg.cross{height:14px;width:14px}body .kanban-plugin__icon>svg,body .kanban-plugin__lane-settings-button svg{width:18px;height:18px}body .kanban-plugin{--kanban-border:var(--border-width);--interactive-accent:var(--text-selection);--interactive-accent-hover:var(--background-modifier-hover);--text-on-accent:var(--text-normal);background-color:var(--background-primary)}body .kanban-plugin__markdown-preview-view{font-family:var(--font-text)}body .kanban-plugin__board>div{margin:0 auto}body .kanban-plugin__checkbox-label{color:var(--text-muted)}body .kanban-plugin__item-markdown ul{margin:0}body .kanban-plugin__item-content-wrapper{box-shadow:none}body .kanban-plugin__grow-wrap::after,body .kanban-plugin__grow-wrap>textarea{padding:0;border:0;border-radius:0}body .kanban-plugin__grow-wrap::after,body .kanban-plugin__grow-wrap>textarea,body .kanban-plugin__item-title p,body .kanban-plugin__markdown-preview-view{font-size:var(--font-ui-medium);line-height:1.3}body .kanban-plugin__item{background-color:var(--background-primary)}body .kanban-plugin__item-title-wrapper{align-items:center}body .kanban-plugin__lane-form-wrapper{border:1px solid var(--background-modifier-border)}body .kanban-plugin__lane-header-wrapper{border-bottom:0}body .kanban-plugin__lane-header-wrapper .kanban-plugin__grow-wrap>textarea,body .kanban-plugin__lane-input-wrapper .kanban-plugin__grow-wrap>textarea,body .kanban-plugin__lane-title p{background:rgba(0,0,0,0);color:var(--text-normal);font-size:var(--font-ui-medium);font-weight:500}body .kanban-plugin__item-input-wrapper .kanban-plugin__grow-wrap>textarea{padding:0;border-radius:0;height:auto}body .kanban-plugin__item-form .kanban-plugin__grow-wrap{background-color:var(--background-primary)}body .kanban-plugin__item-input-wrapper .kanban-plugin__grow-wrap>textarea::placeholder{color:var(--text-faint)}body .kanban-plugin__item .kanban-plugin__item-edit-archive-button,body .kanban-plugin__item button.kanban-plugin__item-edit-button,body .kanban-plugin__item-settings-actions>button,body .kanban-plugin__lane button.kanban-plugin__lane-settings-button,body .kanban-plugin__lane button.kanban-plugin__lane-settings-button.is-enabled,body .kanban-plugin__lane-action-wrapper>button{background:rgba(0,0,0,0);transition:color .1s ease-in-out}body .kanban-plugin__item .kanban-plugin__item-edit-archive-button:hover,body .kanban-plugin__item button.kanban-plugin__item-edit-button.is-enabled,body .kanban-plugin__item button.kanban-plugin__item-edit-button:hover,body .kanban-plugin__lane button.kanban-plugin__lane-settings-button.is-enabled,body .kanban-plugin__lane button.kanban-plugin__lane-settings-button:hover{color:var(--text-normal);transition:color .1s ease-in-out;background:rgba(0,0,0,0)}body .kanban-plugin__new-lane-button-wrapper{position:fixed;bottom:30px}body .kanban-plugin__lane-items>.kanban-plugin__placeholder:only-child{border:1px dashed var(--background-modifier-border);height:2em}body .kanban-plugin__item-postfix-button-wrapper{align-self:flex-start}body .kanban-plugin__item button.kanban-plugin__item-postfix-button.is-enabled,body .kanban-plugin__item button.kanban-plugin__item-prefix-button.is-enabled,body .kanban-plugin__lane button.kanban-plugin__lane-settings-button.is-enabled{color:var(--text-muted)}body .kanban-plugin button{box-shadow:none;cursor:var(--cursor);height:auto}body .kanban-plugin__item button.kanban-plugin__item-postfix-button:hover,body .kanban-plugin__item button.kanban-plugin__item-prefix-button:hover,body .kanban-plugin__lane button.kanban-plugin__lane-settings-button:hover{background-color:var(--background-modifier-hover)}body .kanban-plugin__item-button-wrapper>button{color:var(--text-muted);font-weight:400;background:rgba(0,0,0,0);min-height:calc(var(--input-height) + 8px)}body .kanban-plugin__item-button-wrapper>button:hover{color:var(--text-normal);background:var(--background-modifier-hover)}body .kanban-plugin__item-button-wrapper>button:focus{box-shadow:none}body .kanban-plugin__item-button-wrapper{padding:1px 6px 5px;border-top:none}body .kanban-plugin__lane-setting-wrapper>div:last-child{border:none;margin:0}body .kanban-plugin.something-is-dragging{cursor:grabbing;cursor:-webkit-grabbing}body .kanban-plugin__item.is-dragging{box-shadow:0 5px 30px rgba(0,0,0,.15),0 0 0 2px var(--text-selection)}body .kanban-plugin__lane-items{border:var(--kanban-border) solid var(--background-modifier-border);padding:0 4px;margin:0;background-color:var(--background-secondary)}body .kanban-plugin__lane{background:rgba(0,0,0,0);padding:0;border:var(--border-width) solid transparent}body .kanban-plugin__lane.is-dragging{box-shadow:0 5px 30px rgba(0,0,0,.15);border:1px solid var(--background-modifier-border)}body .kanban-plugin__lane .kanban-plugin__item-button-wrapper{border-top-left-radius:8px;border-top-right-radius:8px;border-top:1px solid var(--background-modifier-border);border-bottom-width:0;padding:4px 4px 0 4px}body .kanban-plugin__lane.will-prepend .kanban-plugin__lane-items{border-radius:8px}body .kanban-plugin__lane.will-prepend .kanban-plugin__item-form{border-top:1px solid var(--background-modifier-border);border-radius:8px 8px 0 0;padding:4px 4px 0;border-bottom-width:0}body .kanban-plugin__lane.will-prepend .kanban-plugin__item-form+.kanban-plugin__lane-items{border-top-width:0;border-radius:0 0 8px 8px}body .kanban-plugin__lane.will-prepend .kanban-plugin__item-button-wrapper+.kanban-plugin__lane-items{border-top-width:0;border-radius:0 0 8px 8px}body .kanban-plugin__lane:not(.will-prepend) .kanban-plugin__item-button-wrapper,body .kanban-plugin__lane:not(.will-prepend) .kanban-plugin__item-form{border-top:none;border-radius:0 0 8px 8px}body .kanban-plugin__lane:not(.will-prepend) .kanban-plugin__item-button-wrapper{padding:0 4px 4px 4px;border-bottom-width:1px}body .kanban-plugin__lane:not(.will-prepend) .kanban-plugin__lane-items{border-bottom:none;border-top-width:1px;border-radius:8px 8px 0 0}body .kanban-plugin__item-form .kanban-plugin__item-input-wrapper{min-height:calc(var(--input-height) + 8px);display:flex;justify-content:center}body .kanban-plugin__item-button-wrapper,body .kanban-plugin__item-form{background-color:var(--background-secondary);border:var(--kanban-border) solid var(--background-modifier-border)}body .kanban-plugin__item-form{padding:0 4px 5px}body .kanban-plugin__markdown-preview-view ol,body .kanban-plugin__markdown-preview-view ol.contains-task-list .contains-task-list,body .kanban-plugin__markdown-preview-view ul,body .kanban-plugin__markdown-preview-view ul.contains-task-list .contains-task-list{padding-inline-start:1.8em!important}@media(max-width:400pt){.kanban-plugin__board{flex-direction:column!important}.kanban-plugin__lane{width:100%!important;margin-bottom:1rem!important}}body .cm-heading-marker{cursor:var(--cursor);padding-left:10px}.theme-light{--leaflet-buttons:var(--bg1);--leaflet-borders:rgba(0,0,0,0.1)}.theme-dark{--leaflet-buttons:var(--bg2);--leaflet-borders:rgba(255,255,255,0.1)}.leaflet-container{--image-radius:0}.leaflet-top{transition:top .1s linear}body .leaflet-container{background-color:var(--background-secondary);font-family:var(--font-interface)}.leaflet-control-attribution{display:none}.leaflet-popup-content{margin:10px}.block-language-leaflet{border-radius:var(--radius-m);overflow:hidden;border:var(--border-width) solid var(--background-modifier-border)}.map-wide .block-language-leaflet{border-radius:var(--radius-l)}.map-max .block-language-leaflet{border-radius:var(--radius-xl)}.workspace-leaf-content[data-type=obsidian-leaflet-map-view] .block-language-leaflet{border-radius:0;border:none}.map-100 .block-language-leaflet{border-radius:0;border-left:none;border-right:none}.block-language-leaflet .leaflet-control-expandable-list .input-container .input-item>input{appearance:none}body .block-language-leaflet .leaflet-bar.disabled>a{background-color:rgba(0,0,0,0);opacity:.3}body .leaflet-touch .leaflet-bar a:first-child{border-top-left-radius:4px;border-top-right-radius:4px}body .leaflet-touch .leaflet-bar a:last-child{border-bottom-left-radius:4px;border-bottom-right-radius:4px}body .leaflet-control-layers-toggle{border-radius:4px}body .block-language-leaflet .leaflet-control-expandable,body .block-language-leaflet .leaflet-control-has-actions .control-actions.expanded,body .block-language-leaflet .leaflet-distance-control,body .leaflet-bar,body .leaflet-bar a,body .leaflet-control-layers-expanded,body .leaflet-control-layers-toggle{background-color:var(--leaflet-buttons);color:var(--text-muted);border:none;user-select:none}body .leaflet-bar a.leaflet-disabled,body .leaflet-bar a.leaflet-disabled:hover{background-color:var(--leaflet-buttons);color:var(--text-faint);opacity:.6;cursor:not-allowed}body .leaflet-control a{cursor:var(--cursor);color:var(--text-normal)}body .leaflet-bar a:hover{background-color:var(--background-modifier-hover);color:var(--text-normal);border:none}body .leaflet-touch .leaflet-control-layers{background-color:var(--leaflet-buttons)}body .leaflet-touch .leaflet-bar,body .leaflet-touch .leaflet-control-layers{border-radius:5px;box-shadow:2px 0 8px 0 rgba(0,0,0,.1);border:1px solid var(--ui1)}body .block-language-leaflet .leaflet-control-has-actions .control-actions{box-shadow:0;border:1px solid var(--ui1)}body .leaflet-control-expandable-list .leaflet-bar{box-shadow:none;border-radius:0}body .block-language-leaflet .leaflet-distance-control{padding:4px 10px;height:auto;cursor:var(--cursor)!important}body .block-language-leaflet .leaflet-marker-link-popup>.leaflet-popup-content-wrapper>*{font-size:var(--font-adaptive-small);font-family:var(--font-interface)}body .block-language-leaflet .leaflet-marker-link-popup>.leaflet-popup-content-wrapper{padding:4px 10px!important}.leaflet-marker-icon svg path{stroke:var(--background-primary);stroke-width:18px}.map-view-marker-name{font-weight:400}.workspace-leaf-content[data-type=map] .graph-controls{background-color:var(--background-primary)}body:not(.is-mobile):not(.plugin-sliding-panes-rotate-header) .workspace-split.mod-root .workspace-leaf-content[data-type=map] .view-header{position:fixed;background:rgba(0,0,0,0)!important;width:100%;z-index:99}body:not(.plugin-sliding-panes-rotate-header) .workspace-leaf-content[data-type=map] .view-header-title{display:none}body:not(.is-mobile):not(.plugin-sliding-panes-rotate-header) .workspace-leaf-content[data-type=map] .view-actions{background:rgba(0,0,0,0)}body:not(.is-mobile):not(.plugin-sliding-panes-rotate-header) .workspace-leaf-content[data-type=map] .view-content{height:100%}body:not(.is-mobile):not(.plugin-sliding-panes-rotate-header) .workspace-leaf-content[data-type=map] .leaflet-top.leaflet-right{top:var(--header-height)}.obsidian-metatable{--metatable-font-size:calc(var(--font-adaptive-normal) - 2px);--metatable-font-family:var(--font-interface);--metatable-background:transparent;--metatable-foreground:var(--text-faint);--metatable-key-background:transparent;--metatable-key-border-width:0;--metatable-key-border-color:transparent;--metatable-value-background:transparent;padding-bottom:.5rem}.obsidian-metatable::part(key),.obsidian-metatable::part(value){border-bottom:0 solid var(--background-modifier-border);padding:.1rem 0;text-overflow:ellipsis;overflow:hidden}.obsidian-metatable::part(key){font-weight:400;color:var(--tx3);font-size:calc(var(--font-adaptive-normal) - 2px)}.obsidian-metatable::part(value){font-size:calc(var(--font-adaptive-normal) - 2px);color:var(--tx1)}body .NLT__header-menu-header-container{font-size:85%}body .NLT__button{background:rgba(0,0,0,0);box-shadow:none;color:var(--text-muted)}body .NLT__button:active,body .NLT__button:focus,body .NLT__button:hover{background:rgba(0,0,0,0);color:var(--text-normal);box-shadow:none}.NLT__app .NLT__button{background:rgba(0,0,0,0);border:1px solid var(--background-modifier-border);box-shadow:0 .5px 1px 0 var(--btn-shadow-color);color:var(--text-muted);padding:2px 8px}.NLT__app .NLT__button:active,.NLT__app .NLT__button:focus,.NLT__app .NLT__button:hover{background:rgba(0,0,0,0);border-color:var(--background-modifier-border-hover);color:var(--text-normal);box-shadow:0 .5px 1px 0 var(--btn-shadow-color)}.NLT__td:nth-last-child(2),.NLT__th:nth-last-child(2){border-right:0}.NLT__app .NLT__td:last-child,.NLT__app .NLT__th:last-child{padding-right:0}.NLT__app .NLT__th{background-image:none!important}.NLT__app th.NLT__selectable:hover{background-color:rgba(0,0,0,0);cursor:var(--cursor)}.NLT__menu .NLT__menu-container{background-color:var(--background-secondary)}.NLT__menu .NLT__header-menu-item{font-size:var(--font-adaptive-small)}.NLT__menu .NLT__header-menu{padding:6px 4px}.NLT__menu .NLT__drag-menu{font-size:var(--font-adaptive-small);padding:6px 4px}.NLT__menu svg{color:var(--text-faint);margin-right:6px}.NLT__menu .NLT__selectable:hover,.NLT__menu .NLT__selected{background:rgba(0,0,0,0)}.NLT__menu .NLT__selected>.NLT__selectable{background-color:var(--background-modifier-hover)}.NLT__menu .NLT__selectable{cursor:var(--cursor)}.NLT__menu div.NLT__selectable{min-width:110px;border-radius:var(--radius-m);padding:3px 8px 3px 4px;margin:1px 2px 1px;cursor:var(--cursor);height:auto;line-height:20px}.NLT__menu div.NLT__selectable:hover{background-color:var(--background-modifier-hover)}.NLT__menu .NLT__textarea{font-size:var(--table-text-size)}.NLT__tfoot tr:hover td{background-color:rgba(0,0,0,0)}.modal .quickAddPrompt>h1,.modal .quickAddYesNoPrompt h1{margin-top:0;text-align:left!important;font-size:var(--h1);font-weight:600}.modal .quickAddYesNoPrompt p{text-align:left!important}.modal .quickAddYesNoPrompt button{font-size:var(--font-ui-small)}.modal .yesNoPromptButtonContainer{font-size:var(--font-ui-small);justify-content:flex-end}.quickAddModal .modal-content{padding:20px 2px 5px}div#quick-explorer{display:flex}div#quick-explorer span.explorable{align-items:center;color:var(--text-muted);display:flex;font-size:var(--font-adaptive-smaller);line-height:16px}div#quick-explorer span.explorable:last-of-type{font-size:var(--font-adaptive-smaller)}div#quick-explorer span.explorable.selected,div#quick-explorer span.explorable:hover{background-color:unset!important}div#quick-explorer span.explorable.selected .explorable-name,div#quick-explorer span.explorable:hover .explorable-name{color:var(--text-normal)}div#quick-explorer span.explorable.selected .explorable-separator,div#quick-explorer span.explorable:hover .explorable-separator{color:var(--text-normal)}div#quick-explorer .explorable-name{padding:0 4px;border-radius:4px}div#quick-explorer .explorable-separator::before{content:" ›"!important;font-size:1.3em;font-weight:400;margin:0}body:not(.colorful-active) .qe-popup-menu .menu-item:not(.is-disabled):not(.is-label).selected,body:not(.colorful-active) .qe-popup-menu .menu-item:not(.is-disabled):not(.is-label):hover{background-color:var(--background-modifier-hover);color:var(--text-normal)}body:not(.colorful-active) .qe-popup-menu .menu-item:not(.is-disabled):not(.is-label).selected .menu-item-icon,body:not(.colorful-active) .qe-popup-menu .menu-item:not(.is-disabled):not(.is-label):hover .menu-item-icon{color:var(--text-normal)}.workspace-leaf-content[data-type=recent-files] .view-content{padding-top:10px}.mod-root .workspace-leaf-content[data-type=reminder-list] main{max-width:var(--max-width);margin:0 auto;padding:0}.modal .reminder-actions .later-select{font-size:var(--font-settings-small);vertical-align:bottom;margin-left:3px}.modal .reminder-actions .icon{line-height:1}:not(.mod-root) .workspace-leaf-content[data-type=reminder-list] main{margin:0 auto;padding:15px}:not(.mod-root) .workspace-leaf-content[data-type=reminder-list] main .group-name{font-weight:500;color:var(--text-muted);font-size:var(--font-adaptive-small);padding-bottom:.5em;border-bottom:1px solid var(--background-modifier-border)}:not(.mod-root) .workspace-leaf-content[data-type=reminder-list] main .reminder-group .reminder-list-item{line-height:1.3;font-size:var(--font-adaptive-small)}:not(.mod-root) .workspace-leaf-content[data-type=reminder-list] main .reminder-group .no-reminders{color:var(--text-faint)}:not(.mod-root) .workspace-leaf-content[data-type=reminder-list] main .reminder-group .reminder-time{font-family:var(--font-text);font-size:var(--font-adaptive-small)}:not(.mod-root) .workspace-leaf-content[data-type=reminder-list] main .reminder-group .reminder-file{color:var(--text-faint)}body .modal .dtchooser{background-color:rgba(0,0,0,0)}body .modal .dtchooser .reminder-calendar .year-month{font-weight:400;font-size:var(--font-adaptive-normal);padding-bottom:10px}body .modal .dtchooser .reminder-calendar .year-month .month,body .modal .dtchooser .reminder-calendar .year-month .year{color:var(--text-normal)}body .modal .dtchooser .reminder-calendar .year-month .month-nav:first-child{background-color:currentColor;-webkit-mask-image:url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' class='h-5 w-5' viewBox='0 0 20 20' fill='currentColor'%3E%3Cpath fill-rule='evenodd' d='M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z' clip-rule='evenodd' /%3E%3C/svg%3E")}body .modal .dtchooser .reminder-calendar .year-month .month-nav:last-child{background-color:currentColor;-webkit-mask-image:url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' class='h-5 w-5' viewBox='0 0 20 20' fill='currentColor'%3E%3Cpath fill-rule='evenodd' d='M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z' clip-rule='evenodd' /%3E%3C/svg%3E")}body .modal .dtchooser .reminder-calendar .year-month .month-nav{-webkit-mask-size:20px 20px;-webkit-mask-repeat:no-repeat;-webkit-mask-position:50% 50%;color:var(--text-faint);cursor:var(--cursor);border-radius:var(--radius-m);padding:0;width:30px;display:inline-block}body .modal .dtchooser .reminder-calendar .year-month .month-nav:hover{color:var(--text-muted)}body .modal .dtchooser .reminder-calendar th{padding:.5em 0;font-size:var(--font-adaptive-smallest);font-weight:500;text-transform:uppercase;letter-spacing:.1em}body .modal .dtchooser .reminder-calendar .calendar-date{transition:background-color .1s ease-in;padding:.3em 0;border-radius:var(--radius-m)}body .modal .dtchooser .reminder-calendar .calendar-date.is-selected,body .modal .dtchooser .reminder-calendar .calendar-date:hover{transition:background-color .1s ease-in;background-color:var(--background-modifier-hover)!important}body .modal .dtchooser .reminder-calendar .calendar-date.is-selected{font-weight:var(--bold-weight);color:var(--text-accent)!important}body .markdown-preview-view th,body .markdown-source-view.mod-cm6 .dataview.table-view-table thead.table-view-thead tr th,body .table-view-table>thead>tr>th{cursor:var(--cursor);background-image:none}.markdown-source-view.mod-cm6 th{background-repeat:no-repeat;background-position:right}.style-settings-container[data-level="2"]{background:var(--background-secondary);border:1px solid var(--ui1);border-radius:5px;padding:10px 20px;margin:2px 0 2px -20px}.workspace-leaf-content[data-type=style-settings] div[data-id=instructions] .setting-item-name{display:none}.workspace-leaf-content[data-type=style-settings] div[data-id=instructions] .setting-item-description{color:var(--text-normal);font-size:var(--font-adaptive-smaller);padding-bottom:.5em}.workspace-leaf-content[data-type=style-settings] .view-content{padding:var(--size-4-4) 0}.workspace-leaf-content[data-type=style-settings] .view-content>div{width:var(--line-width);max-width:var(--max-width);margin:0 auto}.workspace-leaf-content[data-type=style-settings] .style-settings-heading[data-level="0"] .setting-item-name{padding-left:17px}.workspace-leaf-content[data-type=style-settings] .setting-item{max-width:100%;margin:0 auto}.workspace-leaf-content[data-type=style-settings] .setting-item-name{position:relative}.workspace-leaf-content[data-type=style-settings] .style-settings-collapse-indicator{position:absolute;left:0}.setting-item-heading.style-settings-heading,.style-settings-container .style-settings-heading{cursor:var(--cursor)}.modal.mod-settings .setting-item .pickr button.pcr-button{box-shadow:none;border-radius:40px;height:24px;width:24px}.setting-item .pickr .pcr-button:after,.setting-item .pickr .pcr-button:before{border-radius:40px;box-shadow:none;border:none}.setting-item.setting-item-heading.style-settings-heading.is-collapsed{border-bottom:1px solid var(--background-modifier-border)}.setting-item.setting-item-heading.style-settings-heading{border:0;padding:10px 0 5px;margin-bottom:0}.setting-item .style-settings-export,.setting-item .style-settings-import{text-decoration:none;font-size:var(--font-ui-small);font-weight:500;color:var(--text-muted);margin:0;padding:2px 8px;border-radius:5px;cursor:var(--cursor)}.setting-item .style-settings-export:hover,.setting-item .style-settings-import:hover{background-color:var(--background-modifier-hover);color:var(--text-normal);cursor:var(--cursor)}.mod-root .workspace-leaf-content[data-type=style-settings] .style-settings-container .setting-item:not(.setting-item-heading){flex-direction:row;align-items:center;padding:.5em 0}.workspace-split:not(.mod-root) .workspace-leaf-content[data-type=style-settings] .setting-item-name{font-size:var(--font-adaptive-smaller)}.themed-color-wrapper>div+div{margin-top:0;margin-left:6px}.theme-light .themed-color-wrapper>.theme-light{background-color:rgba(0,0,0,0)}.theme-light .themed-color-wrapper>.theme-dark{background-color:rgba(0,0,0,.8)}.theme-dark .themed-color-wrapper>.theme-dark{background-color:rgba(0,0,0,0)}@media(max-width:400pt){.workspace-leaf-content[data-type=style-settings] .setting-item-name{font-size:var(--font-adaptive-small)}.workspace-leaf-content[data-type=style-settings] .setting-item-info:has(.search-input-container){width:100%;margin-right:0}}body .todoist-query-title{display:inline;font-size:var(--h4);font-variant:var(--h4-variant);letter-spacing:.02em;color:var(--h4-color);font-weight:var(--h4-weight);font-style:var(--h4-style)}body .is-live-preview .block-language-todoist{padding-left:0}ul.todoist-task-list>li.task-list-item .task-list-item-checkbox{margin:0}body .todoist-refresh-button{display:inline;float:right;background:rgba(0,0,0,0);padding:5px 6px 0;margin-right:0}body .is-live-preview .todoist-refresh-button{margin-right:30px}body .todoist-refresh-button:hover{box-shadow:none;background-color:var(--background-modifier-hover)}.todoist-refresh-button svg{width:15px;height:15px;opacity:var(--icon-muted)}ul.todoist-task-list{margin-left:-.25em}.is-live-preview ul.todoist-task-list{padding-left:0;margin-left:.5em;margin-block-start:0;margin-block-end:0}.contains-task-list.todoist-task-list .task-metadata{font-size:var(--font-adaptive-small);display:flex;color:var(--text-muted);justify-content:space-between;margin-left:.1em;margin-bottom:.25rem}.is-live-preview .contains-task-list.todoist-task-list .task-metadata{padding-left:calc(var(--checkbox-size) + .6em)}.todoist-task-list .task-date.task-overdue{color:var(--color-orange)}body .todoist-p1>input[type=checkbox]{border:1px solid var(--color-red)}body .todoist-p1>input[type=checkbox]:hover{opacity:.8}body .todoist-p2>input[type=checkbox]{border:1px solid var(--color-yellow)}body .todoist-p2>input[type=checkbox]:hover{opacity:.8}body .todoist-p3>input[type=checkbox]{border:1px solid var(--color-blue)}body .todoist-p3>input[type=checkbox]:hover{opacity:.8}body.theme-light{--color-axis-label:var(--tx1);--color-tick-label:var(--tx2);--color-dot-fill:var(--ax1);--color-line:var(--ui1)}.tracker-axis-label{font-family:var(--font-interface)}.tracker-axis{color:var(--ui2)}.tabs-manager .chat-view{--assistant-message-color:var(--background-primary);--padding-md:var(--size-4-2) var(--size-4-3);--padding-lg:var(--size-4-3) var(--size-4-3);--chat-box-color:var(--background-primary)}.tabs-manager .chat-view .ow-dialogue-timeline{padding:var(--size-4-4) var(--size-4-3) var(--size-4-8)}.tabs-manager .chat-view .ow-dialogue-timeline .ow-message-bubble .ow-content-wrapper{box-shadow:none;border-color:var(--background-modifier-border);border-radius:var(--radius-m)}.tabs-manager .chat-view .ow-dialogue-timeline .ow-message-bubble.ow-user-bubble .ow-content-wrapper{border-width:0;background-color:var(--interactive-accent)}.tabs-manager .chat-view .input-area .input-form .chat-box{border-radius:0;box-shadow:none;grid-row:1;grid-column:1/3;height:100px;border:none;padding:var(--size-4-3) var(--size-4-4) var(--size-4-2)}.tabs-manager .chat-view .input-area .input-form .chat-box:hover{height:100px}.tabs-manager .chat-view .input-area{padding:0;gap:0}.tabs-manager .chat-view .header{border-bottom:1px solid var(--background-modifier-border)}.tabs-manager .chat-view .input-form{border-top:1px solid var(--background-modifier-border)}.tabs-manager .chat-view .input-area .input-form .chat-box .info-bar span{color:var(--text-faint)}.tabs-manager .chat-view .input-area .input-form .btn-new-chat{display:none}.zoom-plugin-header{--link-color:var(--text-normal);--link-decoration:none;font-size:var(--font-ui-small);padding:0;justify-content:center;margin:var(--size-4-2) auto;max-width:var(--max-width)}.zoom-plugin-header>.zoom-plugin-title{text-decoration:none;max-width:15em;overflow:hidden}.zoom-plugin-header>.zoom-plugin-delimiter{color:var(--text-faint);padding:0 var(--size-4-1)}.theme-dark.minimal-atom-dark{--color-red-rgb:225,109,118;--color-orange-rgb:209,154,102;--color-yellow-rgb:206,193,103;--color-green-rgb:152,195,121;--color-cyan-rgb:88,182,194;--color-blue-rgb:98,175,239;--color-purple-rgb:198,120,222;--color-pink-rgb:225,109,118;--color-red:#e16d76;--color-orange:#d19a66;--color-yellow:#cec167;--color-green:#98c379;--color-cyan:#58b6c2;--color-blue:#62afef;--color-purple:#c678de;--color-pink:#e16d76}.theme-light.minimal-atom-light{--color-red-rgb:228,87,73;--color-orange-rgb:183,107,2;--color-yellow-rgb:193,131,2;--color-green-rgb:80,161,80;--color-cyan-rgb:13,151,179;--color-blue-rgb:98,175,239;--color-purple-rgb:166,38,164;--color-pink-rgb:228,87,73;--color-red:#e45749;--color-orange:#b76b02;--color-yellow:#c18302;--color-green:#50a150;--color-cyan:#0d97b3;--color-blue:#62afef;--color-purple:#a626a4;--color-pink:#e45749}.theme-light.minimal-atom-light{--base-h:106;--base-s:0%;--base-l:98%;--accent-h:231;--accent-s:76%;--accent-l:62%;--bg1:#fafafa;--bg2:#eaeaeb;--bg3:rgba(0,0,0,.1);--ui1:#dbdbdc;--ui2:#d8d8d9;--tx1:#232324;--tx2:#8e8e90;--tx3:#a0a1a8;--hl1:rgba(180,180,183,0.3);--hl2:rgba(209,154,102,0.35)}.theme-light.minimal-atom-light.minimal-light-white{--bg3:#eaeaeb}.theme-dark.minimal-atom-dark,.theme-light.minimal-atom-light.minimal-light-contrast .mod-left-split,.theme-light.minimal-atom-light.minimal-light-contrast .theme-dark,.theme-light.minimal-atom-light.minimal-light-contrast .titlebar,.theme-light.minimal-atom-light.minimal-light-contrast .workspace-drawer.mod-left,.theme-light.minimal-atom-light.minimal-light-contrast .workspace-ribbon.mod-left:not(.is-collapsed),.theme-light.minimal-atom-light.minimal-light-contrast.minimal-status-off .status-bar{--base-h:220;--base-s:12%;--base-l:18%;--accent-h:220;--accent-s:86%;--accent-l:65%;--bg1:#282c34;--bg2:#21252c;--bg3:#3a3f4b;--divider-color:#181a1f;--tab-outline-color:#181a1f;--tx1:#d8dae1;--tx2:#898f9d;--tx3:#5d6370;--hl1:rgba(114,123,141,0.3);--hl2:rgba(209,154,102,0.3);--sp1:#fff}.theme-dark.minimal-atom-dark.minimal-dark-black{--base-d:5%;--bg3:#282c34;--divider-color:#282c34;--tab-outline-color:#282c34}.theme-light.minimal-ayu-light{--color-red-rgb:230,80,80;--color-orange-rgb:250,141,62;--color-yellow-rgb:242,174,73;--color-green-rgb:108,191,67;--color-cyan-rgb:76,191,153;--color-blue-rgb:57,158,230;--color-purple-rgb:163,122,204;--color-pink-rgb:255,115,131;--color-red:#e65050;--color-orange:#fa8d3e;--color-yellow:#f2ae49;--color-green:#6CBF43;--color-cyan:#4cbf99;--color-blue:#399ee6;--color-purple:#a37acc;--color-pink:#ff7383}.theme-dark.minimal-ayu-dark{--color-red-rgb:255,102,102;--color-orange-rgb:250,173,102;--color-yellow-rgb:255,209,55;--color-green-rgb:135,217,108;--color-cyan-rgb:149,230,203;--color-blue-rgb:115,208,255;--color-purple-rgb:223,191,255;--color-pink-rgb:242,121,131;--color-red:#ff6666;--color-orange:#ffad66;--color-yellow:#ffd137;--color-green:#87D96C;--color-cyan:#95e6cb;--color-blue:#73d0ff;--color-purple:#dfbfff;--color-pink:#f27983}.theme-light.minimal-ayu-light{--base-h:210;--base-s:17%;--base-l:98%;--accent-h:36;--accent-s:100%;--accent-l:50%;--bg1:#fff;--bg2:#f8f9fa;--bg3:rgba(209,218,224,0.5);--ui1:#E6EAED;--tx1:#5C6165;--tx2:#8A9199;--tx3:#AAAEB0;--hl1:rgba(3,91,214,0.15)}.theme-dark.minimal-ayu-dark,.theme-light.minimal-ayu-light.minimal-light-contrast .mod-left-split,.theme-light.minimal-ayu-light.minimal-light-contrast .theme-dark,.theme-light.minimal-ayu-light.minimal-light-contrast .titlebar,.theme-light.minimal-ayu-light.minimal-light-contrast .workspace-drawer.mod-left,.theme-light.minimal-ayu-light.minimal-light-contrast .workspace-ribbon.mod-left:not(.is-collapsed),.theme-light.minimal-ayu-light.minimal-light-contrast.minimal-status-off .status-bar{--base-h:222;--base-s:22%;--base-l:15%;--accent-h:35;--accent-s:100%;--accent-l:60%;--bg1:#232937;--bg2:#1E2431;--bg3:rgba(51,61,80,0.5);--ui1:#333C4A;--ui2:#333C4A;--ui3:#333C4A;--tx1:#cccac2;--tx2:#707A8C;--tx3:#495063;--hl1:rgba(64,159,255,0.25)}.theme-dark.minimal-ayu-dark.minimal-dark-black{--accent-h:40;--accent-s:75%;--accent-l:61%;--bg3:#0E1017;--tx1:#BFBDB6;--divider-color:#11151C;--tab-outline-color:#11151C}.theme-light.minimal-catppuccin-light{--color-red-rgb:230,69,83;--color-orange-rgb:254,100,12;--color-yellow-rgb:223,142,29;--color-green-rgb:64,160,43;--color-cyan-rgb:23,146,154;--color-blue-rgb:33,102,246;--color-purple-rgb:137,56,239;--color-pink-rgb:234,119,203;--color-red:#E64553;--color-orange:#FE640C;--color-yellow:#DF8E1D;--color-green:#40A02B;--color-cyan:#17929A;--color-blue:#2166F6;--color-purple:#8938EF;--color-pink:#EA77CB}.theme-dark.minimal-catppuccin-dark{--color-red-rgb:235,153,156;--color-orange-rgb:239,160,118;--color-yellow-rgb:229,200,144;--color-green-rgb:166,209,138;--color-cyan-rgb:129,200,190;--color-blue-rgb:140,170,238;--color-purple-rgb:202,158,230;--color-pink-rgb:244,185,229;--color-red:#EB999C;--color-orange:#EFA076;--color-yellow:#E5C890;--color-green:#A6D18A;--color-cyan:#81C8BE;--color-blue:#8CAAEE;--color-purple:#CA9EE6;--color-pink:#F4B9E5}.theme-light.minimal-catppuccin-light{--base-h:228;--base-s:20%;--base-l:95%;--accent-h:11;--accent-s:59%;--accent-l:67%;--bg1:#F0F1F5;--bg2:#DCE0E8;--bg3:hsla(228,11%,65%,.25);--ui1:#CCD0DA;--ui2:#BCC0CC;--ui3:#ACB0BE;--tx1:#4D4F69;--tx2:#5D5F77;--tx3:#8D8FA2;--hl1:rgba(172,176,190,.3);--hl2:rgba(223,142,29,.3)}.theme-light.minimal-catppuccin-light.minimal-light-tonal{--bg2:#DCE0E8}.theme-light.minimal-catppuccin-light.minimal-light-white{--bg3:#F0F1F5;--ui1:#DCE0E8}.theme-dark.minimal-catppuccin-dark,.theme-light.minimal-catppuccin-light.minimal-light-contrast .mod-left-split,.theme-light.minimal-catppuccin-light.minimal-light-contrast .theme-dark,.theme-light.minimal-catppuccin-light.minimal-light-contrast .titlebar,.theme-light.minimal-catppuccin-light.minimal-light-contrast .workspace-drawer.mod-left,.theme-light.minimal-catppuccin-light.minimal-light-contrast .workspace-ribbon.mod-left:not(.is-collapsed),.theme-light.minimal-catppuccin-light.minimal-light-contrast.minimal-status-off .status-bar{--base-h:229;--base-s:19%;--base-l:23%;--accent-h:10;--accent-s:57%;--accent-l:88%;--bg1:#303446;--bg2:#242634;--bg3:hsla(229,13%,52%,0.25);--ui1:#41455A;--ui2:#51576D;--ui3:#626880;--tx1:#C6D0F5;--tx2:#A6ADCE;--tx3:#848BA7;--sp1:#242634;--hl1:rgba(98,104,128,.5);--hl2:rgba(223,142,29,.4)}.theme-dark.minimal-catppuccin-dark.minimal-dark-black{--ui1:#303446;--hl2:rgba(223,142,29,.5)}.theme-dark.minimal-dracula-dark{--color-red-rgb:255,85,85;--color-orange-rgb:255,184,108;--color-yellow-rgb:241,250,140;--color-green-rgb:80,250,123;--color-cyan-rgb:139,233,253;--color-blue-rgb:98,114,164;--color-purple-rgb:189,147,249;--color-pink-rgb:255,121,198;--color-red:#ff5555;--color-orange:#ffb86c;--color-yellow:#f1fa8c;--color-green:#50fa7b;--color-cyan:#8be9fd;--color-blue:#6272a4;--color-purple:#bd93f9;--color-pink:#ff79c6}.theme-dark.minimal-dracula-dark,.theme-light.minimal-dracula-light.minimal-light-contrast .mod-left-split,.theme-light.minimal-dracula-light.minimal-light-contrast .theme-dark,.theme-light.minimal-dracula-light.minimal-light-contrast .titlebar,.theme-light.minimal-dracula-light.minimal-light-contrast .workspace-drawer.mod-left,.theme-light.minimal-dracula-light.minimal-light-contrast .workspace-ribbon.mod-left:not(.is-collapsed),.theme-light.minimal-dracula-light.minimal-light-contrast.minimal-status-off .status-bar{--base-h:232;--base-s:16%;--base-l:19%;--accent-h:265;--accent-s:89%;--accent-l:78%;--bg1:#282a37;--bg2:#21222c;--ui2:#44475a;--ui3:#6272a4;--tx1:#f8f8f2;--tx2:#949FBE;--tx3:#6272a4;--hl1:rgba(134, 140, 170, 0.3);--hl2:rgba(189, 147, 249, 0.35)}.theme-dark.minimal-dracula-dark.minimal-dark-black{--ui1:#282a36}.theme-dark.minimal-eink-dark,.theme-light.minimal-eink-light{--collapse-icon-color:var(--text-normal);--icon-color-active:var(--bg1);--icon-color-hover:var(--bg1);--icon-color-focused:var(--bg1);--icon-opacity:1;--indentation-guide-color:var(--tx1);--indentation-guide-color-active:var(--tx1);--indentation-guide-width-active:3px;--interactive-normal:var(--bg1);--input-shadow:0 0 0 1px var(--tx1);--link-unresolved-opacity:1;--link-unresolved-decoration-style:dashed;--link-unresolved-decoration-color:var(--tx1);--metadata-label-background-active:var(--bg1);--metadata-input-background-active:var(--bg1);--modal-border-color:var(--tx1);--modal-border-width:2px;--prompt-border-color:var(--tx1);--prompt-border-width:2px;--calendar-dot-active:var(--bg1);--calendar-dot-today:var(--bg1);--calendar-text-active:var(--bg1);--tag-border-width:1.25px;--tag-background:transparent;--tag-background-hover:transparent;--tag-border-color:var(--tx1);--tag-border-color-hover:var(--tx1);--text-on-accent:var(--bg1);--text-on-accent-inverted:var(--bg1);--text-selection:var(--tx1);--vault-profile-color:var(--tx1);--nav-item-color-active:var(--bg1);--nav-item-color-hover:var(--bg1)}.theme-dark.minimal-eink-dark ::selection,.theme-dark.minimal-eink-dark button:hover,.theme-light.minimal-eink-light ::selection,.theme-light.minimal-eink-light button:hover{color:var(--bg1)}.theme-dark.minimal-eink-dark .nav-files-container,.theme-light.minimal-eink-light .nav-files-container{--nav-item-color-active:var(--bg1)}.theme-dark.minimal-eink-dark .tree-item-self:hover,.theme-light.minimal-eink-light .tree-item-self:hover{--nav-collapse-icon-color:var(--bg1)}.theme-dark.minimal-eink-dark.is-focused .mod-active .workspace-tab-header.is-active .workspace-tab-header-inner-icon,.theme-dark.minimal-eink-dark.labeled-nav .mod-left-split .mod-top .workspace-tab-header.is-active,.theme-dark.minimal-eink-dark.tabs-modern,.theme-light.minimal-eink-light.is-focused .mod-active .workspace-tab-header.is-active .workspace-tab-header-inner-icon,.theme-light.minimal-eink-light.labeled-nav .mod-left-split .mod-top .workspace-tab-header.is-active,.theme-light.minimal-eink-light.tabs-modern{--minimal-tab-text-color-active:var(--bg1);--tab-text-color-focused:var(--bg1);--tab-text-color-focused-active-current:var(--bg1)}.theme-dark.minimal-eink-dark .setting-hotkey,.theme-light.minimal-eink-light .setting-hotkey{background-color:rgba(0,0,0,0);border:1px solid var(--tx1)}.theme-dark.minimal-eink-dark .suggestion-container,.theme-light.minimal-eink-light .suggestion-container{border-width:3px}.theme-dark.minimal-eink-dark .cm-s-obsidian span.cm-inline-code,.theme-dark.minimal-eink-dark .markdown-rendered code,.theme-light.minimal-eink-light .cm-s-obsidian span.cm-inline-code,.theme-light.minimal-eink-light .markdown-rendered code{font-weight:600}.theme-dark.minimal-eink-dark .tree-item-self.is-active,.theme-dark.minimal-eink-dark .tree-item-self:hover,.theme-light.minimal-eink-light .tree-item-self.is-active,.theme-light.minimal-eink-light .tree-item-self:hover{--icon-color:var(--bg1)}.theme-dark.minimal-eink-dark .metadata-property-icon,.theme-light.minimal-eink-light .metadata-property-icon{--icon-color-focused:var(--tx1)}.theme-dark.minimal-eink-dark .checkbox-container,.theme-light.minimal-eink-light .checkbox-container{background-color:var(--bg1);box-shadow:0 0 0 1px var(--tx1);--toggle-thumb-color:var(--tx1)}.theme-dark.minimal-eink-dark .checkbox-container.is-enabled,.theme-light.minimal-eink-light .checkbox-container.is-enabled{background-color:var(--tx1);--toggle-thumb-color:var(--bg1)}.theme-dark.minimal-eink-dark.labeled-nav .mod-left-split .mod-top .workspace-tab-header.is-active:not(:hover),.theme-dark.minimal-eink-dark.tabs-modern .mod-root .workspace-tabs:not(.mod-stacked) .workspace-tab-header.is-active,.theme-light.minimal-eink-light.labeled-nav .mod-left-split .mod-top .workspace-tab-header.is-active:not(:hover),.theme-light.minimal-eink-light.tabs-modern .mod-root .workspace-tabs:not(.mod-stacked) .workspace-tab-header.is-active{background-color:var(--tx1)}.theme-dark.minimal-eink-dark #calendar-container .reset-button:hover,.theme-dark.minimal-eink-dark .cm-s-obsidian span.cm-formatting-highlight,.theme-dark.minimal-eink-dark .cm-s-obsidian span.cm-highlight,.theme-dark.minimal-eink-dark .community-item .suggestion-highlight,.theme-dark.minimal-eink-dark .dropdown:hover,.theme-dark.minimal-eink-dark .horizontal-tab-nav-item:hover,.theme-dark.minimal-eink-dark .markdown-rendered mark,.theme-dark.minimal-eink-dark .mod-root .workspace-tab-header-status-icon,.theme-dark.minimal-eink-dark .mod-root .workspace-tab-header:hover,.theme-dark.minimal-eink-dark .search-result-file-match:hover,.theme-dark.minimal-eink-dark .search-result-file-matched-text,.theme-dark.minimal-eink-dark .status-bar .plugin-sync:hover .sync-status-icon.mod-success,.theme-dark.minimal-eink-dark .status-bar .plugin-sync:hover .sync-status-icon.mod-working,.theme-dark.minimal-eink-dark .status-bar-item.mod-clickable:hover,.theme-dark.minimal-eink-dark .suggestion-item.is-selected,.theme-dark.minimal-eink-dark .text-icon-button:hover,.theme-dark.minimal-eink-dark .vertical-tab-nav-item:hover,.theme-dark.minimal-eink-dark button.mod-cta,.theme-dark.minimal-eink-dark select:hover,.theme-dark.minimal-eink-dark.is-focused.tabs-modern .mod-active .workspace-tab-header.is-active .workspace-tab-header-inner-title,.theme-dark.minimal-eink-dark.labeled-nav .mod-left-split .mod-top .workspace-tab-header.is-active,.theme-dark.minimal-eink-dark.labeled-nav .mod-left-split .mod-top .workspace-tab-header:hover,.theme-dark.minimal-eink-dark:not(.colorful-active) .horizontal-tab-nav-item.is-active,.theme-dark.minimal-eink-dark:not(.colorful-active) .vertical-tab-nav-item.is-active,.theme-light.minimal-eink-light #calendar-container .reset-button:hover,.theme-light.minimal-eink-light .cm-s-obsidian span.cm-formatting-highlight,.theme-light.minimal-eink-light .cm-s-obsidian span.cm-highlight,.theme-light.minimal-eink-light .community-item .suggestion-highlight,.theme-light.minimal-eink-light .dropdown:hover,.theme-light.minimal-eink-light .horizontal-tab-nav-item:hover,.theme-light.minimal-eink-light .markdown-rendered mark,.theme-light.minimal-eink-light .mod-root .workspace-tab-header-status-icon,.theme-light.minimal-eink-light .mod-root .workspace-tab-header:hover,.theme-light.minimal-eink-light .search-result-file-match:hover,.theme-light.minimal-eink-light .search-result-file-matched-text,.theme-light.minimal-eink-light .status-bar .plugin-sync:hover .sync-status-icon.mod-success,.theme-light.minimal-eink-light .status-bar .plugin-sync:hover .sync-status-icon.mod-working,.theme-light.minimal-eink-light .status-bar-item.mod-clickable:hover,.theme-light.minimal-eink-light .suggestion-item.is-selected,.theme-light.minimal-eink-light .text-icon-button:hover,.theme-light.minimal-eink-light .vertical-tab-nav-item:hover,.theme-light.minimal-eink-light button.mod-cta,.theme-light.minimal-eink-light select:hover,.theme-light.minimal-eink-light.is-focused.tabs-modern .mod-active .workspace-tab-header.is-active .workspace-tab-header-inner-title,.theme-light.minimal-eink-light.labeled-nav .mod-left-split .mod-top .workspace-tab-header.is-active,.theme-light.minimal-eink-light.labeled-nav .mod-left-split .mod-top .workspace-tab-header:hover,.theme-light.minimal-eink-light:not(.colorful-active) .horizontal-tab-nav-item.is-active,.theme-light.minimal-eink-light:not(.colorful-active) .vertical-tab-nav-item.is-active{color:var(--bg1)}.theme-dark.minimal-eink-dark .is-flashing,.theme-light.minimal-eink-light .is-flashing{--text-highlight-bg:#999}.theme-dark.minimal-eink-dark #calendar-container .day:hover,.theme-light.minimal-eink-light #calendar-container .day:hover{--color-dot:var(--bg1)}.theme-light.minimal-eink-light{--base-h:0;--base-s:0%;--base-l:100%;--accent-h:0;--accent-s:0%;--accent-l:0%;--ax3:#000;--bg1:#fff;--bg2:#fff;--bg3:#000;--ui1:#000;--ui2:#000;--ui3:#000;--tx1:#000;--tx2:#000;--tx3:#000;--hl1:#000;--hl2:#000;--sp1:#fff;--text-on-accent:#fff;--background-modifier-cover:rgba(235,235,235,1)}.theme-dark.minimal-eink-dark,.theme-light.minimal-eink-light.minimal-light-contrast .mod-left-split,.theme-light.minimal-eink-light.minimal-light-contrast .theme-dark,.theme-light.minimal-eink-light.minimal-light-contrast .titlebar,.theme-light.minimal-eink-light.minimal-light-contrast .workspace-drawer.mod-left,.theme-light.minimal-eink-light.minimal-light-contrast .workspace-ribbon.mod-left:not(.is-collapsed),.theme-light.minimal-eink-light.minimal-light-contrast.minimal-status-off .status-bar{--base-h:0;--base-s:0%;--base-l:0%;--accent-h:0;--accent-s:0%;--accent-l:100%;--ax3:#fff;--bg1:#000;--bg2:#000;--bg3:#fff;--ui1:#fff;--ui2:#fff;--ui3:#fff;--tx1:#fff;--tx2:#fff;--tx3:#fff;--hl1:#fff;--hl2:#fff;--sp1:#000;--background-modifier-cover:rgba(20,20,20,1);--vault-profile-color:var(--tx1);--vault-profile-color-hover:var(--bg1);--nav-item-color-hover:var(--bg1);--nav-item-color-active:var(--bg1)}.theme-light.minimal-eink-light.minimal-light-tonal{--bg3:#bbb;--ui1:#bbb;--tx3:#999}.theme-dark.minimal-eink-dark.minimal-dark-tonal{--bg3:#444;--ui1:#444;--tx3:#999}.theme-dark.minimal-eink-dark.minimal-dark-tonal,.theme-light.minimal-eink-light.minimal-light-tonal{--hl2:var(--bg3);--modal-border-color:var(--ui1);--prompt-border-color:var(--ui1);--tag-border-color:var(--ui1);--text-selection:var(--bg3);--icon-color-active:var(--tx1);--icon-color-focused:var(--tx1);--nav-item-color-active:var(--tx1);--nav-item-color-hover:var(--tx1);--minimal-tab-text-color-active:var(--tx1)}.theme-dark.minimal-eink-dark.minimal-dark-tonal .is-flashing,.theme-dark.minimal-eink-dark.minimal-dark-tonal .search-result-file-matched-text,.theme-light.minimal-eink-light.minimal-light-tonal .is-flashing,.theme-light.minimal-eink-light.minimal-light-tonal .search-result-file-matched-text{background-color:var(--bg3);color:var(--tx1)}.theme-dark.minimal-eink-dark.minimal-dark-tonal #calendar-container .reset-button:hover,.theme-dark.minimal-eink-dark.minimal-dark-tonal ::selection,.theme-dark.minimal-eink-dark.minimal-dark-tonal:not(.colorful-active) .vertical-tab-nav-item.is-active,.theme-dark.minimal-eink-dark.minimal-dark-tonal:not(.colorful-active) .vertical-tab-nav-item:hover,.theme-light.minimal-eink-light.minimal-light-tonal #calendar-container .reset-button:hover,.theme-light.minimal-eink-light.minimal-light-tonal ::selection,.theme-light.minimal-eink-light.minimal-light-tonal:not(.colorful-active) .vertical-tab-nav-item.is-active,.theme-light.minimal-eink-light.minimal-light-tonal:not(.colorful-active) .vertical-tab-nav-item:hover{color:var(--tx1)}.theme-light.minimal-everforest-light{--color-red-rgb:248,85,82;--color-orange-rgb:245,125,38;--color-yellow-rgb:223,160,0;--color-green-rgb:141,161,1;--color-cyan-rgb:53,167,124;--color-blue-rgb:56,148,196;--color-purple-rgb:223,105,186;--color-pink-rgb:223,105,186;--color-red:#f85552;--color-orange:#f57d26;--color-yellow:#dfa000;--color-green:#8da101;--color-cyan:#35a77c;--color-blue:#3795C5;--color-purple:#df69ba;--color-pink:#df69ba}.theme-dark.minimal-everforest-dark{--color-red-rgb:230,126,128;--color-orange-rgb:230,152,117;--color-yellow-rgb:219,188,127;--color-green-rgb:167,192,128;--color-cyan-rgb:131,192,146;--color-blue-rgb:127,187,179;--color-purple-rgb:223,105,186;--color-pink-rgb:223,105,186;--color-red:#e67e80;--color-orange:#e69875;--color-yellow:#dbbc7f;--color-green:#a7c080;--color-cyan:#83c092;--color-blue:#7fbbb3;--color-purple:#d699b6;--color-pink:#d699b6}.theme-light.minimal-everforest-light{--base-h:44;--base-s:87%;--base-l:94%;--accent-h:83;--accent-s:36%;--accent-l:53%;--bg1:#fdf6e3;--bg2:#efebd4;--bg3:rgba(226,222,198,.5);--ui1:#e0dcc7;--ui2:#bec5b2;--ui3:#bec5b2;--tx1:#5C6A72;--tx2:#829181;--tx3:#a6b0a0;--hl1:rgba(198,214,152,.4);--hl2:rgba(222,179,51,.3)}.theme-light.minimal-everforest-light.minimal-light-tonal{--bg2:#fdf6e3}.theme-light.minimal-everforest-light.minimal-light-white{--bg3:#f3efda;--ui1:#edead5}.theme-dark.minimal-everforest-dark,.theme-light.minimal-everforest-light.minimal-light-contrast .mod-left-split,.theme-light.minimal-everforest-light.minimal-light-contrast .theme-dark,.theme-light.minimal-everforest-light.minimal-light-contrast .titlebar,.theme-light.minimal-everforest-light.minimal-light-contrast .workspace-drawer.mod-left,.theme-light.minimal-everforest-light.minimal-light-contrast .workspace-ribbon.mod-left:not(.is-collapsed),.theme-light.minimal-everforest-light.minimal-light-contrast.minimal-status-off .status-bar{--base-h:203;--base-s:15%;--base-l:23%;--accent-h:81;--accent-s:34%;--accent-l:63%;--bg1:#2d353b;--bg2:#232a2e;--bg3:rgba(71,82,88,0.5);--ui1:#475258;--ui2:#4f585e;--ui3:#525c62;--tx1:#d3c6aa;--tx2:#9da9a0;--tx3:#7a8478;--hl1:rgba(134,70,93,.5);--hl2:rgba(147,185,96,.3)}.theme-dark.minimal-everforest-dark.minimal-dark-black{--hl1:rgba(134,70,93,.4);--ui1:#2b3339}.theme-light.minimal-flexoki-light{--color-red-rgb:175,48,41;--color-orange-rgb:188,82,21;--color-yellow-rgb:173,131,1;--color-green-rgb:102,128,11;--color-cyan-rgb:36,131,123;--color-blue-rgb:32,94,166;--color-purple-rgb:94,64,157;--color-pink-rgb:160,47,111;--color-red:#AF3029;--color-orange:#BC5215;--color-yellow:#AD8301;--color-green:#66800B;--color-cyan:#24837B;--color-blue:#205EA6;--color-purple:#5E409D;--color-pink:#A02F6F}.theme-dark.minimal-flexoki-dark{--color-red-rgb:209,77,65;--color-orange-rgb:218,112,44;--color-yellow-rgb:208,162,21;--color-green-rgb:135,154,57;--color-cyan-rgb:58,169,159;--color-blue-rgb:67,133,190;--color-purple-rgb:139,126,200;--color-pink-rgb:206,93,151;--color-red:#D14D41;--color-orange:#DA702C;--color-yellow:#D0A215;--color-green:#879A39;--color-cyan:#3AA99F;--color-blue:#4385BE;--color-purple:#8B7EC8;--color-pink:#CE5D97}.theme-light.minimal-flexoki-light{--base-h:48;--base-s:100%;--base-l:97%;--accent-h:175;--accent-s:57%;--accent-l:33%;--bg1:#FFFCF0;--bg2:#F2F0E5;--bg3:rgba(16,15,15,0.05);--ui1:#E6E4D9;--ui2:#DAD8CE;--ui3:#CECDC3;--tx1:#100F0F;--tx2:#6F6E69;--tx3:#B7B5AC;--hl1:rgba(187,220,206,0.3);--hl2:rgba(247,209,61,0.3)}.theme-light.minimal-flexoki-light.minimal-light-tonal{--bg2:#FFFCF0}.theme-dark.minimal-flexoki-dark,.theme-light.minimal-flexoki-light.minimal-light-contrast .mod-left-split,.theme-light.minimal-flexoki-light.minimal-light-contrast .theme-dark,.theme-light.minimal-flexoki-light.minimal-light-contrast .titlebar,.theme-light.minimal-flexoki-light.minimal-light-contrast .workspace-drawer.mod-left,.theme-light.minimal-flexoki-light.minimal-light-contrast .workspace-ribbon.mod-left:not(.is-collapsed),.theme-light.minimal-flexoki-light.minimal-light-contrast.minimal-status-off .status-bar{--base-h:360;--base-s:3%;--base-l:6%;--accent-h:175;--accent-s:49%;--accent-l:45%;--bg1:#100F0F;--bg2:#1C1B1A;--bg3:rgba(254,252,240,0.05);--ui1:#282726;--ui2:#343331;--ui3:#403E3C;--tx1:#CECDC3;--tx2:#878580;--tx3:#575653;--hl1:rgba(30,95,91,0.3);--hl2:rgba(213,159,17,0.3)}.theme-dark.minimal-flexoki-dark.minimal-dark-black{--ui1:#1C1B1A}.theme-dark.minimal-gruvbox-dark,.theme-light.minimal-gruvbox-light{--color-red-rgb:204,36,29;--color-orange-rgb:214,93,14;--color-yellow-rgb:215,153,33;--color-green-rgb:152,151,26;--color-cyan-rgb:104,157,106;--color-blue-rgb:69,133,136;--color-purple-rgb:177,98,134;--color-pink-rgb:177,98,134;--color-red:#cc241d;--color-orange:#d65d0e;--color-yellow:#d79921;--color-green:#98971a;--color-cyan:#689d6a;--color-blue:#458588;--color-purple:#b16286;--color-pink:#b16286}.theme-light.minimal-gruvbox-light{--base-h:49;--base-s:92%;--base-l:89%;--accent-h:24;--accent-s:88%;--accent-l:45%;--bg1:#fcf2c7;--bg2:#f2e6bd;--bg3:#ebd9b3;--ui1:#ebdbb2;--ui2:#d5c4a1;--ui3:#bdae93;--tx1:#282828;--tx2:#7c7065;--tx3:#a89a85;--hl1:rgba(192,165,125,.3);--hl2:rgba(215,153,33,.4)}.theme-light.minimal-gruvbox-light.minimal-light-tonal{--bg2:#fcf2c7}.theme-light.minimal-gruvbox-light.minimal-light-white{--bg3:#faf5d7;--ui1:#f2e6bd}.theme-dark.minimal-gruvbox-dark,.theme-light.minimal-gruvbox-light.minimal-light-contrast .mod-left-split,.theme-light.minimal-gruvbox-light.minimal-light-contrast .theme-dark,.theme-light.minimal-gruvbox-light.minimal-light-contrast .titlebar,.theme-light.minimal-gruvbox-light.minimal-light-contrast .workspace-drawer.mod-left,.theme-light.minimal-gruvbox-light.minimal-light-contrast .workspace-ribbon.mod-left:not(.is-collapsed),.theme-light.minimal-gruvbox-light.minimal-light-contrast.minimal-status-off .status-bar{--accent-h:24;--accent-s:88%;--accent-l:45%;--bg1:#282828;--bg2:#1e2021;--bg3:#3d3836;--bg3:rgba(62,57,55,0.5);--ui1:#3c3836;--ui2:#504945;--ui3:#665c54;--tx1:#fbf1c7;--tx2:#bdae93;--tx3:#7c6f64;--hl1:rgba(173,149,139,0.3);--hl2:rgba(215,153,33,.4)}.theme-dark.minimal-gruvbox-dark.minimal-dark-black{--hl1:rgba(173,149,139,0.4);--ui1:#282828}.theme-dark.minimal-macos-dark,.theme-light.minimal-macos-light{--color-red-rgb:255,59,49;--color-orange-rgb:255,149,2;--color-yellow-rgb:255,204,0;--color-green-rgb:42,205,65;--color-cyan-rgb:2,199,190;--color-blue-rgb:2,122,255;--color-purple-rgb:176,81,222;--color-pink-rgb:255,46,85;--color-red:#ff3b31;--color-orange:#ff9502;--color-yellow:#ffcc00;--color-green:#2acd41;--color-cyan:#02c7be;--color-blue:#027aff;--color-purple:#b051de;--color-pink:#ff2e55}.theme-light.minimal-macos-light{--base-h:106;--base-s:0%;--base-l:94%;--accent-h:212;--accent-s:100%;--accent-l:50%;--bg1:#fff;--bg2:#f0f0f0;--bg3:rgba(0,0,0,.1);--ui1:#e7e7e7;--tx1:#454545;--tx2:#808080;--tx3:#b0b0b0;--hl1:#b3d7ff}.theme-light.minimal-macos-light.minimal-light-tonal{--bg1:#f0f0f0;--bg2:#f0f0f0}.theme-dark.minimal-macos-dark,.theme-light.minimal-macos-light.minimal-light-contrast .mod-left-split,.theme-light.minimal-macos-light.minimal-light-contrast .theme-dark,.theme-light.minimal-macos-light.minimal-light-contrast .titlebar,.theme-light.minimal-macos-light.minimal-light-contrast .workspace-drawer.mod-left,.theme-light.minimal-macos-light.minimal-light-contrast .workspace-ribbon.mod-left:not(.is-collapsed),.theme-light.minimal-macos-light.minimal-light-contrast.minimal-status-off .status-bar{--base-h:106;--base-s:0%;--base-l:12%;--accent-h:212;--accent-s:100%;--accent-l:50%;--bg1:#1e1e1e;--bg2:#282828;--bg3:rgba(255,255,255,0.11);--divider-color:#000;--tab-outline-color:#000;--ui1:#373737;--ui2:#515151;--ui3:#595959;--tx1:#dcdcdc;--tx2:#8c8c8c;--tx3:#686868;--hl1:rgba(98,169,252,0.5);--sp1:#fff}.theme-dark.minimal-macos-dark.minimal-dark-black{--divider-color:#1e1e1e;--tab-outline-color:#1e1e1e}.theme-dark.minimal-nord-dark,.theme-light.minimal-nord-light{--color-red-rgb:191,97,106;--color-orange-rgb:208,138,112;--color-yellow-rgb:235,203,139;--color-green-rgb:163,190,140;--color-cyan-rgb:136,192,208;--color-blue-rgb:129,161,193;--color-purple-rgb:180,142,173;--color-pink-rgb:180,142,173;--color-red:#BF616A;--color-orange:#D08770;--color-yellow:#EBCB8B;--color-green:#A3BE8C;--color-cyan:#88C0D0;--color-blue:#81A1C1;--color-purple:#B48EAD;--color-pink:#B48EAD}.theme-light.minimal-nord-light{--base-h:221;--base-s:27%;--base-l:94%;--accent-h:213;--accent-s:32%;--accent-l:52%;--bg1:#fff;--bg2:#eceff4;--bg3:rgba(157,174,206,0.25);--ui1:#d8dee9;--ui2:#BBCADC;--ui3:#81a1c1;--tx1:#2e3440;--tx2:#7D8697;--tx3:#ADB1B8;--hl2:rgba(208, 135, 112, 0.35)}.theme-dark.minimal-nord-dark,.theme-light.minimal-nord-light.minimal-light-contrast .mod-left-split,.theme-light.minimal-nord-light.minimal-light-contrast .theme-dark,.theme-light.minimal-nord-light.minimal-light-contrast .titlebar,.theme-light.minimal-nord-light.minimal-light-contrast .workspace-drawer.mod-left,.theme-light.minimal-nord-light.minimal-light-contrast .workspace-ribbon.mod-left:not(.is-collapsed),.theme-light.minimal-nord-light.minimal-light-contrast.minimal-status-off .status-bar{--base-h:220;--base-s:16%;--base-l:22%;--accent-h:213;--accent-s:32%;--accent-l:52%;--bg1:#2e3440;--bg2:#3b4252;--bg3:rgba(135,152,190,0.15);--ui1:#434c5e;--ui2:#58647b;--ui3:#58647b;--tx1:#d8dee9;--tx2:#9eafcc;--tx3:#4c566a;--hl1:rgba(129,142,180,0.3);--hl2:rgba(208, 135, 112, 0.35)}.theme-dark.minimal-nord-dark.minimal-dark-black{--ui1:#2e3440}.theme-light.minimal-notion-light{--base-h:39;--base-s:18%;--base-d:96%;--accent-h:197;--accent-s:71%;--accent-l:52%;--bg2:#f7f6f4;--bg3:#e8e7e4;--ui1:#ededec;--ui2:#dbdbda;--ui3:#aaa9a5;--tx1:#37352f;--tx2:#72706c;--tx3:#aaa9a5;--hl1:rgba(131,201,229,0.3)}.theme-dark.minimal-notion-dark,.theme-light.minimal-notion-light.minimal-light-contrast .mod-left-split,.theme-light.minimal-notion-light.minimal-light-contrast .theme-dark,.theme-light.minimal-notion-light.minimal-light-contrast .titlebar,.theme-light.minimal-notion-light.minimal-light-contrast .workspace-drawer.mod-left,.theme-light.minimal-notion-light.minimal-light-contrast .workspace-ribbon.mod-left:not(.is-collapsed),.theme-light.minimal-notion-light.minimal-light-contrast.minimal-status-off .status-bar{--base-h:203;--base-s:8%;--base-d:20%;--accent-h:197;--accent-s:71%;--accent-l:52%;--bg1:#2f3437;--bg2:#373c3f;--bg3:#4b5053;--ui1:#3e4245;--ui2:#585d5f;--ui3:#585d5f;--tx1:#ebebeb;--tx2:#909295;--tx3:#585d5f;--hl1:rgba(57,134,164,0.3)}.theme-dark.minimal-notion-dark.minimal-dark-black{--base-d:5%;--bg3:#232729;--ui1:#2f3437}.theme-light.minimal-rose-pine-light{--color-red-rgb:180,99,122;--color-orange-rgb:215,130,125;--color-yellow-rgb:234,157,53;--color-green-rgb:40,105,131;--color-cyan-rgb:87,147,159;--color-blue-rgb:87,147,159;--color-purple-rgb:144,122,169;--color-pink-rgb:144,122,169;--color-red:#b4637a;--color-orange:#d7827e;--color-yellow:#ea9d34;--color-green:#286983;--color-cyan:#56949f;--color-blue:#56949f;--color-purple:#907aa9;--color-pink:#907aa9}.theme-dark.minimal-rose-pine-dark{--color-red-rgb:234,111,146;--color-orange-rgb:233,155,151;--color-yellow-rgb:246,193,119;--color-green-rgb:47,116,143;--color-cyan-rgb:157,207,215;--color-blue-rgb:157,207,215;--color-purple-rgb:196,167,231;--color-pink-rgb:196,167,231;--color-red:#eb6f92;--color-orange:#ea9a97;--color-yellow:#f6c177;--color-green:#31748f;--color-cyan:#9ccfd8;--color-blue:#9ccfd8;--color-purple:#c4a7e7;--color-pink:#c4a7e7}.theme-light.minimal-rose-pine-light{--base-h:32;--base-s:57%;--base-l:95%;--accent-h:3;--accent-s:53%;--accent-l:67%;--bg1:#fffaf3;--bg2:#faf4ed;--bg3:rgba(233,223,218,0.5);--ui1:#EAE3E1;--ui2:#dfdad9;--ui3:#cecacd;--tx1:#575279;--tx2:#797593;--tx3:#9893a5;--hl1:rgba(191,180,181,0.35)}.theme-dark.minimal-rose-pine-dark,.theme-light.minimal-rose-pine-light.minimal-light-contrast .mod-left-split,.theme-light.minimal-rose-pine-light.minimal-light-contrast .theme-dark,.theme-light.minimal-rose-pine-light.minimal-light-contrast .titlebar,.theme-light.minimal-rose-pine-light.minimal-light-contrast .workspace-drawer.mod-left,.theme-light.minimal-rose-pine-light.minimal-light-contrast .workspace-ribbon.mod-left:not(.is-collapsed),.theme-light.minimal-rose-pine-light.minimal-light-contrast.minimal-status-off .status-bar{--base-h:247;--base-s:23%;--base-l:15%;--accent-h:2;--accent-s:55%;--accent-l:83%;--bg1:#1f1d2e;--bg2:#191724;--bg3:rgba(68,66,86,0.5);--ui1:#312F41;--ui2:#403d52;--ui3:#524f67;--tx1:#e0def4;--tx2:#908caa;--tx3:#6e6a86;--hl1:rgba(126,121,155,0.35)}.theme-dark.minimal-rose-pine-dark.minimal-dark-black{--ui1:#21202e}.theme-dark.minimal-solarized-dark,.theme-light.minimal-solarized-light{--color-red-rgb:220,50,47;--color-orange-rgb:203,77,22;--color-yellow-rgb:181,137,0;--color-green-rgb:133,153,0;--color-cyan-rgb:42,161,152;--color-blue-rgb:38,139,210;--color-purple-rgb:108,113,196;--color-pink-rgb:211,54,130;--color-red:#dc322f;--color-orange:#cb4b16;--color-yellow:#b58900;--color-green:#859900;--color-cyan:#2aa198;--color-blue:#268bd2;--color-purple:#6c71c4;--color-pink:#d33682}.theme-light.minimal-solarized-light{--base-h:44;--base-s:87%;--base-l:94%;--accent-h:205;--accent-s:70%;--accent-l:48%;--bg1:#fdf6e3;--bg2:#eee8d5;--bg3:rgba(0,0,0,0.062);--ui1:#e9e1c8;--ui2:#d0cab8;--ui3:#d0cab8;--tx1:#073642;--tx2:#586e75;--tx3:#ABB2AC;--tx4:#586e75;--hl1:rgba(202,197,182,0.3);--hl2:rgba(203,75,22,0.3)}.theme-light.minimal-solarized-light.minimal-light-tonal{--bg2:#fdf6e3}.theme-dark.minimal-solarized-dark,.theme-light.minimal-solarized-light.minimal-light-contrast .mod-left-split,.theme-light.minimal-solarized-light.minimal-light-contrast .theme-dark,.theme-light.minimal-solarized-light.minimal-light-contrast .titlebar,.theme-light.minimal-solarized-light.minimal-light-contrast .workspace-drawer.mod-left,.theme-light.minimal-solarized-light.minimal-light-contrast .workspace-ribbon.mod-left:not(.is-collapsed),.theme-light.minimal-solarized-light.minimal-light-contrast.minimal-status-off .status-bar{--accent-h:205;--accent-s:70%;--accent-l:48%;--base-h:193;--base-s:98%;--base-l:11%;--bg1:#002b36;--bg2:#073642;--bg3:rgba(255,255,255,0.062);--ui1:#19414B;--ui2:#274850;--ui3:#31535B;--tx1:#93a1a1;--tx2:#657b83;--tx3:#31535B;--tx4:#657b83;--hl1:rgba(15,81,98,0.3);--hl2:rgba(203, 75, 22, 0.35)}.theme-dark.minimal-solarized-dark.minimal-dark-black{--hl1:rgba(15,81,98,0.55);--ui1:#002b36}.theme-dark.minimal-things-dark,.theme-light.minimal-things-light{--color-red-rgb:255,48,108;--color-orange-rgb:255,149,2;--color-yellow-rgb:255,213,0;--color-green-rgb:75,191,94;--color-cyan-rgb:73,174,164;--color-purple-rgb:176,81,222;--color-pink-rgb:255,46,85;--color-red:#FF306C;--color-orange:#ff9502;--color-yellow:#FFD500;--color-green:#4BBF5E;--color-cyan:#49AEA4;--color-purple:#b051de;--color-pink:#ff2e55}.theme-light.minimal-things-light{--color-blue-rgb:27,97,194;--color-blue:#1b61c2}.theme-dark.minimal-things-dark{--color-blue-rgb:77,149,247;--color-blue:#4d95f7}.theme-light.minimal-things-light{--accent-h:215;--accent-s:76%;--accent-l:43%;--bg1:white;--bg2:#f5f6f8;--bg3:rgba(162,177,187,0.25);--ui1:#eef0f4;--ui2:#D8DADD;--ui3:#c1c3c6;--tx1:#26272b;--tx2:#7D7F84;--tx3:#a9abb0;--hl1:#cae2ff}.theme-light.minimal-things-light.minimal-light-tonal{--ui1:#e6e8ec}.theme-light.minimal-things-light.minimal-light-white{--bg3:#f5f6f8}.theme-dark.minimal-things-dark,.theme-light.minimal-things-light.minimal-light-contrast .mod-left-split,.theme-light.minimal-things-light.minimal-light-contrast .theme-dark,.theme-light.minimal-things-light.minimal-light-contrast .titlebar,.theme-light.minimal-things-light.minimal-light-contrast .workspace-drawer.mod-left,.theme-light.minimal-things-light.minimal-light-contrast .workspace-ribbon.mod-left:not(.is-collapsed),.theme-light.minimal-things-light.minimal-light-contrast.minimal-status-off .status-bar{--base-h:218;--base-s:9%;--base-l:15%;--accent-h:215;--accent-s:91%;--accent-l:64%;--bg1:#24262a;--bg2:#202225;--bg3:#3d3f41;--divider-color:#17191c;--tab-outline-color:#17191c;--ui1:#3A3B3F;--ui2:#45464a;--ui3:#6c6e70;--tx1:#fbfbfb;--tx2:#CBCCCD;--tx3:#6c6e70;--hl1:rgba(40,119,236,0.35);--sp1:#fff}.theme-dark.minimal-things-dark.minimal-dark-black{--base-d:5%;--bg3:#24262a;--divider-color:#24262a;--tab-outline-color:#24262a}
/* Plugin compatibility */

/* @plugins
core:
- backlink
- command-palette
- daily-notes
- file-explorer
- file-recovery
- global-search
- graph
- outgoing-link
- outline
- page-preview
- publish
- random-note
- starred
- switcher
- sync
- tag-pane
- word-count
community:
- buttons
- dataview
- calendar
- obsidian-charts
- obsidian-checklist-plugin
- obsidian-codemirror-options
- obsidian-dictionary-plugin
- obsidian-embedded-note-titles
- obsidian-excalidraw-plugin
- obsidian-git
- obsidian-hider
- obsidian-hover-editor
- obsidian-kanban
- obsidian-metatable
- obsidian-minimal-settings
- obsidian-outliner
- obsidian-system-dark-mode
- obsidian-style-settings
- quickadd
- sliding-panes-obsidian
- todoist-sync-plugin
*/
/* @settings

name: Minimal
id: minimal-style
settings:
	-
		id: instructions
		title: Documentation
		type: heading
		level: 2
		collapsed: true
		description: Use the Minimal Theme Settings plugin to set hotkeys, adjust features, select fonts, and choose from preset color schemes. Use the settings below for more granular customization. Go to https://minimal.guide for documentation.
	-
		id: interface
		title: Interface colors
		type: heading
		level: 2
		collapsed: true
	-
		id: base
		title: Base color
		description: Defines all background and border colors unless overridden in more granular settings
		type: variable-themed-color
		format: hsl-split
		default-light: '#'
		default-dark: '#'
	-
		id: bg1
		title: Primary background
		description: Background color for the main window
		type: variable-themed-color
		format: hex
		default-light: '#'
		default-dark: '#'
	-
		id: bg2
		title: Secondary background
		description: Background color for left sidebar and menus
		type: variable-themed-color
		format: hex
		default-light: '#'
		default-dark: '#'
	-
		id: bg3
		title: Active background
		description: Background color for hovered buttons and currently selected file
		type: variable-themed-color
		opacity: true
		format: hex
		default-light: '#'
		default-dark: '#'
	-
		id: ui1
		title: Border color
		type: variable-themed-color
		description: For buttons, divider lines, and outlined elements
		opacity: true
		format: hex
		default-light: '#'
		default-dark: '#'
	-
		id: ui2
		title: Highlighted border color
		description: Used when hovering over buttons, dividers, and outlined elements
		type: variable-themed-color
		opacity: true
		format: hex
		default-light: '#'
		default-dark: '#'
	-
		id: ui3
		title: Active border color
		description: Used when clicking buttons and outlined elements
		type: variable-themed-color
		opacity: true
		format: hex
		default-light: '#'
		default-dark: '#'
	-
		id: accent-color
		title: Accent color
		type: heading
		level: 2
		collapsed: true
	-
		id: ax1
		title: Accent color
		type: variable-themed-color
		description: Used primarily for links
		format: hex
		default-light: '#'
		default-dark: '#'
	-
		id: ax2
		title: Accent color (hover)
		type: variable-themed-color
		description: Used primarily for hovered links
		format: hex
		default-light: '#'
		default-dark: '#'
	-
		id: ax3
		title: Accent color interactive
		type: variable-themed-color
		description: Used for buttons, checklists, toggles
		format: hex
		default-light: '#'
		default-dark: '#'
	-
		id: sp1
		title: Text on accent
		type: variable-themed-color
		description: Used primarily for text on accented buttons
		format: hex
		default-light: '#'
		default-dark: '#'
	-
		id: extended-palette
		title: Extended colors
		type: heading
		level: 2
		collapsed: true
	-
		id: color-red
		title: Red
		description: Extended palette colors are defaults used for progress bar status, syntax highlighting, colorful headings, and graph nodes
		type: variable-themed-color
		opacity: true
		format: hex
		default-light: '#'
		default-dark: '#'
	-
		id: color-orange
		title: Orange
		type: variable-themed-color
		opacity: true
		format: hex
		default-light: '#'
		default-dark: '#'
	-
		id: color-yellow
		title: Yellow
		type: variable-themed-color
		opacity: true
		format: hex
		default-light: '#'
		default-dark: '#'
	-
		id: color-green
		title: Green
		type: variable-themed-color
		opacity: true
		format: hex
		default-light: '#'
		default-dark: '#'
	-
		id: color-cyan
		title: Cyan
		type: variable-themed-color
		opacity: true
		format: hex
		default-light: '#'
		default-dark: '#'
	-
		id: color-blue
		title: Blue
		type: variable-themed-color
		opacity: true
		format: hex
		default-light: '#'
		default-dark: '#'
	-
		id: color-purple
		title: Purple
		type: variable-themed-color
		opacity: true
		format: hex
		default-light: '#'
		default-dark: '#'
	-
		id: color-pink
		title: Pink
		type: variable-themed-color
		opacity: true
		format: hex
		default-light: '#'
		default-dark: '#'
	-
		id: blockquotes
		title: Blockquotes
		type: heading
		level: 2
		collapsed: true
	-
		id: blockquote-color
		title: Blockquote text color
		type: variable-themed-color
		format: hex
		default-light: '#'
		default-dark: '#'
	-
		id: blockquote-background-color
		title: Blockquote background color
		type: variable-themed-color
		format: hex
		default-light: '#'
		default-dark: '#'
	-
		id: blockquote-border-color
		title: Blockquote border color
		type: variable-themed-color
		format: hex
		default-light: '#'
		default-dark: '#'
	-
		id: blockquote-border-thickness
		title: Blockquote border thickness
		type: variable-number-slider
		format: px
		default: 1
		min: 0
		max: 5
		step: 1
	-
		id: blockquote-size
		title: Blockquote font size
		description: Accepts any CSS font-size value
		type: variable-text
		default: ''
	-
		id: blockquote-font-style
		title: Blockquote font style
		type: variable-select
		allowEmpty: false
		default: normal
		options:
			-
				label: Normal
				value: normal
			-
				label: Italic
				value: italic
	-
		id: callouts
		title: Callouts
		type: heading
		level: 2
		collapsed: true
	-
		id: callouts-style
		title: Callout style
		type: class-select
		allowEmpty: false
		default: callouts-default
		options:
			-
				label: Filled
				value: callouts-default
			-
				label: Outlined
				value: callouts-outlined
	-
		id: callout-blend-mode
		title: Color blending
		description: Blend the color of nested callouts
		type: variable-select
		allowEmpty: false
		default: var(--highlight-mix-blend-mode)
		options:
			-
				label: On
				value: var(--highlight-mix-blend-mode)
			-
				label: Off
				value: normal
	-
		id: canvas
		title: Canvas
		type: heading
		level: 2
		collapsed: true
	-
		id: canvas-dot-pattern
		title: Canvas dot pattern
		description: Color for background dot pattern
		type: variable-themed-color
		format: hex
		default-light: '#'
		default-dark: '#'
	-
		id: code-blocks
		title: Code blocks
		type: heading
		level: 2
		collapsed: true
	-
		id: code-size
		title: Code font size
		description: Accepts any CSS font-size value
		type: variable-text
		default: 0.8em
	-
		id: minimal-code-scroll
		title: Scroll long lines
		description: Turns off line wrap for code
		type: class-toggle
		default: false
	-
		id: code-background
		title: Code background color
		description: Background for code blocks
		type: variable-themed-color
		format: hex
		default-light: '#'
		default-dark: '#'
	-
		id: code-normal
		title: Code text color
		description: Color of code when syntax highlighting is not present
		type: variable-themed-color
		format: hex
		default-light: '#'
		default-dark: '#'
	-
		id: syntax-highlighting
		title: Syntax highlighting
		type: heading
		level: 3
		collapsed: false
	-
		id: code-comment
		title: "Syntax: comments"
		description: Syntax highlighting for comments
		type: variable-themed-color
		format: hex
		default-light: '#'
		default-dark: '#'
	-
		id: code-function
		title: "Syntax: functions"
		description: Syntax highlighting for functions
		type: variable-themed-color
		format: hex
		default-light: '#'
		default-dark: '#'
	-
		id: code-keyword
		title: "Syntax: keywords"
		description: Syntax highlighting for keywords
		type: variable-themed-color
		format: hex
		default-light: '#'
		default-dark: '#'
	-
		id: code-important
		title: "Syntax: important"
		description: Syntax highlighting for important text
		type: variable-themed-color
		format: hex
		default-light: '#'
		default-dark: '#'
	-
		id: code-operator
		title: "Syntax: operators"
		description: Syntax highlighting for operators
		type: variable-themed-color
		format: hex
		default-light: '#'
		default-dark: '#'
	-
		id: code-property
		title: "Syntax: properties"
		description: Syntax highlighting for properties
		type: variable-themed-color
		format: hex
		default-light: '#'
		default-dark: '#'
	-
		id: code-punctuation
		title: "Syntax: punctuation"
		description: Syntax highlighting for punctuation
		type: variable-themed-color
		format: hex
		default-light: '#'
		default-dark: '#'
	-
		id: code-string
		title: "Syntax: strings"
		description: Syntax highlighting for strings
		type: variable-themed-color
		format: hex
		default-light: '#'
		default-dark: '#'
	-
		id: code-tag
		title: "Syntax: tags"
		description: Syntax highlighting for tags
		type: variable-themed-color
		format: hex
		default-light: '#'
		default-dark: '#'
	-
		id: code-value
		title: "Syntax: values"
		description: Syntax highlighting for values
		type: variable-themed-color
		format: hex
		default-light: '#'
		default-dark: '#'
	-
		id: dataview
		title: Dataview
		type: heading
		level: 2
		collapsed: true
	-
		id: trim-cols
		title: Trim Dataview columns
		description: Disables word wrapping in table cells, and trims long text
		type: class-toggle
		default: true
	-
		id: dataview-inline-lists
		title: Force tables lists inline
		description: Makes lists inside of table cells inline and comma separated
		type: class-toggle
		default: false
	-
		id: max-col-width
		title: Dataview maximum column width
		description: Maximum width for Dataview columns, accepts any CSS width value
		type: variable-text
		default: 18em
	-
		id: embed-blocks
		title: Embeds and transclusions
		type: heading
		level: 2
		collapsed: true
	-
		id: embed-strict
		title: Use strict embed style globally
		description: Transclusions appear seamlessly in the flow of text. Can be enabled per file using the embed-strict helper class
		type: class-toggle
		default: false
	-
		id: embed-hide-title
		title: Hide embed titles
		description: Hide title of the transcluded file (if strict embed is off)
		type: class-toggle
		default: false
	-
		id: embed-underline
		title: Underline embedded text
		description: Transcluded text is underlined. Can be enabled per file using the embed-underline helper class
		type: class-toggle
		default: false
	-
		id: embed-max-height
		title: Maximum height of embeds
		type: variable-text
		description: For transcluded text, accepts valid CSS units
		default: ''
	-
		id: embed-decoration-style
		title: Embedded text underline style
		type: variable-select
		description: Requires underlines to be enabled
		allowEmpty: true
		default: solid
		options:
			-
				label: Solid
				value: solid
			-
				label: Dashed
				value: dashed
			-
				label: Dotted
				value: dotted
			-
				label: Double
				value: double
			-
				label: Wavy
				value: wavy
	-
		id: embed-decoration-color
		title: Embedded text underline color
		type: variable-themed-color
		description: Requires underlines to be enabled
		format: hex
		default-light: '#'
		default-dark: '#'
	-
		id: embed-background
		title: Embed background
		type: variable-themed-color
		opacity: true
		format: hex
		default-light: '#'
		default-dark: '#'
	-
		id: graphs
		title: Graphs
		type: heading
		level: 2
		collapsed: true
	-
		id: graph-line
		title: Line color
		description: Changing graph colors requires closing and reopening graph panes or restarting Obsidian
		type: variable-themed-color
		format: hex
		default-light: '#'
		default-dark: '#'
	-
		id: graph-node
		title: Node color
		type: variable-themed-color
		format: hex
		default-light: '#'
		default-dark: '#'
	-
		id: graph-node-focused
		title: Active node color
		type: variable-themed-color
		format: hex
		default-light: '#'
		default-dark: '#'
	-  
		id: graph-node-tag
		title: Tag node color
		type: variable-themed-color
		format: hex
		default-light: '#'
		default-dark: '#'
	-  
		id: graph-node-attachment
		title: Attachment node color
		type: variable-themed-color
		format: hex
		default-light: '#'
		default-dark: '#'
	-  
		id: graph-node-unresolved
		title: Unresolved node color
		type: variable-themed-color
		format: hex
		default-light: '#'
		default-dark: '#'
	-
		id: headings
		title: Headings
		type: heading
		level: 2
		collapsed: true
	-
		id: level-1-headings
		title: Level 1 Headings
		type: heading
		level: 3
		collapsed: true
	-
		id: h1-font
		title: H1 font
		description: Name of the font as it appears on your system
		type: variable-text
		default: ''
	-
		id: h1-size
		title: H1 font size
		description: Accepts any CSS font-size value
		type: variable-text
		default: 1.125em
	-
		id: h1-weight
		title: H1 font weight
		type: variable-number-slider
		default: 600
		min: 100
		max: 900
		step: 100
	-
		id: h1-color
		title: H1 text color
		type: variable-themed-color
		format: hex
		default-light: '#'
		default-dark: '#'
	-
		id: h1-variant
		title: H1 font variant
		type: variable-select
		allowEmpty: false
		default: normal
		options:
			-
				label: Normal
				value: normal
			-
				label: Small caps
				value: small-caps
			-
				label: All small caps
				value: all-small-caps 
	-
		id: h1-style
		title: H1 font style
		type: variable-select
		allowEmpty: false
		default: normal
		options:
			-
				label: Normal
				value: normal
			-
				label: Italic
				value: italic
	-
		id: h1-l
		title: H1 divider line
		description: Adds a border below the heading
		type: class-toggle
		default: false
	-
		id: level-2-headings
		title: Level 2 Headings
		type: heading
		level: 3
		collapsed: true
	-
		id: h2-font
		title: H2 font
		description: Name of the font as it appears on your system
		type: variable-text
		default: ''
	-
		id: h2-size
		title: H2 font size
		description: Accepts any CSS font-size value
		type: variable-text
		default: 1em
	-
		id: h2-weight
		title: H2 font weight
		type: variable-number-slider
		default: 600
		min: 100
		max: 900
		step: 100
	-
		id: h2-color
		title: H2 text color
		type: variable-themed-color
		format: hex
		default-light: '#'
		default-dark: '#'
	-
		id: h2-variant
		title: H2 font variant
		type: variable-select
		allowEmpty: false
		default: normal
		options:
			-
				label: Normal
				value: normal
			-
				label: Small caps
				value: small-caps
			-
				label: All small caps
				value: all-small-caps
	-
		id: h2-style
		title: H2 font style
		type: variable-select
		allowEmpty: false
		default: normal
		options:
			-
				label: Normal
				value: normal
			-
				label: Italic
				value: italic
	-
		id: h2-l
		title: H2 divider line
		description: Adds a border below the heading
		type: class-toggle
		default: false
	-
		id: level-3-headings
		title: Level 3 Headings
		type: heading
		level: 3
		collapsed: true
	-
		id: h3-font
		title: H3 font
		description: Name of the font as it appears on your system
		type: variable-text
		default: ''
	-
		id: h3-size
		title: H3 font size
		description: Accepts any CSS font-size value
		type: variable-text
		default: 1em
	-
		id: h3-weight
		title: H3 font weight
		type: variable-number-slider
		default: 600
		min: 100
		max: 900
		step: 100
	-
		id: h3-color
		title: H3 text color
		type: variable-themed-color
		format: hex
		default-light: '#'
		default-dark: '#'
	-
		id: h3-variant
		title: H3 font variant
		type: variable-select
		allowEmpty: false
		default: normal
		options:
			-
				label: Normal
				value: normal
			-
				label: Small caps
				value: small-caps
			-
				label: All small caps
				value: all-small-caps
	-
		id: h3-style
		title: H3 font style
		type: variable-select
		allowEmpty: false
		default: normal
		options:
			-
				label: Normal
				value: normal
			-
				label: Italic
				value: italic
	-
		id: h3-l
		title: H3 divider line
		description: Adds a border below the heading
		type: class-toggle
		default: false
	-
		id: level-4-headings
		title: Level 4 Headings
		type: heading
		level: 3
		collapsed: true
	-
		id: h4-font
		title: H4 font
		description: Name of the font as it appears on your system
		type: variable-text
		default: ''
	-
		id: h4-size
		title: H4 font size
		description: Accepts any CSS font-size value
		type: variable-text
		default: 0.9em
	-
		id: h4-weight
		title: H4 font weight
		type: variable-number-slider
		default: 500
		min: 100
		max: 900
		step: 100
	-
		id: h4-color
		title: H4 text color
		type: variable-themed-color
		format: hex
		default-light: '#'
		default-dark: '#'
	-
		id: h4-variant
		title: H4 font variant
		type: variable-select
		allowEmpty: false
		default: small-caps
		options:
			-
				label: Normal
				value: normal
			-
				label: Small caps
				value: small-caps
			-
				label: All small caps
				value: all-small-caps 
	-
		id: h4-style
		title: H4 font style
		type: variable-select
		allowEmpty: false
		default: normal
		options:
			-
				label: Normal
				value: normal
			-
				label: Italic
				value: italic
	-
		id: h4-l
		title: H4 divider line
		description: Adds a border below the heading
		type: class-toggle
		default: false   
	-
		id: level-5-headings
		title: Level 5 Headings
		type: heading
		level: 3
		collapsed: true
	-
		id: h5-font
		title: H5 font
		description: Name of the font as it appears on your system
		type: variable-text
		default: ''
	- 
		id: h5-size
		title: H5 font size
		description: Accepts any CSS font-size value
		type: variable-text
		default: 0.85em
	-
		id: h5-weight
		title: H5 font weight
		type: variable-number-slider
		default: 500
		min: 100
		max: 900
		step: 100
	-
		id: h5-color
		title: H5 text color
		type: variable-themed-color
		format: hex
		default-light: '#'
		default-dark: '#'
	-
		id: h5-variant
		title: H5 font variant
		type: variable-select
		allowEmpty: false
		default: small-caps
		options:
			-
				label: Normal
				value: normal
			-
				label: Small caps
				value: small-caps
			-
				label: All small caps
				value: all-small-caps 
	-
		id: h5-style
		title: H5 font style
		type: variable-select
		allowEmpty: false
		default: normal
		options:
			-
				label: Normal
				value: normal
			-
				label: Italic
				value: italic
	-
		id: h5-l
		title: H5 divider line
		description: Adds a border below the heading
		type: class-toggle
		default: false
	-
		id: level-6-headings
		title: Level 6 Headings
		type: heading
		level: 3
		collapsed: true
	-
		id: h6-font
		title: H6 font
		description: Name of the font as it appears on your system
		type: variable-text
		default: ''
	-
		id: h6-size
		title: H6 font size
		description: Accepts any CSS font-size value
		type: variable-text
		default: 0.85em
	-
		id: h6-weight
		title: H6 font weight
		type: variable-number-slider
		default: 400
		min: 100
		max: 900
		step: 100
	-
		id: h6-color
		title: H6 text color
		type: variable-themed-color
		format: hex
		default-light: '#'
		default-dark: '#'
	-
		id: h6-variant
		title: H6 font variant
		type: variable-select
		allowEmpty: false
		default: small-caps
		options:
			-
				label: Normal
				value: normal
			-
				label: Small caps
				value: small-caps
			-
				label: All small caps
				value: all-small-caps
	-
		id: h6-style
		title: H6 font style
		type: variable-select
		allowEmpty: false
		default: normal
		options:
			-
				label: Normal
				value: normal
			-
				label: Italic
				value: italic
	-
		id: h6-l
		title: H6 divider line
		type: class-toggle
		description: Adds a border below the heading
		default: false
	-
		id: icons
		title: Icons
		type: heading
		level: 2
		collapsed: true
	-
		id: icon-muted
		title: Icon opacity (inactive)
		type: variable-number-slider
		default: 0.5
		min: 0.25
		max: 1
		step: 0.05
	-
		id: icon-color
		title: Icon color
		type: variable-themed-color
		opacity: true
		format: hex
		default-light: '#'
		default-dark: '#'
	-
		id: icon-color-hover
		title: Icon color (hover)
		type: variable-themed-color
		opacity: true
		format: hex
		default-light: '#'
		default-dark: '#'
	-
		id: icon-color-active
		title: Icon color (active)
		type: variable-themed-color
		opacity: true
		format: hex
		default-light: '#'
		default-dark: '#'
	-
		id: icon-color-focused
		title: Icon color (focused)
		type: variable-themed-color
		opacity: true
		format: hex
		default-light: '#'
		default-dark: '#'
	-
		id: images
		title: Images
		type: heading
		level: 2
		collapsed: true
	-
		id: image-muted
		title: Image opacity in dark mode
		description: Level of fading for images in dark mode. Hover over images to display at full brightness.
		type: variable-number-slider
		default: 0.7
		min: 0.25
		max: 1
		step: 0.05
	-
		id: image-radius
		title: Image radius
		description: Rounded corners for images
		type: variable-number-slider
		default: 4
		min: 0
		max: 16
		step: 1
		format: px
	-
		id: image-blend-light
		title: Blend images in light mode
		description: Allow images to blend into the color scheme background color
		type: class-toggle
		default: false
	-
		id: zoom-off
		title: Disable image zoom
		description: Turns off click + hold to zoom images
		type: class-toggle
	-
		id: image-grid-fit
		title: Image grid crop
		description: Sets how images get cropped in a grid
		type: variable-select
		default: cover
		options:
			-
				label: Crop to fit
				value: cover
			-
				label: Show full image
				value: contain
	-
		id: image-grid-background
		title: Image grid background
		description: Background of images in cells, useful when images are not cropped to fit
		type: variable-themed-color
		opacity: true
		format: hex
		default-light: '#'
		default-dark: '#'
	-
		id: indentation-guides
		title: Indentation guides
		type: heading
		level: 2
		collapsed: true
	-
		id: indentation-guide-color
		title: Indentation guide color
		type: variable-themed-color
		format: hex
		opacity:  true
		default-light: '#'
		default-dark: '#'
	-
		id: indentation-guide-color-active
		title: Indentation guide color (active)
		type: variable-themed-color
		format: hex
		opacity:  true
		default-light: '#'
		default-dark: '#'
	-
		id: links
		title: Links
		type: heading
		level: 2
		collapsed: true
	-
		id: links-internal
		title: Internal links
		type: heading
		level: 3
		collapsed: true
	-
		id: link-color
		title: Internal link color
		type: variable-themed-color
		format: hex
		default-light: '#'
		default-dark: '#'
	-
		id: link-color-hover
		title: Internal link color (hover)
		type: variable-themed-color
		format: hex
		default-light: '#'
		default-dark: '#'
	-
		id: link-unresolved-opacity
		title: Unresolved link opacity
		type: variable-number-slider
		default: 0.7
		min: 0.25
		max: 1
		step: 0.05
	-
		id: link-unresolved-color
		title: Unresolved link color
		type: variable-themed-color
		format: hex
		default-light: '#'
		default-dark: '#'
	-
		id: link-unresolved-decoration-color
		title: Unresolved link underline color
		type: variable-themed-color
		format: hex
		opacity:  true
		default-light: '#'
		default-dark: '#'
	-
		id: links-external
		title: External links
		type: heading
		level: 3
		collapsed: true
	-
		id: link-external-color
		title: External link color
		type: variable-themed-color
		format: hex
		default-light: '#'
		default-dark: '#'
	-
		id: link-external-color-hover
		title: External link color (hover)
		type: variable-themed-color
		format: hex
		default-light: '#'
		default-dark: '#'
	-
		id: active-line
		title: Line  numbers
		type: heading
		level: 2
		collapsed: true
	-
		id: active-line-on
		title: Highlight active line
		description: Adds a background to current line in editor
		type: class-toggle
		default: false
	-
		id: folding-offset
		title: Gutter offset
		description: Width of the file margin used for gutter
		type: variable-number-slider
		default: 32
		min: 0
		max: 60
		step: 1
		format: px
	-  
		id: gutter-background
		title: Gutter background
		type: variable-themed-color
		format: hex
		opacity: true
		default-light: '#'
		default-dark: '#'
	-  
		id: line-number-color
		title: Line number color
		type: variable-themed-color
		format: hex
		default-light: '#'
		default-dark: '#'
	-  
		id: line-number-color-active
		title: Active line number color
		type: variable-themed-color
		format: hex
		default-light: '#'
		default-dark: '#'
	-
		id: active-line-bg
		title: Active line background
		description: Using a low opacity color is recommended to avoid conflicting with highlights
		type: variable-themed-color
		opacity: true
		format: hex
		default-light: '#'
		default-dark: '#'
	-
		id: lists
		title: Lists and tasks
		type: heading
		level: 2
		collapsed: true
	-
		id: checkbox-color
		title: Checkbox color
		description: Background color for completed tasks
		type: variable-themed-color
		format: hex
		default-light: '#'
		default-dark: '#'
	-
		id: checkbox-shape
		title: Checkbox shape
		type: class-select
		allowEmpty: false
		default: checkbox-circle
		options:
			-
				label: Circle
				value: checkbox-circle
			-
				label: Square
				value: checkbox-square
	-
		id: minimal-strike-lists
		title: Strike completed tasks
		description: Adds strikethrough line and greyed text for completed tasks
		type: class-toggle
		default: false
	-
		id: list-spacing
		title: List item spacing
		description: Vertical space between list items in em units
		type: variable-number-slider
		default: 0.075
		min: 0
		max: 0.3
		step: 0.005
		format: em
	-
		id: list-indent
		title: Nested list indentation
		description: Horizontal space from left in em units
		type: variable-number-slider
		default: 2
		min: 1
		max: 3.5
		step: 0.1
		format: em
	-
		id: pdf
		title: PDFs
		type: heading
		level: 2
		collapsed: true
	-
		id: pdf-page-style
		title: PDF page style
		description: Borders and shadows around pages
		type: class-select
		allowEmpty: false
		default: pdf-seamless-on
		options:
			-
				label: Seamless
				value: pdf-seamless-on
			-
				label: Shadows
				value: pdf-shadows-on
	-
		id: pdf-invert-dark
		title: Invert PDFs in dark mode
		description: Best for working with black text on white pages
		type: class-toggle
		default: true
	-
		id: pdf-blend-light
		title: Blend PDFs in light mode
		description: Allow PDFs to blend into the color scheme background color
		type: class-toggle
		default: true
	-
		id: pdf-dark-opacity
		title: PDF opacity in dark mode
		description: Fade PDF pages in dark mode
		type: variable-number-slider
		default: 1
		min: 0.25
		max: 1
		step: 0.05
	-
		id: progress
		title: Progress bars
		type: heading
		level: 2
		collapsed: true
	-
		id: progress-complete
		title: Completed progress bar color
		description: Defaults to your accent color
		type: variable-themed-color
		format: hex
		default-light: '#'
		default-dark: '#'
	-
		id: properties
		title: Properties
		type: heading
		level: 2
		collapsed: true
	-
		id: metadata-heading-off
		title: Hide properties heading
		description: Hide "Properties" heading above properties
		type: class-toggle
		default: false
	-
		id: metadata-add-property-off
		title: Hide "Add property" button
		description: Hide "Add property" button below properties
		type: class-toggle
		default: false
	-
		id: metadata-icons-off
		title: Hide property icons
		description: Hide icons next to property names
		type: class-toggle
		default: false
	-
		id: metadata-dividers
		title: Property row lines
		description: Display borders between properties
		type: class-toggle
		default: false
	-
		id: metadata-label-width
		title: Property name width
		description: Width for the name of the property
		type: variable-number-slider
		format: rem
		default: 8
		min: 4
		max: 12
		step: 0.25
	-
		id: sidebars
		title: Sidebars
		type: heading
		level: 2
		collapsed: true
	-
		id: sidebar-tabs-style
		title: Sidebar tab style
		type: class-select
		allowEmpty: false
		default: sidebar-tabs-default
		options:
			-
				label: Index round
				value: sidebar-tabs-index
			-
				label: Index square
				value: sidebar-tabs-square
			-
				label: Modern compact
				value: sidebar-tabs-default
			-
				label: Modern wide
				value: sidebar-tabs-wide
			-
				label: Square
				value: sidebar-tabs-plain-square
			-
				label: Underline
				value: sidebar-tabs-underline
	-
		id: sidebar-tabs-names
		title: Sidebar tab names
		type: class-select
		allowEmpty: false
		default: tab-names-off
		options:
			-
				label: Hidden
				value: tab-names-off
			-
				label: Visible
				value: tab-names-on
			-
				label: Single tab only
				value: tab-names-single
	-
		id: vault-profile-display
		title: Vault profile position
		type: class-select
		allowEmpty: false
		default: vault-profile-default
		options:
			-
				label: Bottom
				value: vault-profile-default
			-
				label: Top
				value: vault-profile-top
	-
		id: hide-help
		title: Hide help button
		description: 
		type: class-toggle
		default: false
	-
		id: hide-settings
		title: Hide settings button
		description: 
		type: class-toggle
		default: false
	-
		id: mobile-left-sidebar-width
		title: Mobile left sidebar width
		description: Maximum width for pinned left sidebar on mobile
		type: variable-number
		default: 280
		format: pt
	-
		id: mobile-right-sidebar-width
		title: Mobile right sidebar width
		description: Maximum width for pinned right sidebar on mobile
		type: variable-number
		default: 240
		format: pt
	-
		id: ribbon
		title: Ribbon
		type: heading
		level: 2
		collapsed: true
	-
		id: ribbon-style
		title: Ribbon style
		description: Display options for ribbon menu. Ribbon must be turned on in Obsidian Appearance settings for these options to work.
		type: class-select
		allowEmpty: false
		default: ribbon-hidden
		options:
			-
				label: Default
				value: ribbon-hidden
			-
				label: Expand (vertical)
				value: ribbon-vertical-expand
			-
				label: Hover (vertical)
				value: ribbon-bottom-left-hover-vertical
			-
				label: Hover (horizontal)
				value: ribbon-bottom-left-hover	
	-
		id: tables
		title: Tables
		type: heading
		level: 2
		collapsed: true
	-
		id: table-text-size
		title: Table font size
		description: All of the following settings apply to all tables globally. To turn on these features on a per-note basis use helper classes. See documentation.
		type: variable-text
		default: 1em
	-
		id: table-column-min-width
		title: Minimum column width
		type: variable-text
		default: 6ch
	-
		id: table-column-max-width
		title: Maximum column width
		type: variable-text
		default: none
	-
		id: maximize-tables-off
		title: Maximize table width 
		description: Determines how wide tables should behave when they become wider than the line width
		type: class-select
		allowEmpty: false
		default: maximize-tables-off
		options:
			-
				label: Fit to line width
				value: maximize-tables-off
			-
				label: Overflow line width
				value: maximize-tables-auto
			-
				label: Always fill
				value: maximize-tables
	-
		id: row-lines
		title: Row lines
		description: Display borders between table rows globally
		type: class-toggle
		default: false
	-
		id: col-lines
		title: Column lines
		description: Display borders between table columns globally
		type: class-toggle
		default: false
	-
		id: table-lines
		title: Cell lines
		description: Display borders around all table cells globally
		type: class-toggle
		default: false 
	-
		id: row-alt
		title: Striped rows
		description: Display striped background in alternating table rows globally
		type: class-toggle
		default: false
	-
		id: col-alt
		title: Striped columns
		description: Display striped background in alternating table columns globally
		type: class-toggle
		default: false
	-
		id: table-tabular
		title: Tabular figures
		description: Use fixed width numbers in tables globally
		type: class-toggle
		default: false
	-
		id: table-numbers
		title: Row numbers
		description: Display row numbers in tables globally
		type: class-toggle
		default: false
	-
		id: table-center
		title: Center small tables
		description: Make small tables centered
		type: class-toggle
		default: false
	-
		id: table-nowrap
		title: Disable line wrap
		description: Turn off line wrapping in table cells globally
		type: class-toggle
		default: false
	-
		id: row-hover
		title: Highlight active row
		description: Highlight rows on hover
		type: class-toggle
		default: false
	-
		id: table-row-background-hover
		title: Active row background
		description: Background color for hovered tables rows
		type: variable-themed-color
		format: hex
		opacity: true
		default-light: '#'
		default-dark: '#'
	-
		id: tabs
		title: Tabs
		type: heading
		level: 2
		collapsed: true
	-
		id: header-height
		title: Tab bar height
		type: variable-text
		default: 40px
	-
		id: tabs-style
		title: Tab style
		type: class-select
		allowEmpty: false
		default: tabs-default
		options:
			-
				label: Index round
				value: tabs-default
			-
				label: Index square
				value: tabs-square
			-
				label: Modern
				value: tabs-modern
			-
				label: Square
				value: tabs-plain-square
			-
				label: Underline
				value: tabs-underline
	-
		id: minimal-tab-text-color
		title: Tab text color
		type: variable-themed-color
		format: hex
		opacity: true
		default-light: '#'
		default-dark: '#'
	-
		id: minimal-tab-text-color-active
		title: Tab text color (active)
		type: variable-themed-color
		format: hex
		opacity: true
		default-light: '#'
		default-dark: '#'
	-
		id: tab-stacks
		title: Tab stacks
		type: heading
		level: 2
		collapsed: true
	-
		id: tab-stacked-pane-width
		title: Stacked width
		type: variable-number
		description: Width of a stacked tab in pixels
		default: 700
		format: px
	-
		id: tab-stacked-header-width
		title: Spine width
		type: variable-number
		description: Width of the spine in pixels
		default: 40
		format: px
	-
		id: tab-stacked-spine-orientation
		title: Spine text orientation
		type: class-select
		default: tab-stack-top
		options:
			-
				label: Top
				value: tab-stack-top
			-
				label: Top flipped
				value: tab-stack-top-flipped
			-
				label: Bottom
				value: tab-stack-bottom
			-
				label: Bottom flipped
				value: tab-stack-bottom-flipped
			-
				label: Center
				value: tab-stack-center
			-
				label: Center flipped
				value: tab-stack-center-flipped
	-
		id: tags
		title: Tags
		type: heading
		level: 2
		collapsed: true
	-
		id: minimal-unstyled-tags
		title: Plain tags
		description: Tags will render as normal text, overrides settings below
		type: class-toggle
		default: false
	-
		id: tag-radius
		title: Tag shape
		type: variable-select
		default: 14px
		options:
			-
				label: Pill
				value: 14px
			-
				label: Rounded
				value: 4px
			-
				label: Square
				value: 0px
	-
		id: tag-border-width
		title: Tag border width
		type: variable-select
		default: 1px
		options:
			-
				label: None
				value: 0
			-
				label: Thin
				value: 1px
			-
				label: Thick
				value: 2px
	-
		id: tag-color
		title: Tag text color
		type: variable-themed-color
		opacity: true
		format: hex
		default-light: '#'
		default-dark: '#'
	-
		id: tag-background
		title: Tag background color
		type: variable-themed-color
		opacity: true
		format: hex
		default-light: '#'
		default-dark: '#'
	-
		id: tag-background-hover
		title: Tag background color (hover)
		type: variable-themed-color
		opacity: true
		format: hex
		default-light: '#'
		default-dark: '#'
	-
		id: text
		title: Text
		type: heading
		level: 2
		collapsed: true
	-
		id: tx1
		title: Normal text color
		type: variable-themed-color
		description: Primary text color used by default across all elements
		opacity: true
		format: hex
		default-light: '#'
		default-dark: '#'
	-
		id: hl1
		title: Selected text background
		type: variable-themed-color
		opacity: true
		format: hex
		default-light: '#'
		default-dark: '#'
	-
		id: hl2
		title: Highlighted text background
		type: variable-themed-color
		opacity: true
		format: hex
		default-light: '#'
		default-dark: '#'
	-
		id: tx2
		title: Muted text color
		description: Secondary text such as sidebar note titles and table headings
		type: variable-themed-color
		opacity: true
		format: hex
		default-light: '#'
		default-dark: '#'
	-
		id: tx3
		title: Faint text color
		description: tertiary text such as input placeholders, empty checkboxes, and disabled statuses
		type: variable-themed-color
		opacity: true
		format: hex
		default-light: '#'
		default-dark: '#'
	-
		id: text-formatting
		title: Markdown syntax color
		description: Markdown formatting syntax text color
		type: variable-themed-color
		opacity: true
		format: hex
		default-light: '#'
		default-dark: '#'
	-
		id: italic-color
		title: Italic text color
		type: variable-themed-color
		format: hex
		default-light: '#'
		default-dark: '#'
	-
		id: bold-color
		title: Bold text color
		type: variable-themed-color
		format: hex
		default-light: '#'
		default-dark: '#'
	-
		id: bold-modifier
		title: Bold text weight
		type: variable-number-slider
		default: 200
		min: 100
		max: 500
		step: 100
	-
		id: p-spacing
		title: Paragraph spacing
		description: Space between paragraphs in reading mode (Obsidian 1.3.7+)
		type: variable-text
		default: 1.75rem
	-
		id: heading-spacing
		title: Heading spacing
		description: Space between paragraphs and headings in reading mode (Obsidian 1.3.7+)
		type: variable-text
		default: 2em
	-
		id: titles
		title: Titles
		type: heading
		level: 2
		collapsed: true
	-
		id: tab-title-bar
		title: Tab title bar
		description: Tab title bar must be turned on in Appearance settings
		type: heading
		level: 3
		collapsed: true
	-
		id: file-header-visibility
		title: Tab title visibility
		description: Visibility of the tab title text
		type: class-select
		default: minimal-tab-title-hover
		options:
			-
				label: Hover only
				value: minimal-tab-title-hover
			-
				label: Hidden
				value: minimal-tab-title-hidden
			-
				label: Visible
				value: minimal-tab-title-visible
	-
		id: file-header-font-size
		title: Tab title font size
		description: Accepts any CSS font-size value
		type: variable-text
		default: 0.9em
	-
		id: file-header-font-weight
		title: Tab title font weight
		type: variable-number-slider
		default: 400
		min: 100
		max: 900
		step: 100
	-
		id: file-header-justify
		title: Tab title alignment
		type: variable-select
		default: center
		options:
			-
				label: Center
				value: center
			-
				label: Left
				value: left
	-
		id: title-color
		title: Tab title text color (active)
		type: variable-themed-color
		format: hex
		default-light: '#'
		default-dark: '#'
	-
		id: title-color-inactive
		title: Tab title text color (inactive)
		type: variable-themed-color
		format: hex
		default-light: '#'
		default-dark: '#'
	-
		id: inline-title
		title: Inline title
		description: Inline titles must be turned on in Appearance settings
		type: heading
		level: 3
		collapsed: true
	-
		id: inline-title-font
		title: Inline title font
		description: Name of the font as it appears on your system
		type: variable-text
		default: ''
	-
		id: inline-title-size
		title: Inline title font size
		description: Accepts any CSS font-size value
		type: variable-text
		default: 1.125em
	-
		id: inline-title-weight
		title: Inline title font weight
		type: variable-number-slider
		default: 600
		min: 100
		max: 900
		step: 100
	-
		id: inline-title-color
		title: Inline title text color (active)
		type: variable-themed-color
		format: hex
		default-light: '#'
		default-dark: '#'
	-
		id: translucency
		title: Translucency
		type: heading
		level: 2
		collapsed: true
	-
		id: workspace-background-translucent
		title: Translucent background color
		type: variable-themed-color
		opacity: true
		format: hex
		default-light: '#'
		default-dark: '#'
	-
		id: window-frame
		title: Window frame
		type: heading
		level: 2
		collapsed: true
	-
		id: window-title-off
		title: Hide window frame title
		description: Hide title in the custom title bar
		type: class-toggle
	-
		id: frame-background
		title: Frame background
		description: Requires colorful window frame
		type: variable-themed-color
		opacity: true
		format: hsl-split
		default-light: '#'
		default-dark: '#'
	-
		id: frame-icon-color
		title: Frame icon color
		description: Requires colorful frame
		type: variable-themed-color
		format: hex
		default-light: '#'
		default-dark: '#'
	-
		id: titlebar-text-color-focused
		title: Frame title color (focused)
		description: Requires custom title bar
		type: variable-themed-color
		format: hex
		default-light: '#'
		default-dark: '#'
	-
		id: titlebar-text-color
		title: Frame title color (inactive)
		description: Requires custom title bar
		type: variable-themed-color
		format: hex
		default-light: '#'
		default-dark: '#'
	-
		id: titlebar-text-weight
		title: Frame title font weight
		description: Requires custom title bar
		type: variable-number-slider
		default: 600
		min: 100
		max: 900
		step: 100
*/

/* @settings
name: Minimal Cards
id: minimal-cards-style
settings:
	-
		id: cards-min-width
		title: Card minimum width
		type: variable-text
		default: 180px
	-
		id: cards-max-width
		title: Card maximum width
		description: Default fills the available width, accepts valid CSS units
		type: variable-text
		default: 1fr
	-
		id: cards-mobile-width
		title: Card minimum width on mobile
		type: variable-text
		default: 120px
	-
		id: cards-padding
		title: Card padding
		type: variable-text
		default: 1.2em
	-
		id: cards-image-height
		title: Card maximum image height
		type: variable-text
		default: 400px
	-
		id: cards-border-width
		title: Card border width
		type: variable-text
		default: 1px
	- 
		id: cards-background
		title: Card background color
		type: variable-themed-color
		format: hex
		default-light: '#'
		default-dark: '#'
	- 
		id: cards-background-hover (hover)
		title: Card background color
		type: variable-themed-color
		format: hex
		default-light: '#'
		default-dark: '#'

*/

/* @settings
name: Minimal Advanced Settings
id: minimal-advanced
settings:
	-
		id: hide-markdown
		title: Hide Markdown syntax
		description: (EXPERIMENTAL) Warning, this can be confusing because the characters still exist they are just hidden.
		type: class-toggle
	-
		id: styled-scrollbars
		title: Styled scrollbars
		description: Use styled scrollbars (replaces native scrollbars)
		type: class-toggle
	-
		id: animations
		title: Animation speed
		description: Animation
		type: class-select
		default: default
		options:
			-
				label: Normal
				value: default
			-
				label: Disabled
				value: disable-animations
			-
				label: Fast
				value: fast-animations
	-
		id: cursor
		title: Cursor style
		description: The cursor style for UI elements
		type: variable-select
		default: default
		options:
			-
				label: Default
				value: default
			-
				label: Pointer
				value: pointer
			-
				label: Crosshair
				value: crosshair
	-
		id: font-ui-small
		title: Small font size
		description: Font size in px of smaller text
		type: variable-number
		default: 13
		format: px
	-
		id: font-ui-smaller
		title: Smaller font size
		description: Font size in px of smallest text
		type: variable-number
		default: 11
		format: px
	- 
		id: mobile-toolbar-off
		title: Disable mobile toolbar
		description: Turns off toolbar in editor on mobile
		type: class-toggle

*/
