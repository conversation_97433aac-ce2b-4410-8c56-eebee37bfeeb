{"main": {"id": "5324373015726ba8", "type": "split", "children": [{"id": "4509724f8bf84da7", "type": "tabs", "children": [{"id": "e7a7b303c61786dc", "type": "leaf", "state": {"type": "canvas", "state": {"file": "Dashboard.canvas", "viewState": {"x": -3930, "y": 90, "zoom": -1.6979089202271123}}, "icon": "lucide-layout-dashboard", "title": "Dashboard"}}, {"id": "1f236eee491b3824", "type": "leaf", "state": {"type": "markdown", "state": {"file": "Final Chiller Notes.md", "mode": "source", "source": false, "backlinks": true, "backlinkOpts": {"collapseAll": false, "extraContext": false, "sortOrder": "alphabetical", "showSearch": false, "searchQuery": "", "backlinkCollapsed": false, "unlinkedCollapsed": true}}, "icon": "lucide-file", "title": "Final Chiller Notes"}}], "currentTab": 1}], "direction": "vertical"}, "left": {"id": "8f178b3df702212a", "type": "split", "children": [{"id": "4d61994b4fedee40", "type": "tabs", "children": [{"id": "a1eb7f986d53863d", "type": "leaf", "state": {"type": "file-explorer", "state": {"sortOrder": "alphabetical", "autoReveal": false}, "icon": "lucide-folder-closed", "title": "Files"}}, {"id": "3dc351a13fe2b70a", "type": "leaf", "state": {"type": "search", "state": {"query": "PC ", "matchingCase": false, "explainSearch": false, "collapseAll": false, "extraContext": false, "sortOrder": "alphabetical"}, "icon": "lucide-search", "title": "Search"}}, {"id": "ef059d27d1207828", "type": "leaf", "state": {"type": "markdown", "state": {"file": "Meeting Notes/Weekly Engineering meeting 2024-08-19.md", "mode": "source", "backlinks": true, "source": false}, "icon": "lucide-file", "title": "Weekly Engineering meeting 2024-08-19"}}, {"id": "381bf201e5ebbb3a", "type": "leaf", "state": {"type": "bookmarks", "state": {}, "icon": "lucide-bookmark", "title": "Bookmarks"}}, {"id": "980fca8fc04587b7", "type": "leaf", "state": {"type": "recent-files", "state": {}, "icon": "clock", "title": "Recent Files"}}]}], "direction": "horizontal", "width": 323.5086555480957}, "right": {"id": "c8844fea9a19314a", "type": "split", "children": [{"id": "66985796c0ef29e7", "type": "tabs", "dimension": 48.661800486618006, "children": [{"id": "01fb1c10d968dcc6", "type": "leaf", "state": {"type": "advanced-tables-toolbar", "state": {}, "icon": "spreadsheet", "title": "Advanced Tables"}}, {"id": "7111c742193aa633", "type": "leaf", "state": {"type": "backlink", "state": {"file": "injectors safety Update Meeting.md", "collapseAll": false, "extraContext": false, "sortOrder": "alphabetical", "showSearch": false, "searchQuery": "", "backlinkCollapsed": false, "unlinkedCollapsed": true}, "icon": "links-coming-in", "title": "Backlinks for injectors safety Update Meeting"}}, {"id": "b399c5891da72292", "type": "leaf", "state": {"type": "outgoing-link", "state": {"file": "injectors safety Update Meeting.md", "linksCollapsed": false, "unlinkedCollapsed": true}, "icon": "links-going-out", "title": "Outgoing links from injectors safety Update Meeting"}}, {"id": "7bb34b11da6c2f0f", "type": "leaf", "state": {"type": "outline", "state": {"file": "Reports/Weekly Schedule.md", "followCursor": false, "showSearch": false, "searchQuery": ""}, "icon": "lucide-list", "title": "Outline of Weekly Schedule"}}, {"id": "5ff4e640118e1e48", "type": "leaf", "state": {"type": "all-properties", "state": {"sortOrder": "frequency", "showSearch": false, "searchQuery": ""}, "icon": "lucide-file", "title": "Plugin no longer active"}}, {"id": "e9fc057022930aa5", "type": "leaf", "state": {"type": "all-properties", "state": {"sortOrder": "frequency", "showSearch": false, "searchQuery": ""}, "icon": "lucide-archive", "title": "All properties"}}], "currentTab": 4}, {"id": "10dd0a85d40662a9", "type": "tabs", "dimension": 51.338199513381994, "children": [{"id": "3787784068874fd3", "type": "leaf", "state": {"type": "tag", "state": {"sortOrder": "frequency", "useHierarchy": true, "showSearch": false, "searchQuery": ""}, "icon": "lucide-tags", "title": "Tags"}}]}], "direction": "horizontal", "width": 200, "collapsed": true}, "left-ribbon": {"hiddenItems": {"obsidian-leaflet-plugin:Open Leaflet Map": false, "3d-graph:3D Graph": false, "obsidian-excalidraw-plugin:Create new drawing": false, "switcher:Open quick switcher": false, "graph:Open graph view": false, "canvas:Create new canvas": false, "templates:Insert template": false, "command-palette:Open command palette": false, "templater-obsidian:Templater": false, "omnisearch:Omnisearch": false, "table-editor-obsidian:Advanced Tables Toolbar": false, "homepage:Open homepage": false, "webpage-html-export:Export Vault to HTML": false, "daily-notes:Open today's daily note": false, "dbfolder:Create a new database table": false}}, "active": "1f236eee491b3824", "lastOpenFiles": ["Dashboard.canvas", "Final Chiller Notes.md", "Chiller Notes.md", "Shop AC Action Items.md", "Test Bench PC.md", "Callahan Daily Notes/2025-04-30 Wednesday JC.md", "engineering_tools_detailed_diagram.md", "engineering_tools_diagram.md", "Machines/HEX/PPV-117.md", "Machines/Injectors/PHX-1009.md", "Reports/Need Manual Documents.md", "Reports/Need Printed Manual.md", "Reports/Need Manual 1.md", "Reports/Manuals Needed 1.md", "Sales Orders/SO 692.md", "Machines/Chiller/241035.md", "Machines/Chiller/Untitled.md", "Locations/Dobson, NC.md", "Sales Orders/SO 1191.md", "SO 1191.md", "<PERSON>, NC.md", "Machines/PHP/PHP-052.md", "Machines/Chiller/241019.md", "Sales Orders/SO 701.md", "Final Chiller Cable Runs.canvas", "Machines/Chiller/241018.md", "Chiller Cable Runs.canvas", "Machines/Chiller/241029.md", "ext_docs/Pasted image 20250218075211.png", "Documentation/Manual Creation.canvas", "Distributers/OEMs", "Distributers", "Machines/Chiller/Docs", "Documentation/Electrical Department Workflow.canvas", "Documentation", "Machine Features/Machine Descriptors", "Machines/Wren", "Meeting Notes", "Drawing Types", "Projects", "Systems", "Manual Creation.canvas", "Electrical Department Workflow.canvas", "Pasted image 20250218075211.png", "ext_docs/Pasted image 20250212234217.png", "ext_docs/Pasted image 20250212232349.png", "ext_docs/Pasted image 20250212231955.png", "ext_docs/Pasted image 20250212231255.png", "ext_docs/Pasted image 20250212231043.png", "Pasted image 20250212215715.png", "Pasted image 20250212214714.png", "Pasted image 20250212213444.png", "Untitled 1.canvas", "HOPE AR.canvas", "project.canvas"]}