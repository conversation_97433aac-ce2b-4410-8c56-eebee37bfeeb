# Mix Chill
```dataview
list 
from "Machines"
where Documentation != True and row["Machine Type"] = [[Mixing System]]
```
# Injectors
```dataview
list 
from "Machines"
where Documentation != True and row["Machine Type"] = [[Injector]]
```
# Chiller
```dataview
list 
from "Machines"
where Documentation != True and row["Machine Type"] = [[Chiller]]
```
# Paw Heater
```dataview
list 
from "Machines"
where Documentation != True and row["Machine Type"] = [[Phoenix Heat Processor|PHP]]
```
# Heat Exchangers
```dataview
list 
from "Machines"
where Documentation != True and row["Machine Type"] = [[Heat Exchanger]]
```
# Rechiller
```dataview
list 
from "Machines"
where Documentation != True and row["Machine Type"] = [[Pumped Rechiller]]
```
