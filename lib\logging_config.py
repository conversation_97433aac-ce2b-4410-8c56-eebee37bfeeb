"""
Logging Configuration Module

This module provides centralized logging configuration for the Engineering Tools suite.
It sets up logging to both file and console with appropriate formatting.
"""

import os
import sys
import logging
from logging.handlers import RotatingFileHandler
from typing import Optional
from .utils import get_user_data_dir, ensure_dir_exists

def setup_logging(
    app_name: str,
    log_level: int = logging.INFO,
    console_level: int = logging.WARNING,
    max_file_size: int = 5 * 1024 * 1024,  # 5 MB
    backup_count: int = 3
) -> str:
    """
    Set up logging to both file and console.
    
    Args:
        app_name: Name of the application (used for log file name)
        log_level: Logging level for file logging
        console_level: Logging level for console output
        max_file_size: Maximum size of log file before rotation (in bytes)
        backup_count: Number of backup log files to keep
        
    Returns:
        str: Path to the log file
    """
    # Create logs directory in user data directory
    log_dir = ensure_dir_exists(os.path.join(get_user_data_dir(), 'logs'))
    log_file = os.path.join(log_dir, f"{app_name}.log")
    
    # Create logger
    logger = logging.getLogger()
    logger.setLevel(log_level)
    
    # Remove any existing handlers
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)
    
    # Create formatters
    file_formatter = logging.Formatter(
        "%(asctime)s - %(levelname)s - %(name)s - %(filename)s:%(lineno)d - %(message)s"
    )
    console_formatter = logging.Formatter(
        "%(levelname)s: %(message)s"
    )
    
    # Create file handler with rotation
    file_handler = RotatingFileHandler(
        log_file,
        maxBytes=max_file_size,
        backupCount=backup_count
    )
    file_handler.setLevel(log_level)
    file_handler.setFormatter(file_formatter)
    
    # Create console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(console_level)
    console_handler.setFormatter(console_formatter)
    
    # Add handlers to logger
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)
    
    logging.info(f"Logging initialized for {app_name}")
    logging.debug(f"Log file: {log_file}")
    
    return log_file

def get_logger(name: str) -> logging.Logger:
    """
    Get a logger with the specified name.
    
    Args:
        name: Name for the logger
        
    Returns:
        logging.Logger: Configured logger
    """
    return logging.getLogger(name)
