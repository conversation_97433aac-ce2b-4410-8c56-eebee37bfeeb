@charset "UTF-8";

/* src/ddc-library/settings-styles.scss */
.ddc_ink_primary-2-button-set .setting-item-info {
  display: none;
}
.ddc_ink_primary-2-button-set .setting-item-control > * {
  flex-grow: 1;
  font-size: var(--font-ui-medium);
  height: unset;
  line-height: 2em;
  width: 50%;
}
.ddc_ink_modal-actions .setting-item-info {
  display: none;
}

/* src/components/dom-components/support-button-set.scss */
.ddc_ink_support-btn-set .setting-item {
  border-top: none !important;
}
.ddc_ink_support-btn-set .setting-item .setting-item-info {
  flex-grow: 2;
  text-align: right;
}
.ddc_ink_support-btn-set .setting-item .setting-item-control {
  flex-grow: 0;
}
.ddc_ink_support-btn-set .ddc_ink_tertiary-button {
  color: var(--text-subtle);
  background: none;
  box-shadow: none;
  padding-inline: var(--size-4-1);
}
.ddc_ink_support-btn-set .ddc_ink_tertiary-button:hover {
  color: var(--text-normal);
}
.ddc_ink_support-btn-set .ddc_ink_secondary-button {
  color: var(--text-normal);
}
.ddc_ink_support-btn-set .ddc_ink_secondary-button svg path {
  fill: var(--text-normal);
}
.ddc_ink_support-btn-set .ddc_ink_primary-button {
  color: var(--text-on-accent);
  background-color: var(--interactive-accent);
}
.ddc_ink_support-btn-set .ddc_ink_primary-button svg path {
  fill: var(--text-on-accent);
}
.ddc_ink_support-btn-set .ddc_ink_primary-button:hover {
  background-color: var(--interactive-accent-hover);
}

/* src/tabs/settings-tab/settings-tab.scss */
.ddc_ink_section {
  margin-bottom: 2em;
}
.ddc_ink_section details {
  overflow: hidden;
  border-style: solid;
  border-color: rgba(var(--callout-color), var(--callout-border-opacity));
  border-width: var(--callout-border-width);
  border-radius: var(--callout-radius);
  background-color: rgba(var(--callout-color), 0.05);
  padding: 1em;
  margin-block: 1em;
  margin-inline: -1em;
  --callout-color:
    255,255,255;
}
.ddc_ink_section details.warning {
  --callout-color: var(--callout-warning);
}
.ddc_ink_section details summary {
  font-weight: 600;
}
.ddc_ink_controls-section {
  padding-block-start: 0.2em;
  padding-block-end: 0.2em;
  padding-inline: 1em;
  margin-inline: -1em;
  border: none;
  border-radius: 10px;
  background-color: var(--color-base-20);
}
.ddc_ink_input-medium .setting-item-control input {
  width: 100%;
}
.ddc_ink_button-set {
  flex-wrap: wrap;
  row-gap: 0.5em;
}
.ddc_ink_button-set .setting-item-info {
  width: 100%;
}
.ddc_ink_button-set .setting-item-control {
  gap: 2px;
  flex-wrap: wrap;
  padding-bottom: 0.9em;
}
.ddc_ink_button-set .setting-item-control button {
  flex-grow: 1;
  flex-shrink: 1;
}
.ddc_ink_button-set .setting-item-control .ddc_ink_middle {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.ddc_ink_button-set .setting-item-control .ddc_ink_left-most {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.ddc_ink_button-set .setting-item-control .ddc_ink_right-most {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}
.ddc_ink_button-set .setting-item-control .mod-cta:disabled {
  box-shadow: inset 1px 1px 3px 0.5px rgba(0, 0, 0, 0.6);
  pointer-events: none;
}

/* src/components/dom-components/notice-components.scss */
.ddc_ink_notice {
  background-color: var(--background-primary-alt) !important;
  border: solid 1px var(--background-modifier-border) !important;
  border-radius: var(--radius-m) !important;
  box-shadow: 0 2px 8px var(--background-modifier-box-shadow) !important;
  text-align: left !important;
  pointer-events: none;
}
.ddc_ink_notice p.ddc_ink_notice-label {
  color: var(--text-faint);
  font-size: var(--font-small);
  margin: 0;
  padding: 0;
}
.ddc_ink_notice h1 {
  margin-block-start: 0.1em;
  color: var(--text-accent);
}
.ddc_ink_notice p {
  font-size: var(--font-text-size);
  color: var(--text-normal);
}
.ddc_ink_notice blockquote {
  font-size: var(--font-text-size);
  padding: 0.5em 0.8em;
  margin-inline: 0;
  color: var(--text-accent);
  background-color: var(--background-primary);
  border-radius: var(--button-radius);
  display: block;
}
.ddc_ink_notice ul,
.ddc_ink_notice ol {
  padding-inline-start: 1em;
  list-style-position: outside;
}
.ddc_ink_notice ul li,
.ddc_ink_notice ol li {
  margin-block-end: 1ch;
}
.ddc_ink_notice a {
  pointer-events: all;
}
.ddc_ink_notice .ddc_ink_notice-cta-bar {
  display: flex;
  flex-direction: row-reverse;
  justify-content: space-between;
}
.ddc_ink_notice .ddc_ink_notice-cta-bar Button {
  pointer-events: all;
}
.ddc_ink_notice .ddc_ink_notice-cta-bar Button.ddc_ink_tertiary-btn {
  color: var(--text-faint);
  background: none;
  box-shadow: none;
  margin-inline: 0;
  padding-inline: 0;
  padding-top: calc(var(--size-4-1) * 2);
  padding-bottom: 0 !important;
  display: inline-flex;
  align-items: flex-end;
  line-height: var(--font-ui-small) !important;
}
.ddc_ink_notice .ddc_ink_notice-cta-bar Button.ddc_ink_tertiary-btn:hover {
  color: var(--text-normal);
  text-decoration: underline;
}
.ddc_ink_notice .ddc_ink_notice-cta-bar Button.ddc_ink_primary-btn {
  color: var(--text-on-accent);
  background-color: var(--interactive-accent);
}
.ddc_ink_notice .ddc_ink_notice-cta-bar Button.ddc_ink_primary-btn:hover {
  background-color: var(--interactive-accent-hover);
}

/* src/components/dom-components/toggle-accordion-setting.scss */
.ddc_ink_toggle-accordion {
  padding: 0;
}
.ddc_ink_toggle-accordion .ddc_ink_toggle-accordion-header .ddc_ink_setting {
  padding: 0.75em 0;
  border-top: 1px solid var(--background-modifier-border);
}
.ddc_ink_toggle-accordion .ddc_ink_toggle-accordion-content {
  display: none;
}
.ddc_ink_toggle-accordion.ddc_ink_expanded {
  margin-inline: -1em;
  margin-bottom: 2em;
  border: none;
  border-radius: 10px;
  overflow: hidden;
  background-color: var(--color-base-20);
}
.ddc_ink_toggle-accordion.ddc_ink_expanded .ddc_ink_toggle-accordion-header {
  padding-block: 0em;
  padding-inline: 1em;
  margin-inline: 0;
  background-color: var(--background-secondary-alt);
}
.ddc_ink_toggle-accordion.ddc_ink_expanded .ddc_ink_toggle-accordion-header .ddc_ink_setting {
  border-top: none;
}
.ddc_ink_toggle-accordion.ddc_ink_expanded .ddc_ink_toggle-accordion-content {
  display: block;
  background-color: var(--background-primary-alt);
  padding-block-start: 0.8em;
  padding-block-end: 0;
  padding-inline: 1em;
}

/* src/tldraw/writing/writing-embed.scss */
.ink_writing-embed {
  transition-property: padding, height;
  transition-duration: 0.5s;
  transition-timing-function: ease-out;
}
.markdown-source-view.mod-cm6 .cm-content > .cm-preview-code-block.cm-embed-block.markdown-rendered.ddc_ink_embed-block,
.markdown-source-view.mod-cm6 .cm-content > .cm-preview-code-block.cm-embed-block.markdown-rendered.ddc_ink_embed-block:hover {
  contain: unset !important;
  overflow: visible !important;
  box-shadow: none;
}
.markdown-source-view.mod-cm6 .cm-content > .cm-preview-code-block.cm-embed-block.markdown-rendered.ddc_ink_embed-block .edit-block-button,
.markdown-source-view.mod-cm6 .cm-content > .cm-preview-code-block.cm-embed-block.markdown-rendered.ddc_ink_embed-block:hover .edit-block-button {
  display: none;
}

/* src/tldraw/writing/tldraw-writing-editor.scss */
.ddc_ink_writing-editor {
  transition: height 0.5s ease;
}
.ddc_ink_writing-editor.preventTransitions {
  transition: none;
}
.ddc_ink_writing-editor .tl-canvas {
  background: none;
  overflow: hidden;
  border-radius: 10px;
  border: 1px solid var(--color-base-20);
}
.ddc_ink_writing-editor .tl-canvas .tl-background {
  background: none;
}
.ddc_ink_writing-editor {
  border: solid 1px var(--color-base-20);
  border-radius: 20px;
  background-color: var(--color-base-05);
}
.ddc_ink_writing-editor line {
  stroke: var(--color-base-50);
  stroke-width: 2px;
}

/* src/tldraw/writing-menu/writing-menu.scss */
.ink_menu-bar {
  flex-grow: 1;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  pointer-events: none;
}
.ink_menu-bar button {
  animation: scale-up 0.3s 0.1s ease-out;
  animation-fill-mode: both;
  pointer-events: auto;
  background-color: var(--interactive-normal);
  color: var(--color-base-60);
  fill: var(--color-base-60);
}
.ink_menu-bar button:hover {
  background-color: var(--interactive-hover);
  color: var(--color-base-100);
  fill: var(--color-base-100);
}
.ink_menu-bar button:disabled {
  animation: scale-up 0.3s ease-out;
  cursor: default;
  color: var(--text-on-accent);
  fill: var(--text-on-accent);
  background-color: var(--interactive-accent);
}
.ink_menu-bar button:disabled:hover {
  box-shadow: var(--input-shadow);
}
@keyframes scale-up {
  from {
    transform: scale(0);
  }
  to {
    transform: scale(1);
  }
}
.ink_quick-menu {
  display: flex;
  flex-direction: row;
  gap: 8px;
}
.ink_quick-menu button {
  width: 2.5em;
  height: 2.5em;
  border-radius: 0.8em;
  padding: 0 !important;
}
.ink_quick-menu button svg {
  width: 1.5em !important;
  height: 1.5em !important;
}
.ink_quick-menu button:disabled {
  visibility: hidden;
}
.ink_tool-menu {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: row;
  justify-content: center;
  gap: 8px;
}
.ink_tool-menu button {
  width: 2.5em;
  height: 2.5em;
  border-radius: 2.5em;
  padding: 0 !important;
}
.ink_tool-menu button svg {
  width: 1.5em !important;
  height: 1.5em !important;
}
.ink_other-menu {
  display: flex;
  flex-direction: row;
}

/* src/tldraw/primary-menu-bar/primary-menu-bar.scss */
.ink_write_primary-menu-bar {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  padding: 8px var(--file-margins);
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  pointer-events: none;
}
.cm-embed-block .ink_write_primary-menu-bar {
  transform: translate(0, -100%);
}

/* src/tldraw/extended-writing-menu/extended-writing-menu.scss */
.ink_extended-writing-menu {
  display: flex;
  gap: 4px;
  pointer-events: none;
}
.ink_extended-writing-menu button {
  width: auto;
  pointer-events: auto;
  background-color: var(--interactive-normal);
  color: var(--color-base-60);
  fill: var(--color-base-60);
}
.ink_extended-writing-menu button:hover {
  background-color: var(--interactive-hover);
  color: var(--color-base-100);
  fill: var(--color-base-100);
}
.ink_extended-writing-menu button:disabled {
  cursor: default;
  color: var(--text-on-accent);
  fill: var(--text-on-accent);
  background-color: var(--interactive-accent);
}
.ink_extended-writing-menu button:disabled:hover {
  box-shadow: var(--input-shadow);
}
.ink_extended-writing-menu button {
  width: 2.5em;
  height: 2.5em;
  border-radius: 0.8em;
  padding: 0 !important;
}
.ink_extended-writing-menu button.ddc_ink_btn-slim {
  width: 1.9em;
  height: 2.5em;
}
.ink_extended-writing-menu button svg {
  width: 1.5em !important;
  height: 1.5em !important;
}

/* src/tldraw/overflow-menu/overflow-menu.scss */
.ddc_ink_overflow-button-and-menu {
  position: relative;
}
.ddc_ink_overflow-button-and-menu .ddc_ink_dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  width: 10em;
  background-color: var(--interactive-normal);
  color: var(--color-base-60);
  fill: var(--color-base-60);
  box-shadow: var(--input-shadow);
  border-radius: 10px;
  display: none;
}
.ddc_ink_overflow-button-and-menu .ddc_ink_dropdown.ddc_ink_visible {
  display: block;
}
.ddc_ink_overflow-button-and-menu .ddc_ink_dropdown li {
  padding: 0.5em;
}

/* src/tldraw/writing/writing-embed-preview/writing-embed-preview.scss */
.ddc_ink_writing-embed-preview {
  pointer-events: none;
  overflow: visible;
}
.ddc_ink_writing-embed-preview.ddc_ink_visible-background {
  border: solid 1px var(--color-base-20);
  border-radius: 20px;
  background-color: var(--color-base-05);
}
.ddc_ink_writing-embed-preview line {
  stroke: var(--color-base-50);
  stroke-width: 2px;
}
.ddc_ink_writing-embed-preview:not(.ddc_ink_visible-lines) line {
  display: none;
}
.theme-dark .ddc_ink_writing-embed-preview path {
  fill: rgb(242, 242, 242);
}
.theme-light .ddc_ink_writing-embed-preview path {
  fill: rgb(29, 29, 29);
}

/* src/tldraw/transition-menu/transition-menu.scss */
.ink_transition_menu {
  display: flex;
  width: 100%;
  justify-content: flex-end;
  gap: 4px;
  pointer-events: none;
}
.ink_transition_menu button {
  width: auto;
  pointer-events: auto;
  background-color: var(--interactive-normal);
  color: var(--color-base-60);
  fill: var(--color-base-60);
}
.ink_transition_menu button:hover {
  background-color: var(--interactive-hover);
  color: var(--color-base-100);
  fill: var(--color-base-100);
}
.ink_transition_menu button:disabled {
  cursor: default;
  color: var(--text-on-accent);
  fill: var(--text-on-accent);
  background-color: var(--interactive-accent);
}
.ink_transition_menu button:disabled:hover {
  box-shadow: var(--input-shadow);
}
.ink_transition_menu button {
  width: 2.5em;
  height: 2.5em;
  border-radius: 0.8em;
  padding: 0 !important;
  overflow: visible;
}
.ink_transition_menu button.ddc_ink_btn-slim {
  width: 1.9em;
  height: 2.5em;
  border-radius: 0.8em;
  padding: 0;
}
.ink_transition_menu button svg {
  width: 1.5em !important;
  height: 1.5em !important;
}

/* src/tldraw/drawing/tldraw-drawing-editor.scss */
.ddc_ink_drawing-editor,
.ddc_ink_writing-editor {
}
.ddc_ink_drawing-editor .tl-container,
.ddc_ink_writing-editor .tl-container {
  width: 100%;
  height: 100%;
  font-size: 12px;
  --space-1: 2px;
  --space-2: 4px;
  --space-3: 8px;
  --space-4: 12px;
  --space-5: 16px;
  --space-6: 20px;
  --space-7: 28px;
  --space-8: 32px;
  --space-9: 64px;
  --space-10: 72px;
  --radius-0: 2px;
  --radius-1: 4px;
  --radius-2: 6px;
  --radius-3: 9px;
  --radius-4: 11px;
  --layer-background: 100;
  --layer-grid: 150;
  --layer-canvas: 200;
  --layer-shapes: 300;
  --layer-overlays: 400;
  --layer-following-indicator: 1000;
  --tl-zoom: 1;
  --tl-cursor-none: none;
  --tl-cursor-default: url("data:image/svg+xml,<svg height='32' width='32' viewBox='0 0 32 32' xmlns='http://www.w3.org/2000/svg' style='color: black;'><defs><filter id='shadow' y='-40%' x='-40%' width='180px' height='180%' color-interpolation-filters='sRGB'><feDropShadow dx='1' dy='1' stdDeviation='1.2' flood-opacity='.5'/></filter></defs><g fill='none' transform='rotate(0 16 16)' filter='url(%23shadow)'><path d='m12 24.4219v-16.015l11.591 11.619h-6.781l-.411.124z' fill='white'/><path d='m21.0845 25.0962-3.605 1.535-4.682-11.089 3.686-1.553z' fill='white'/><path d='m19.751 24.4155-1.844.774-3.1-7.374 1.841-.775z' fill='black'/><path d='m13 10.814v11.188l2.969-2.866.428-.139h4.768z' fill='black'/></g></svg>") 12 8, default;
  --tl-cursor-pointer: url("data:image/svg+xml,<svg height='32' width='32' viewBox='0 0 32 32' xmlns='http://www.w3.org/2000/svg' style='color: black;'><defs><filter id='shadow' y='-40%' x='-40%' width='180px' height='180%' color-interpolation-filters='sRGB'><feDropShadow dx='1' dy='1' stdDeviation='1.2' flood-opacity='.5'/></filter></defs><g fill='none' transform='rotate(0 16 16)' filter='url(%23shadow)'><path d='m13.3315 21.3799c-.284-.359-.629-1.093-1.243-1.984-.348-.504-1.211-1.453-1.468-1.935-.223-.426-.199-.617-.146-.97.094-.628.738-1.117 1.425-1.051.519.049.959.392 1.355.716.239.195.533.574.71.788.163.196.203.277.377.509.23.307.302.459.214.121-.071-.496-.187-1.343-.355-2.092-.128-.568-.159-.657-.281-1.093-.129-.464-.195-.789-.316-1.281-.084-.348-.235-1.059-.276-1.459-.057-.547-.087-1.439.264-1.849.275-.321.906-.418 1.297-.22.512.259.803 1.003.936 1.3.239.534.387 1.151.516 1.961.164 1.031.466 2.462.476 2.763.024-.369-.068-1.146-.004-1.5.058-.321.328-.694.666-.795.286-.085.621-.116.916-.055.313.064.643.288.766.499.362.624.369 1.899.384 1.831.086-.376.071-1.229.284-1.584.14-.234.497-.445.687-.479.294-.052.655-.068.964-.008.249.049.586.345.677.487.218.344.342 1.317.379 1.658.015.141.074-.392.293-.736.406-.639 1.843-.763 1.898.639.025.654.02.624.02 1.064 0 .517-.012.828-.04 1.202-.031.4-.117 1.304-.242 1.742-.086.301-.371.978-.652 1.384 0 0-1.074 1.25-1.191 1.813-.118.562-.079.566-.102.965-.023.398.121.922.121.922s-.802.104-1.234.035c-.391-.063-.875-.841-1-1.079-.172-.328-.539-.265-.682-.023-.225.383-.709 1.07-1.051 1.113-.668.084-2.054.031-3.139.02 0 0 .185-1.011-.227-1.358-.305-.259-.83-.784-1.144-1.06z' fill='white'/><g stroke='black' stroke-linecap='round' stroke-width='.75'><path d='m13.3315 21.3799c-.284-.359-.629-1.093-1.243-1.984-.348-.504-1.211-1.453-1.468-1.935-.223-.426-.199-.617-.146-.97.094-.628.738-1.117 1.425-1.051.519.049.959.392 1.355.716.239.195.533.574.71.788.163.196.203.277.377.509.23.307.302.459.214.121-.071-.496-.187-1.343-.355-2.092-.128-.568-.159-.657-.281-1.093-.129-.464-.195-.789-.316-1.281-.084-.348-.235-1.059-.276-1.459-.057-.547-.087-1.439.264-1.849.275-.321.906-.418 1.297-.22.512.259.803 1.003.936 1.3.239.534.387 1.151.516 1.961.164 1.031.466 2.462.476 2.763.024-.369-.068-1.146-.004-1.5.058-.321.328-.694.666-.795.286-.085.621-.116.916-.055.313.064.643.288.766.499.362.624.369 1.899.384 1.831.086-.376.071-1.229.284-1.584.14-.234.497-.445.687-.479.294-.052.655-.068.964-.008.249.049.586.345.677.487.218.344.342 1.317.379 1.658.015.141.074-.392.293-.736.406-.639 1.843-.763 1.898.639.025.654.02.624.02 1.064 0 .517-.012.828-.04 1.202-.031.4-.117 1.304-.242 1.742-.086.301-.371.978-.652 1.384 0 0-1.074 1.25-1.191 1.813-.118.562-.079.566-.102.965-.023.398.121.922.121.922s-.802.104-1.234.035c-.391-.063-.875-.841-1-1.079-.172-.328-.539-.265-.682-.023-.225.383-.709 1.07-1.051 1.113-.668.084-2.054.031-3.139.02 0 0 .185-1.011-.227-1.358-.305-.259-.83-.784-1.144-1.06z' stroke-linejoin='round'/><path d='m21.5664 21.7344v-3.459'/><path d='m19.5508 21.7461-.016-3.473'/><path d='m17.5547 18.3047.021 3.426'/></g></g></svg>") 14 10, pointer;
  --tl-cursor-cross: url("data:image/svg+xml,<svg height='32' width='32' viewBox='0 0 32 32' xmlns='http://www.w3.org/2000/svg' style='color: black;'><defs><filter id='shadow' y='-40%' x='-40%' width='180px' height='180%' color-interpolation-filters='sRGB'><feDropShadow dx='1' dy='1' stdDeviation='1.2' flood-opacity='.5'/></filter></defs><g fill='none' transform='rotate(0 16 16)' filter='url(%23shadow)'><path d='m25 16h-6.01v-6h-2.98v6h-6.01v3h6.01v6h2.98v-6h6.01z' fill='white'/><path d='m23.9902 17.0103h-6v-6.01h-.98v6.01h-6v.98h6v6.01h.98v-6.01h6z' fill='%23231f1f'/></g></svg>") 16 16, crosshair;
  --tl-cursor-move: url("data:image/svg+xml,<svg height='32' width='32' viewBox='0 0 32 32' xmlns='http://www.w3.org/2000/svg' style='color: black;'><defs><filter id='shadow' y='-40%' x='-40%' width='180px' height='180%' color-interpolation-filters='sRGB'><feDropShadow dx='1' dy='1' stdDeviation='1.2' flood-opacity='.5'/></filter></defs><g fill='none' transform='rotate(0 16 16)' filter='url(%23shadow)'><path d='m19 14h1v1h-1zm1 6h-1v-1h1zm-5-5h-1v-1h1zm0 5h-1v-1h1zm2-10.987-7.985 7.988 5.222 5.221 2.763 2.763 7.984-7.985z' fill='white'/><g fill='black'><path d='m23.5664 16.9971-2.557-2.809v1.829h-4.009-4.001v-1.829l-2.571 2.809 2.572 2.808-.001-1.808h4.001 4.009l-.001 1.808z'/><path d='m17.9873 17h.013v-4.001l1.807.001-2.807-2.571-2.809 2.57h1.809v4.001h.008v4.002l-1.828-.001 2.807 2.577 2.805-2.576h-1.805z'/></g></g></svg>") 16 16, move;
  --tl-cursor-grab: url("data:image/svg+xml,<svg height='32' width='32' viewBox='0 0 32 32' xmlns='http://www.w3.org/2000/svg' style='color: black;'><defs><filter id='shadow' y='-40%' x='-40%' width='180px' height='180%' color-interpolation-filters='sRGB'><feDropShadow dx='1' dy='1' stdDeviation='1.2' flood-opacity='.5'/></filter></defs><g fill='none' transform='rotate(0 16 16)' filter='url(%23shadow)'><path d='m13.5557 17.5742c-.098-.375-.196-.847-.406-1.552-.167-.557-.342-.859-.47-1.233-.155-.455-.303-.721-.496-1.181-.139-.329-.364-1.048-.457-1.44-.119-.509.033-.924.244-1.206.253-.339.962-.49 1.357-.351.371.13.744.512.916.788.288.46.357.632.717 1.542.393.992.564 1.918.611 2.231l.085.452c-.001-.04-.043-1.122-.044-1.162-.035-1.029-.06-1.823-.038-2.939.002-.126.064-.587.084-.715.078-.5.305-.8.673-.979.412-.201.926-.215 1.401-.017.423.173.626.55.687 1.022.014.109.094.987.093 1.107-.013 1.025.006 1.641.015 2.174.004.231.003 1.625.017 1.469.061-.656.094-3.189.344-3.942.144-.433.405-.746.794-.929.431-.203 1.113-.07 1.404.243.285.305.446.692.482 1.153.032.405-.019.897-.02 1.245 0 .867-.021 1.324-.037 2.121-.001.038-.015.298.023.182.094-.28.188-.542.266-.745.049-.125.241-.614.359-.859.114-.234.211-.369.415-.688.2-.313.415-.448.668-.561.54-.235 1.109.112 1.301.591.086.215.009.713-.028 1.105-.061.647-.254 1.306-.352 1.648-.128.447-.274 1.235-.34 1.601-.072.394-.234 1.382-.359 1.82-.086.301-.371.978-.652 1.384 0 0-1.074 1.25-1.192 1.812-.117.563-.078.567-.101.965-.024.399.121.923.121.923s-.802.104-1.234.034c-.391-.062-.875-.841-1-1.078-.172-.328-.539-.265-.682-.023-.225.383-.709 1.07-1.051 1.113-.668.084-2.054.03-3.139.02 0 0 .185-1.011-.227-1.358-.305-.26-.83-.784-1.144-1.06l-.832-.921c-.284-.36-.629-1.093-1.243-1.985-.348-.504-1.027-1.085-1.284-1.579-.223-.425-.331-.954-.19-1.325.225-.594.675-.897 1.362-.832.519.05.848.206 1.238.537.225.19.573.534.75.748.163.195.203.276.377.509.23.307.302.459.214.121' fill='white'/><g stroke='black' stroke-linecap='round' stroke-width='.75'><path d='m13.5557 17.5742c-.098-.375-.196-.847-.406-1.552-.167-.557-.342-.859-.47-1.233-.155-.455-.303-.721-.496-1.181-.139-.329-.364-1.048-.457-1.44-.119-.509.033-.924.244-1.206.253-.339.962-.49 1.357-.351.371.13.744.512.916.788.288.46.357.632.717 1.542.393.992.564 1.918.611 2.231l.085.452c-.001-.04-.043-1.122-.044-1.162-.035-1.029-.06-1.823-.038-2.939.002-.126.064-.587.084-.715.078-.5.305-.8.673-.979.412-.201.926-.215 1.401-.017.423.173.626.55.687 1.022.014.109.094.987.093 1.107-.013 1.025.006 1.641.015 2.174.004.231.003 1.625.017 1.469.061-.656.094-3.189.344-3.942.144-.433.405-.746.794-.929.431-.203 1.113-.07 1.404.243.285.305.446.692.482 1.153.032.405-.019.897-.02 1.245 0 .867-.021 1.324-.037 2.121-.001.038-.015.298.023.182.094-.28.188-.542.266-.745.049-.125.241-.614.359-.859.114-.234.211-.369.415-.688.2-.313.415-.448.668-.561.54-.235 1.109.112 1.301.591.086.215.009.713-.028 1.105-.061.647-.254 1.306-.352 1.648-.128.447-.274 1.235-.34 1.601-.072.394-.234 1.382-.359 1.82-.086.301-.371.978-.652 1.384 0 0-1.074 1.25-1.192 1.812-.117.563-.078.567-.101.965-.024.399.121.923.121.923s-.802.104-1.234.034c-.391-.062-.875-.841-1-1.078-.172-.328-.539-.265-.682-.023-.225.383-.709 1.07-1.051 1.113-.668.084-2.054.03-3.139.02 0 0 .185-1.011-.227-1.358-.305-.26-.83-.784-1.144-1.06l-.832-.921c-.284-.36-.629-1.093-1.243-1.985-.348-.504-1.027-1.085-1.284-1.579-.223-.425-.331-.954-.19-1.325.225-.594.675-.897 1.362-.832.519.05.848.206 1.238.537.225.19.573.534.75.748.163.195.203.276.377.509.23.307.302.459.214.121' stroke-linejoin='round'/><path d='m20.5664 21.7344v-3.459'/><path d='m18.5508 21.7461-.016-3.473'/><path d='m16.5547 18.3047.021 3.426'/></g></g></svg>") 16 16, grab;
  --tl-cursor-grabbing: url("data:image/svg+xml,<svg height='32' width='32' viewBox='0 0 32 32' xmlns='http://www.w3.org/2000/svg' style='color: black;'><defs><filter id='shadow' y='-40%' x='-40%' width='180px' height='180%' color-interpolation-filters='sRGB'><feDropShadow dx='1' dy='1' stdDeviation='1.2' flood-opacity='.5'/></filter></defs><g fill='none' transform='rotate(0 16 16)' filter='url(%23shadow)'><path d='m13.5732 12.0361c.48-.178 1.427-.069 1.677.473.213.462.396 1.241.406 1.075.024-.369-.024-1.167.137-1.584.117-.304.347-.59.686-.691.285-.086.62-.116.916-.055.313.064.642.287.765.499.362.623.368 1.899.385 1.831.064-.272.07-1.229.283-1.584.141-.235.497-.445.687-.479.294-.052.656-.068.964-.008.249.049.586.344.677.487.219.344.342 1.316.379 1.658.016.141.074-.393.293-.736.406-.639 1.844-.763 1.898.639.026.654.02.624.02 1.064 0 .516-.012.828-.04 1.202-.03.399-.116 1.304-.241 1.742-.086.301-.371.978-.653 1.384 0 0-1.074 1.25-1.191 1.812-.117.563-.078.567-.102.965-.023.399.121.923.121.923s-.801.104-1.234.034c-.391-.062-.875-.84-1-1.078-.172-.328-.539-.265-.682-.023-.224.383-.709 1.07-1.05 1.113-.669.084-2.055.03-3.14.02 0 0 .185-1.011-.227-1.358-.305-.26-.83-.784-1.144-1.06l-.832-.921c-.283-.36-1.002-.929-1.243-1.985-.213-.936-.192-1.395.037-1.77.232-.381.67-.589.854-.625.208-.042.692-.039.875.062.223.123.313.159.488.391.23.307.312.456.213.121-.076-.262-.322-.595-.434-.97-.109-.361-.401-.943-.38-1.526.008-.221.103-.771.832-1.042' fill='white'/><g stroke='black' stroke-width='.75'><path d='m13.5732 12.0361c.48-.178 1.427-.069 1.677.473.213.462.396 1.241.406 1.075.024-.369-.024-1.167.137-1.584.117-.304.347-.59.686-.691.285-.086.62-.116.916-.055.313.064.642.287.765.499.362.623.368 1.899.385 1.831.064-.272.07-1.229.283-1.584.141-.235.497-.445.687-.479.294-.052.656-.068.964-.008.249.049.586.344.677.487.219.344.342 1.316.379 1.658.016.141.074-.393.293-.736.406-.639 1.844-.763 1.898.639.026.654.02.624.02 1.064 0 .516-.012.828-.04 1.202-.03.399-.116 1.304-.241 1.742-.086.301-.371.978-.653 1.384 0 0-1.074 1.25-1.191 1.812-.117.563-.078.567-.102.965-.023.399.121.923.121.923s-.801.104-1.234.034c-.391-.062-.875-.84-1-1.078-.172-.328-.539-.265-.682-.023-.224.383-.709 1.07-1.05 1.113-.669.084-2.055.03-3.14.02 0 0 .185-1.011-.227-1.358-.305-.26-.83-.784-1.144-1.06l-.832-.921c-.283-.36-1.002-.929-1.243-1.985-.213-.936-.192-1.395.037-1.77.232-.381.67-.589.854-.625.208-.042.692-.039.875.062.223.123.313.159.488.391.23.307.312.456.213.121-.076-.262-.322-.595-.434-.97-.109-.361-.401-.943-.38-1.526.008-.221.103-.771.832-1.042z' stroke-linejoin='round'/><path d='m20.5664 19.7344v-3.459' stroke-linecap='round'/><path d='m18.5508 19.7461-.016-3.473' stroke-linecap='round'/><path d='m16.5547 16.3047.021 3.426' stroke-linecap='round'/></g></g></svg>") 16 16, grabbing;
  --tl-cursor-text: url("data:image/svg+xml,<svg height='32' width='32' viewBox='0 0 32 32' xmlns='http://www.w3.org/2000/svg' style='color: black;'><defs><filter id='shadow' y='-40%' x='-40%' width='180px' height='180%' color-interpolation-filters='sRGB'><feDropShadow dx='1' dy='1' stdDeviation='1.2' flood-opacity='.5'/></filter></defs><g fill='none' transform='rotate(0 16 16)' filter='url(%23shadow)'><path fill='white' d='M7.94 0a5.25 5.25 0 0 0-3.47 1.17A5.27 5.27 0 0 0 1 0H0v3h1c1.41 0 1.85.7 2 1v3.94H2v3h1v3c-.13.3-.57 1-2 1H0v3h1a5.27 5.27 0 0 0 3.47-1.17c.98.8 2.21 1.21 3.47 1.17h1v-3h-1c-1.41 0-1.85-.7-2-1v-3H7v-3H6V4c.13-.3.57-1 2-1h1V0H7.94z'/><path fill='black' d='M7.94 2V1a4 4 0 0 0-3.47 1.64A4 4 0 0 0 1 1v1c1.3-.17 2.56.6 3 1.84v5.1H3v1h1v4.16c-.45 1.24-1.7 2-3 1.84v1a4.05 4.05 0 0 0 3.47-1.63 4.05 4.05 0 0 0 3.47 1.63v-1A2.82 2.82 0 0 1 5 14.1V9.93h1v-1H5V3.85A2.81 2.81 0 0 1 7.94 2z'/></g></svg>") 4 10, text;
  --tl-cursor-zoom-in: url("data:image/svg+xml,<svg height='32' width='32' viewBox='0 0 32 32' xmlns='http://www.w3.org/2000/svg' style='color: black;'><defs><filter id='shadow' y='-40%' x='-40%' width='180px' height='180%' color-interpolation-filters='sRGB'><feDropShadow dx='1' dy='1' stdDeviation='1.2' flood-opacity='.5'/></filter></defs><g fill='none' transform='rotate(0 16 16)' filter='url(%23shadow)'><path d='m20.5 15c0 3.038-2.462 5.5-5.5 5.5s-5.5-2.462-5.5-5.5 2.462-5.5 5.5-5.5 5.5 2.462 5.5 5.5' fill='white'/><path d='m20.5 15c0 3.038-2.462 5.5-5.5 5.5s-5.5-2.462-5.5-5.5 2.462-5.5 5.5-5.5 5.5 2.462 5.5 5.5z' stroke='black'/><g fill='black'><path d='m18 14h-2v-2h-2v2h-2v1.98h2v2.02h2v-2.02h2z'/><path d='m23.5859 25 1.414-1.414-5.449-5.449-1.414 1.414z'/></g></g></svg>") 16 16, zoom-in;
  --tl-cursor-zoom-out: url("data:image/svg+xml,<svg height='32' width='32' viewBox='0 0 32 32' xmlns='http://www.w3.org/2000/svg' style='color: black;'><defs><filter id='shadow' y='-40%' x='-40%' width='180px' height='180%' color-interpolation-filters='sRGB'><feDropShadow dx='1' dy='1' stdDeviation='1.2' flood-opacity='.5'/></filter></defs><g fill='none' transform='rotate(0 16 16)' filter='url(%23shadow)'><path d='m20.5 15c0 3.038-2.462 5.5-5.5 5.5s-5.5-2.462-5.5-5.5 2.462-5.5 5.5-5.5 5.5 2.462 5.5 5.5' fill='white'/><path d='m20.5 15c0 3.038-2.462 5.5-5.5 5.5s-5.5-2.462-5.5-5.5 2.462-5.5 5.5-5.5 5.5 2.462 5.5 5.5z' stroke='black'/><g fill='black'><path d='m18 16h-5.98v-1.98h5.98z'/><path d='m23.5859 25 1.414-1.414-5.449-5.449-1.414 1.414z'/></g></g></svg>") 16 16, zoom-out;
  --tl-cursor: var(--tl-cursor-default);
  --tl-cursor-resize-edge: ew-resize;
  --tl-cursor-resize-corner: nesw-resize;
  --tl-cursor-ew-resize: ew-resize;
  --tl-cursor-ns-resize: ns-resize;
  --tl-cursor-nesw-resize: nesw-resize;
  --tl-cursor-nwse-resize: nwse-resize;
  --tl-cursor-rotate: pointer;
  --tl-cursor-nwse-rotate: pointer;
  --tl-cursor-nesw-rotate: pointer;
  --tl-cursor-senw-rotate: pointer;
  --tl-cursor-swne-rotate: pointer;
  --tl-scale: calc(1 / var(--tl-zoom));
  --tl-font-draw: "tldraw_draw", sans-serif;
  --tl-font-sans: "tldraw_sans", sans-serif;
  --tl-font-serif: "tldraw_serif", serif;
  --tl-font-mono: "tldraw_mono", monospace;
  --a: calc(min(0.5, 1 / var(--tl-zoom)) * 2px);
  --b: calc(min(0.5, 1 / var(--tl-zoom)) * -2px);
  --tl-text-outline:
    0 var(--b) 0 var(--color-background),
    0 var(--a) 0 var(--color-background),
    var(--b) var(--b) 0 var(--color-background),
    var(--a) var(--b) 0 var(--color-background),
    var(--a) var(--a) 0 var(--color-background),
    var(--b) var(--a) 0 var(--color-background);
  position: relative;
  inset: 0px;
  height: 100%;
  width: 100%;
  overflow: clip;
}
.ddc_ink_drawing-editor .tl-theme__light,
.ddc_ink_writing-editor .tl-theme__light {
  --color-accent: hsl(0, 76%, 60%);
  --color-background: hsl(210, 20%, 98%);
  --color-brush-fill: hsl(0, 0%, 56%, 10.2%);
  --color-brush-stroke: hsl(0, 0%, 56%, 25.1%);
  --color-grid: hsl(0, 0%, 43%);
  --color-low: hsl(204, 16%, 94%);
  --color-low-border: hsl(204, 16%, 92%);
  --color-culled: hsl(204, 14%, 93%);
  --color-muted-none: hsl(0, 0%, 0%, 0%);
  --color-muted-0: hsl(0, 0%, 0%, 2%);
  --color-muted-1: hsl(0, 0%, 0%, 10%);
  --color-muted-2: hsl(0, 0%, 0%, 4.3%);
  --color-hint: hsl(0, 0%, 0%, 5.5%);
  --color-overlay: hsl(0, 0%, 0%, 20%);
  --color-divider: hsl(0, 0%, 91%);
  --color-panel-contrast: hsl(0, 0%, 100%);
  --color-panel-overlay: hsl(0, 0%, 100%, 82%);
  --color-panel: hsl(0, 0%, 99%);
  --color-focus: hsl(214, 100%, 29%);
  --color-selected: hsl(214, 84%, 56%);
  --color-selected-contrast: hsl(0, 0%, 100%);
  --color-selection-fill: hsl(210, 100%, 56%, 24%);
  --color-selection-stroke: hsl(214, 84%, 56%);
  --color-text-0: hsl(0, 0%, 11%);
  --color-text-1: hsl(0, 0%, 18%);
  --color-text-3: hsl(220, 2%, 65%);
  --color-text-shadow: hsl(0, 0%, 100%);
  --color-primary: hsl(214, 84%, 56%);
  --color-warn: hsl(0, 90%, 43%);
  --color-text: hsl(0, 0%, 0%);
  --color-laser: hsl(0, 100%, 50%);
  --shadow-1: 0px 1px 2px hsl(0, 0%, 0%, 25%), 0px 1px 3px hsl(0, 0%, 0%, 9%);
  --shadow-2:
    0px 0px 2px hsl(0, 0%, 0%, 16%),
    0px 2px 3px hsl(0, 0%, 0%, 24%),
    0px 2px 6px hsl(0, 0%, 0%, 0.1),
    inset 0px 0px 0px 1px var(--color-panel-contrast);
  --shadow-3:
    0px 1px 2px hsl(0, 0%, 0%, 28%),
    0px 2px 6px hsl(0, 0%, 0%, 14%),
    inset 0px 0px 0px 1px var(--color-panel-contrast);
  --shadow-4:
    0px 0px 3px hsl(0, 0%, 0%, 19%),
    0px 5px 4px hsl(0, 0%, 0%, 16%),
    0px 2px 16px hsl(0, 0%, 0%, 6%),
    inset 0px 0px 0px 1px var(--color-panel-contrast);
}
.ddc_ink_drawing-editor .tl-theme__dark,
.ddc_ink_writing-editor .tl-theme__dark {
  --color-accent: hsl(0, 76%, 60%);
  --color-background: hsl(240, 5%, 8%);
  --color-brush-fill: hsl(0, 0%, 71%, 5.1%);
  --color-brush-stroke: hsl(0, 0%, 71%, 25.1%);
  --color-grid: hsl(0, 0%, 56%);
  --color-low: hsl(260, 5%, 12.5%);
  --color-low-border: hsl(207, 10%, 10%);
  --color-culled: hsl(210, 11%, 19%);
  --color-muted-none: hsl(0, 0%, 100%, 0%);
  --color-muted-0: hsl(0, 0%, 100%, 2%);
  --color-muted-1: hsl(0, 0%, 100%, 10%);
  --color-muted-2: hsl(0, 0%, 100%, 5%);
  --color-hint: hsl(0, 0%, 100%, 7%);
  --color-overlay: hsl(0, 0%, 0%, 50%);
  --color-divider: hsl(240, 9%, 25%);
  --color-panel-contrast: hsl(240, 13%, 22%);
  --color-panel: hsl(220, 8%, 15%);
  --color-panel-overlay: hsl(210, 11%, 24%, 82%);
  --color-focus: hsl(217, 76%, 80%);
  --color-selected: hsl(217, 89%, 61%);
  --color-selected-contrast: hsl(0, 0%, 100%);
  --color-selection-fill: hsl(209, 100%, 57%, 20%);
  --color-selection-stroke: hsl(214, 84%, 56%);
  --color-text-0: hsl(0, 9%, 94%);
  --color-text-1: hsl(0, 0%, 85%);
  --color-text-3: hsl(210, 6%, 45%);
  --color-text-shadow: hsl(210, 13%, 18%);
  --color-primary: hsl(214, 84%, 56%);
  --color-warn: hsl(0, 81%, 66%);
  --color-text: hsl(210, 17%, 98%);
  --color-laser: hsl(0, 100%, 50%);
  --shadow-1:
    0px 1px 2px hsl(0, 0%, 0%, 16.1%),
    0px 1px 3px hsl(0, 0%, 0%, 22%),
    inset 0px 0px 0px 1px var(--color-panel-contrast);
  --shadow-2:
    0px 1px 3px hsl(0, 0%, 0%, 66.6%),
    0px 2px 6px hsl(0, 0%, 0%, 33%),
    inset 0px 0px 0px 1px var(--color-panel-contrast);
  --shadow-3:
    0px 1px 3px hsl(0, 0%, 0%, 50%),
    0px 2px 12px hsl(0, 0%, 0%, 50%),
    inset 0px 0px 0px 1px var(--color-panel-contrast);
}
.ddc_ink_drawing-editor .tl-counter-scaled,
.ddc_ink_writing-editor .tl-counter-scaled {
  transform: scale(var(--tl-scale));
  transform-origin: top left;
  width: calc(100% * var(--tl-zoom));
  height: calc(100% * var(--tl-zoom));
}
.ddc_ink_drawing-editor .tl-container,
.ddc_ink_drawing-editor .tl-container *,
.ddc_ink_writing-editor .tl-container,
.ddc_ink_writing-editor .tl-container * {
  -webkit-touch-callout: none;
  -webkit-tap-highlight-color: transparent;
  scrollbar-highlight-color: transparent;
  -webkit-user-select: none;
  user-select: none;
  box-sizing: border-box;
  outline: none;
}
.ddc_ink_drawing-editor .tl-container a,
.ddc_ink_writing-editor .tl-container a {
  -webkit-touch-callout: initial;
}
.ddc_ink_drawing-editor .tl-container:focus-within,
.ddc_ink_writing-editor .tl-container:focus-within {
  outline: 1px solid var(--color-low);
}
.ddc_ink_drawing-editor input,
.ddc_ink_drawing-editor *[contenteditable],
.ddc_ink_drawing-editor *[contenteditable] *,
.ddc_ink_writing-editor input,
.ddc_ink_writing-editor *[contenteditable],
.ddc_ink_writing-editor *[contenteditable] * {
  -webkit-user-select: text;
}
.ddc_ink_drawing-editor .tl-canvas,
.ddc_ink_writing-editor .tl-canvas {
  position: absolute;
  inset: 0px;
  height: 100%;
  width: 100%;
  color: var(--color-text);
  z-index: var(--layer-canvas);
  cursor: var(--tl-cursor);
  overflow: clip;
  content-visibility: auto;
  touch-action: none;
  contain: strict;
}
.ddc_ink_drawing-editor .tl-shapes,
.ddc_ink_writing-editor .tl-shapes {
  position: relative;
  z-index: var(--layer-shapes);
}
.ddc_ink_drawing-editor .tl-overlays,
.ddc_ink_writing-editor .tl-overlays {
  position: absolute;
  inset: 0px;
  height: 100%;
  width: 100%;
  contain: strict;
  pointer-events: none;
  z-index: var(--layer-overlays);
}
.ddc_ink_drawing-editor .tl-overlays__item,
.ddc_ink_writing-editor .tl-overlays__item {
  position: absolute;
  top: 0px;
  left: 0px;
  overflow: visible;
  pointer-events: none;
  transform-origin: top left;
}
.ddc_ink_drawing-editor .tl-svg-context,
.ddc_ink_writing-editor .tl-svg-context {
  position: absolute;
  top: 0px;
  left: 0px;
  pointer-events: none;
}
.ddc_ink_drawing-editor .tl-background,
.ddc_ink_writing-editor .tl-background {
  position: absolute;
  background-color: var(--color-background);
  inset: 0px;
  height: 100%;
  width: 100%;
  z-index: var(--layer-background);
}
.ddc_ink_drawing-editor .tl-grid,
.ddc_ink_writing-editor .tl-grid {
  position: absolute;
  inset: 0px;
  width: 100%;
  height: 100%;
  touch-action: none;
  pointer-events: none;
  z-index: var(--layer-grid);
  contain: strict;
}
.ddc_ink_drawing-editor .tl-grid-dot,
.ddc_ink_writing-editor .tl-grid-dot {
  fill: var(--color-grid);
}
.ddc_ink_drawing-editor .tl-html-layer,
.ddc_ink_writing-editor .tl-html-layer {
  position: absolute;
  top: 0px;
  left: 0px;
  width: 1px;
  height: 1px;
  contain: layout style size;
}
.ddc_ink_drawing-editor .tl-brush,
.ddc_ink_writing-editor .tl-brush {
  stroke-width: calc(var(--tl-scale) * 1px);
  contain: size layout;
}
.ddc_ink_drawing-editor .tl-brush__default,
.ddc_ink_writing-editor .tl-brush__default {
  stroke: var(--color-brush-stroke);
  fill: var(--color-brush-fill);
}
.ddc_ink_drawing-editor .tl-scribble,
.ddc_ink_writing-editor .tl-scribble {
  stroke-linejoin: round;
  stroke-linecap: round;
  pointer-events: none;
  contain: size layout;
}
.ddc_ink_drawing-editor .tl-shape,
.ddc_ink_writing-editor .tl-shape {
  position: absolute;
  pointer-events: none;
  overflow: visible;
  transform-origin: top left;
  contain: size layout;
}
.ddc_ink_drawing-editor .tl-shape__culled,
.ddc_ink_writing-editor .tl-shape__culled {
  position: relative;
  background-color: var(--color-culled);
}
.ddc_ink_drawing-editor .tl-svg-container,
.ddc_ink_writing-editor .tl-svg-container {
  position: absolute;
  inset: 0px;
  height: 100%;
  width: 100%;
  pointer-events: none;
  stroke-linecap: round;
  stroke-linejoin: round;
  transform-origin: top left;
  overflow: visible;
}
.ddc_ink_drawing-editor .tl-html-container,
.ddc_ink_writing-editor .tl-html-container {
  position: absolute;
  inset: 0px;
  height: 100%;
  width: 100%;
  pointer-events: none;
  stroke-linecap: round;
  stroke-linejoin: round;
  transform-origin: top left;
  color: inherit;
}
.ddc_ink_drawing-editor .tl-collaborator__scribble,
.ddc_ink_writing-editor .tl-collaborator__scribble {
  z-index: 10;
}
.ddc_ink_drawing-editor .tl-collaborator__brush,
.ddc_ink_writing-editor .tl-collaborator__brush {
  z-index: 20;
}
.ddc_ink_drawing-editor .tl-collaborator__shape-indicator,
.ddc_ink_writing-editor .tl-collaborator__shape-indicator {
  z-index: 30;
}
.ddc_ink_drawing-editor .tl-user-scribble,
.ddc_ink_writing-editor .tl-user-scribble {
  z-index: 40;
}
.ddc_ink_drawing-editor .tl-user-brush,
.ddc_ink_writing-editor .tl-user-brush {
  z-index: 50;
}
.ddc_ink_drawing-editor .tl-user-indicator__selected,
.ddc_ink_writing-editor .tl-user-indicator__selected {
  z-index: 60;
}
.ddc_ink_drawing-editor .tl-user-indicator__hovered,
.ddc_ink_writing-editor .tl-user-indicator__hovered {
  z-index: 70;
}
.ddc_ink_drawing-editor .tl-user-handles,
.ddc_ink_writing-editor .tl-user-handles {
  z-index: 80;
}
.ddc_ink_drawing-editor .tl-user-snapline,
.ddc_ink_writing-editor .tl-user-snapline {
  z-index: 90;
}
.ddc_ink_drawing-editor .tl-selection__fg,
.ddc_ink_writing-editor .tl-selection__fg {
  pointer-events: none;
  z-index: 100;
}
.ddc_ink_drawing-editor .tl-user-indicator__hint,
.ddc_ink_writing-editor .tl-user-indicator__hint {
  z-index: 110;
  stroke-width: calc(2.5px * var(--tl-scale));
}
.ddc_ink_drawing-editor .tl-collaborator__cursor-hint,
.ddc_ink_writing-editor .tl-collaborator__cursor-hint {
  z-index: 120;
}
.ddc_ink_drawing-editor .tl-collaborator__cursor,
.ddc_ink_writing-editor .tl-collaborator__cursor {
  z-index: 130;
}
.ddc_ink_drawing-editor .tl-cursor,
.ddc_ink_writing-editor .tl-cursor {
  overflow: visible;
}
.ddc_ink_drawing-editor .tl-shape-indicator,
.ddc_ink_writing-editor .tl-shape-indicator {
  transform-origin: top left;
  fill: none;
  stroke-width: calc(1.5px * var(--tl-scale));
  contain: size;
}
.ddc_ink_drawing-editor .tl-selection__bg,
.ddc_ink_writing-editor .tl-selection__bg {
  position: absolute;
  top: 0px;
  left: 0px;
  transform-origin: top left;
  background-color: transparent;
  pointer-events: all;
}
.ddc_ink_drawing-editor .tl-selection__fg__outline,
.ddc_ink_writing-editor .tl-selection__fg__outline {
  fill: none;
  pointer-events: none;
  stroke: var(--color-selection-stroke);
  stroke-width: calc(1.5px * var(--tl-scale));
}
.ddc_ink_drawing-editor .tl-corner-handle,
.ddc_ink_writing-editor .tl-corner-handle {
  pointer-events: none;
  stroke: var(--color-selection-stroke);
  fill: var(--color-background);
  stroke-width: calc(1.5px * var(--tl-scale));
}
.ddc_ink_drawing-editor .tl-text-handle,
.ddc_ink_writing-editor .tl-text-handle {
  pointer-events: none;
  fill: var(--color-selection-stroke);
}
.ddc_ink_drawing-editor .tl-corner-crop-handle,
.ddc_ink_writing-editor .tl-corner-crop-handle {
  pointer-events: none;
  fill: none;
  stroke: var(--color-selection-stroke);
}
.ddc_ink_drawing-editor .tl-corner-crop-edge-handle,
.ddc_ink_writing-editor .tl-corner-crop-edge-handle {
  pointer-events: none;
  fill: none;
  stroke: var(--color-selection-stroke);
}
.ddc_ink_drawing-editor .tl-mobile-rotate__bg,
.ddc_ink_writing-editor .tl-mobile-rotate__bg {
  pointer-events: all;
  cursor: var(--tl-cursor-grab);
}
.ddc_ink_drawing-editor .tl-mobile-rotate__fg,
.ddc_ink_writing-editor .tl-mobile-rotate__fg {
  pointer-events: none;
  stroke: var(--color-selection-stroke);
  fill: var(--color-background);
  stroke-width: calc(1.5px * var(--tl-scale));
}
.ddc_ink_drawing-editor .tl-transparent,
.ddc_ink_writing-editor .tl-transparent {
  fill: transparent;
  stroke: transparent;
}
.ddc_ink_drawing-editor .tl-handle,
.ddc_ink_writing-editor .tl-handle {
  pointer-events: all;
}
.ddc_ink_drawing-editor .tl-handle__bg,
.ddc_ink_writing-editor .tl-handle__bg {
  fill: transparent;
  stroke: transparent;
  pointer-events: all;
}
.ddc_ink_drawing-editor .tl-handle__fg,
.ddc_ink_writing-editor .tl-handle__fg {
  fill: var(--color-selected-contrast);
  stroke: var(--color-selection-stroke);
  stroke-width: calc(1.5px * var(--tl-scale));
  pointer-events: none;
}
.ddc_ink_drawing-editor .tl-handle__create,
.ddc_ink_writing-editor .tl-handle__create {
  opacity: 0;
}
.ddc_ink_drawing-editor .tl-handle__create:hover,
.ddc_ink_writing-editor .tl-handle__create:hover {
  opacity: 1;
}
.ddc_ink_drawing-editor .tl-handle__bg:active,
.ddc_ink_writing-editor .tl-handle__bg:active {
  fill: none;
}
.ddc_ink_drawing-editor .tl-handle__bg:hover,
.ddc_ink_writing-editor .tl-handle__bg:hover {
  cursor: var(--tl-cursor-grab);
  fill: var(--color-selection-fill);
}
@media (pointer: coarse) {
  .ddc_ink_drawing-editor .tl-handle__bg:active,
  .ddc_ink_writing-editor .tl-handle__bg:active {
    fill: var(--color-selection-fill);
  }
  .ddc_ink_drawing-editor .tl-handle__create,
  .ddc_ink_writing-editor .tl-handle__create {
    opacity: 1;
  }
}
.ddc_ink_drawing-editor .tl-image,
.ddc_ink_drawing-editor .tl-video,
.ddc_ink_writing-editor .tl-image,
.ddc_ink_writing-editor .tl-video {
  object-fit: cover;
  background-size: cover;
  width: 100%;
  height: 100%;
}
.ddc_ink_drawing-editor .tl-image-container,
.ddc_ink_drawing-editor .tl-embed-container,
.ddc_ink_writing-editor .tl-image-container,
.ddc_ink_writing-editor .tl-embed-container {
  width: 100%;
  height: 100%;
  pointer-events: all;
  display: flex;
  justify-content: center;
  align-items: center;
}
.ddc_ink_drawing-editor .tl-image__tg,
.ddc_ink_writing-editor .tl-image__tg {
  --scale: calc(min(2, var(--tl-scale)));
  position: absolute;
  top: calc(var(--scale) * 8px);
  right: calc(var(--scale) * 8px);
  font-size: 10px;
  transform-origin: top right;
  background-color: var(--color-background);
  padding: 2px 4px;
  border-radius: var(--radius-1);
}
.ddc_ink_drawing-editor .tl-collaborator-cursor,
.ddc_ink_writing-editor .tl-collaborator-cursor {
  position: absolute;
}
.ddc_ink_drawing-editor .tl-nametag,
.ddc_ink_writing-editor .tl-nametag {
  position: absolute;
  top: 16px;
  left: 13px;
  width: fit-content;
  height: fit-content;
  max-width: 120px;
  padding: 3px 6px;
  white-space: nowrap;
  position: absolute;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 12px;
  font-family: var(--font-body);
  border-radius: var(--radius-2);
  color: var(--color-selected-contrast);
}
.ddc_ink_drawing-editor .tl-nametag-title,
.ddc_ink_writing-editor .tl-nametag-title {
  position: absolute;
  top: -2px;
  left: 13px;
  width: fit-content;
  height: fit-content;
  padding: 0px 6px;
  max-width: 120px;
  white-space: nowrap;
  position: absolute;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 12px;
  font-family: var(--font-body);
  text-shadow: var(--tl-text-outline);
  color: var(--color-selected-contrast);
}
.ddc_ink_drawing-editor .tl-nametag-chat,
.ddc_ink_writing-editor .tl-nametag-chat {
  position: absolute;
  top: 16px;
  left: 13px;
  width: fit-content;
  height: fit-content;
  color: var(--color-selected-contrast);
  white-space: nowrap;
  position: absolute;
  padding: 3px 6px;
  font-size: 12px;
  font-family: var(--font-body);
  opacity: 1;
  border-radius: var(--radius-2);
}
.ddc_ink_drawing-editor .tl-cursor-chat,
.ddc_ink_writing-editor .tl-cursor-chat {
  position: absolute;
  color: var(--color-selected-contrast);
  white-space: nowrap;
  padding: 3px 6px;
  font-size: 12px;
  font-family: var(--font-body);
  pointer-events: none;
  z-index: var(--layer-cursor);
  margin-top: 16px;
  margin-left: 13px;
  opacity: 1;
  border: none;
  user-select: text;
  border-radius: var(--radius-2);
}
.ddc_ink_drawing-editor .tl-cursor-chat .tl-cursor-chat__bubble,
.ddc_ink_writing-editor .tl-cursor-chat .tl-cursor-chat__bubble {
  padding-right: 12px;
}
.ddc_ink_drawing-editor .tl-cursor-chat::selection,
.ddc_ink_writing-editor .tl-cursor-chat::selection {
  background: var(--color-selected);
  color: var(--color-selected-contrast);
  text-shadow: none;
}
.ddc_ink_drawing-editor .tl-cursor-chat-fade,
.ddc_ink_writing-editor .tl-cursor-chat-fade {
  opacity: 0.0001;
  transition: opacity 5s ease-in-out;
}
.ddc_ink_drawing-editor .tl-cursor-chat::placeholder,
.ddc_ink_writing-editor .tl-cursor-chat::placeholder {
  color: var(--color-selected-contrast);
  opacity: 0.7;
}
@keyframes spinner {
  to {
    transform: rotate(360deg);
  }
}
.ddc_ink_drawing-editor .tl-text-shape__wrapper,
.ddc_ink_writing-editor .tl-text-shape__wrapper {
  position: relative;
  font-weight: normal;
  min-width: 1px;
  padding: 0px;
  margin: 0px;
  border: none;
  height: 100%;
  font-variant: normal;
  font-style: normal;
  pointer-events: all;
  white-space: pre-wrap;
  overflow-wrap: break-word;
  text-shadow: var(--tl-text-outline);
}
.ddc_ink_drawing-editor .tl-text-shape__wrapper[data-align=start],
.ddc_ink_writing-editor .tl-text-shape__wrapper[data-align=start] {
  text-align: left;
}
.ddc_ink_drawing-editor .tl-text-shape__wrapper[data-align=middle],
.ddc_ink_writing-editor .tl-text-shape__wrapper[data-align=middle] {
  text-align: center;
}
.ddc_ink_drawing-editor .tl-text-shape__wrapper[data-align=end],
.ddc_ink_writing-editor .tl-text-shape__wrapper[data-align=end] {
  text-align: right;
}
.ddc_ink_drawing-editor .tl-text-shape__wrapper[data-font=draw],
.ddc_ink_writing-editor .tl-text-shape__wrapper[data-font=draw] {
  font-family: var(--tl-font-draw);
}
.ddc_ink_drawing-editor .tl-text-shape__wrapper[data-font=sans],
.ddc_ink_writing-editor .tl-text-shape__wrapper[data-font=sans] {
  font-family: var(--tl-font-sans);
}
.ddc_ink_drawing-editor .tl-text-shape__wrapper[data-font=serif],
.ddc_ink_writing-editor .tl-text-shape__wrapper[data-font=serif] {
  font-family: var(--tl-font-serif);
}
.ddc_ink_drawing-editor .tl-text-shape__wrapper[data-font=mono],
.ddc_ink_writing-editor .tl-text-shape__wrapper[data-font=mono] {
  font-family: var(--tl-font-mono);
}
.ddc_ink_drawing-editor .tl-text-shape__wrapper[data-isediting=true] .tl-text-content,
.ddc_ink_writing-editor .tl-text-shape__wrapper[data-isediting=true] .tl-text-content {
  opacity: 0;
}
.ddc_ink_drawing-editor .tl-text,
.ddc_ink_writing-editor .tl-text {
  margin: 0px;
  padding: 0px;
  border: 0px;
  color: inherit;
  caret-color: var(--color-text);
  background: none;
  border-image: none;
  font-size: inherit;
  font-family: inherit;
  font-weight: inherit;
  line-height: inherit;
  font-variant: inherit;
  font-style: inherit;
  text-align: inherit;
  letter-spacing: inherit;
  text-shadow: inherit;
  outline: none;
  white-space: pre-wrap;
  word-wrap: break-word;
  overflow-wrap: break-word;
  pointer-events: all;
  text-rendering: auto;
  text-transform: none;
  text-indent: 0px;
  display: inline-block;
  appearance: auto;
  column-count: initial !important;
  writing-mode: horizontal-tb !important;
  word-spacing: 0px;
}
.ddc_ink_drawing-editor .tl-text-measure,
.ddc_ink_writing-editor .tl-text-measure {
  position: absolute;
  z-index: -999999;
  top: 0px;
  left: 0px;
  opacity: 0;
  width: max-content;
  box-sizing: border-box;
  pointer-events: none;
  line-break: normal;
  white-space: pre-wrap;
  word-wrap: break-word;
  overflow-wrap: break-word;
  resize: none;
  border: none;
  user-select: none;
  contain: style paint;
  -webkit-user-select: none;
}
.ddc_ink_drawing-editor .tl-text-input,
.ddc_ink_drawing-editor .tl-text-content,
.ddc_ink_writing-editor .tl-text-input,
.ddc_ink_writing-editor .tl-text-content {
  position: absolute;
  inset: 0px;
  height: 100%;
  width: 100%;
  min-width: 1px;
  min-height: 1px;
  overflow: visible;
  outline: none;
}
.ddc_ink_drawing-editor .tl-text-content,
.ddc_ink_writing-editor .tl-text-content {
  pointer-events: none;
}
.ddc_ink_drawing-editor .tl-text-input,
.ddc_ink_writing-editor .tl-text-input {
  resize: none;
  user-select: all;
  -webkit-user-select: text;
  overflow: hidden;
  cursor: var(--tl-cursor-text);
}
.ddc_ink_drawing-editor .tl-text-input::selection,
.ddc_ink_writing-editor .tl-text-input::selection {
  background: var(--color-selected);
  color: var(--color-selected-contrast);
  text-shadow: none;
}
.ddc_ink_drawing-editor .tl-snap-indicator,
.ddc_ink_writing-editor .tl-snap-indicator {
  stroke: var(--color-accent);
  stroke-width: calc(1px * var(--tl-scale));
  fill: none;
}
.ddc_ink_drawing-editor .tl-snap-point,
.ddc_ink_writing-editor .tl-snap-point {
  stroke: var(--color-accent);
  stroke-width: calc(1px * var(--tl-scale));
  fill: none;
}
.ddc_ink_drawing-editor .tl-group,
.ddc_ink_writing-editor .tl-group {
  stroke: var(--color-text);
  stroke-width: calc(1px * var(--tl-scale));
  opacity: 0.5;
}
.ddc_ink_drawing-editor .tl-bookmark__container,
.ddc_ink_writing-editor .tl-bookmark__container {
  width: 100%;
  height: 100%;
  position: relative;
  border: 1px solid var(--color-panel-contrast);
  background-color: var(--color-panel);
  border-radius: var(--radius-2);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}
.ddc_ink_drawing-editor .tl-bookmark__image_container,
.ddc_ink_writing-editor .tl-bookmark__image_container {
  flex: 1;
  overflow: hidden;
  border-top-left-radius: var(--radius-1);
  border-top-right-radius: var(--radius-1);
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: flex-end;
  align-items: flex-start;
}
.ddc_ink_drawing-editor .tl-bookmark__image_container > .tl-hyperlink-button::after,
.ddc_ink_writing-editor .tl-bookmark__image_container > .tl-hyperlink-button::after {
  background-color: var(--color-panel);
}
.ddc_ink_drawing-editor .tl-bookmark__placeholder,
.ddc_ink_writing-editor .tl-bookmark__placeholder {
  width: 100%;
  height: 100%;
  background-color: var(--color-muted-2);
  border-bottom: 1px solid var(--color-muted-2);
}
.ddc_ink_drawing-editor .tl-bookmark__image,
.ddc_ink_writing-editor .tl-bookmark__image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
}
.ddc_ink_drawing-editor .tl-bookmark__copy_container,
.ddc_ink_writing-editor .tl-bookmark__copy_container {
  background-color: var(--color-muted);
  padding: var(--space-4);
  pointer-events: all;
}
.ddc_ink_drawing-editor .tl-bookmark__heading,
.ddc_ink_drawing-editor .tl-bookmark__description,
.ddc_ink_drawing-editor .tl-bookmark__link,
.ddc_ink_writing-editor .tl-bookmark__heading,
.ddc_ink_writing-editor .tl-bookmark__description,
.ddc_ink_writing-editor .tl-bookmark__link {
  margin: 0px;
  width: 100%;
  font-family: inherit;
}
.ddc_ink_drawing-editor .tl-bookmark__heading,
.ddc_ink_writing-editor .tl-bookmark__heading {
  font-size: 16px;
  font-weight: bold;
  padding-bottom: var(--space-2);
  margin: 8px 0px;
}
.ddc_ink_drawing-editor .tl-bookmark__description,
.ddc_ink_writing-editor .tl-bookmark__description {
  font-size: 12px;
  padding-bottom: var(--space-4);
}
.ddc_ink_drawing-editor .tl-bookmark__link,
.ddc_ink_writing-editor .tl-bookmark__link {
  font-size: 14px;
  pointer-events: all;
  z-index: 999;
  overflow: hidden;
  display: block;
  color: var(--color-text);
  text-overflow: ellipsis;
  text-decoration: none;
  color: var(--color-text-1);
  cursor: var(--tl-cursor-pointer);
}
.ddc_ink_drawing-editor .tl-bookmark__link:hover,
.ddc_ink_writing-editor .tl-bookmark__link:hover {
  color: var(--color-selected);
}
.ddc_ink_drawing-editor .tl-hyperlink-button,
.ddc_ink_writing-editor .tl-hyperlink-button {
  background: none;
  margin: 0px;
  position: absolute;
  top: 0px;
  right: 0px;
  height: 44px;
  width: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 200;
  font-size: 12px;
  font-weight: 400;
  color: var(--color-text-1);
  padding: 13px;
  cursor: var(--tl-cursor-pointer);
  border: none;
  outline: none;
  pointer-events: all;
}
.ddc_ink_drawing-editor .tl-hyperlink-button::after,
.ddc_ink_writing-editor .tl-hyperlink-button::after {
  content: "";
  z-index: -1;
  position: absolute;
  right: 6px;
  bottom: 6px;
  display: block;
  width: calc(100% - 12px);
  height: calc(100% - 12px);
  border-radius: var(--radius-1);
  background-color: var(--color-background);
  pointer-events: none;
}
.ddc_ink_drawing-editor .tl-hyperlink-button:hover,
.ddc_ink_writing-editor .tl-hyperlink-button:hover {
  color: var(--color-selected);
}
.ddc_ink_drawing-editor .tl-hyperlink-button:focus-visible,
.ddc_ink_writing-editor .tl-hyperlink-button:focus-visible {
  color: var(--color-selected);
}
.ddc_ink_drawing-editor .tl-hyperlink-button__icon,
.ddc_ink_writing-editor .tl-hyperlink-button__icon {
  width: 18px;
  height: 18px;
  background-color: currentColor;
  pointer-events: none;
}
.ddc_ink_drawing-editor .tl-hyperlink-button__hidden,
.ddc_ink_writing-editor .tl-hyperlink-button__hidden {
  display: none;
}
.ddc_ink_drawing-editor .tl-text-label,
.ddc_ink_writing-editor .tl-text-label {
  display: flex;
  justify-content: center;
  align-items: center;
  color: var(--color-text);
  text-shadow: var(--tl-text-outline);
  line-height: inherit;
  position: absolute;
  inset: 0px;
  height: 100%;
  width: 100%;
  z-index: 10;
}
.ddc_ink_drawing-editor .tl-text-label[data-isediting=true] .tl-text-content,
.ddc_ink_writing-editor .tl-text-label[data-isediting=true] .tl-text-content {
  opacity: 0;
}
.ddc_ink_drawing-editor .tl-text-label[data-hastext=false][data-isediting=false] > .tl-text-label__inner,
.ddc_ink_writing-editor .tl-text-label[data-hastext=false][data-isediting=false] > .tl-text-label__inner {
  width: 40px;
  height: 40px;
}
.ddc_ink_drawing-editor .tl-text-label[data-hastext=true][data-isediting=false] .tl-text-content,
.ddc_ink_writing-editor .tl-text-label[data-hastext=true][data-isediting=false] .tl-text-content {
  pointer-events: all;
}
.ddc_ink_drawing-editor .tl-text-label__inner,
.ddc_ink_writing-editor .tl-text-label__inner {
  position: relative;
  width: fit-content;
  height: fit-content;
  display: flex;
  align-items: center;
  justify-content: center;
  pointer-events: none;
  min-height: auto;
}
.ddc_ink_drawing-editor .tl-text-label__inner > .tl-text-content,
.ddc_ink_writing-editor .tl-text-label__inner > .tl-text-content {
  position: relative;
  top: 0px;
  left: 0px;
  padding: 16px;
  height: fit-content;
  width: fit-content;
  border-radius: var(--radius-1);
  max-width: 100%;
  z-index: 3;
}
.ddc_ink_drawing-editor .tl-text-label__inner > .tl-text-input,
.ddc_ink_writing-editor .tl-text-label__inner > .tl-text-input {
  position: absolute;
  inset: 0px;
  height: 100%;
  width: 100%;
  padding: 16px;
  z-index: 4;
}
.ddc_ink_drawing-editor .tl-text-label[data-textwrap=true] > .tl-text-label__inner,
.ddc_ink_writing-editor .tl-text-label[data-textwrap=true] > .tl-text-label__inner {
  max-width: 100%;
}
.ddc_ink_drawing-editor .tl-text-label[data-isediting=true],
.ddc_ink_writing-editor .tl-text-label[data-isediting=true] {
  background-color: transparent;
  min-height: auto;
}
.ddc_ink_drawing-editor .tl-text-label[data-isediting=true] p,
.ddc_ink_writing-editor .tl-text-label[data-isediting=true] p {
  opacity: 0;
}
.ddc_ink_drawing-editor .tl-text-label[data-align=start],
.ddc_ink_drawing-editor .tl-text-label[data-align=start-legacy],
.ddc_ink_writing-editor .tl-text-label[data-align=start],
.ddc_ink_writing-editor .tl-text-label[data-align=start-legacy] {
  text-align: left;
}
.ddc_ink_drawing-editor .tl-text-label[data-align=middle],
.ddc_ink_drawing-editor .tl-text-label[data-align=middle-legacy],
.ddc_ink_writing-editor .tl-text-label[data-align=middle],
.ddc_ink_writing-editor .tl-text-label[data-align=middle-legacy] {
  text-align: center;
}
.ddc_ink_drawing-editor .tl-text-label[data-align=end],
.ddc_ink_drawing-editor .tl-text-label[data-align=end-legacy],
.ddc_ink_writing-editor .tl-text-label[data-align=end],
.ddc_ink_writing-editor .tl-text-label[data-align=end-legacy] {
  text-align: right;
}
.ddc_ink_drawing-editor .tl-arrow-hint,
.ddc_ink_writing-editor .tl-arrow-hint {
  stroke: var(--color-text-1);
  fill: none;
  stroke-linecap: round;
  overflow: visible;
}
.ddc_ink_drawing-editor .tl-arrow-label[data-font=draw],
.ddc_ink_drawing-editor .tl-text-label[data-font=draw],
.ddc_ink_writing-editor .tl-arrow-label[data-font=draw],
.ddc_ink_writing-editor .tl-text-label[data-font=draw] {
  font-family: var(--tl-font-draw);
}
.ddc_ink_drawing-editor .tl-arrow-label[data-font=sans],
.ddc_ink_drawing-editor .tl-text-label[data-font=sans],
.ddc_ink_writing-editor .tl-arrow-label[data-font=sans],
.ddc_ink_writing-editor .tl-text-label[data-font=sans] {
  font-family: var(--tl-font-sans);
}
.ddc_ink_drawing-editor .tl-arrow-label[data-font=serif],
.ddc_ink_drawing-editor .tl-text-label[data-font=serif],
.ddc_ink_writing-editor .tl-arrow-label[data-font=serif],
.ddc_ink_writing-editor .tl-text-label[data-font=serif] {
  font-family: var(--tl-font-serif);
}
.ddc_ink_drawing-editor .tl-arrow-label[data-font=mono],
.ddc_ink_drawing-editor .tl-text-label[data-font=mono],
.ddc_ink_writing-editor .tl-arrow-label[data-font=mono],
.ddc_ink_writing-editor .tl-text-label[data-font=mono] {
  font-family: var(--tl-font-mono);
}
.ddc_ink_drawing-editor .tl-arrow-label,
.ddc_ink_writing-editor .tl-arrow-label {
  position: absolute;
  top: -1px;
  left: -1px;
  width: 2px;
  height: 2px;
  padding: 0px;
  display: flex;
  justify-content: center;
  align-items: center;
  color: var(--color-text);
  text-shadow: var(--tl-text-outline);
}
.ddc_ink_drawing-editor .tl-arrow-label[data-isediting=true] p,
.ddc_ink_writing-editor .tl-arrow-label[data-isediting=true] p {
  opacity: 0;
}
.ddc_ink_drawing-editor .tl-arrow-label[data-isediting=true] > .tl-arrow-label__inner,
.ddc_ink_writing-editor .tl-arrow-label[data-isediting=true] > .tl-arrow-label__inner {
  background-color: var(--color-background);
}
.ddc_ink_drawing-editor .tl-arrow-label__inner,
.ddc_ink_writing-editor .tl-arrow-label__inner {
  border-radius: var(--radius-1);
  box-sizing: content-box;
  position: relative;
  height: max-content;
  width: max-content;
  pointer-events: all;
  display: flex;
  justify-content: center;
  align-items: center;
}
.ddc_ink_drawing-editor .tl-arrow-label p,
.ddc_ink_drawing-editor .tl-arrow-label textarea,
.ddc_ink_writing-editor .tl-arrow-label p,
.ddc_ink_writing-editor .tl-arrow-label textarea {
  margin: 0px;
  padding: 0px;
  border: 0px;
  color: inherit;
  caret-color: var(--color-text);
  background: none;
  border-image: none;
  font-size: inherit;
  font-family: inherit;
  font-weight: inherit;
  line-height: inherit;
  font-variant: inherit;
  font-style: inherit;
  text-align: inherit;
  letter-spacing: inherit;
  text-shadow: inherit;
  outline: none;
  white-space: pre-wrap;
  word-wrap: break-word;
  overflow-wrap: break-word;
  pointer-events: all;
  text-rendering: auto;
  text-transform: none;
  text-indent: 0px;
  display: inline-block;
  appearance: auto;
  column-count: initial !important;
  writing-mode: horizontal-tb !important;
  word-spacing: 0px;
}
.ddc_ink_drawing-editor .tl-arrow-label p,
.ddc_ink_writing-editor .tl-arrow-label p {
  position: relative;
  height: max-content;
  z-index: 2;
  padding: 4px;
  overflow: visible;
}
.ddc_ink_drawing-editor .tl-arrow-label textarea,
.ddc_ink_writing-editor .tl-arrow-label textarea {
  z-index: 3;
  margin: 0px;
  padding: 4px;
  height: 100%;
  width: 100%;
  position: absolute;
  resize: none;
  border: 0px;
  user-select: all;
  -webkit-user-select: text;
  caret-color: var(--color-text);
  border-image: none;
  min-width: 4px;
}
.ddc_ink_drawing-editor .tl-note__container,
.ddc_ink_writing-editor .tl-note__container {
  position: relative;
  width: 100%;
  height: 100%;
  border-radius: var(--radius-2);
  box-shadow: var(--shadow-1);
  overflow: hidden;
  border-color: currentColor;
  border-style: solid;
  border-width: 1px;
}
.ddc_ink_drawing-editor .tl-note__container .tl-text-label,
.ddc_ink_writing-editor .tl-note__container .tl-text-label {
  text-shadow: none;
}
.ddc_ink_drawing-editor .tl-note__scrim,
.ddc_ink_writing-editor .tl-note__scrim {
  position: absolute;
  z-index: 1;
  inset: 0px;
  height: 100%;
  width: 100%;
  background-color: var(--color-background);
  opacity: 0.28;
}
.ddc_ink_drawing-editor .tl-loading,
.ddc_ink_writing-editor .tl-loading {
  background-color: var(--color-background);
  color: var(--color-text-1);
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: var(--space-2);
  font-size: 14px;
  font-weight: 500;
  opacity: 0;
  animation: fade-in 0.2s ease-in-out forwards;
  animation-delay: 0.2s;
}
@keyframes fade-in {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
.ddc_ink_drawing-editor .tl-frame__body,
.ddc_ink_writing-editor .tl-frame__body {
  stroke-width: calc(1px * var(--tl-scale));
}
.ddc_ink_drawing-editor .tl-frame__creating,
.ddc_ink_writing-editor .tl-frame__creating {
  stroke: var(--color-selected);
  fill: none;
}
.ddc_ink_drawing-editor .tl-frame-heading,
.ddc_ink_writing-editor .tl-frame-heading {
  display: flex;
  align-items: center;
  position: absolute;
  transform-origin: 0% 100%;
  overflow: hidden;
  max-width: 100%;
  min-width: 32px;
  height: auto;
  font-size: 12px;
  padding-bottom: 4px;
  pointer-events: all;
}
.ddc_ink_drawing-editor .tl-frame-heading-hit-area,
.ddc_ink_writing-editor .tl-frame-heading-hit-area {
  pointer-events: all;
  transform-origin: 0% 100%;
  display: flex;
  height: 100%;
  width: 100%;
  align-items: center;
  border-radius: var(--radius-1);
  background-color: var(--color-background);
}
.ddc_ink_drawing-editor .tl-frame-label,
.ddc_ink_writing-editor .tl-frame-label {
  pointer-events: all;
  overflow: hidden;
  text-overflow: ellipsis;
  padding: var(--space-3) var(--space-3);
  position: relative;
  font-size: inherit;
  white-space: pre;
  border: 1px solid transparent;
}
.ddc_ink_drawing-editor .tl-frame-label__editing,
.ddc_ink_writing-editor .tl-frame-label__editing {
  color: transparent;
  white-space: pre;
  width: auto;
  overflow: visible;
  background-color: var(--color-panel);
  border-radius: var(--radius-1);
  border-color: var(--color-selected);
}
.ddc_ink_drawing-editor .tl-frame-name-input,
.ddc_ink_writing-editor .tl-frame-name-input {
  position: absolute;
  border: none;
  background: none;
  outline: none;
  padding: var(--space-3) var(--space-3);
  inset: 0px;
  height: 100%;
  width: 100%;
  font-size: inherit;
  font-family: inherit;
  font-weight: inherit;
  width: 100%;
  color: var(--color-text-1);
  border-radius: var(--radius-1);
  user-select: all;
  -webkit-user-select: text;
  white-space: pre;
  cursor: var(--tl-cursor-text);
}
@media (max-width: 600px) {
  .ddc_ink_drawing-editor .tl-frame-heading,
  .ddc_ink_writing-editor .tl-frame-heading {
    font-size: 16px;
  }
}
.ddc_ink_drawing-editor .tl-embed,
.ddc_ink_writing-editor .tl-embed {
  border: none;
  border-radius: var(--radius-2);
}
.ddc_ink_drawing-editor .tl-shape-error-boundary,
.ddc_ink_writing-editor .tl-shape-error-boundary {
  width: 100%;
  height: 100%;
  background-color: var(--color-muted-1);
  border-width: calc(1px * var(--tl-scale));
  border-color: var(--color-muted-1);
  border-style: solid;
  border-radius: calc(var(--radius-1) * var(--tl-scale));
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: left;
  position: relative;
  pointer-events: all;
  overflow: hidden;
  padding: var(--space-2);
}
.ddc_ink_drawing-editor .tl-shape-error-boundary::before,
.ddc_ink_writing-editor .tl-shape-error-boundary::before {
  transform: scale(var(--tl-scale));
  content: "Error";
  font-size: 12px;
  font-family: inherit;
  color: var(--color-text-0);
}
.ddc_ink_drawing-editor .tl-error-boundary,
.ddc_ink_writing-editor .tl-error-boundary {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-4);
  background-color: var(--color-background);
  color: var(--color-text-1);
  position: absolute;
  z-index: 600;
}
.ddc_ink_drawing-editor .tl-error-boundary__overlay,
.ddc_ink_writing-editor .tl-error-boundary__overlay {
  position: absolute;
  inset: 0px;
  height: 100%;
  width: 100%;
  z-index: 500;
  background-color: var(--color-overlay);
}
.ddc_ink_drawing-editor .tl-error-boundary__content *,
.ddc_ink_writing-editor .tl-error-boundary__content * {
  user-select: all;
  -webkit-user-select: text;
  pointer-events: all;
}
.ddc_ink_drawing-editor .tl-error-boundary__canvas,
.ddc_ink_writing-editor .tl-error-boundary__canvas {
  pointer-events: none;
  position: absolute;
  inset: 0px;
  height: 100%;
  width: 100%;
  z-index: -1;
}
.ddc_ink_drawing-editor .tl-error-boundary__canvas::after,
.ddc_ink_writing-editor .tl-error-boundary__canvas::after {
  content: " ";
  display: block;
  position: absolute;
  inset: 0px;
  height: 100%;
  width: 100%;
  z-index: 600;
  pointer-events: all;
}
.ddc_ink_drawing-editor .tl-error-boundary__content,
.ddc_ink_writing-editor .tl-error-boundary__content {
  width: fit-content;
  height: fit-content;
  max-width: 100%;
  width: 400px;
  max-height: 100%;
  background-color: var(--color-panel);
  padding: 16px;
  border-radius: 16px;
  box-shadow: var(--shadow-2);
  font-size: 14px;
  font-weight: 400;
  display: flex;
  flex-direction: column;
  overflow: auto;
  z-index: 600;
  gap: 12px;
}
.ddc_ink_drawing-editor .tl-error-boundary__content__expanded,
.ddc_ink_writing-editor .tl-error-boundary__content__expanded {
  width: 600px;
}
.ddc_ink_drawing-editor .tl-error-boundary__content h2,
.ddc_ink_writing-editor .tl-error-boundary__content h2 {
  font-size: 16px;
  margin: 0px;
  font-weight: 500;
}
.ddc_ink_drawing-editor .tl-error-boundary__content h4,
.ddc_ink_writing-editor .tl-error-boundary__content h4 {
  border: 1px solid var(--color-low-border);
  margin: -6px 0 0 0;
  padding: var(--space-5);
  border-radius: var(--radius-2);
  font-weight: normal;
}
.ddc_ink_drawing-editor .tl-error-boundary__content p,
.ddc_ink_writing-editor .tl-error-boundary__content p {
  line-height: 1.5;
  margin: 0px;
}
.ddc_ink_drawing-editor .tl-error-boundary__content pre,
.ddc_ink_writing-editor .tl-error-boundary__content pre {
  background-color: var(--color-muted-2);
  margin-top: 0;
  padding: var(--space-5);
  border-radius: var(--radius-2);
  overflow: auto;
  font-size: 12px;
  max-height: 320px;
}
.ddc_ink_drawing-editor .tl-error-boundary__content button,
.ddc_ink_writing-editor .tl-error-boundary__content button {
  background: none;
  border: none;
  font-family: inherit;
  font-size: 14px;
  font-weight: 500;
  padding: var(--space-4);
  border-radius: var(--radius-3);
  cursor: var(--tl-cursor-pointer);
  color: inherit;
  background-color: transparent;
}
.ddc_ink_drawing-editor .tl-error-boundary__content button:hover,
.ddc_ink_writing-editor .tl-error-boundary__content button:hover {
  background-color: var(--color-low);
}
.ddc_ink_drawing-editor .tl-error-boundary__content a,
.ddc_ink_writing-editor .tl-error-boundary__content a {
  color: var(--color-text-1);
  font-weight: 500;
  text-decoration: none;
}
.ddc_ink_drawing-editor .tl-error-boundary__content a:hover,
.ddc_ink_writing-editor .tl-error-boundary__content a:hover {
  color: var(--color-text-1);
}
.ddc_ink_drawing-editor .tl-error-boundary__content__error,
.ddc_ink_writing-editor .tl-error-boundary__content__error {
  position: relative;
  margin: -6px 0 0 0;
}
.ddc_ink_drawing-editor .tl-error-boundary__content__error button,
.ddc_ink_writing-editor .tl-error-boundary__content__error button {
  position: absolute;
  top: var(--space-2);
  right: var(--space-2);
  font-size: 12px;
  padding: var(--space-2) var(--space-3);
  background-color: var(--color-panel);
  border-radius: var(--radius-1);
}
.ddc_ink_drawing-editor .tl-error-boundary__content__actions,
.ddc_ink_writing-editor .tl-error-boundary__content__actions {
  display: flex;
  justify-content: space-between;
  gap: var(--space-4);
  margin: 0px;
  margin-left: -4px;
}
.ddc_ink_drawing-editor .tl-error-boundary__content__actions__group,
.ddc_ink_writing-editor .tl-error-boundary__content__actions__group {
  display: flex;
  gap: var(--space-4);
}
.ddc_ink_drawing-editor .tl-error-boundary__content .tl-error-boundary__reset,
.ddc_ink_writing-editor .tl-error-boundary__content .tl-error-boundary__reset {
  color: var(--color-warn);
}
.ddc_ink_drawing-editor .tl-error-boundary__content .tl-error-boundary__refresh,
.ddc_ink_writing-editor .tl-error-boundary__content .tl-error-boundary__refresh {
  background-color: var(--color-primary);
  color: var(--color-selected-contrast);
}
.ddc_ink_drawing-editor .tl-error-boundary__content .tl-error-boundary__refresh:hover,
.ddc_ink_writing-editor .tl-error-boundary__content .tl-error-boundary__refresh:hover {
  background-color: var(--color-primary);
  opacity: 0.9;
}
.ddc_ink_drawing-editor .tl-hidden,
.ddc_ink_writing-editor .tl-hidden {
  opacity: 0;
  pointer-events: none;
}
.ddc_ink_drawing-editor .debug__ui-logger,
.ddc_ink_writing-editor .debug__ui-logger {
  position: absolute;
  top: 62px;
  left: 16px;
  color: #555;
  font-size: 12px;
  font-family: monospace;
}
.ddc_ink_drawing-editor .tl-container,
.ddc_ink_writing-editor .tl-container {
  --layer-panels: 300;
  --layer-menus: 400;
  --layer-overlays: 500;
  --layer-toasts: 650;
  --layer-cursor: 700;
}
.ddc_ink_drawing-editor .tlui-button,
.ddc_ink_writing-editor .tlui-button {
  position: relative;
  height: 40px;
  min-width: 40px;
  padding: 0px 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: transparent;
  border: transparent;
  color: currentColor;
  cursor: pointer;
  pointer-events: all;
  font-weight: inherit;
  font-family: inherit;
  text-rendering: optimizeLegibility;
  font-size: 12px;
  gap: 0px;
  color: var(--color-text-1);
}
.ddc_ink_drawing-editor .tlui-button:disabled,
.ddc_ink_writing-editor .tlui-button:disabled {
  color: var(--color-text-3);
  text-shadow: none;
  cursor: default;
}
.ddc_ink_drawing-editor .tlui-button:disabled .tlui-kbd,
.ddc_ink_writing-editor .tlui-button:disabled .tlui-kbd {
  color: var(--color-text-3);
}
.ddc_ink_drawing-editor .tlui-button > *,
.ddc_ink_writing-editor .tlui-button > * {
  position: relative;
  z-index: 1;
}
.ddc_ink_drawing-editor .tlui-button__label,
.ddc_ink_writing-editor .tlui-button__label {
  flex-grow: 2;
  text-align: left;
}
.ddc_ink_drawing-editor .tlui-button:focus-visible:not(:hover),
.ddc_ink_writing-editor .tlui-button:focus-visible:not(:hover) {
  outline: 1px solid var(--color-selected);
  outline-offset: -4px;
  border-radius: var(--radius-3);
}
.ddc_ink_drawing-editor .tlui-button::after,
.ddc_ink_writing-editor .tlui-button::after {
  display: block;
  content: "";
  position: absolute;
  inset: 4px;
  background-color: transparent;
  border-radius: var(--radius-2);
}
.ddc_ink_drawing-editor .tlui-button[aria-expanded=true]::after,
.ddc_ink_writing-editor .tlui-button[aria-expanded=true]::after {
  background-color: var(--color-muted-0);
  opacity: 1;
}
.ddc_ink_drawing-editor .tlui-button__icon + .tlui-button__label,
.ddc_ink_writing-editor .tlui-button__icon + .tlui-button__label {
  margin-left: var(--space-2);
}
.ddc_ink_drawing-editor .tlui-button[data-state=hinted]::after,
.ddc_ink_writing-editor .tlui-button[data-state=hinted]::after {
  background-color: var(--color-hint);
  opacity: 1;
}
.ddc_ink_drawing-editor .tlui-button[data-state=hinted]:not(:disabled, :focus-visible):active:after,
.ddc_ink_writing-editor .tlui-button[data-state=hinted]:not(:disabled, :focus-visible):active:after {
  background: var(--color-hint);
  opacity: 1;
}
@media (hover: hover) {
  .ddc_ink_drawing-editor .tlui-button::after,
  .ddc_ink_writing-editor .tlui-button::after {
    background-color: var(--color-muted-2);
    opacity: 0;
  }
  .ddc_ink_drawing-editor .tlui-button:not(:disabled):hover::after,
  .ddc_ink_writing-editor .tlui-button:not(:disabled):hover::after {
    opacity: 1;
  }
}
.ddc_ink_drawing-editor .tlui-button__low,
.ddc_ink_writing-editor .tlui-button__low {
  border-radius: var(--radius-3);
  background-color: var(--color-low);
}
@media (hover: hover) {
  .ddc_ink_drawing-editor .tlui-button__low::after,
  .ddc_ink_writing-editor .tlui-button__low::after {
    background-color: var(--color-muted-2);
  }
}
.ddc_ink_drawing-editor .tlui-button__primary,
.ddc_ink_writing-editor .tlui-button__primary {
  color: var(--color-primary);
}
.ddc_ink_drawing-editor .tlui-button__danger,
.ddc_ink_writing-editor .tlui-button__danger {
  color: var(--color-warn);
  text-shadow: none;
}
@media (hover: hover) {
  .ddc_ink_drawing-editor .tlui-button__primary:not(:disabled, :focus-visible):hover,
  .ddc_ink_writing-editor .tlui-button__primary:not(:disabled, :focus-visible):hover {
    color: var(--color-primary);
  }
  .ddc_ink_drawing-editor .tlui-button__danger:not(:disabled, :focus-visible):hover,
  .ddc_ink_writing-editor .tlui-button__danger:not(:disabled, :focus-visible):hover {
    color: var(--color-warn);
    text-shadow: none;
  }
}
.ddc_ink_drawing-editor .tlui-button__panel,
.ddc_ink_writing-editor .tlui-button__panel {
  position: relative;
}
.ddc_ink_drawing-editor .tlui-button__menu,
.ddc_ink_writing-editor .tlui-button__menu {
  height: 40px;
  min-height: 40px;
  width: 100%;
  gap: 8px;
  margin: -4px 0px;
}
.ddc_ink_drawing-editor .tlui-button__menu:nth-child(1),
.ddc_ink_writing-editor .tlui-button__menu:nth-child(1) {
  margin-top: 0px;
}
.ddc_ink_drawing-editor .tlui-button__menu:nth-last-child(1),
.ddc_ink_writing-editor .tlui-button__menu:nth-last-child(1) {
  margin-bottom: 0px;
}
@media (hover: hover) {
  .ddc_ink_drawing-editor .tlui-button__menu::after,
  .ddc_ink_writing-editor .tlui-button__menu::after {
    inset: 4px;
    border-radius: var(--radius-2);
  }
}
.ddc_ink_drawing-editor .tlui-button__checkbox,
.ddc_ink_writing-editor .tlui-button__checkbox {
  padding-left: 8px;
}
.ddc_ink_drawing-editor .tlui-button__checkbox__indicator,
.ddc_ink_writing-editor .tlui-button__checkbox__indicator {
  width: 15px;
  height: 15px;
}
.ddc_ink_drawing-editor .tlui-toolbar__lock-button,
.ddc_ink_writing-editor .tlui-toolbar__lock-button {
  position: absolute;
  top: 4px;
  right: 0px;
  pointer-events: all;
  height: 40px;
  width: 40px;
  min-width: 0px;
  border-radius: var(--radius-2);
}
.ddc_ink_drawing-editor .tlui-toolbar__lock-button::after,
.ddc_ink_writing-editor .tlui-toolbar__lock-button::after {
  top: 4px;
  left: 8px;
  inset: 4px;
}
.ddc_ink_drawing-editor .tlui-button__tool,
.ddc_ink_writing-editor .tlui-button__tool {
  position: relative;
  height: 48px;
  width: 48px;
  margin-left: -2px;
  margin-right: -2px;
}
.ddc_ink_drawing-editor .tlui-button__tool:nth-of-type(1),
.ddc_ink_writing-editor .tlui-button__tool:nth-of-type(1) {
  margin-left: 0px;
}
.ddc_ink_drawing-editor .tlui-button__tool:nth-last-of-type(1),
.ddc_ink_writing-editor .tlui-button__tool:nth-last-of-type(1) {
  margin-right: 0px;
}
@media (hover: hover) {
  .ddc_ink_drawing-editor .tlui-button__tool::after,
  .ddc_ink_writing-editor .tlui-button__tool::after {
    inset: 4px;
    border-radius: 8px;
  }
  .ddc_ink_drawing-editor .tlui-button__tool[data-state=selected]:not(:disabled, :focus-visible):hover,
  .ddc_ink_writing-editor .tlui-button__tool[data-state=selected]:not(:disabled, :focus-visible):hover {
    color: var(--color-selected-contrast);
  }
}
.ddc_ink_drawing-editor .tlui-button__tool[data-state=selected],
.ddc_ink_writing-editor .tlui-button__tool[data-state=selected] {
  color: var(--color-selected-contrast);
}
.ddc_ink_drawing-editor .tlui-button__tool[data-state=selected]:not(:disabled, :focus-visible):active,
.ddc_ink_writing-editor .tlui-button__tool[data-state=selected]:not(:disabled, :focus-visible):active {
  color: var(--color-selected-contrast);
}
.ddc_ink_drawing-editor .tlui-button__tool[data-state=selected]:not(:disabled)::after,
.ddc_ink_writing-editor .tlui-button__tool[data-state=selected]:not(:disabled)::after {
  background: var(--color-selected);
  opacity: 1;
}
.ddc_ink_drawing-editor .tlui-layout__mobile .tlui-button__tool,
.ddc_ink_writing-editor .tlui-layout__mobile .tlui-button__tool {
  height: 48px;
  width: 44px;
}
.ddc_ink_drawing-editor .tlui-layout__mobile .tlui-button__tool > .tlui-icon,
.ddc_ink_writing-editor .tlui-layout__mobile .tlui-button__tool > .tlui-icon {
  height: 16px;
  width: 16px;
}
.ddc_ink_drawing-editor .tlui-button__help,
.ddc_ink_writing-editor .tlui-button__help {
  height: 32px;
  width: 32px;
  padding: 0px;
  min-width: 32px;
  border-radius: 100%;
  background-color: var(--color-low);
  border: 1px solid var(--color-low-border);
}
@media (hover: hover) {
  .ddc_ink_drawing-editor .tlui-button__help::after,
  .ddc_ink_writing-editor .tlui-button__help::after {
    background-color: var(--color-muted-2);
    border-radius: 100%;
    inset: 4px;
  }
}
.ddc_ink_drawing-editor .tlui-buttons__horizontal,
.ddc_ink_writing-editor .tlui-buttons__horizontal {
  display: flex;
  flex-direction: row;
}
.ddc_ink_drawing-editor .tlui-buttons__horizontal > *,
.ddc_ink_writing-editor .tlui-buttons__horizontal > * {
  margin-left: -2px;
  margin-right: -2px;
}
.ddc_ink_drawing-editor .tlui-buttons__horizontal > *:nth-child(1),
.ddc_ink_writing-editor .tlui-buttons__horizontal > *:nth-child(1) {
  margin-left: 0px;
}
.ddc_ink_drawing-editor .tlui-buttons__horizontal > *:nth-last-child(1),
.ddc_ink_writing-editor .tlui-buttons__horizontal > *:nth-last-child(1) {
  margin-right: 0px;
}
.ddc_ink_drawing-editor .tlui-buttons__horizontal > *:only-child,
.ddc_ink_writing-editor .tlui-buttons__horizontal > *:only-child {
  width: 56px;
}
.ddc_ink_drawing-editor .tlui-buttons__grid,
.ddc_ink_writing-editor .tlui-buttons__grid {
  display: grid;
  grid-template-columns: repeat(4, auto);
  grid-auto-flow: row;
  overflow: hidden;
}
.ddc_ink_drawing-editor .tlui-buttons__grid > .tlui-button,
.ddc_ink_writing-editor .tlui-buttons__grid > .tlui-button {
  margin: -2px;
}
.ddc_ink_drawing-editor .tlui-buttons__grid > .tlui-button:nth-of-type(4n),
.ddc_ink_writing-editor .tlui-buttons__grid > .tlui-button:nth-of-type(4n) {
  margin-right: 0px;
}
.ddc_ink_drawing-editor .tlui-buttons__grid > .tlui-button:nth-of-type(4n - 3),
.ddc_ink_writing-editor .tlui-buttons__grid > .tlui-button:nth-of-type(4n - 3) {
  margin-left: 0px;
}
.ddc_ink_drawing-editor .tlui-buttons__grid > .tlui-button:nth-of-type(-n + 4),
.ddc_ink_writing-editor .tlui-buttons__grid > .tlui-button:nth-of-type(-n + 4) {
  margin-top: 0px;
}
.ddc_ink_drawing-editor .tlui-buttons__grid > .tlui-button:nth-last-of-type(-n + 4),
.ddc_ink_writing-editor .tlui-buttons__grid > .tlui-button:nth-last-of-type(-n + 4) {
  margin-bottom: 0px;
}
.ddc_ink_drawing-editor .tlui-zoom-menu__button__pct,
.ddc_ink_writing-editor .tlui-zoom-menu__button__pct {
  width: 60px;
  min-width: 60px;
  text-align: center;
}
.ddc_ink_drawing-editor .tlui-layout,
.ddc_ink_writing-editor .tlui-layout {
  position: relative;
  display: grid;
  grid-template-columns: 1fr;
  grid-template-rows: minmax(0px, 1fr) auto;
  grid-auto-rows: auto;
  height: 100%;
  max-height: 100%;
  overflow: clip;
  pointer-events: none;
  user-select: none;
  contain: strict;
  z-index: var(--layer-panels);
  -webkit-transform: translate3d(0, 0, 0);
  --sab: env(safe-area-inset-bottom);
}
.ddc_ink_drawing-editor .tlui-layout__top,
.ddc_ink_writing-editor .tlui-layout__top {
  grid-column: 1;
  grid-row: 1;
  display: flex;
  min-width: 0px;
  justify-content: space-between;
}
.ddc_ink_drawing-editor .tlui-layout__top__left,
.ddc_ink_writing-editor .tlui-layout__top__left {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  width: 100%;
  height: 100%;
  flex: 0 1 0;
}
.ddc_ink_drawing-editor .tlui-layout__top__right,
.ddc_ink_writing-editor .tlui-layout__top__right {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  justify-content: flex-start;
  height: 100%;
  flex: 0 0 auto;
  min-width: 0px;
}
.ddc_ink_drawing-editor .scrollable,
.ddc_ink_drawing-editor .scrollable *,
.ddc_ink_writing-editor .scrollable,
.ddc_ink_writing-editor .scrollable * {
  pointer-events: all;
  touch-action: auto;
  overscroll-behavior: none;
}
.ddc_ink_drawing-editor .tlui-helper-buttons,
.ddc_ink_writing-editor .tlui-helper-buttons {
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  width: min-content;
  gap: var(--space-3);
  margin: var(--space-2) var(--space-3);
  white-space: nowrap;
  pointer-events: none;
  z-index: var(--layer-panels);
}
.ddc_ink_drawing-editor .tlui-icon,
.ddc_ink_writing-editor .tlui-icon {
  flex-shrink: 0;
  width: 18px;
  height: 18px;
  background-color: currentColor;
}
.ddc_ink_drawing-editor .tlui-icon__small,
.ddc_ink_writing-editor .tlui-icon__small {
  width: 15px;
  height: 15px;
}
.ddc_ink_drawing-editor .tlui-slider,
.ddc_ink_writing-editor .tlui-slider {
  position: relative;
  display: flex;
  align-items: center;
  user-select: none;
  touch-action: none;
}
.ddc_ink_drawing-editor .tlui-slider__container,
.ddc_ink_writing-editor .tlui-slider__container {
  width: 100%;
  padding: 0px var(--space-4);
}
.ddc_ink_drawing-editor .tlui-slider__track,
.ddc_ink_writing-editor .tlui-slider__track {
  position: relative;
  flex-grow: 1;
  height: 44px;
  cursor: pointer;
}
.ddc_ink_drawing-editor .tlui-slider__track::after,
.ddc_ink_writing-editor .tlui-slider__track::after {
  display: block;
  position: absolute;
  top: calc(50% - 2px);
  content: "";
  height: 3px;
  width: 100%;
  background-color: var(--color-muted-1);
  border-radius: 14px;
}
.ddc_ink_drawing-editor .tlui-slider__range,
.ddc_ink_writing-editor .tlui-slider__range {
  position: absolute;
  top: calc(50% - 2px);
  left: 0px;
  height: 3px;
  background-color: var(--color-selected);
  border-radius: 14px;
}
.ddc_ink_drawing-editor .tlui-slider__thumb,
.ddc_ink_writing-editor .tlui-slider__thumb {
  all: unset;
  cursor: grab;
  display: block;
  width: 18px;
  height: 18px;
  position: relative;
  top: -1px;
  background-color: var(--color-panel);
  border-radius: 999px;
  box-shadow: inset 0px 0px 0px 2px var(--color-text-1);
}
.ddc_ink_drawing-editor .tlui-slider__thumb:active,
.ddc_ink_writing-editor .tlui-slider__thumb:active {
  cursor: grabbing;
  box-shadow: inset 0px 0px 0px 2px var(--color-text-1), var(--shadow-1);
}
.ddc_ink_drawing-editor .tlui-slider__thumb:focus-visible,
.ddc_ink_writing-editor .tlui-slider__thumb:focus-visible {
  box-shadow: inset 0 0 0 2px var(--color-focus);
}
.ddc_ink_drawing-editor .tlui-kbd,
.ddc_ink_writing-editor .tlui-kbd {
  font-family: inherit;
  font-size: 11px;
  line-height: 11px;
  display: grid;
  justify-items: center;
  grid-auto-flow: column;
  grid-template-columns: auto;
  grid-auto-columns: minmax(1em, auto);
  align-self: bottom;
  color: var(--color-text-1);
  margin-left: var(--space-4);
}
.ddc_ink_drawing-editor .tlui-kbd > span,
.ddc_ink_writing-editor .tlui-kbd > span {
  width: 100%;
  text-align: center;
  display: inline;
  margin: 0px;
  padding: 2px;
  border-radius: 2px;
}
.ddc_ink_drawing-editor .tlui-kbd > span:last-child,
.ddc_ink_writing-editor .tlui-kbd > span:last-child {
  padding-right: 0;
}
.ddc_ink_drawing-editor .tlui-kbd:not(:last-child),
.ddc_ink_writing-editor .tlui-kbd:not(:last-child) {
  margin-right: var(--space-2);
}
.ddc_ink_drawing-editor .tlui-focus-button,
.ddc_ink_writing-editor .tlui-focus-button {
  z-index: var(--layer-panels);
  pointer-events: all;
}
.ddc_ink_drawing-editor .tlui-popover,
.ddc_ink_writing-editor .tlui-popover {
  position: relative;
  display: flex;
  align-content: stretch;
}
.ddc_ink_drawing-editor .tlui-popover__content,
.ddc_ink_writing-editor .tlui-popover__content {
  position: relative;
  max-height: 75vh;
  margin: 0px;
  border: none;
  border-radius: var(--radius-3);
  background-color: var(--color-panel);
  box-shadow: var(--shadow-3);
  z-index: var(--layer-menus);
  overflow: hidden;
  overflow-y: auto;
  touch-action: auto;
  overscroll-behavior: none;
  scrollbar-width: none;
  -ms-overflow-style: none;
}
.ddc_ink_drawing-editor .tlui-debug-panel,
.ddc_ink_writing-editor .tlui-debug-panel {
  background-color: var(--color-low);
  width: 100%;
  display: grid;
  align-items: center;
  grid-template-columns: 1fr auto auto auto;
  justify-content: space-between;
  padding-left: var(--space-4);
  border-top: 1px solid var(--color-background);
  font-size: 12px;
  color: var(--color-text-1);
  z-index: var(--layer-panels);
  pointer-events: all;
}
.ddc_ink_drawing-editor .tlui-debug-panel__current-state,
.ddc_ink_writing-editor .tlui-debug-panel__current-state {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.ddc_ink_drawing-editor .tlui-debug-panel__fps,
.ddc_ink_writing-editor .tlui-debug-panel__fps {
  margin-right: 8px;
}
.ddc_ink_drawing-editor .tlui-debug-panel__fps__slow,
.ddc_ink_writing-editor .tlui-debug-panel__fps__slow {
  font-weight: bold;
  color: var(--color-warn);
}
.ddc_ink_drawing-editor .tlui-menu-zone,
.ddc_ink_writing-editor .tlui-menu-zone {
  position: relative;
  z-index: var(--layer-panels);
  width: fit-content;
  border-right: 2px solid var(--color-background);
  border-bottom: 2px solid var(--color-background);
  border-bottom-right-radius: var(--radius-4);
  background-color: var(--color-low);
}
.ddc_ink_drawing-editor .tlui-menu-zone *[data-state=open]::after,
.ddc_ink_writing-editor .tlui-menu-zone *[data-state=open]::after {
  background: linear-gradient(180deg, rgba(144, 144, 144, 0) 0%, var(--color-muted-2) 100%);
}
.ddc_ink_drawing-editor .tlui-style-panel__wrapper,
.ddc_ink_writing-editor .tlui-style-panel__wrapper {
  box-shadow: var(--shadow-2);
  border-radius: var(--radius-3);
  pointer-events: all;
  background-color: var(--color-panel);
  height: fit-content;
  max-height: 100%;
  margin: 8px;
  touch-action: auto;
  overscroll-behavior: none;
  overflow-y: auto;
  overflow-x: hidden;
  color: var(--color-text);
}
.ddc_ink_drawing-editor .tlui-style-panel,
.ddc_ink_writing-editor .tlui-style-panel {
  position: relative;
  z-index: var(--layer-panels);
  pointer-events: all;
  width: 148px;
  max-width: 148px;
}
.ddc_ink_drawing-editor .tlui-style-panel::-webkit-scrollbar,
.ddc_ink_writing-editor .tlui-style-panel::-webkit-scrollbar {
  display: none;
}
.ddc_ink_drawing-editor .tlui-style-panel .tlui-button.select,
.ddc_ink_writing-editor .tlui-style-panel .tlui-button.select {
  width: 100%;
}
.ddc_ink_drawing-editor .tlui-style-panel__section,
.ddc_ink_writing-editor .tlui-style-panel__section {
  display: flex;
  position: relative;
  flex-direction: column;
}
.ddc_ink_drawing-editor .tlui-style-panel__section:nth-of-type(n + 2):not(:last-child),
.ddc_ink_writing-editor .tlui-style-panel__section:nth-of-type(n + 2):not(:last-child) {
  border-bottom: 1px solid var(--color-divider);
}
.ddc_ink_drawing-editor .tlui-style-panel__section:empty,
.ddc_ink_writing-editor .tlui-style-panel__section:empty {
  display: none;
}
.ddc_ink_drawing-editor .tlui-style-panel__section__common:not(:only-child),
.ddc_ink_writing-editor .tlui-style-panel__section__common:not(:only-child) {
  margin-bottom: 7px;
  border-bottom: 1px solid var(--color-divider);
}
.ddc_ink_drawing-editor .tlui-style-panel__row,
.ddc_ink_writing-editor .tlui-style-panel__row {
  display: flex;
}
.ddc_ink_drawing-editor .tlui-style-panel__row__extra-button,
.ddc_ink_writing-editor .tlui-style-panel__row__extra-button {
  margin-left: -2px;
}
.ddc_ink_drawing-editor .tlui-style-panel__double-select-picker,
.ddc_ink_writing-editor .tlui-style-panel__double-select-picker {
  display: flex;
  grid-template-columns: 1fr auto;
  align-items: center;
  padding-left: var(--space-4);
  color: var(--color-text-1);
  font-size: 12px;
}
.ddc_ink_drawing-editor .tlui-style-panel__double-select-picker-label,
.ddc_ink_writing-editor .tlui-style-panel__double-select-picker-label {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  flex-grow: 2;
  max-width: 100%;
}
.ddc_ink_drawing-editor .tlui-style-panel__section *[data-state=open]::after,
.ddc_ink_writing-editor .tlui-style-panel__section *[data-state=open]::after {
  background: var(--color-muted-0);
}
.ddc_ink_drawing-editor .tlui-input,
.ddc_ink_writing-editor .tlui-input {
  background: none;
  margin: 0px;
  position: relative;
  z-index: 1;
  height: 40px;
  max-height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: inherit;
  font-size: 12px;
  font-weight: inherit;
  color: var(--color-text-1);
  padding: var(--space-4);
  padding-left: 0px;
  border: none;
  outline: none;
  text-overflow: ellipsis;
  width: 100%;
  user-select: all;
  text-rendering: optimizeLegibility;
  -webkit-user-select: auto !important;
}
.ddc_ink_drawing-editor .tlui-input__wrapper,
.ddc_ink_writing-editor .tlui-input__wrapper {
  width: 100%;
  height: 44px;
  display: flex;
  align-items: center;
  gap: var(--space-4);
  color: var(--color-text);
}
.ddc_ink_drawing-editor .tlui-input__wrapper > .tlui-icon,
.ddc_ink_writing-editor .tlui-input__wrapper > .tlui-icon {
  flex-shrink: 0;
}
@media (max-width: 600px) {
  @supports (-webkit-touch-callout: none) {
    .ddc_ink_drawing-editor,
    .ddc_ink_writing-editor {
    }
    .ddc_ink_drawing-editor .tlui-input,
    .ddc_ink_writing-editor .tlui-input {
      font-size: 16px;
    }
  }
}
.ddc_ink_drawing-editor .tlui-dialog__overlay,
.ddc_ink_writing-editor .tlui-dialog__overlay {
  position: absolute;
  top: 0px;
  left: 0px;
  width: 100%;
  height: 100%;
  z-index: var(--layer-overlays);
  background-color: var(--color-overlay);
  pointer-events: all;
  animation: fadeIn 0.12s ease-out;
  display: grid;
  place-items: center;
  overflow-y: auto;
}
.ddc_ink_drawing-editor .tlui-dialog__content,
.ddc_ink_writing-editor .tlui-dialog__content {
  display: flex;
  flex-direction: column;
  position: relative;
  cursor: default;
  background-color: var(--color-panel);
  box-shadow: var(--shadow-3);
  border-radius: var(--radius-3);
  font-size: 12px;
  overflow: hidden;
  min-width: 300px;
  max-width: 80%;
  max-height: 80%;
}
.ddc_ink_drawing-editor .tlui-dialog__header,
.ddc_ink_writing-editor .tlui-dialog__header {
  position: relative;
  display: flex;
  align-items: center;
  flex: 0;
  z-index: 999;
  padding-left: var(--space-4);
  color: var(--color-text);
  height: 44px;
}
.ddc_ink_drawing-editor .tlui-dialog__header__title,
.ddc_ink_writing-editor .tlui-dialog__header__title {
  flex: 1;
  font-weight: inherit;
  font-size: 12px;
  margin: 0px;
  color: var(--color-text-1);
}
.ddc_ink_drawing-editor .tlui-dialog__header__close,
.ddc_ink_writing-editor .tlui-dialog__header__close {
  justify-self: flex-end;
}
.ddc_ink_drawing-editor .tlui-dialog__body,
.ddc_ink_writing-editor .tlui-dialog__body {
  padding: var(--space-4) var(--space-4);
  flex: 0 1;
  overflow-y: auto;
  overflow-x: hidden;
  color: var(--color-text-1);
  user-select: all;
  -webkit-user-select: text;
}
.ddc_ink_drawing-editor .tlui-dialog__footer,
.ddc_ink_writing-editor .tlui-dialog__footer {
  position: relative;
  z-index: 999;
}
.ddc_ink_drawing-editor .tlui-dialog__footer__actions,
.ddc_ink_writing-editor .tlui-dialog__footer__actions {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.ddc_ink_drawing-editor .tlui-dialog__footer__actions > .tlui-button:nth-last-child(n+2),
.ddc_ink_writing-editor .tlui-dialog__footer__actions > .tlui-button:nth-last-child(n+2) {
  margin-right: -4px;
}
.ddc_ink_drawing-editor .tlui-toolbar,
.ddc_ink_writing-editor .tlui-toolbar {
  grid-column: 1/span 3;
  grid-row: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-grow: 2;
  padding-bottom: calc(var(--space-3) + var(--sab));
}
.ddc_ink_drawing-editor .tlui-toolbar__inner,
.ddc_ink_writing-editor .tlui-toolbar__inner {
  position: relative;
  width: fit-content;
  display: flex;
  gap: var(--space-3);
  align-items: flex-end;
}
.ddc_ink_drawing-editor .tlui-toolbar__left,
.ddc_ink_writing-editor .tlui-toolbar__left {
  width: fit-content;
}
.ddc_ink_drawing-editor .tlui-toolbar__extras,
.ddc_ink_writing-editor .tlui-toolbar__extras {
  position: relative;
  z-index: 1;
  width: 100%;
  pointer-events: none;
  top: 6px;
  height: 48px;
}
.ddc_ink_drawing-editor .tlui-toolbar__extras:empty,
.ddc_ink_writing-editor .tlui-toolbar__extras:empty {
  display: none;
}
.ddc_ink_drawing-editor .tlui-toolbar__extras__controls,
.ddc_ink_writing-editor .tlui-toolbar__extras__controls {
  display: flex;
  position: relative;
  flex-direction: row;
  z-index: 1;
  background-color: var(--color-low);
  border-top-left-radius: var(--radius-4);
  border-top-right-radius: var(--radius-4);
  border: 2px solid var(--color-background);
  margin-left: 8px;
  margin-right: 0px;
  pointer-events: all;
  width: fit-content;
}
.ddc_ink_drawing-editor .tlui-toolbar__tools,
.ddc_ink_writing-editor .tlui-toolbar__tools {
  display: flex;
  flex-direction: row;
  background-color: var(--color-low);
  border-radius: var(--radius-4);
  z-index: var(--layer-panels);
  pointer-events: all;
  position: relative;
  align-items: center;
  background: var(--color-panel);
  box-shadow: var(--shadow-2);
}
.ddc_ink_drawing-editor .tlui-toolbar__overflow,
.ddc_ink_writing-editor .tlui-toolbar__overflow {
  width: 40px;
}
.ddc_ink_drawing-editor .tlui-layout__mobile .tlui-toolbar__overflow,
.ddc_ink_writing-editor .tlui-layout__mobile .tlui-toolbar__overflow {
  width: 32px;
  padding: 0px;
}
.ddc_ink_drawing-editor .tlui-layout__mobile .tlui-toolbar *[data-state=open]::after,
.ddc_ink_writing-editor .tlui-layout__mobile .tlui-toolbar *[data-state=open]::after {
  background: linear-gradient(0deg, rgba(144, 144, 144, 0) 0%, var(--color-muted-2) 100%);
}
.ddc_ink_drawing-editor .tlui-help-menu,
.ddc_ink_writing-editor .tlui-help-menu {
  pointer-events: all;
  position: absolute;
  bottom: var(--space-2);
  right: var(--space-2);
  z-index: var(--layer-panels);
  border: 2px solid var(--color-background);
  border-radius: 100%;
}
.ddc_ink_drawing-editor .tlui-context-menu__move-to-page__name,
.ddc_ink_writing-editor .tlui-context-menu__move-to-page__name {
  max-width: min(300px, 35vw);
  overflow: hidden;
  text-overflow: ellipsis;
}
.ddc_ink_drawing-editor .tlui-context-menu__move-to-page__name[data-disabled],
.ddc_ink_writing-editor .tlui-context-menu__move-to-page__name[data-disabled] {
  color: var(--color-text-3);
  pointer-events: none;
}
.ddc_ink_drawing-editor .tlui-menu:empty,
.ddc_ink_writing-editor .tlui-menu:empty {
  display: none;
}
.ddc_ink_drawing-editor .tlui-menu,
.ddc_ink_writing-editor .tlui-menu {
  z-index: var(--layer-menus);
  height: fit-content;
  width: fit-content;
  max-height: 80vh;
  border-radius: var(--radius-3);
  pointer-events: all;
  touch-action: auto;
  overflow-y: auto;
  overscroll-behavior: none;
  background-color: var(--color-panel);
  box-shadow: var(--shadow-3);
}
.ddc_ink_drawing-editor .tlui-menu::-webkit-scrollbar,
.ddc_ink_writing-editor .tlui-menu::-webkit-scrollbar {
  display: none;
}
.ddc_ink_drawing-editor .tlui-menu__arrow,
.ddc_ink_writing-editor .tlui-menu__arrow {
  position: relative;
  top: -1px;
  fill: var(--color-panel);
  stroke: var(--color-panel-contrast);
  stroke-width: 1px;
}
.ddc_ink_drawing-editor .tlui-menu__group,
.ddc_ink_writing-editor .tlui-menu__group {
  width: 100%;
}
.ddc_ink_drawing-editor .tlui-menu__group:empty,
.ddc_ink_writing-editor .tlui-menu__group:empty {
  display: none;
}
.ddc_ink_drawing-editor .tlui-menu__group,
.ddc_ink_writing-editor .tlui-menu__group {
  border-bottom: 1px solid var(--color-divider);
}
.ddc_ink_drawing-editor .tlui-menu__group:nth-last-of-type(1),
.ddc_ink_writing-editor .tlui-menu__group:nth-last-of-type(1) {
  border-bottom: none;
}
.ddc_ink_drawing-editor .tlui-menu__submenu__trigger[data-state=open]:not(:hover)::after,
.ddc_ink_writing-editor .tlui-menu__submenu__trigger[data-state=open]:not(:hover)::after {
  border-radius: var(--radius-1);
  background: linear-gradient(90deg, rgba(144, 144, 144, 0) 0%, var(--color-muted-2) 100%);
}
.ddc_ink_drawing-editor .tlui-menu__submenu__trigger[data-direction=left][data-state=open]:not(:hover)::after,
.ddc_ink_writing-editor .tlui-menu__submenu__trigger[data-direction=left][data-state=open]:not(:hover)::after {
  border-radius: var(--radius-1);
  background: linear-gradient(270deg, rgba(144, 144, 144, 0) 0%, var(--color-muted-2) 100%);
}
.ddc_ink_drawing-editor .tlui-menu[data-size=large] > .tlui-menu__group,
.ddc_ink_writing-editor .tlui-menu[data-size=large] > .tlui-menu__group {
  min-width: initial;
}
.ddc_ink_drawing-editor .tlui-menu[data-size=medium] > .tlui-menu__group,
.ddc_ink_writing-editor .tlui-menu[data-size=medium] > .tlui-menu__group {
  min-width: 144px;
}
.ddc_ink_drawing-editor .tlui-menu[data-size=small] > .tlui-menu__group,
.ddc_ink_writing-editor .tlui-menu[data-size=small] > .tlui-menu__group {
  min-width: 96px;
}
.ddc_ink_drawing-editor .tlui-menu[data-size=tiny] > .tlui-menu__group,
.ddc_ink_writing-editor .tlui-menu[data-size=tiny] > .tlui-menu__group {
  min-width: 0px;
}
.ddc_ink_drawing-editor .tlui-actions-menu,
.ddc_ink_writing-editor .tlui-actions-menu {
  max-height: calc(100vh - 150px);
}
.ddc_ink_drawing-editor .tlui-toast__viewport,
.ddc_ink_writing-editor .tlui-toast__viewport {
  position: absolute;
  inset: 0px;
  margin: 0px;
  display: flex;
  align-items: flex-end;
  justify-content: flex-end;
  flex-direction: column;
  gap: var(--space-3);
  pointer-events: none;
  padding: 0px var(--space-3) 64px 0px;
  z-index: var(--layer-toasts);
}
.ddc_ink_drawing-editor .tlui-toast__viewport > *,
.ddc_ink_writing-editor .tlui-toast__viewport > * {
  pointer-events: all;
}
.ddc_ink_drawing-editor .tlui-toast__icon,
.ddc_ink_writing-editor .tlui-toast__icon {
  padding-top: var(--space-4);
  padding-left: var(--space-4);
  color: var(--color-text-1);
}
.ddc_ink_drawing-editor .tlui-toast__container,
.ddc_ink_writing-editor .tlui-toast__container {
  min-width: 200px;
  display: flex;
  flex-direction: row;
  background-color: var(--color-panel);
  box-shadow: var(--shadow-2);
  border-radius: var(--radius-3);
  font-size: 12px;
}
.ddc_ink_drawing-editor .tlui-toast__main,
.ddc_ink_writing-editor .tlui-toast__main {
  flex-grow: 2;
  max-width: 280px;
}
.ddc_ink_drawing-editor .tlui-toast__content,
.ddc_ink_writing-editor .tlui-toast__content {
  padding: var(--space-4);
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
}
.ddc_ink_drawing-editor .tlui-toast__title,
.ddc_ink_writing-editor .tlui-toast__title {
  font-weight: bold;
  color: var(--color-text-1);
}
.ddc_ink_drawing-editor .tlui-toast__description,
.ddc_ink_writing-editor .tlui-toast__description {
  color: var(--color-text-1);
  padding: var(--space-3);
  margin: 0px;
  padding: 0px;
}
.ddc_ink_drawing-editor .tlui-toast__icon + .tlui-toast__main > .tlui-toast__actions,
.ddc_ink_writing-editor .tlui-toast__icon + .tlui-toast__main > .tlui-toast__actions {
  padding-left: 0px;
}
.ddc_ink_drawing-editor .tlui-toast__actions,
.ddc_ink_writing-editor .tlui-toast__actions {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  margin-left: 0;
}
.ddc_ink_drawing-editor .tlui-toast__close,
.ddc_ink_writing-editor .tlui-toast__close {
  align-self: flex-end;
  flex-shrink: 0;
}
@media (prefers-reduced-motion: no-preference) {
  .ddc_ink_drawing-editor .tlui-toast__container[data-state=open],
  .ddc_ink_writing-editor .tlui-toast__container[data-state=open] {
    animation: slide-in 200ms cubic-bezier(0.785, 0.135, 0.15, 0.86);
  }
  .ddc_ink_drawing-editor .tlui-toast__container[data-state=closed],
  .ddc_ink_writing-editor .tlui-toast__container[data-state=closed] {
    animation: hide 100ms ease-in;
  }
  .ddc_ink_drawing-editor .tlui-toast__container[data-swipe=move],
  .ddc_ink_writing-editor .tlui-toast__container[data-swipe=move] {
    transform: translateX(var(--radix-toast-swipe-move-x));
  }
  .ddc_ink_drawing-editor .tlui-toast__container[data-swipe=cancel],
  .ddc_ink_writing-editor .tlui-toast__container[data-swipe=cancel] {
    transform: translateX(0);
    transition: transform 200ms ease-out;
  }
  .ddc_ink_drawing-editor .tlui-toast__container[data-swipe=end],
  .ddc_ink_writing-editor .tlui-toast__container[data-swipe=end] {
    animation: swipe-out 100ms ease-out;
  }
}
.ddc_ink_drawing-editor .tlui-layout__bottom,
.ddc_ink_writing-editor .tlui-layout__bottom {
  grid-row: 2;
}
.ddc_ink_drawing-editor .tlui-layout__bottom__main,
.ddc_ink_writing-editor .tlui-layout__bottom__main {
  width: 100%;
  position: relative;
  display: flex;
  align-items: flex-end;
  justify-content: center;
}
.ddc_ink_drawing-editor .tlui-navigation-panel,
.ddc_ink_writing-editor .tlui-navigation-panel {
  display: flex;
  width: min-content;
  flex-direction: column;
  z-index: var(--layer-panels);
  pointer-events: all;
  position: absolute;
  left: 0px;
  bottom: 0px;
}
.ddc_ink_drawing-editor .tlui-navigation-panel::before,
.ddc_ink_writing-editor .tlui-navigation-panel::before {
  content: "";
  display: block;
  position: absolute;
  z-index: -1;
  inset: -2px -2px 0px 0px;
  border-radius: 0;
  border-top: 2px solid var(--color-background);
  border-right: 2px solid var(--color-background);
  border-top-right-radius: var(--radius-4);
  background-color: var(--color-low);
}
.ddc_ink_drawing-editor .tlui-navigation-panel__toggle .tlui-icon,
.ddc_ink_writing-editor .tlui-navigation-panel__toggle .tlui-icon {
  opacity: 0.24;
}
.ddc_ink_drawing-editor .tlui-navigation-panel__toggle:active .tlui-icon,
.ddc_ink_writing-editor .tlui-navigation-panel__toggle:active .tlui-icon {
  opacity: 1;
}
@media (hover: hover) {
  .ddc_ink_drawing-editor .tlui-navigation-panel__toggle:hover .tlui-icon,
  .ddc_ink_writing-editor .tlui-navigation-panel__toggle:hover .tlui-icon {
    opacity: 1;
  }
}
.ddc_ink_drawing-editor .tlui-minimap,
.ddc_ink_writing-editor .tlui-minimap {
  width: 100%;
  height: 96px;
  min-height: 96px;
  overflow: hidden;
  padding: var(--space-3);
  padding-top: 0px;
}
.ddc_ink_drawing-editor .tlui-minimap__canvas,
.ddc_ink_writing-editor .tlui-minimap__canvas {
  position: relative;
  width: 100%;
  height: 100%;
}
@keyframes hide {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}
@keyframes slide-in {
  from {
    transform: translateX(calc(100% + var(--space-3)));
  }
  to {
    transform: translateX(0px);
  }
}
@keyframes swipe-out {
  from {
    transform: translateX(var(--radix-toast-swipe-end-x));
  }
  to {
    transform: translateX(calc(100% + var(--space-3)));
  }
}
.ddc_ink_drawing-editor .tlui-page-menu__wrapper,
.ddc_ink_writing-editor .tlui-page-menu__wrapper {
  position: relative;
  display: flex;
  flex-direction: column;
  width: 220px;
  height: fit-content;
  max-height: 50vh;
}
.ddc_ink_drawing-editor .tlui-page-menu__trigger,
.ddc_ink_writing-editor .tlui-page-menu__trigger {
  width: 128px;
}
.ddc_ink_drawing-editor .tlui-page-menu__header,
.ddc_ink_writing-editor .tlui-page-menu__header {
  display: flex;
  flex-direction: row;
  align-items: center;
  width: 100%;
  height: 40px;
  padding-left: var(--space-4);
  border-bottom: 1px solid var(--color-divider);
}
.ddc_ink_drawing-editor .tlui-page-menu__header > .tlui-button:nth-of-type(1),
.ddc_ink_writing-editor .tlui-page-menu__header > .tlui-button:nth-of-type(1) {
  margin-right: -4px;
}
.ddc_ink_drawing-editor .tlui-page-menu__header__title,
.ddc_ink_writing-editor .tlui-page-menu__header__title {
  color: var(--color-text);
  font-size: 12px;
  flex-grow: 2;
}
.ddc_ink_drawing-editor .tlui-page-menu__name,
.ddc_ink_writing-editor .tlui-page-menu__name {
  flex-grow: 2;
  text-align: left;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.ddc_ink_drawing-editor .tlui-page-menu__list,
.ddc_ink_writing-editor .tlui-page-menu__list {
  position: relative;
  touch-action: auto;
  flex-direction: column;
  max-height: 100%;
  overflow-x: hidden;
  overflow-y: auto;
  touch-action: auto;
}
.ddc_ink_drawing-editor .tlui-page-menu__item,
.ddc_ink_writing-editor .tlui-page-menu__item {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  gap: 0px;
}
.ddc_ink_drawing-editor .tlui-page-menu__item:nth-of-type(n + 2),
.ddc_ink_writing-editor .tlui-page-menu__item:nth-of-type(n + 2) {
  margin-top: -4px;
}
.ddc_ink_drawing-editor .tlui-page-menu__item__button,
.ddc_ink_writing-editor .tlui-page-menu__item__button {
  width: 100%;
}
.ddc_ink_drawing-editor .tlui-page-menu__item__button:not(:only-child),
.ddc_ink_writing-editor .tlui-page-menu__item__button:not(:only-child) {
  flex-grow: 2;
  margin-right: -2px;
}
.ddc_ink_drawing-editor .tlui-page-menu__item__button > span,
.ddc_ink_writing-editor .tlui-page-menu__item__button > span {
  display: block;
  flex-grow: 2;
  text-align: left;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.ddc_ink_drawing-editor .tlui-page-menu__item__button__checkbox,
.ddc_ink_writing-editor .tlui-page-menu__item__button__checkbox {
  padding-left: 35px;
}
.ddc_ink_drawing-editor .tlui-page-menu__item__button__check,
.ddc_ink_writing-editor .tlui-page-menu__item__button__check {
  position: absolute;
  left: 0px;
  width: 24px;
  padding-left: 10px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  color: var(--color-text);
}
.ddc_ink_drawing-editor .tlui-page_menu__item__sortable,
.ddc_ink_writing-editor .tlui-page_menu__item__sortable {
  position: absolute;
  top: 0px;
  left: 0px;
  width: 100%;
  height: fit-content;
  display: flex;
  flex-direction: row;
  align-items: center;
  overflow: hidden;
  z-index: 1;
}
.ddc_ink_drawing-editor .tlui-page_menu__item__sortable__title,
.ddc_ink_writing-editor .tlui-page_menu__item__sortable__title {
  flex: 1;
}
.ddc_ink_drawing-editor .tlui-page_menu__item__sortable__title > .tlui-input__wrapper,
.ddc_ink_writing-editor .tlui-page_menu__item__sortable__title > .tlui-input__wrapper {
  height: 100%;
}
.ddc_ink_drawing-editor .tlui-page_menu__item__sortable:focus-within,
.ddc_ink_writing-editor .tlui-page_menu__item__sortable:focus-within {
  z-index: 10;
}
.ddc_ink_drawing-editor .tlui-page_menu__item__sortable__handle,
.ddc_ink_writing-editor .tlui-page_menu__item__sortable__handle {
  touch-action: none;
  width: 32px;
  min-width: 0px;
  height: 40px;
  cursor: grab;
  color: var(--color-text-3);
  flex-shrink: 0;
  margin-right: -9px;
}
.ddc_ink_drawing-editor .tlui-page_menu__item__sortable__handle:active,
.ddc_ink_writing-editor .tlui-page_menu__item__sortable__handle:active {
  cursor: grabbing;
}
.ddc_ink_drawing-editor .tlui-page-menu__item__input,
.ddc_ink_writing-editor .tlui-page-menu__item__input {
  margin-left: 12px;
  height: 100%;
}
.ddc_ink_drawing-editor .tlui-page_menu__item__submenu,
.ddc_ink_writing-editor .tlui-page_menu__item__submenu {
  pointer-events: all;
  flex: 0;
  cursor: pointer;
  margin: 0px;
  display: none;
  margin-left: -2px;
}
.ddc_ink_drawing-editor .tlui-page_menu__item__submenu[data-isediting=true],
.ddc_ink_writing-editor .tlui-page_menu__item__submenu[data-isediting=true] {
  display: block;
  opacity: 1;
}
.ddc_ink_drawing-editor .tlui-page_menu__item__submenu > .tlui-button,
.ddc_ink_writing-editor .tlui-page_menu__item__submenu > .tlui-button {
  opacity: 0;
}
@media (any-pointer: coarse) {
  .ddc_ink_drawing-editor .tlui-page_menu__item__submenu > .tlui-button,
  .ddc_ink_writing-editor .tlui-page_menu__item__submenu > .tlui-button {
    opacity: 1;
  }
}
.ddc_ink_drawing-editor .tlui-page-menu__item__button .tlui-button__icon,
.ddc_ink_writing-editor .tlui-page-menu__item__button .tlui-button__icon {
  margin-right: 4px;
}
@media (hover: hover) {
  .ddc_ink_drawing-editor .tlui-page_menu__item__submenu,
  .ddc_ink_writing-editor .tlui-page_menu__item__submenu {
    display: block;
  }
  .ddc_ink_drawing-editor .tlui-page_menu__item__submenu[data-isediting=true] > .tlui-button,
  .ddc_ink_writing-editor .tlui-page_menu__item__submenu[data-isediting=true] > .tlui-button {
    opacity: 0;
  }
  .ddc_ink_drawing-editor .tlui-page_menu__item__submenu > .tlui-button[data-state=open],
  .ddc_ink_drawing-editor .tlui-page_menu__item__submenu:hover > .tlui-button,
  .ddc_ink_drawing-editor .tlui-page_menu__item__sortable:focus-within > .tlui-page_menu__item__submenu > .tlui-button,
  .ddc_ink_writing-editor .tlui-page_menu__item__submenu > .tlui-button[data-state=open],
  .ddc_ink_writing-editor .tlui-page_menu__item__submenu:hover > .tlui-button,
  .ddc_ink_writing-editor .tlui-page_menu__item__sortable:focus-within > .tlui-page_menu__item__submenu > .tlui-button {
    opacity: 1;
  }
}
.ddc_ink_drawing-editor .tlui-edit-link-dialog,
.ddc_ink_writing-editor .tlui-edit-link-dialog {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
  color: var(--color-text);
}
.ddc_ink_drawing-editor .tlui-edit-link-dialog__input,
.ddc_ink_writing-editor .tlui-edit-link-dialog__input {
  background-color: var(--color-muted-2);
  flex-grow: 2;
  border-radius: var(--radius-2);
  padding: 0px var(--space-4);
}
.ddc_ink_drawing-editor .tlui-embed__spacer,
.ddc_ink_writing-editor .tlui-embed__spacer {
  flex-grow: 2;
  min-height: 0px;
  margin-left: calc(-1 * var(--space-4));
  margin-top: calc(-1 * var(--space-4));
  pointer-events: none;
}
.ddc_ink_drawing-editor .tlui-embed-dialog__list,
.ddc_ink_writing-editor .tlui-embed-dialog__list {
  display: flex;
  flex-direction: column;
  padding-bottom: var(--space-5);
}
.ddc_ink_drawing-editor .tlui-embed-dialog__item__image,
.ddc_ink_writing-editor .tlui-embed-dialog__item__image {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center center;
}
.ddc_ink_drawing-editor .tlui-embed-dialog__enter,
.ddc_ink_writing-editor .tlui-embed-dialog__enter {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
  color: var(--color-text-1);
}
.ddc_ink_drawing-editor .tlui-embed-dialog__input,
.ddc_ink_writing-editor .tlui-embed-dialog__input {
  background-color: var(--color-muted-2);
  flex-grow: 2;
  border-radius: var(--radius-2);
  padding: 0px var(--space-4);
}
.ddc_ink_drawing-editor .tlui-embed-dialog__warning,
.ddc_ink_writing-editor .tlui-embed-dialog__warning {
  color: var(--color-warn);
  text-shadow: none;
}
.ddc_ink_drawing-editor .tlui-embed-dialog__instruction__link,
.ddc_ink_writing-editor .tlui-embed-dialog__instruction__link {
  display: flex;
  gap: var(--space-1);
  margin-top: var(--space-4);
}
.ddc_ink_drawing-editor .tlui-embed-dialog__enter a,
.ddc_ink_writing-editor .tlui-embed-dialog__enter a {
  color: var(--color-text-1);
}
.ddc_ink_drawing-editor .tlui-following-indicator,
.ddc_ink_writing-editor .tlui-following-indicator {
  display: block;
  position: absolute;
  inset: 0px;
  border-width: 2px;
  border-style: solid;
  z-index: var(--layer-following-indicator);
  pointer-events: none;
}
.ddc_ink_drawing-editor .tlui-offline-indicator,
.ddc_ink_writing-editor .tlui-offline-indicator {
  display: flex;
  flex-direction: row;
  gap: var(--space-3);
  color: var(--color-text);
  background-color: var(--color-low);
  border: 3px solid var(--color-background);
  padding: 0px var(--space-5);
  height: 42px;
  align-items: center;
  justify-content: center;
  border-radius: 99px;
  opacity: 0;
  animation: fade-in;
  animation-duration: 0.12s;
  animation-delay: 2s;
  animation-fill-mode: forwards;
}
.ddc_ink_drawing-editor .tlui-shortcuts-dialog__header,
.ddc_ink_writing-editor .tlui-shortcuts-dialog__header {
  border-bottom: 1px solid var(--color-divider);
}
.ddc_ink_drawing-editor .tlui-shortcuts-dialog__body,
.ddc_ink_writing-editor .tlui-shortcuts-dialog__body {
  position: relative;
  columns: 3;
  column-gap: var(--space-9);
  pointer-events: all;
  touch-action: auto;
}
.ddc_ink_drawing-editor .tlui-shortcuts-dialog__body__tablet,
.ddc_ink_writing-editor .tlui-shortcuts-dialog__body__tablet {
  columns: 2;
}
.ddc_ink_drawing-editor .tlui-shortcuts-dialog__body__mobile,
.ddc_ink_writing-editor .tlui-shortcuts-dialog__body__mobile {
  columns: 1;
}
.ddc_ink_drawing-editor .tlui-shortcuts-dialog__group,
.ddc_ink_writing-editor .tlui-shortcuts-dialog__group {
  break-inside: avoid-column;
  padding-bottom: var(--space-6);
}
.ddc_ink_drawing-editor .tlui-shortcuts-dialog__group__title,
.ddc_ink_writing-editor .tlui-shortcuts-dialog__group__title {
  font-size: inherit;
  font-weight: inherit;
  margin: 0px;
  color: var(--color-text-3);
  height: 32px;
  display: flex;
  align-items: center;
}
.ddc_ink_drawing-editor .tlui-shortcuts-dialog__group__content,
.ddc_ink_writing-editor .tlui-shortcuts-dialog__group__content {
  display: flex;
  flex-direction: column;
  color: var(--color-text-1);
}
.ddc_ink_drawing-editor .tlui-shortcuts-dialog__key-pair,
.ddc_ink_writing-editor .tlui-shortcuts-dialog__key-pair {
  display: flex;
  gap: var(--space-4);
  align-items: center;
  justify-content: space-between;
  height: 32px;
}
.ddc_ink_drawing-editor .tlui-shortcuts-dialog__key-pair__key,
.ddc_ink_writing-editor .tlui-shortcuts-dialog__key-pair__key {
  flex: 1;
  font-size: 12px;
}
.ddc_ink_drawing-editor {
  transition: height 0.5s ease;
}
.ddc_ink_drawing-editor.preventTransitions {
  transition: none;
}
.ddc_ink_drawing-editor .tl-canvas {
  background: none;
  overflow: hidden;
  border: 2px solid var(--color-base-30);
  border-radius: 20px;
  background-color: var(--color-base-05);
}
.ddc_ink_drawing-editor .tl-canvas .tl-background {
  background: none;
}

/* src/tldraw/drawing-menu/drawing-menu.scss */

/* src/tldraw/extended-drawing-menu/extended-drawing-menu.scss */
.ink_extended-writing-menu {
  display: flex;
  gap: 4px;
  pointer-events: none;
}
.ink_extended-writing-menu button {
  width: auto;
  pointer-events: auto;
  background-color: var(--interactive-normal);
  color: var(--color-base-60);
}
.ink_extended-writing-menu button:hover {
  background-color: var(--interactive-hover);
  color: var(--color-base-100);
}
.ink_extended-writing-menu button:disabled {
  cursor: default;
  color: var(--text-on-accent);
  background-color: var(--interactive-accent);
}
.ink_extended-writing-menu button:disabled:hover {
  box-shadow: var(--input-shadow);
}
.ink_extended-writing-menu button {
  width: 2.5em;
  height: 2.5em;
  border-radius: 0.8em;
  padding: 0 !important;
}
.ink_extended-writing-menu button.ddc_ink_btn-slim {
  width: 1.9em;
  height: 2.5em;
  border-radius: 0.8em;
}
.ink_extended-writing-menu button svg {
  width: 1.5em !important;
  height: 1.5em !important;
}

/* src/tldraw/drawing/drawing-embed.scss */
.markdown-source-view.mod-cm6 .cm-content > .cm-preview-code-block.cm-embed-block.markdown-rendered[contenteditable=false]:has(.ddc_ink_drawing-embed),
.markdown-source-view.mod-cm6 .cm-content > .cm-preview-code-block.cm-embed-block.markdown-rendered[contenteditable=false]:has(.ddc_ink_drawing-embed):hover {
  contain: unset !important;
  overflow: visible;
}
.cm-preview-code-block.cm-embed-block.markdown-rendered:has(.ddc_ink_drawing-embed) {
  margin: 0 calc(-1 * var(--file-margins)) !important;
}
.cm-preview-code-block.cm-embed-block.markdown-rendered:has(.ddc_ink_drawing-embed):hover {
  box-shadow: none;
}
.cm-preview-code-block.cm-embed-block.markdown-rendered:has(.ddc_ink_drawing-embed) .edit-block-button {
  display: none;
}
.cm-preview-code-block.cm-embed-block.markdown-rendered:has(.ddc_ink_drawing-embed) .ddc_ink_drawing-editor .tl-container {
  overflow: visible;
  clip: unset;
  contain: none !important;
}
.cm-preview-code-block.cm-embed-block.markdown-rendered:has(.ddc_ink_drawing-embed) .ddc_ink_drawing-editor .tl-container .tlui-layout {
  overflow: visible;
  clip: unset;
  contain: none !important;
}
.cm-preview-code-block.cm-embed-block.markdown-rendered:has(.ddc_ink_drawing-embed) .ddc_ink_drawing-editor .tl-container .tlui-layout .tlui-button.tlui-menu__trigger {
  display: none;
}
.cm-preview-code-block.cm-embed-block.markdown-rendered:has(.ddc_ink_drawing-embed) .ddc_ink_drawing-editor .tl-container .tlui-layout .tlui-navigation-zone {
  display: none;
}
.cm-preview-code-block.cm-embed-block.markdown-rendered:has(.ddc_ink_drawing-embed) .ddc_ink_drawing-editor .tl-container .tlui-layout .tlui-help-menu {
  display: none;
}
.cm-preview-code-block.cm-embed-block.markdown-rendered:has(.ddc_ink_drawing-embed) .ddc_ink_drawing-editor .tl-container .tlui-layout .tlui-layout__top {
  grid-row: 1;
  position: absolute;
  top: 0;
  right: 0;
  translate: 0 -100%;
}
.cm-preview-code-block.cm-embed-block.markdown-rendered:has(.ddc_ink_drawing-embed) .ddc_ink_drawing-editor .tl-container .tlui-layout .tlui-layout__bottom {
  grid-row: 1;
  position: absolute;
  top: 0;
  left: 0;
  translate: 0 -100%;
}
.ddc_ink_drawing-embed {
  box-sizing: content-box;
  transition-property: padding;
  transition-duration: 0.5s;
  transition-timing-function: ease-out;
}
.theme-dark .ddc_ink_drawing-embed-preview path {
  fill: rgb(242, 242, 242);
}
.theme-dark .ddc_ink_drawing-embed-preview .ddc_ink_drawing-placeholder path {
  stroke: rgb(242, 242, 242) !important;
}
.theme-dark .ddc_ink_drawing-embed-preview .ddc_ink_drawing-placeholder rect,
.theme-dark .ddc_ink_drawing-embed-preview .ddc_ink_drawing-placeholder circle {
  stroke: rgb(242, 242, 242) !important;
}
.theme-dark .ddc_ink_drawing-embed-preview rect,
.theme-dark .ddc_ink_drawing-embed-preview circle {
  stroke: rgb(242, 242, 242);
}
.theme-light .ddc_ink_drawing-embed-preview path {
  fill: rgb(29, 29, 29);
}
.theme-light .ddc_ink_drawing-embed-preview .ddc_ink_drawing-placeholder path {
  stroke: rgb(29, 29, 29) !important;
}
.theme-light .ddc_ink_drawing-embed-preview .ddc_ink_drawing-placeholder rect,
.theme-light .ddc_ink_drawing-embed-preview .ddc_ink_drawing-placeholder circle {
  stroke: rgb(29, 29, 29) !important;
}

/* src/tldraw/drawing/drawing-embed-preview/drawing-embed-preview.scss */
.ddc_ink_drawing-embed-preview {
  pointer-events: none;
}
.ddc_ink_drawing-embed-preview img {
  pointer-events: auto;
  cursor: pointer;
}
.ddc_ink_drawing-embed-preview.ddc_ink_visible-background {
  background-color: var(--color-base-05);
}
.ddc_ink_drawing-embed-preview.ddc_ink_visible-frame {
  border: 2px solid var(--color-base-30);
  border-radius: 20px;
}
/*# sourceMappingURL=data:application/json;base64,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 */
