# Past DUE
```tasks
not done
sort by priority
due before 2024-09-03
tags include #J<PERSON> 
```
# DUE TODAY
```tasks
not done
sort by priority
due on 2024-09-03
tags include #J<PERSON> 
```
# DUE This Week
```tasks
not done
sort by priority
due between last friday and this friday
tags include #JC 
```
# Assigned to [[<PERSON>|<PERSON>]]
```tasks 
not done
tags include JC
```
# LOG
Had an eventful weekend with the [[Alatrade]] install and the [[Carthage, MS|Carthage]] install. 
- The [[Pumped Rechiller|Rechiller]] ([[PPV-075]]) at [[Alatrade]] had a bug where once the mechanical high limit trips the acknowledge wasn't clearing the fault. I added a button to the alarm screen to clear it. [[Scot Arndt|Scot]] was getting very frustrated and that was the quickest way to get it resolved.
- [[<PERSON>|<PERSON>]] had a couple issues with the [[Steam Valve]] on [[241005]]. 
- [[<PERSON>|<PERSON>]] was at the [[Carthage, MS|Carthage]] install of the [[Marination HEX]] [[PPV-036]]. He called me [[2024-09-02 Monday JC|Monday]] and asked why it had a float column on the program. I told him that was the wrong program. I walked him through the very painful process of installing [[Connected Components Workbench|CCW]] 
[[Weekly Engineering meeting 2024-09-03]] 

# Tasks
- [x] #JC get the overloads ordered for the paw heaters 📅 2024-09-03 ✅ 2024-09-09
- [x] #JC swap the location of the plugin modules on the paw heater Schematics 📅 2024-09-06 ✅ 2024-09-19