# Engineering Tools Suite - System Diagram

```mermaid
graph TD
    %% Main Components
    AppLauncher[Apps Launcher]
    ConfigApp[Configuration App]
    AddModel[Add Model Tool]
    
    %% Core Libraries
    LibUtils[Utility Library]
    LibTheme[Theme Utilities]
    LibManualCreator[Manual Creator Library]
    LibPDFPrinting[PDF Printing Library]
    
    %% Configuration
    ModelsJSON[models.json]
    ConfigJSON[config.json]
    
    %% Applications
    PublishApp[Publish Tool]
    CreateManuals[Manual Creator App]
    CopyMergedFiles[MERGED Files Copy Tool]
    PrintPDFSections[PDF Section Printing]
    CreateJobJSON[Job Data Creator]
    SumMotorFLA[Motor FLA Calculator]
    UpdateDocs[Documentation Status Updater]
    
    %% External Systems
    WordApp[Microsoft Word]
    CADApp[CAD Application]
    ObsidianVault[Obsidian Vault]
    
    %% Relationships - Configuration Flow
    AppLauncher --> ConfigApp
    ConfigApp --> ModelsJSON
    ConfigApp --> ConfigJSON
    AddModel --> ModelsJSON
    
    %% Utility Dependencies
    LibUtils --> AppLauncher
    LibUtils --> ConfigApp
    LibUtils --> PublishApp
    LibUtils --> CreateManuals
    LibUtils --> CopyMergedFiles
    LibUtils --> PrintPDFSections
    LibUtils --> CreateJobJSON
    LibUtils --> SumMotorFLA
    LibUtils --> UpdateDocs
    
    %% Theme Dependencies
    LibTheme --> AppLauncher
    LibTheme --> ConfigApp
    LibTheme --> PublishApp
    LibTheme --> CreateManuals
    LibTheme --> CopyMergedFiles
    LibTheme --> PrintPDFSections
    LibTheme --> CreateJobJSON
    LibTheme --> SumMotorFLA
    LibTheme --> UpdateDocs
    
    %% Configuration Dependencies
    ModelsJSON --> PublishApp
    ModelsJSON --> CreateManuals
    ConfigJSON --> PublishApp
    ConfigJSON --> CreateManuals
    
    %% Library Dependencies
    LibManualCreator --> CreateManuals
    LibManualCreator --> PublishApp
    LibPDFPrinting --> PrintPDFSections
    
    %% External System Interactions
    CreateManuals --> WordApp
    PublishApp --> CADApp
    PublishApp --> WordApp
    UpdateDocs --> ObsidianVault
    
    %% App Launcher Relationships
    AppLauncher --> PublishApp
    AppLauncher --> CreateManuals
    AppLauncher --> CopyMergedFiles
    AppLauncher --> PrintPDFSections
    AppLauncher --> CreateJobJSON
    AppLauncher --> SumMotorFLA
    AppLauncher --> UpdateDocs
    
    %% Data Flow
    PublishApp -- "Creates Job Data" --> CreateJobJSON
    PublishApp -- "Can trigger" --> CreateManuals
    CopyMergedFiles -- "Prepares files for" --> PrintPDFSections
    
    %% Subgraph for Resources
    subgraph Resources
        Templates[Document Templates]
        Drawings[Engineering Drawings]
    end
    
    Templates --> CreateManuals
    Drawings --> CreateManuals
    Templates --> PublishApp
    
    %% Styling
    classDef mainApp fill:#f96,stroke:#333,stroke-width:2px;
    classDef library fill:#9cf,stroke:#333,stroke-width:1px;
    classDef config fill:#fcf,stroke:#333,stroke-width:1px;
    classDef app fill:#cf9,stroke:#333,stroke-width:1px;
    classDef external fill:#ccc,stroke:#333,stroke-width:1px;
    classDef resource fill:#ffc,stroke:#333,stroke-width:1px;
    
    class AppLauncher,ConfigApp,AddModel mainApp;
    class LibUtils,LibTheme,LibManualCreator,LibPDFPrinting library;
    class ModelsJSON,ConfigJSON config;
    class PublishApp,CreateManuals,CopyMergedFiles,PrintPDFSections,CreateJobJSON,SumMotorFLA,UpdateDocs app;
    class WordApp,CADApp,ObsidianVault external;
    class Templates,Drawings resource;
```

## Data Flow Diagram

```mermaid
flowchart TD
    %% Main Components
    User((User))
    
    %% Configuration
    Config[Configuration Files]
    
    %% Applications
    AppLauncher[Apps Launcher]
    PublishApp[Publish Tool]
    ManualCreator[Manual Creator]
    CopyMerged[MERGED Files Copy Tool]
    PrintPDF[PDF Section Printing]
    
    %% External Systems
    CAD[CAD System]
    Word[Microsoft Word]
    Printer[Printer]
    FileSystem[File System]
    
    %% User Interactions
    User --> AppLauncher
    User --> PublishApp
    User --> ManualCreator
    User --> CopyMerged
    User --> PrintPDF
    
    %% Configuration Flow
    Config --> AppLauncher
    Config --> PublishApp
    Config --> ManualCreator
    
    %% Data Flow - Publish Tool
    CAD --> PublishApp
    PublishApp --> FileSystem
    PublishApp --> ManualCreator
    
    %% Data Flow - Manual Creator
    FileSystem --> ManualCreator
    ManualCreator --> Word
    Word --> FileSystem
    
    %% Data Flow - Copy Merged Files
    FileSystem --> CopyMerged
    CopyMerged --> FileSystem
    
    %% Data Flow - PDF Printing
    FileSystem --> PrintPDF
    PrintPDF --> Printer
    
    %% Styling
    classDef user fill:#f96,stroke:#333,stroke-width:2px;
    classDef app fill:#9cf,stroke:#333,stroke-width:1px;
    classDef system fill:#cfc,stroke:#333,stroke-width:1px;
    classDef data fill:#fcf,stroke:#333,stroke-width:1px;
    
    class User user;
    class AppLauncher,PublishApp,ManualCreator,CopyMerged,PrintPDF app;
    class CAD,Word,Printer,FileSystem system;
    class Config data;
```

## Component Architecture

```mermaid
graph LR
    %% Main Layers
    subgraph UI["User Interface Layer"]
        CustomTkinter[CustomTkinter Framework]
        ThemeUtils[Theme Utilities]
        AppLauncher[Apps Launcher]
        AppGUIs[Application GUIs]
    end
    
    subgraph Business["Business Logic Layer"]
        ManualCreator[Manual Creator]
        PDFProcessor[PDF Processing]
        CADIntegration[CAD Integration]
        ObsidianIntegration[Obsidian Integration]
    end
    
    subgraph Data["Data Layer"]
        ConfigFiles[Configuration Files]
        Templates[Document Templates]
        Drawings[Engineering Drawings]
        FileSystem[File System]
    end
    
    subgraph External["External Systems"]
        MSWord[Microsoft Word]
        CADSystem[CAD System]
        ObsidianVault[Obsidian Vault]
        Printers[Printers]
    end
    
    %% Connections between layers
    UI --> Business
    Business --> Data
    Business --> External
    
    %% Detailed connections
    CustomTkinter --> AppLauncher
    CustomTkinter --> AppGUIs
    ThemeUtils --> CustomTkinter
    
    AppLauncher --> ManualCreator
    AppLauncher --> PDFProcessor
    AppLauncher --> CADIntegration
    AppLauncher --> ObsidianIntegration
    
    ManualCreator --> ConfigFiles
    ManualCreator --> Templates
    ManualCreator --> Drawings
    ManualCreator --> MSWord
    
    PDFProcessor --> FileSystem
    PDFProcessor --> Printers
    
    CADIntegration --> CADSystem
    ObsidianIntegration --> ObsidianVault
    
    %% Styling
    classDef ui fill:#f96,stroke:#333,stroke-width:2px;
    classDef business fill:#9cf,stroke:#333,stroke-width:1px;
    classDef data fill:#cfc,stroke:#333,stroke-width:1px;
    classDef external fill:#fcf,stroke:#333,stroke-width:1px;
    
    class UI ui;
    class Business business;
    class Data data;
    class External external;
```
