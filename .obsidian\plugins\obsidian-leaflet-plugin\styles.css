/* required styles */

.leaflet-pane,
.leaflet-tile,
.leaflet-marker-icon,
.leaflet-marker-shadow,
.leaflet-tile-container,
.leaflet-pane > svg,
.leaflet-pane > canvas,
.leaflet-zoom-box,
.leaflet-image-layer,
.leaflet-layer {
	position: absolute;
	left: 0;
	top: 0;
	}
.leaflet-container {
	overflow: hidden;
	}
.leaflet-tile,
.leaflet-marker-icon,
.leaflet-marker-shadow {
	-webkit-user-select: none;
	   -moz-user-select: none;
	        user-select: none;
	  -webkit-user-drag: none;
	}
/* Prevents IE11 from highlighting tiles in blue */
.leaflet-tile::selection {
	background: transparent;
}
/* <PERSON><PERSON> renders non-retina tile on retina better with this, but Chrome is worse */
.leaflet-safari .leaflet-tile {
	image-rendering: -webkit-optimize-contrast;
	}
/* hack that prevents hw layers "stretching" when loading new tiles */
.leaflet-safari .leaflet-tile-container {
	width: 1600px;
	height: 1600px;
	-webkit-transform-origin: 0 0;
	}
.leaflet-marker-icon,
.leaflet-marker-shadow {
	display: block;
	}
/* .leaflet-container svg: reset svg max-width decleration shipped in Joomla! (joomla.org) 3.x */
/* .leaflet-container img: map is broken in FF if you have max-width: 100% on tiles */
.leaflet-container .leaflet-overlay-pane svg,
.leaflet-container .leaflet-marker-pane img,
.leaflet-container .leaflet-shadow-pane img,
.leaflet-container .leaflet-tile-pane img,
.leaflet-container img.leaflet-image-layer,
.leaflet-container .leaflet-tile {
	max-width: none !important;
	max-height: none !important;
	}

.leaflet-container.leaflet-touch-zoom {
	-ms-touch-action: pan-x pan-y;
	touch-action: pan-x pan-y;
	}
.leaflet-container.leaflet-touch-drag {
	-ms-touch-action: pinch-zoom;
	/* Fallback for FF which doesn't support pinch-zoom */
	touch-action: none;
	touch-action: pinch-zoom;
}
.leaflet-container.leaflet-touch-drag.leaflet-touch-zoom {
	-ms-touch-action: none;
	touch-action: none;
}
.leaflet-container {
	-webkit-tap-highlight-color: transparent;
}
.leaflet-container a {
	-webkit-tap-highlight-color: rgba(51, 181, 229, 0.4);
}
.leaflet-tile {
	filter: inherit;
	visibility: hidden;
	}
.leaflet-tile-loaded {
	visibility: inherit;
	}
.leaflet-zoom-box {
	width: 0;
	height: 0;
	-moz-box-sizing: border-box;
	     box-sizing: border-box;
	z-index: 800;
	}
/* workaround for https://bugzilla.mozilla.org/show_bug.cgi?id=888319 */
.leaflet-overlay-pane svg {
	-moz-user-select: none;
	}

.leaflet-pane         { z-index: 400; }

.leaflet-tile-pane    { z-index: 200; }
.leaflet-overlay-pane { z-index: 400; }
.leaflet-shadow-pane  { z-index: 500; }
.leaflet-marker-pane  { z-index: 600; }
.leaflet-tooltip-pane   { z-index: 650; }
.leaflet-popup-pane   { z-index: 700; }

.leaflet-map-pane canvas { z-index: 100; }
.leaflet-map-pane svg    { z-index: 200; }

.leaflet-vml-shape {
	width: 1px;
	height: 1px;
	}
.lvml {
	behavior: url(#default#VML);
	display: inline-block;
	position: absolute;
	}


/* control positioning */

.leaflet-control {
	position: relative;
	z-index: 800;
	pointer-events: visiblePainted; /* IE 9-10 doesn't have auto */
	pointer-events: auto;
	}
.leaflet-top,
.leaflet-bottom {
	position: absolute;
	z-index: 1000;
	pointer-events: none;
	}
.leaflet-top {
	top: 0;
	}
.leaflet-right {
	right: 0;
	}
.leaflet-bottom {
	bottom: 0;
	}
.leaflet-left {
	left: 0;
	}
.leaflet-control {
	float: left;
	clear: both;
	}
.leaflet-right .leaflet-control {
	float: right;
	}
.leaflet-top .leaflet-control {
	margin-top: 10px;
	}
.leaflet-bottom .leaflet-control {
	margin-bottom: 10px;
	}
.leaflet-left .leaflet-control {
	margin-left: 10px;
	}
.leaflet-right .leaflet-control {
	margin-right: 10px;
	}


/* zoom and fade animations */

.leaflet-fade-anim .leaflet-tile {
	will-change: opacity;
	}
.leaflet-fade-anim .leaflet-popup {
	opacity: 0;
	-webkit-transition: opacity 0.2s linear;
	   -moz-transition: opacity 0.2s linear;
	        transition: opacity 0.2s linear;
	}
.leaflet-fade-anim .leaflet-map-pane .leaflet-popup {
	opacity: 1;
	}
.leaflet-zoom-animated {
	-webkit-transform-origin: 0 0;
	    -ms-transform-origin: 0 0;
	        transform-origin: 0 0;
	}
.leaflet-zoom-anim .leaflet-zoom-animated {
	will-change: transform;
	}
.leaflet-zoom-anim .leaflet-zoom-animated {
	-webkit-transition: -webkit-transform 0.25s cubic-bezier(0,0,0.25,1);
	   -moz-transition:    -moz-transform 0.25s cubic-bezier(0,0,0.25,1);
	        transition:         transform 0.25s cubic-bezier(0,0,0.25,1);
	}
.leaflet-zoom-anim .leaflet-tile,
.leaflet-pan-anim .leaflet-tile {
	-webkit-transition: none;
	   -moz-transition: none;
	        transition: none;
	}

.leaflet-zoom-anim .leaflet-zoom-hide {
	visibility: hidden;
	}


/* cursors */

.leaflet-interactive {
	cursor: pointer;
	}
.leaflet-grab {
	cursor: -webkit-grab;
	cursor:    -moz-grab;
	cursor:         grab;
	}
.leaflet-crosshair,
.leaflet-crosshair .leaflet-interactive {
	cursor: crosshair;
	}
.leaflet-popup-pane,
.leaflet-control {
	cursor: auto;
	}
.leaflet-dragging .leaflet-grab,
.leaflet-dragging .leaflet-grab .leaflet-interactive,
.leaflet-dragging .leaflet-marker-draggable {
	cursor: move;
	cursor: -webkit-grabbing;
	cursor:    -moz-grabbing;
	cursor:         grabbing;
	}

/* marker & overlays interactivity */
.leaflet-marker-icon,
.leaflet-marker-shadow,
.leaflet-image-layer,
.leaflet-pane > svg path,
.leaflet-tile-container {
	pointer-events: none;
	}

.leaflet-marker-icon.leaflet-interactive,
.leaflet-image-layer.leaflet-interactive,
.leaflet-pane > svg path.leaflet-interactive,
svg.leaflet-image-layer.leaflet-interactive path {
	pointer-events: visiblePainted; /* IE 9-10 doesn't have auto */
	pointer-events: auto;
	}

/* visual tweaks */

.leaflet-container {
	background: #ddd;
	outline: 0;
	}
.leaflet-container a {
	color: #0078A8;
	}
.leaflet-container a.leaflet-active {
	outline: 2px solid orange;
	}
.leaflet-zoom-box {
	border: 2px dotted #38f;
	background: rgba(255,255,255,0.5);
	}


/* general typography */
.leaflet-container {
	font: 12px/1.5 "Helvetica Neue", Arial, Helvetica, sans-serif;
	}


/* general toolbar styles */

.leaflet-bar {
	box-shadow: 0 1px 5px rgba(0,0,0,0.65);
	border-radius: 4px;
	}
.leaflet-bar a,
.leaflet-bar a:hover {
	background-color: #fff;
	border-bottom: 1px solid #ccc;
	width: 26px;
	height: 26px;
	line-height: 26px;
	display: block;
	text-align: center;
	text-decoration: none;
	color: black;
	}
.leaflet-bar a,
.leaflet-control-layers-toggle {
	background-position: 50% 50%;
	background-repeat: no-repeat;
	display: block;
	}
.leaflet-bar a:hover {
	background-color: #f4f4f4;
	}
.leaflet-bar a:first-child {
	border-top-left-radius: 4px;
	border-top-right-radius: 4px;
	}
.leaflet-bar a:last-child {
	border-bottom-left-radius: 4px;
	border-bottom-right-radius: 4px;
	border-bottom: none;
	}
.leaflet-bar a.leaflet-disabled {
	cursor: default;
	background-color: #f4f4f4;
	color: #bbb;
	}

.leaflet-touch .leaflet-bar a {
	width: 30px;
	height: 30px;
	line-height: 30px;
	}
.leaflet-touch .leaflet-bar a:first-child {
	border-top-left-radius: 2px;
	border-top-right-radius: 2px;
	}
.leaflet-touch .leaflet-bar a:last-child {
	border-bottom-left-radius: 2px;
	border-bottom-right-radius: 2px;
	}

/* zoom control */

.leaflet-control-zoom-in,
.leaflet-control-zoom-out {
	font: bold 18px 'Lucida Console', Monaco, monospace;
	text-indent: 1px;
	}

.leaflet-touch .leaflet-control-zoom-in, .leaflet-touch .leaflet-control-zoom-out  {
	font-size: 22px;
	}


/* layers control */

.leaflet-control-layers {
	box-shadow: 0 1px 5px rgba(0,0,0,0.4);
	background: #fff;
	border-radius: 5px;
	}
.leaflet-control-layers-toggle {
	background-image: url(images/layers.png);
	width: 36px;
	height: 36px;
	}
.leaflet-retina .leaflet-control-layers-toggle {
	background-image: url(images/layers-2x.png);
	background-size: 26px 26px;
	}
.leaflet-touch .leaflet-control-layers-toggle {
	width: 44px;
	height: 44px;
	}
.leaflet-control-layers .leaflet-control-layers-list,
.leaflet-control-layers-expanded .leaflet-control-layers-toggle {
	display: none;
	}
.leaflet-control-layers-expanded .leaflet-control-layers-list {
	display: block;
	position: relative;
	}
.leaflet-control-layers-expanded {
	padding: 6px 10px 6px 6px;
	color: #333;
	background: #fff;
	}
.leaflet-control-layers-scrollbar {
	overflow-y: scroll;
	overflow-x: hidden;
	padding-right: 5px;
	}
.leaflet-control-layers-selector {
	margin-top: 2px;
	position: relative;
	top: 1px;
	}
.leaflet-control-layers label {
	display: block;
	}
.leaflet-control-layers-separator {
	height: 0;
	border-top: 1px solid #ddd;
	margin: 5px -10px 5px -6px;
	}

/* Default icon URLs */
.leaflet-default-icon-path {
	background-image: url(images/marker-icon.png);
	}


/* attribution and scale controls */

.leaflet-container .leaflet-control-attribution {
	background: #fff;
	background: rgba(255, 255, 255, 0.7);
	margin: 0;
	}
.leaflet-control-attribution,
.leaflet-control-scale-line {
	padding: 0 5px;
	color: #333;
	}
.leaflet-control-attribution a {
	text-decoration: none;
	}
.leaflet-control-attribution a:hover {
	text-decoration: underline;
	}
.leaflet-container .leaflet-control-attribution,
.leaflet-container .leaflet-control-scale {
	font-size: 11px;
	}
.leaflet-left .leaflet-control-scale {
	margin-left: 5px;
	}
.leaflet-bottom .leaflet-control-scale {
	margin-bottom: 5px;
	}
.leaflet-control-scale-line {
	border: 2px solid #777;
	border-top: none;
	line-height: 1.1;
	padding: 2px 5px 1px;
	font-size: 11px;
	white-space: nowrap;
	overflow: hidden;
	-moz-box-sizing: border-box;
	     box-sizing: border-box;

	background: #fff;
	background: rgba(255, 255, 255, 0.5);
	}
.leaflet-control-scale-line:not(:first-child) {
	border-top: 2px solid #777;
	border-bottom: none;
	margin-top: -2px;
	}
.leaflet-control-scale-line:not(:first-child):not(:last-child) {
	border-bottom: 2px solid #777;
	}

.leaflet-touch .leaflet-control-attribution,
.leaflet-touch .leaflet-control-layers,
.leaflet-touch .leaflet-bar {
	box-shadow: none;
	}
.leaflet-touch .leaflet-control-layers,
.leaflet-touch .leaflet-bar {
	border: 2px solid rgba(0,0,0,0.2);
	background-clip: padding-box;
	}


/* popup */

.leaflet-popup {
	position: absolute;
	text-align: center;
	margin-bottom: 20px;
	}
.leaflet-popup-content-wrapper {
	padding: 1px;
	text-align: left;
	border-radius: 12px;
	}
.leaflet-popup-content {
	margin: 13px 19px;
	line-height: 1.4;
	}
.leaflet-popup-content p {
	margin: 18px 0;
	}
.leaflet-popup-tip-container {
	width: 40px;
	height: 20px;
	position: absolute;
	left: 50%;
	margin-left: -20px;
	overflow: hidden;
	pointer-events: none;
	}
.leaflet-popup-tip {
	width: 17px;
	height: 17px;
	padding: 1px;

	margin: -10px auto 0;

	-webkit-transform: rotate(45deg);
	   -moz-transform: rotate(45deg);
	    -ms-transform: rotate(45deg);
	        transform: rotate(45deg);
	}
.leaflet-popup-content-wrapper,
.leaflet-popup-tip {
	background: white;
	color: #333;
	box-shadow: 0 3px 14px rgba(0,0,0,0.4);
	}
.leaflet-container a.leaflet-popup-close-button {
	position: absolute;
	top: 0;
	right: 0;
	padding: 4px 4px 0 0;
	border: none;
	text-align: center;
	width: 18px;
	height: 14px;
	font: 16px/14px Tahoma, Verdana, sans-serif;
	color: #c3c3c3;
	text-decoration: none;
	font-weight: bold;
	background: transparent;
	}
.leaflet-container a.leaflet-popup-close-button:hover {
	color: #999;
	}
.leaflet-popup-scrolled {
	overflow: auto;
	border-bottom: 1px solid #ddd;
	border-top: 1px solid #ddd;
	}

.leaflet-oldie .leaflet-popup-content-wrapper {
	-ms-zoom: 1;
	}
.leaflet-oldie .leaflet-popup-tip {
	width: 24px;
	margin: 0 auto;

	-ms-filter: "progid:DXImageTransform.Microsoft.Matrix(M11=0.70710678, M12=0.70710678, M21=-0.70710678, M22=0.70710678)";
	filter: progid:DXImageTransform.Microsoft.Matrix(M11=0.70710678, M12=0.70710678, M21=-0.70710678, M22=0.70710678);
	}
.leaflet-oldie .leaflet-popup-tip-container {
	margin-top: -1px;
	}

.leaflet-oldie .leaflet-control-zoom,
.leaflet-oldie .leaflet-control-layers,
.leaflet-oldie .leaflet-popup-content-wrapper,
.leaflet-oldie .leaflet-popup-tip {
	border: 1px solid #999;
	}


/* div icon */

.leaflet-div-icon {
	background: #fff;
	border: 1px solid #666;
	}


/* Tooltip */
/* Base styles for the element that has a tooltip */
.leaflet-tooltip {
	position: absolute;
	padding: 6px;
	background-color: #fff;
	border: 1px solid #fff;
	border-radius: 3px;
	color: #222;
	white-space: nowrap;
	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;
	pointer-events: none;
	box-shadow: 0 1px 3px rgba(0,0,0,0.4);
	}
.leaflet-tooltip.leaflet-clickable {
	cursor: pointer;
	pointer-events: auto;
	}
.leaflet-tooltip-top:before,
.leaflet-tooltip-bottom:before,
.leaflet-tooltip-left:before,
.leaflet-tooltip-right:before {
	position: absolute;
	pointer-events: none;
	border: 6px solid transparent;
	background: transparent;
	content: "";
	}

/* Directions */

.leaflet-tooltip-bottom {
	margin-top: 6px;
}
.leaflet-tooltip-top {
	margin-top: -6px;
}
.leaflet-tooltip-bottom:before,
.leaflet-tooltip-top:before {
	left: 50%;
	margin-left: -6px;
	}
.leaflet-tooltip-top:before {
	bottom: 0;
	margin-bottom: -12px;
	border-top-color: #fff;
	}
.leaflet-tooltip-bottom:before {
	top: 0;
	margin-top: -12px;
	margin-left: -6px;
	border-bottom-color: #fff;
	}
.leaflet-tooltip-left {
	margin-left: -6px;
}
.leaflet-tooltip-right {
	margin-left: 6px;
}
.leaflet-tooltip-left:before,
.leaflet-tooltip-right:before {
	top: 50%;
	margin-top: -6px;
	}
.leaflet-tooltip-left:before {
	right: 0;
	margin-right: -12px;
	border-left-color: #fff;
	}
.leaflet-tooltip-right:before {
	left: 0;
	margin-left: -12px;
	border-right-color: #fff;
	}

/* Settings */
.icon > .suggestion-flair {
    background-color: unset;
    width: 12px;
    height: 16px;
    font-size: 12px;
    text-align: center;
}

.obsidian-leaflet-settings .coffee {
    border-top: 1px solid var(--background-modifier-border);
    width: 100%;
    color: var(--text-faint);
    padding: 1rem;
    text-align: center;
}
.obsidian-leaflet-settings .coffee img {
    height: 30px;
}

.additional-markers-container > .setting-item:not(.setting-item-heading) {
    border: 0px;
}

.additional-markers-container {
    border-top: 1px solid var(--background-modifier-border);
    padding: 18px 0 0 0;
}

.additional-markers-container > .setting-item-heading:only-child {
    padding-bottom: 18px;
}

.additional-markers-control > input:first-of-type {
    margin-right: auto !important;
}

.additional-markers-container > .additional-markers {
    margin: 6px 12px;
}
.additional-markers-container > .additional-markers > .setting-item {
    border-top: 0;
    padding-top: 9px;
}
.additional-markers-container
    > .additional-markers
    > .setting-item
    > .setting-item-control
    > *:first-child {
    margin: 0 6px;
}

.setting-item-name > .marker-type-display {
    display: flex;
    justify-content: flex-start;
}
.marker-type-display > .marker-icon-display {
    margin-right: 12px;
    font-size: 24px;
    width: 18px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.marker-creation-modal {
    padding-top: 18px;
    display: grid;
    grid-template-columns: 75% 1fr;
    grid-template-rows: 1fr;
    gap: 1rem;
}

.marker-creation-modal .setting-item {
    border-top: none;
}

.marker-creation-modal .icon-display {
    padding: 1rem;
}

.marker-creation-modal .icon-display canvas {
    width: 100%;
}

.markers {
    display: none;
}

.marker-icon-display * {
    margin-right: 0px !important;
}

.marker-icon-display input[type="color"] {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    opacity: 0;
    width: 100%;
    height: 100%;
    margin-right: 12px;
}

.full-width-height,
.full-width-height > * {
    height: 100% !important;
    width: 100% !important;
}

.full-width,
.full-width > * {
    width: 100% !important;
}

/** Invalid Setting */

.leaflet-settings-modal .unset-align-items {
    align-items: unset;
}

.leaflet-settings-modal .has-invalid-message {
    flex-grow: unset;
    flex-flow: column nowrap;
}

.leaflet-settings-modal input.is-invalid {
    border-color: #dc3545 !important;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

.leaflet-settings-modal .invalid-feedback {
    display: block;
    width: 100%;
    margin-top: 0.25rem;
    font-size: 0.875em;
    color: #dc3545;
}

.block-language-leaflet {
    margin: 0 auto;
}

.block-language-leaflet > .leaflet-container {
    z-index: 0;
    background-color: var(--background-secondary-alt);
}

.block-language-leaflet > .adding-text {
    cursor: text;
}

.block-language-leaflet .leaflet-bar a {
    font-style: unset;
}
.block-language-leaflet .leaflet-bar.disabled {
    cursor: not-allowed;
}
.block-language-leaflet .leaflet-bar.disabled > a {
    background-color: lightgrey;
    pointer-events: none;
}

/* immutable leaflet markers */
/* .leaflet-marker-icon:not(.leaflet-marker-draggable) {
    cursor: not-allowed;
}
 */
/* div icon override */
.block-language-leaflet .leaflet-div-icon img {
    pointer-events: none;
    cursor: pointer;
}

.block-language-leaflet .leaflet-div-icon {
    background: transparent !important;
    border: none !important;
    width: 25px !important;
    height: 25px !important;
    margin-left: -12.5px !important;
    margin-top: -25px !important;
}

.block-language-leaflet .leaflet-marker-link-tooltip {
    box-shadow: 0 2px 8px var(--background-modifier-box-shadow) !important;
    background-color: rgba(0, 0, 0, 0.9) !important;
    border: 1px solid rgba(0, 0, 0, 0.9) !important;
    border-radius: 6px !important;
    color: #dcddde !important;
    font-size: 14px !important;
    left: 50% !important;
    line-height: 20px !important;
    width: auto !important;
    padding: 5px 14px !important;
    /* pointer-events: none !important; */
    opacity: unset !important;
}

.block-language-leaflet .leaflet-marker-link-popup > * {
    animation: leaflet-pop-down 200ms forwards ease-in-out;
}

@keyframes leaflet-pop-down {
    0% {
        opacity: 0;
        transform: scale(1);
    }

    20% {
        opacity: 0.7;
        transform: scale(1.02);
    }
    40% {
        opacity: 1;
        transform: scale(1.05);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

.block-language-leaflet
    .leaflet-marker-link-popup
    > .leaflet-popup-content-wrapper {
    background-color: rgba(0, 0, 0, 0.9) !important;
    color: #dcddde !important;
    padding: 5px 14px !important;
    border-radius: 6px !important;
    line-height: 20px !important;
}
.block-language-leaflet
    .leaflet-marker-link-popup
    > .leaflet-popup-content-wrapper
    > * {
    margin: 0;
    font-size: 14px;
    text-align: center;
}
.block-language-leaflet .leaflet-marker-link-popup .leaflet-popup-tip {
    background-color: rgba(0, 0, 0, 0.9) !important;
    width: 12px;
    height: 12px;
}

.block-language-leaflet .leaflet-marker-link-tooltip::before {
    border-top-color: rgba(0, 0, 0, 0.9) !important;
}

.block-language-leaflet
    .leaflet-control-layers:not(.leaflet-control-layers-expanded)
    .leaflet-control-layers-toggle {
    background-image: unset !important;
    display: flex !important;
}
.block-language-leaflet
    .leaflet-control-layers:not(.leaflet-control-layers-expanded) {
    width: unset;
    height: unset;
}
.block-language-leaflet .leaflet-control-layers-toggle {
    width: 30px !important;
    height: 30px !important;
}

.block-language-leaflet
    .leaflet-control-layers
    .leaflet-control-layers-selector {
    appearance: auto;
}

.block-language-leaflet .leaflet-control.disabled {
    pointer-events: hover;
}

.block-language-leaflet .leaflet-distance-control {
    background-color: #fff;
    border-radius: 2px;
    min-width: 30px;
    width: auto;
    height: 30px;
    padding: 5px;
    cursor: pointer !important;
}

.block-language-leaflet .leaflet-control-draw-paint-icon > svg {
    stroke: black;
    stroke-width: 25;
}
.block-language-leaflet .leaflet-control-draw-palette {
    position: relative;
}
.block-language-leaflet .leaflet-control input[type="color"] {
    visibility: hidden;
    position: absolute;
    top: 0;
    left: 0;
}
.leaflet-file-upload > input[type="file"] {
    display: none;
}

/** Bulk Marker Edit Settings */
.bulk-setting-hover:not(.marker) {
    box-shadow: 0 0px 8px var(--background-modifier-box-shadow) !important;
}
.bulk-setting-hover.marker::before {
    content: "";
    width: 120%;
    height: 120%;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    border-radius: 100%;
    border: 3px solid #00ff00 !important;
}
/* .bulk-edit-markers {
    width: 675px;
} */
.bulk-edit-markers .setting-item,
.bulk-edit-markers > .setting-item-control {
    padding: 8px 2px;
    margin: 0 8px;
}
.bulk-edit-markers > .setting-item:first-child button {
    margin-right: 0;
}
.bulk-edit-markers-holder {
    height: 250px;
    overflow-y: auto;
}
.bulk-edit-markers-holder::-webkit-scrollbar {
    width: 2px !important;
}
.bulk-edit-marker-instance .setting-item-control {
    display: flex;
    flex-flow: row wrap;
    justify-content: space-between;
}
.bulk-edit-marker-instance .setting-item-control > *:not(:last-child) {
    margin-right: 0.5rem;
}

.block-language-leaflet .leaflet-container .dark-mode {
    filter: brightness(0.6) invert(1) contrast(3) hue-rotate(200deg)
        saturate(0.3) brightness(0.7);
}

.block-language-leaflet .leaflet-container.drawing {
    cursor: crosshair;
}
.block-language-leaflet
    .leaflet-container.shape-dragging
    .leaflet-drawing-pane
    path {
    cursor: move;
}

.block-language-leaflet .leaflet-div-icon.initiative-marker-disabled {
    opacity: 50%;
}
.block-language-leaflet .leaflet-div-icon.initiative-marker-active svg {
    filter: drop-shadow(0 0 3px green);
}
.block-language-leaflet .leaflet-div-icon progress {
    width: 100%;
    top: 95%;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
}

.block-language-leaflet .leaflet-div-icon .initiative-marker-status-container {
    position: absolute;
    top: -6px;
    right: -15px;
    display: flex;
    flex-flow: column wrap;
    justify-content: center;
    align-items: center;
    height: 46px;
    gap: 2px;
}
.block-language-leaflet
    .leaflet-div-icon
    .initiative-marker-status-container
    svg {
    height: 10px;
}
.block-language-leaflet
    .leaflet-div-icon
    .initiative-marker-status-container
    svg
    > * {
    stroke-width: 20px;
    stroke: white;
}

/** Filter Control */
.block-language-leaflet .leaflet-control-expandable {
    background: #fff;
}
.block-language-leaflet
    .leaflet-control-expandable:not(.expanded)
    > .leaflet-control-expandable-list {
    display: none;
}
.block-language-leaflet
    .leaflet-control-expandable.expanded
    > .leaflet-control-expandable-icon {
    display: none;
}
.block-language-leaflet .leaflet-control-expandable .task-list-item > * {
    cursor: pointer;
}

.block-language-leaflet .leaflet-control-expandable .input-container {
    margin: 0;
}
.block-language-leaflet .leaflet-control-expandable.expanded .input-container {
    padding: 6px 10px 6px 6px;
    margin: 0;
}

.block-language-leaflet .leaflet-control-expandable-list .input-item {
    display: flex;
    justify-content: flex-start;
    align-items: center;
}

.block-language-leaflet .leaflet-control-expandable input[type="radio"] {
    margin-top: 0;
}

.block-language-leaflet
    .leaflet-control-expandable-list
    .input-container
    .input-item
    > input {
    top: 0px;
    appearance: auto;
}
.block-language-leaflet
    .leaflet-control-expandable-list
    .input-container
    .input-item
    > label {
    display: flex;
    align-items: center;
    justify-content: flex-start;
}
.block-language-leaflet
    .leaflet-control-expandable-list
    .input-container
    .input-item
    .leaflet-control-expandable-icon {
    width: 18px;
    height: 18px;
    margin-right: 4px;
}
.block-language-leaflet
    .leaflet-control-expandable-list
    .leaflet-control-expandable-button-group {
    margin-bottom: 6px;
    padding: 6px 10px 0px 6px;
}
.block-language-leaflet
    .leaflet-control-expandable-list
    .leaflet-control-expandable-button-group
    button:last-child {
    margin-right: 0px;
}

.block-language-leaflet
    .leaflet-control-expandable.leaflet-control-gpx
    .gpx-data {
    padding: 6px 10px 6px 6px;
    border-bottom: 1px solid var(--background-modifier-border);
}
.block-language-leaflet
    .leaflet-control-expandable.leaflet-control-gpx
    .input-container {
    border-bottom: 1px solid var(--background-modifier-border);
}
.block-language-leaflet
    .leaflet-control-expandable.leaflet-control-gpx
    .control-buttons {
    display: flex;
    justify-content: space-evenly;
    margin: 0.25rem;
}

.block-language-leaflet .leaflet-control-gpx .gpx-elevation {
    display: flex;
    justify-content: space-around;
}
.block-language-leaflet .gpx-elevation .elevation-gain {
    color: green;
}
.block-language-leaflet .gpx-elevation .elevation-loss {
    color: red;
}

.gpx-popup {
    display: flex;
    flex-flow: column;
    justify-content: flex-start;
    align-items: flex-start;
}
.block-language-leaflet
    .leaflet-control-expandable.leaflet-control-draw.expanded
    .leaflet-control-expandable-list {
    display: flex;
    flex-flow: column nowrap;
}

.block-language-leaflet
    .leaflet-control-expandable.leaflet-control-draw.expanded
    .leaflet-control-expandable-list
    .leaflet-control,
.block-language-leaflet .leaflet-control-draw a {
    margin: 0;
    border: 0;
}
.block-language-leaflet
    .leaflet-control-expandable.leaflet-control-draw.expanded
    a {
    margin-right: 0;
}

.block-language-leaflet
    .leaflet-control-expandable.leaflet-control-draw.expanded
    .leaflet-control-expandable-list
    .leaflet-control.leaflet-control-draw-trash,
.block-language-leaflet
    .leaflet-control-expandable.leaflet-control-draw.expanded
    .leaflet-control-expandable-list
    .leaflet-control.leaflet-control-draw-paint {
    border-top: 2px solid rgba(0, 0, 0, 0.2);
    border-top-left-radius: 0;
    border-top-right-radius: 0;
}
.block-language-leaflet .leaflet-control-has-actions {
    position: relative;
}

.block-language-leaflet .leaflet-control-has-actions .control-actions.expanded {
    display: flex;
    background-color: white;
}
.block-language-leaflet .leaflet-control-has-actions .control-actions {
    display: none;
    position: absolute;
    top: -1px;
    right: 100%;
    border: 1px solid #ccc;
    border-top-left-radius: 2px;
    border-bottom-left-radius: 2px;
}

.block-language-leaflet
    .leaflet-control-has-actions
    .control-actions
    .leaflet-control
    a {
    border-radius: 0;
}

.block-language-leaflet .leaflet-control-arrow.active,
.block-language-leaflet .leaflet-control-fill-color.active {
    background-color: #3388ff;
    color: white;
    border-radius: 0;
}
.block-language-leaflet .leaflet-control-arrow.active a,
.block-language-leaflet .leaflet-control-fill-color.active a {
    background-color: transparent;
    color: white;
}

.block-language-leaflet .leaflet-div-icon.leaflet-middle-icon,
.block-language-leaflet .leaflet-div-icon.leaflet-vertex-icon {
    border-radius: 100%;
    width: 12px !important;
    height: 12px !important;
    border: 1px solid rgb(204, 204, 204) !important;
    background-color: white !important;
    margin-top: -6px !important;
    margin-left: -6px !important;
    display: block;
}

.block-language-leaflet .leaflet-div-icon.leaflet-middle-icon {
    width: 10px !important;
    height: 10px !important;
    margin-top: -5px !important;
    margin-left: -5px !important;
}

.block-language-leaflet .leaflet-text-entry {
    position: relative;
    width: max-content;
}
.block-language-leaflet .leaflet-text-entry input {
    outline: none;
    position: absolute;
    transform: translate(-50%, 0%);
    left: 50%;
    text-align: center;
    background-color: transparent;
    border: 0;
}
.block-language-leaflet .leaflet-text-entry span {
    width: fit-content;
}

.block-language-leaflet .leaflet-pane.leaflet-geojson-pane {
    z-index: 398;
}
.block-language-leaflet .leaflet-pane.leaflet-image-overlay-pane {
    z-index: 397;
}
.block-language-leaflet .leaflet-pane.leaflet-gpx-pane {
    z-index: 405;
}
.block-language-leaflet .leaflet-pane.leaflet-gpx-canvas-pane {
    z-index: 301;
}
.block-language-leaflet .leaflet-pane.leaflet-base-layer-pane {
    z-index: 300;
}

.hidden-leaflet-popup {
    display: none;
    visibility: hidden;
}
/* 
.block-language-leaflet .leaflet-control-fullscreen a {
    background-image: unset;
} */

.block-language-leaflet .leaflet-control-layers-toggle {
    background-image: unset !important;
}
.block-language-leaflet .leaflet-retina .leaflet-control-layers-toggle {
    background-image: unset !important;
}
.block-language-leaflet .leaflet-default-icon-path {
    background-image: unset !important;
}

.leaflet-layer-targeted {
    filter: drop-shadow(0 0 3px green);
}

.leaflet-edit-parameters .context-buttons {
    display: flex;
    justify-content: flex-end;
    align-items: center;
}

.block-language-leaflet .leaflet-pm-toolbar .button-container {
    margin-top: unset;
}

.leaflet-marker-icon svg {
    filter: drop-shadow(1px 3px 3px black);
}

