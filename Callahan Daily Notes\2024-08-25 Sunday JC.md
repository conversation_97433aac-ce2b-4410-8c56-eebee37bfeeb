# Past DUE
```tasks
not done
sort by priority
due before 2024-08-25
tags include #<PERSON><PERSON> 
```
# DUE TODAY
```tasks
not done
sort by priority
due on 2024-08-25
tags include #JC 
```
# DUE This Week
```tasks
not done
sort by priority
due week of 2024-08-25
tags include #JC 
```
# Assigned to [[<PERSON>|<PERSON>]]
```tasks 
not done
tags include JC
```
# LOG
Got a call from [[<PERSON>|<PERSON>]] and [[<PERSON> Moody|Austin]]. They are at [[Hazlehurst, MS|Hazlehurst]] installing [[PHP-010]]. they said the rtd are sometimes reading 20° off. i told them that it sounds like a  wiring error.
they said the level sensor was locking up. i told them to run through the sensor optimization on the instrument and i am looking for options in the manual
optimization didn't work sent them a new touch probe. [[<PERSON><PERSON>|<PERSON>]] took it down to them


# Tasks
