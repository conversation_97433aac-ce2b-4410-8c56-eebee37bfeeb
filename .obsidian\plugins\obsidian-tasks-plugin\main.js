/*
THIS IS A GENERATED/BUNDLED FILE BY ESBUILD
if you want to view the source visit the plugins github repository
*/

/*
License obsidian-tasks:
MIT License

Copyright (c) 2021 <PERSON>, <PERSON><PERSON> and <PERSON>

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.
*/

/*
License rrule (included library):
rrule.js: Library for working with recurrence rules for calendar dates.
=======================================================================

Copyright 2010, Jakub Roztocil <<EMAIL>> and Lars Schöning

Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions are met:

    1. Redistributions of source code must retain the above copyright notice,
       this list of conditions and the following disclaimer.

    2. Redistributions in binary form must reproduce the above copyright
       notice, this list of conditions and the following disclaimer in the
       documentation and/or other materials provided with the distribution.

    3. Neither the name of The author nor the names of its contributors may
       be used to endorse or promote products derived from this software
       without specific prior written permission.

THIS SOFTWARE IS PROVIDED BY THE AUTHOR AND CONTRIBUTORS "AS IS" AND
ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
DISCLAIMED. IN NO EVENT SHALL THE AUTHOR AND CONTRIBUTORS BE LIABLE FOR
ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
(INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON
ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
(INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.



./rrule.js and ./test/tests.js is based on python-dateutil. LICENCE:

python-dateutil - Extensions to the standard Python datetime module.
====================================================================

Copyright (c) 2003-2011 - Gustavo Niemeyer <<EMAIL>>
Copyright (c) 2012 - Tomi Pieviläinen <<EMAIL>>

All rights reserved.

Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions are met:

    * Redistributions of source code must retain the above copyright notice,
      this list of conditions and the following disclaimer.
    * Redistributions in binary form must reproduce the above copyright notice,
      this list of conditions and the following disclaimer in the documentation
      and/or other materials provided with the distribution.
    * Neither the name of the copyright holder nor the names of its
      contributors may be used to endorse or promote products derived from
      this software without specific prior written permission.

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
"AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR
CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL,
EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR
PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF
LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING
NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
*/

/*
License chrono-node (included library):
The MIT License

Copyright (c) 2014, Wanasit Tanakitrungruang

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in
all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
THE SOFTWARE.
*/

"use strict";var sE=Object.create;var aa=Object.defineProperty,aE=Object.defineProperties,oE=Object.getOwnPropertyDescriptor,uE=Object.getOwnPropertyDescriptors,lE=Object.getOwnPropertyNames,Bo=Object.getOwnPropertySymbols,cE=Object.getPrototypeOf,Sc=Object.prototype.hasOwnProperty,Qh=Object.prototype.propertyIsEnumerable;var Kh=(r,e,t)=>e in r?aa(r,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):r[e]=t,K=(r,e)=>{for(var t in e||(e={}))Sc.call(e,t)&&Kh(r,t,e[t]);if(Bo)for(var t of Bo(e))Qh.call(e,t)&&Kh(r,t,e[t]);return r},he=(r,e)=>aE(r,uE(e));var Ho=(r,e)=>{var t={};for(var n in r)Sc.call(r,n)&&e.indexOf(n)<0&&(t[n]=r[n]);if(r!=null&&Bo)for(var n of Bo(r))e.indexOf(n)<0&&Qh.call(r,n)&&(t[n]=r[n]);return t};var k=(r,e)=>()=>(e||r((e={exports:{}}).exports,e),e.exports),dE=(r,e)=>{for(var t in e)aa(r,t,{get:e[t],enumerable:!0})},Xh=(r,e,t,n)=>{if(e&&typeof e=="object"||typeof e=="function")for(let i of lE(e))!Sc.call(r,i)&&i!==t&&aa(r,i,{get:()=>e[i],enumerable:!(n=oE(e,i))||n.enumerable});return r};var oa=(r,e,t)=>(t=r!=null?sE(cE(r)):{},Xh(e||!r||!r.__esModule?aa(t,"default",{value:r,enumerable:!0}):t,r)),fE=r=>Xh(aa({},"__esModule",{value:!0}),r);var P=(r,e,t)=>new Promise((n,i)=>{var s=u=>{try{o(t.next(u))}catch(l){i(l)}},a=u=>{try{o(t.throw(u))}catch(l){i(l)}},o=u=>u.done?n(u.value):Promise.resolve(u.value).then(s,a);o((t=t.apply(r,e)).next())});var Re=k(Ln=>{"use strict";Object.defineProperty(Ln,"__esModule",{value:!0});Ln.matchAnyPattern=Ln.extractTerms=Ln.repeatedTimeunitPattern=void 0;function gE(r,e){let t=e.replace(/\((?!\?)/g,"(?:");return`${r}${t}\\s{0,5}(?:,?\\s{0,5}${t}){0,10}`}Ln.repeatedTimeunitPattern=gE;function Zh(r){let e;return r instanceof Array?e=[...r]:r instanceof Map?e=Array.from(r.keys()):e=Object.keys(r),e}Ln.extractTerms=Zh;function yE(r){return`(?:${Zh(r).sort((t,n)=>n.length-t.length).join("|").replace(/\./g,"\\.")})`}Ln.matchAnyPattern=yE});var we=k((Dc,xc)=>{(function(r,e){typeof Dc=="object"&&typeof xc!="undefined"?xc.exports=e():typeof define=="function"&&define.amd?define(e):(r=typeof globalThis!="undefined"?globalThis:r||self).dayjs=e()})(Dc,function(){"use strict";var r=1e3,e=6e4,t=36e5,n="millisecond",i="second",s="minute",a="hour",o="day",u="week",l="month",c="quarter",d="year",p="date",m="Invalid Date",y=/^(\d{4})[-/]?(\d{1,2})?[-/]?(\d{0,2})[Tt\s]*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?[.:]?(\d+)?$/,_=/\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g,b={name:"en",weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_")},E=function(j,$,D){var B=String(j);return!B||B.length>=$?j:""+Array($+1-B.length).join(D)+j},R={s:E,z:function(j){var $=-j.utcOffset(),D=Math.abs($),B=Math.floor(D/60),I=D%60;return($<=0?"+":"-")+E(B,2,"0")+":"+E(I,2,"0")},m:function j($,D){if($.date()<D.date())return-j(D,$);var B=12*(D.year()-$.year())+(D.month()-$.month()),I=$.clone().add(B,l),Z=D-I<0,f=$.clone().add(B+(Z?-1:1),l);return+(-(B+(D-I)/(Z?I-f:f-I))||0)},a:function(j){return j<0?Math.ceil(j)||0:Math.floor(j)},p:function(j){return{M:l,y:d,w:u,d:o,D:p,h:a,m:s,s:i,ms:n,Q:c}[j]||String(j||"").toLowerCase().replace(/s$/,"")},u:function(j){return j===void 0}},S="en",F={};F[S]=b;var q=function(j){return j instanceof be},te=function(j,$,D){var B;if(!j)return S;if(typeof j=="string")F[j]&&(B=j),$&&(F[j]=$,B=j);else{var I=j.name;F[I]=j,B=I}return!D&&B&&(S=B),B||!D&&S},G=function(j,$){if(q(j))return j.clone();var D=typeof $=="object"?$:{};return D.date=j,D.args=arguments,new be(D)},H=R;H.l=te,H.i=q,H.w=function(j,$){return G(j,{locale:$.$L,utc:$.$u,x:$.$x,$offset:$.$offset})};var be=function(){function j(D){this.$L=te(D.locale,null,!0),this.parse(D)}var $=j.prototype;return $.parse=function(D){this.$d=function(B){var I=B.date,Z=B.utc;if(I===null)return new Date(NaN);if(H.u(I))return new Date;if(I instanceof Date)return new Date(I);if(typeof I=="string"&&!/Z$/i.test(I)){var f=I.match(y);if(f){var h=f[2]-1||0,g=(f[7]||"0").substring(0,3);return Z?new Date(Date.UTC(f[1],h,f[3]||1,f[4]||0,f[5]||0,f[6]||0,g)):new Date(f[1],h,f[3]||1,f[4]||0,f[5]||0,f[6]||0,g)}}return new Date(I)}(D),this.$x=D.x||{},this.init()},$.init=function(){var D=this.$d;this.$y=D.getFullYear(),this.$M=D.getMonth(),this.$D=D.getDate(),this.$W=D.getDay(),this.$H=D.getHours(),this.$m=D.getMinutes(),this.$s=D.getSeconds(),this.$ms=D.getMilliseconds()},$.$utils=function(){return H},$.isValid=function(){return this.$d.toString()!==m},$.isSame=function(D,B){var I=G(D);return this.startOf(B)<=I&&I<=this.endOf(B)},$.isAfter=function(D,B){return G(D)<this.startOf(B)},$.isBefore=function(D,B){return this.endOf(B)<G(D)},$.$g=function(D,B,I){return H.u(D)?this[B]:this.set(I,D)},$.unix=function(){return Math.floor(this.valueOf()/1e3)},$.valueOf=function(){return this.$d.getTime()},$.startOf=function(D,B){var I=this,Z=!!H.u(B)||B,f=H.p(D),h=function(x,N){var re=H.w(I.$u?Date.UTC(I.$y,N,x):new Date(I.$y,N,x),I);return Z?re:re.endOf(o)},g=function(x,N){return H.w(I.toDate()[x].apply(I.toDate("s"),(Z?[0,0,0,0]:[23,59,59,999]).slice(N)),I)},T=this.$W,w=this.$M,O=this.$D,M="set"+(this.$u?"UTC":"");switch(f){case d:return Z?h(1,0):h(31,11);case l:return Z?h(1,w):h(0,w+1);case u:var A=this.$locale().weekStart||0,v=(T<A?T+7:T)-A;return h(Z?O-v:O+(6-v),w);case o:case p:return g(M+"Hours",0);case a:return g(M+"Minutes",1);case s:return g(M+"Seconds",2);case i:return g(M+"Milliseconds",3);default:return this.clone()}},$.endOf=function(D){return this.startOf(D,!1)},$.$set=function(D,B){var I,Z=H.p(D),f="set"+(this.$u?"UTC":""),h=(I={},I[o]=f+"Date",I[p]=f+"Date",I[l]=f+"Month",I[d]=f+"FullYear",I[a]=f+"Hours",I[s]=f+"Minutes",I[i]=f+"Seconds",I[n]=f+"Milliseconds",I)[Z],g=Z===o?this.$D+(B-this.$W):B;if(Z===l||Z===d){var T=this.clone().set(p,1);T.$d[h](g),T.init(),this.$d=T.set(p,Math.min(this.$D,T.daysInMonth())).$d}else h&&this.$d[h](g);return this.init(),this},$.set=function(D,B){return this.clone().$set(D,B)},$.get=function(D){return this[H.p(D)]()},$.add=function(D,B){var I,Z=this;D=Number(D);var f=H.p(B),h=function(w){var O=G(Z);return H.w(O.date(O.date()+Math.round(w*D)),Z)};if(f===l)return this.set(l,this.$M+D);if(f===d)return this.set(d,this.$y+D);if(f===o)return h(1);if(f===u)return h(7);var g=(I={},I[s]=e,I[a]=t,I[i]=r,I)[f]||1,T=this.$d.getTime()+D*g;return H.w(T,this)},$.subtract=function(D,B){return this.add(-1*D,B)},$.format=function(D){var B=this,I=this.$locale();if(!this.isValid())return I.invalidDate||m;var Z=D||"YYYY-MM-DDTHH:mm:ssZ",f=H.z(this),h=this.$H,g=this.$m,T=this.$M,w=I.weekdays,O=I.months,M=function(N,re,le,fe){return N&&(N[re]||N(B,Z))||le[re].substr(0,fe)},A=function(N){return H.s(h%12||12,N,"0")},v=I.meridiem||function(N,re,le){var fe=N<12?"AM":"PM";return le?fe.toLowerCase():fe},x={YY:String(this.$y).slice(-2),YYYY:this.$y,M:T+1,MM:H.s(T+1,2,"0"),MMM:M(I.monthsShort,T,O,3),MMMM:M(O,T),D:this.$D,DD:H.s(this.$D,2,"0"),d:String(this.$W),dd:M(I.weekdaysMin,this.$W,w,2),ddd:M(I.weekdaysShort,this.$W,w,3),dddd:w[this.$W],H:String(h),HH:H.s(h,2,"0"),h:A(1),hh:A(2),a:v(h,g,!0),A:v(h,g,!1),m:String(g),mm:H.s(g,2,"0"),s:String(this.$s),ss:H.s(this.$s,2,"0"),SSS:H.s(this.$ms,3,"0"),Z:f};return Z.replace(_,function(N,re){return re||x[N]||f.replace(":","")})},$.utcOffset=function(){return 15*-Math.round(this.$d.getTimezoneOffset()/15)},$.diff=function(D,B,I){var Z,f=H.p(B),h=G(D),g=(h.utcOffset()-this.utcOffset())*e,T=this-h,w=H.m(this,h);return w=(Z={},Z[d]=w/12,Z[l]=w,Z[c]=w/3,Z[u]=(T-g)/6048e5,Z[o]=(T-g)/864e5,Z[a]=T/t,Z[s]=T/e,Z[i]=T/r,Z)[f]||T,I?w:H.a(w)},$.daysInMonth=function(){return this.endOf(l).$D},$.$locale=function(){return F[this.$L]},$.locale=function(D,B){if(!D)return this.$L;var I=this.clone(),Z=te(D,B,!0);return Z&&(I.$L=Z),I},$.clone=function(){return H.w(this.$d,this)},$.toDate=function(){return new Date(this.valueOf())},$.toJSON=function(){return this.isValid()?this.toISOString():null},$.toISOString=function(){return this.$d.toISOString()},$.toString=function(){return this.$d.toUTCString()},j}(),Pe=be.prototype;return G.prototype=Pe,[["$ms",n],["$s",i],["$m",s],["$H",a],["$W",o],["$M",l],["$y",d],["$D",p]].forEach(function(j){Pe[j[1]]=function($){return this.$g($,j[0],j[1])}}),G.extend=function(j,$){return j.$i||(j($,be,G),j.$i=!0),G},G.locale=te,G.isDayjs=q,G.unix=function(j){return G(1e3*j)},G.en=F[S],G.Ls=F,G.p={},G})});var ot=k(Un=>{"use strict";var bE=Un&&Un.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(Un,"__esModule",{value:!0});Un.findYearClosestToRef=Un.findMostLikelyADYear=void 0;var TE=bE(we());function _E(r){return r<100&&(r>50?r=r+1900:r=r+2e3),r}Un.findMostLikelyADYear=_E;function vE(r,e,t){let n=TE.default(r),i=n;i=i.month(t-1),i=i.date(e),i=i.year(n.year());let s=i.add(1,"y"),a=i.add(-1,"y");return Math.abs(s.diff(n))<Math.abs(i.diff(n))?i=s:Math.abs(a.diff(n))<Math.abs(i.diff(n))&&(i=a),i.year()}Un.findYearClosestToRef=vE});var ut=k(ce=>{"use strict";Object.defineProperty(ce,"__esModule",{value:!0});ce.parseTimeUnits=ce.TIME_UNITS_PATTERN=ce.parseYear=ce.YEAR_PATTERN=ce.parseOrdinalNumberPattern=ce.ORDINAL_NUMBER_PATTERN=ce.parseNumberPattern=ce.NUMBER_PATTERN=ce.TIME_UNIT_DICTIONARY=ce.ORDINAL_WORD_DICTIONARY=ce.INTEGER_WORD_DICTIONARY=ce.MONTH_DICTIONARY=ce.FULL_MONTH_NAME_DICTIONARY=ce.WEEKDAY_DICTIONARY=void 0;var Ko=Re(),wE=ot();ce.WEEKDAY_DICTIONARY={sunday:0,sun:0,"sun.":0,monday:1,mon:1,"mon.":1,tuesday:2,tue:2,"tue.":2,wednesday:3,wed:3,"wed.":3,thursday:4,thurs:4,"thurs.":4,thur:4,"thur.":4,thu:4,"thu.":4,friday:5,fri:5,"fri.":5,saturday:6,sat:6,"sat.":6};ce.FULL_MONTH_NAME_DICTIONARY={january:1,february:2,march:3,april:4,may:5,june:6,july:7,august:8,september:9,october:10,november:11,december:12};ce.MONTH_DICTIONARY=Object.assign(Object.assign({},ce.FULL_MONTH_NAME_DICTIONARY),{jan:1,"jan.":1,feb:2,"feb.":2,mar:3,"mar.":3,apr:4,"apr.":4,jun:6,"jun.":6,jul:7,"jul.":7,aug:8,"aug.":8,sep:9,"sep.":9,sept:9,"sept.":9,oct:10,"oct.":10,nov:11,"nov.":11,dec:12,"dec.":12});ce.INTEGER_WORD_DICTIONARY={one:1,two:2,three:3,four:4,five:5,six:6,seven:7,eight:8,nine:9,ten:10,eleven:11,twelve:12};ce.ORDINAL_WORD_DICTIONARY={first:1,second:2,third:3,fourth:4,fifth:5,sixth:6,seventh:7,eighth:8,ninth:9,tenth:10,eleventh:11,twelfth:12,thirteenth:13,fourteenth:14,fifteenth:15,sixteenth:16,seventeenth:17,eighteenth:18,nineteenth:19,twentieth:20,"twenty first":21,"twenty-first":21,"twenty second":22,"twenty-second":22,"twenty third":23,"twenty-third":23,"twenty fourth":24,"twenty-fourth":24,"twenty fifth":25,"twenty-fifth":25,"twenty sixth":26,"twenty-sixth":26,"twenty seventh":27,"twenty-seventh":27,"twenty eighth":28,"twenty-eighth":28,"twenty ninth":29,"twenty-ninth":29,thirtieth:30,"thirty first":31,"thirty-first":31};ce.TIME_UNIT_DICTIONARY={sec:"second",second:"second",seconds:"second",min:"minute",mins:"minute",minute:"minute",minutes:"minute",h:"hour",hr:"hour",hrs:"hour",hour:"hour",hours:"hour",day:"d",days:"d",week:"week",weeks:"week",month:"month",months:"month",qtr:"quarter",quarter:"quarter",quarters:"quarter",y:"year",yr:"year",year:"year",years:"year"};ce.NUMBER_PATTERN=`(?:${Ko.matchAnyPattern(ce.INTEGER_WORD_DICTIONARY)}|[0-9]+|[0-9]+\\.[0-9]+|half(?:\\s{0,2}an?)?|an?\\b(?:\\s{0,2}few)?|few|several|a?\\s{0,2}couple\\s{0,2}(?:of)?)`;function eg(r){let e=r.toLowerCase();return ce.INTEGER_WORD_DICTIONARY[e]!==void 0?ce.INTEGER_WORD_DICTIONARY[e]:e==="a"||e==="an"?1:e.match(/few/)?3:e.match(/half/)?.5:e.match(/couple/)?2:e.match(/several/)?7:parseFloat(e)}ce.parseNumberPattern=eg;ce.ORDINAL_NUMBER_PATTERN=`(?:${Ko.matchAnyPattern(ce.ORDINAL_WORD_DICTIONARY)}|[0-9]{1,2}(?:st|nd|rd|th)?)`;function kE(r){let e=r.toLowerCase();return ce.ORDINAL_WORD_DICTIONARY[e]!==void 0?ce.ORDINAL_WORD_DICTIONARY[e]:(e=e.replace(/(?:st|nd|rd|th)$/i,""),parseInt(e))}ce.parseOrdinalNumberPattern=kE;ce.YEAR_PATTERN="(?:[1-9][0-9]{0,3}\\s{0,2}(?:BE|AD|BC|BCE|CE)|[1-2][0-9]{3}|[5-9][0-9])";function EE(r){if(/BE/i.test(r))return r=r.replace(/BE/i,""),parseInt(r)-543;if(/BCE?/i.test(r))return r=r.replace(/BCE?/i,""),-parseInt(r);if(/(AD|CE)/i.test(r))return r=r.replace(/(AD|CE)/i,""),parseInt(r);let e=parseInt(r);return wE.findMostLikelyADYear(e)}ce.parseYear=EE;var tg=`(${ce.NUMBER_PATTERN})\\s{0,3}(${Ko.matchAnyPattern(ce.TIME_UNIT_DICTIONARY)})`,Jh=new RegExp(tg,"i");ce.TIME_UNITS_PATTERN=Ko.repeatedTimeunitPattern("(?:(?:about|around)\\s{0,3})?",tg);function SE(r){let e={},t=r,n=Jh.exec(t);for(;n;)OE(e,n),t=t.substring(n[0].length).trim(),n=Jh.exec(t);return e}ce.parseTimeUnits=SE;function OE(r,e){let t=eg(e[1]),n=ce.TIME_UNIT_DICTIONARY[e[2].toLowerCase()];r[n]=t}});var rg=k((Rc,Mc)=>{(function(r,e){typeof Rc=="object"&&typeof Mc!="undefined"?Mc.exports=e():typeof define=="function"&&define.amd?define(e):(r=typeof globalThis!="undefined"?globalThis:r||self).dayjs_plugin_quarterOfYear=e()})(Rc,function(){"use strict";var r="month",e="quarter";return function(t,n){var i=n.prototype;i.quarter=function(o){return this.$utils().u(o)?Math.ceil((this.month()+1)/3):this.month(this.month()%3+3*(o-1))};var s=i.add;i.add=function(o,u){return o=Number(o),this.$utils().p(u)===e?this.add(3*o,r):s.bind(this)(o,u)};var a=i.startOf;i.startOf=function(o,u){var l=this.$utils(),c=!!l.u(u)||u;if(l.p(o)===e){var d=this.quarter()-1;return c?this.month(3*d).startOf(r).startOf("day"):this.month(3*d+2).endOf(r).endOf("day")}return a.bind(this)(o,u)}}})});var ir=k($r=>{"use strict";Object.defineProperty($r,"__esModule",{value:!0});$r.implySimilarTime=$r.assignSimilarTime=$r.assignSimilarDate=$r.assignTheNextDay=void 0;var ng=ze();function DE(r,e){e=e.add(1,"day"),ig(r,e),sg(r,e)}$r.assignTheNextDay=DE;function ig(r,e){r.assign("day",e.date()),r.assign("month",e.month()+1),r.assign("year",e.year())}$r.assignSimilarDate=ig;function xE(r,e){r.assign("hour",e.hour()),r.assign("minute",e.minute()),r.assign("second",e.second()),r.assign("millisecond",e.millisecond()),r.get("hour")<12?r.assign("meridiem",ng.Meridiem.AM):r.assign("meridiem",ng.Meridiem.PM)}$r.assignSimilarTime=xE;function sg(r,e){r.imply("hour",e.hour()),r.imply("minute",e.minute()),r.imply("second",e.second()),r.imply("millisecond",e.millisecond())}$r.implySimilarTime=sg});var ag=k(ui=>{"use strict";Object.defineProperty(ui,"__esModule",{value:!0});ui.toTimezoneOffset=ui.TIMEZONE_ABBR_MAP=void 0;ui.TIMEZONE_ABBR_MAP={ACDT:630,ACST:570,ADT:-180,AEDT:660,AEST:600,AFT:270,AKDT:-480,AKST:-540,ALMT:360,AMST:-180,AMT:-240,ANAST:720,ANAT:720,AQTT:300,ART:-180,AST:-240,AWDT:540,AWST:480,AZOST:0,AZOT:-60,AZST:300,AZT:240,BNT:480,BOT:-240,BRST:-120,BRT:-180,BST:60,BTT:360,CAST:480,CAT:120,CCT:390,CDT:-300,CEST:120,CET:60,CHADT:825,CHAST:765,CKT:-600,CLST:-180,CLT:-240,COT:-300,CST:-360,CVT:-60,CXT:420,ChST:600,DAVT:420,EASST:-300,EAST:-360,EAT:180,ECT:-300,EDT:-240,EEST:180,EET:120,EGST:0,EGT:-60,EST:-300,ET:-300,FJST:780,FJT:720,FKST:-180,FKT:-240,FNT:-120,GALT:-360,GAMT:-540,GET:240,GFT:-180,GILT:720,GMT:0,GST:240,GYT:-240,HAA:-180,HAC:-300,HADT:-540,HAE:-240,HAP:-420,HAR:-360,HAST:-600,HAT:-90,HAY:-480,HKT:480,HLV:-210,HNA:-240,HNC:-360,HNE:-300,HNP:-480,HNR:-420,HNT:-150,HNY:-540,HOVT:420,ICT:420,IDT:180,IOT:360,IRDT:270,IRKST:540,IRKT:540,IRST:210,IST:330,JST:540,KGT:360,KRAST:480,KRAT:480,KST:540,KUYT:240,LHDT:660,LHST:630,LINT:840,MAGST:720,MAGT:720,MART:-510,MAWT:300,MDT:-360,MESZ:120,MEZ:60,MHT:720,MMT:390,MSD:240,MSK:180,MST:-420,MUT:240,MVT:300,MYT:480,NCT:660,NDT:-90,NFT:690,NOVST:420,NOVT:360,NPT:345,NST:-150,NUT:-660,NZDT:780,NZST:720,OMSST:420,OMST:420,PDT:-420,PET:-300,PETST:720,PETT:720,PGT:600,PHOT:780,PHT:480,PKT:300,PMDT:-120,PMST:-180,PONT:660,PST:-480,PT:-480,PWT:540,PYST:-180,PYT:-240,RET:240,SAMT:240,SAST:120,SBT:660,SCT:240,SGT:480,SRT:-180,SST:-660,TAHT:-600,TFT:300,TJT:300,TKT:780,TLT:540,TMT:300,TVT:720,ULAT:480,UTC:0,UYST:-120,UYT:-180,UZT:300,VET:-210,VLAST:660,VLAT:660,VUT:660,WAST:120,WAT:60,WEST:60,WESZ:60,WET:0,WEZ:0,WFT:720,WGST:-120,WGT:-180,WIB:420,WIT:540,WITA:480,WST:780,WT:0,YAKST:600,YAKT:600,YAPT:600,YEKST:360,YEKT:360};function RE(r){var e;return r==null?null:typeof r=="number"?r:(e=ui.TIMEZONE_ABBR_MAP[r])!==null&&e!==void 0?e:null}ui.toTimezoneOffset=RE});var We=k(jr=>{"use strict";var og=jr&&jr.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(jr,"__esModule",{value:!0});jr.ParsingResult=jr.ParsingComponents=jr.ReferenceWithTimezone=void 0;var ME=og(rg()),Qo=og(we()),Cc=ir(),CE=ag();Qo.default.extend(ME.default);var Ac=class{constructor(e){var t;e=e!=null?e:new Date,e instanceof Date?this.instant=e:(this.instant=(t=e.instant)!==null&&t!==void 0?t:new Date,this.timezoneOffset=CE.toTimezoneOffset(e.timezone))}};jr.ReferenceWithTimezone=Ac;var li=class{constructor(e,t){if(this.reference=e,this.knownValues={},this.impliedValues={},t)for(let i in t)this.knownValues[i]=t[i];let n=Qo.default(e.instant);this.imply("day",n.date()),this.imply("month",n.month()+1),this.imply("year",n.year()),this.imply("hour",12),this.imply("minute",0),this.imply("second",0),this.imply("millisecond",0)}get(e){return e in this.knownValues?this.knownValues[e]:e in this.impliedValues?this.impliedValues[e]:null}isCertain(e){return e in this.knownValues}getCertainComponents(){return Object.keys(this.knownValues)}imply(e,t){return e in this.knownValues?this:(this.impliedValues[e]=t,this)}assign(e,t){return this.knownValues[e]=t,delete this.impliedValues[e],this}delete(e){delete this.knownValues[e],delete this.impliedValues[e]}clone(){let e=new li(this.reference);e.knownValues={},e.impliedValues={};for(let t in this.knownValues)e.knownValues[t]=this.knownValues[t];for(let t in this.impliedValues)e.impliedValues[t]=this.impliedValues[t];return e}isOnlyDate(){return!this.isCertain("hour")&&!this.isCertain("minute")&&!this.isCertain("second")}isOnlyTime(){return!this.isCertain("weekday")&&!this.isCertain("day")&&!this.isCertain("month")}isOnlyWeekdayComponent(){return this.isCertain("weekday")&&!this.isCertain("day")&&!this.isCertain("month")}isOnlyDayMonthComponent(){return this.isCertain("day")&&this.isCertain("month")&&!this.isCertain("year")}isValidDate(){let e=this.dateWithoutTimezoneAdjustment();return!(e.getFullYear()!==this.get("year")||e.getMonth()!==this.get("month")-1||e.getDate()!==this.get("day")||this.get("hour")!=null&&e.getHours()!=this.get("hour")||this.get("minute")!=null&&e.getMinutes()!=this.get("minute"))}toString(){return`[ParsingComponents {knownValues: ${JSON.stringify(this.knownValues)}, impliedValues: ${JSON.stringify(this.impliedValues)}}, reference: ${JSON.stringify(this.reference)}]`}dayjs(){return Qo.default(this.date())}date(){let e=this.dateWithoutTimezoneAdjustment();return new Date(e.getTime()+this.getSystemTimezoneAdjustmentMinute(e)*6e4)}dateWithoutTimezoneAdjustment(){let e=new Date(this.get("year"),this.get("month")-1,this.get("day"),this.get("hour"),this.get("minute"),this.get("second"),this.get("millisecond"));return e.setFullYear(this.get("year")),e}getSystemTimezoneAdjustmentMinute(e){var t,n;(!e||e.getTime()<0)&&(e=new Date);let i=-e.getTimezoneOffset(),s=(n=(t=this.get("timezoneOffset"))!==null&&t!==void 0?t:this.reference.timezoneOffset)!==null&&n!==void 0?n:i;return i-s}static createRelativeFromReference(e,t){let n=Qo.default(e.instant);for(let s in t)n=n.add(t[s],s);let i=new li(e);return t.hour||t.minute||t.second?(Cc.assignSimilarTime(i,n),Cc.assignSimilarDate(i,n),e.timezoneOffset!==null&&i.assign("timezoneOffset",-e.instant.getTimezoneOffset())):(Cc.implySimilarTime(i,n),e.timezoneOffset!==null&&i.imply("timezoneOffset",-e.instant.getTimezoneOffset()),t.d?(i.assign("day",n.date()),i.assign("month",n.month()+1),i.assign("year",n.year())):(t.week&&i.imply("weekday",n.day()),i.imply("day",n.date()),t.month?(i.assign("month",n.month()+1),i.assign("year",n.year())):(i.imply("month",n.month()+1),t.year?i.assign("year",n.year()):i.imply("year",n.year())))),i}};jr.ParsingComponents=li;var ua=class{constructor(e,t,n,i,s){this.reference=e,this.refDate=e.instant,this.index=t,this.text=n,this.start=i||new li(e),this.end=s}clone(){let e=new ua(this.reference,this.index,this.text);return e.start=this.start?this.start.clone():null,e.end=this.end?this.end.clone():null,e}date(){return this.start.date()}toString(){return`[ParsingResult {index: ${this.index}, text: '${this.text}', ...}]`}};jr.ParsingResult=ua});var V=k(Xo=>{"use strict";Object.defineProperty(Xo,"__esModule",{value:!0});Xo.AbstractParserWithWordBoundaryChecking=void 0;var Pc=class{constructor(){this.cachedInnerPattern=null,this.cachedPattern=null}patternLeftBoundary(){return"(\\W|^)"}pattern(e){let t=this.innerPattern(e);return t==this.cachedInnerPattern?this.cachedPattern:(this.cachedPattern=new RegExp(`${this.patternLeftBoundary()}${t.source}`,t.flags),this.cachedInnerPattern=t,this.cachedPattern)}extract(e,t){var n;let i=(n=t[1])!==null&&n!==void 0?n:"";t.index=t.index+i.length,t[0]=t[0].substring(i.length);for(let s=2;s<t.length;s++)t[s-1]=t[s];return this.innerExtract(e,t)}};Xo.AbstractParserWithWordBoundaryChecking=Pc});var ug=k(Fc=>{"use strict";Object.defineProperty(Fc,"__esModule",{value:!0});var Ic=ut(),AE=We(),PE=V(),NE=new RegExp(`(?:within|in|for)\\s*(?:(?:about|around|roughly|approximately|just)\\s*(?:~\\s*)?)?(${Ic.TIME_UNITS_PATTERN})(?=\\W|$)`,"i"),IE=new RegExp(`(?:(?:about|around|roughly|approximately|just)\\s*(?:~\\s*)?)?(${Ic.TIME_UNITS_PATTERN})(?=\\W|$)`,"i"),Nc=class extends PE.AbstractParserWithWordBoundaryChecking{innerPattern(e){return e.option.forwardDate?IE:NE}innerExtract(e,t){let n=Ic.parseTimeUnits(t[1]);return AE.ParsingComponents.createRelativeFromReference(e.reference,n)}};Fc.default=Nc});var mg=k(Uc=>{"use strict";Object.defineProperty(Uc,"__esModule",{value:!0});var FE=ot(),fg=ut(),pg=ut(),Zo=ut(),LE=Re(),UE=V(),WE=new RegExp(`(?:on\\s{0,3})?(${Zo.ORDINAL_NUMBER_PATTERN})(?:\\s{0,3}(?:to|\\-|\\\u2013|until|through|till)?\\s{0,3}(${Zo.ORDINAL_NUMBER_PATTERN}))?(?:-|/|\\s{0,3}(?:of)?\\s{0,3})(${LE.matchAnyPattern(fg.MONTH_DICTIONARY)})(?:(?:-|/|,?\\s{0,3})(${pg.YEAR_PATTERN}(?![^\\s]\\d)))?(?=\\W|$)`,"i"),lg=1,cg=2,qE=3,dg=4,Lc=class extends UE.AbstractParserWithWordBoundaryChecking{innerPattern(){return WE}innerExtract(e,t){let n=e.createParsingResult(t.index,t[0]),i=fg.MONTH_DICTIONARY[t[qE].toLowerCase()],s=Zo.parseOrdinalNumberPattern(t[lg]);if(s>31)return t.index=t.index+t[lg].length,null;if(n.start.assign("month",i),n.start.assign("day",s),t[dg]){let a=pg.parseYear(t[dg]);n.start.assign("year",a)}else{let a=FE.findYearClosestToRef(e.refDate,s,i);n.start.imply("year",a)}if(t[cg]){let a=Zo.parseOrdinalNumberPattern(t[cg]);n.end=n.start.clone(),n.end.assign("day",a)}return n}};Uc.default=Lc});var Tg=k(qc=>{"use strict";Object.defineProperty(qc,"__esModule",{value:!0});var $E=ot(),yg=ut(),Jo=ut(),bg=ut(),jE=Re(),GE=V(),YE=new RegExp(`(${jE.matchAnyPattern(yg.MONTH_DICTIONARY)})(?:-|/|\\s*,?\\s*)(${Jo.ORDINAL_NUMBER_PATTERN})(?!\\s*(?:am|pm))\\s*(?:(?:to|\\-)\\s*(${Jo.ORDINAL_NUMBER_PATTERN})\\s*)?(?:(?:-|/|\\s*,?\\s*)(${bg.YEAR_PATTERN}))?(?=\\W|$)(?!\\:\\d)`,"i"),BE=1,HE=2,hg=3,gg=4,Wc=class extends GE.AbstractParserWithWordBoundaryChecking{innerPattern(){return YE}innerExtract(e,t){let n=yg.MONTH_DICTIONARY[t[BE].toLowerCase()],i=Jo.parseOrdinalNumberPattern(t[HE]);if(i>31)return null;let s=e.createParsingComponents({day:i,month:n});if(t[gg]){let u=bg.parseYear(t[gg]);s.assign("year",u)}else{let u=$E.findYearClosestToRef(e.refDate,i,n);s.imply("year",u)}if(!t[hg])return s;let a=Jo.parseOrdinalNumberPattern(t[hg]),o=e.createParsingResult(t.index,t[0]);return o.start=s,o.end=s.clone(),o.end.assign("day",a),o}};qc.default=Wc});var wg=k(Gc=>{"use strict";Object.defineProperty(Gc,"__esModule",{value:!0});var $c=ut(),VE=ot(),zE=Re(),vg=ut(),KE=V(),QE=new RegExp(`((?:in)\\s*)?(${zE.matchAnyPattern($c.MONTH_DICTIONARY)})\\s*(?:[,-]?\\s*(${vg.YEAR_PATTERN})?)?(?=[^\\s\\w]|\\s+[^0-9]|\\s+$|$)`,"i"),XE=1,ZE=2,_g=3,jc=class extends KE.AbstractParserWithWordBoundaryChecking{innerPattern(){return QE}innerExtract(e,t){let n=t[ZE].toLowerCase();if(t[0].length<=3&&!$c.FULL_MONTH_NAME_DICTIONARY[n])return null;let i=e.createParsingResult(t.index+(t[XE]||"").length,t.index+t[0].length);i.start.imply("day",1);let s=$c.MONTH_DICTIONARY[n];if(i.start.assign("month",s),t[_g]){let a=vg.parseYear(t[_g]);i.start.assign("year",a)}else{let a=VE.findYearClosestToRef(e.refDate,1,s);i.start.imply("year",a)}return i}};Gc.default=jc});var Sg=k(Bc=>{"use strict";Object.defineProperty(Bc,"__esModule",{value:!0});var Eg=ut(),JE=Re(),eS=V(),tS=new RegExp(`([0-9]{4})[\\.\\/\\s](?:(${JE.matchAnyPattern(Eg.MONTH_DICTIONARY)})|([0-9]{1,2}))[\\.\\/\\s]([0-9]{1,2})(?=\\W|$)`,"i"),rS=1,nS=2,kg=3,iS=4,Yc=class extends eS.AbstractParserWithWordBoundaryChecking{innerPattern(){return tS}innerExtract(e,t){let n=t[kg]?parseInt(t[kg]):Eg.MONTH_DICTIONARY[t[nS].toLowerCase()];if(n<1||n>12)return null;let i=parseInt(t[rS]);return{day:parseInt(t[iS]),month:n,year:i}}};Bc.default=Yc});var Og=k(Vc=>{"use strict";Object.defineProperty(Vc,"__esModule",{value:!0});var sS=V(),aS=new RegExp("([0-9]|0[1-9]|1[012])/([0-9]{4})","i"),oS=1,uS=2,Hc=class extends sS.AbstractParserWithWordBoundaryChecking{innerPattern(){return aS}innerExtract(e,t){let n=parseInt(t[uS]),i=parseInt(t[oS]);return e.createParsingComponents().imply("day",1).assign("month",i).assign("year",n)}};Vc.default=Hc});var ci=k(ru=>{"use strict";Object.defineProperty(ru,"__esModule",{value:!0});ru.AbstractTimeExpressionParser=void 0;var wt=ze();function lS(r,e,t,n){return new RegExp(`${r}${e}(\\d{1,4})(?:(?:\\.|:|\uFF1A)(\\d{1,2})(?:(?::|\uFF1A)(\\d{2})(?:\\.(\\d{1,6}))?)?)?(?:\\s*(a\\.m\\.|p\\.m\\.|am?|pm?))?${t}`,n)}function cS(r,e){return new RegExp(`^(${r})(\\d{1,4})(?:(?:\\.|\\:|\\\uFF1A)(\\d{1,2})(?:(?:\\.|\\:|\\\uFF1A)(\\d{1,2})(?:\\.(\\d{1,6}))?)?)?(?:\\s*(a\\.m\\.|p\\.m\\.|am?|pm?))?${e}`,"i")}var Dg=2,zi=3,eu=4,tu=5,la=6,zc=class{constructor(e=!1){this.cachedPrimaryPrefix=null,this.cachedPrimarySuffix=null,this.cachedPrimaryTimePattern=null,this.cachedFollowingPhase=null,this.cachedFollowingSuffix=null,this.cachedFollowingTimePatten=null,this.strictMode=e}patternFlags(){return"i"}primaryPatternLeftBoundary(){return"(^|\\s|T|\\b)"}primarySuffix(){return"(?=\\W|$)"}followingSuffix(){return"(?=\\W|$)"}pattern(e){return this.getPrimaryTimePatternThroughCache()}extract(e,t){let n=this.extractPrimaryTimeComponents(e,t);if(!n)return t.index+=t[0].length,null;let i=t.index+t[1].length,s=t[0].substring(t[1].length),a=e.createParsingResult(i,s,n);t.index+=t[0].length;let o=e.text.substring(t.index),l=this.getFollowingTimePatternThroughCache().exec(o);return s.match(/^\d{3,4}/)&&l&&l[0].match(/^\s*([+-])\s*\d{2,4}$/)?null:!l||l[0].match(/^\s*([+-])\s*\d{3,4}$/)?this.checkAndReturnWithoutFollowingPattern(a):(a.end=this.extractFollowingTimeComponents(e,l,a),a.end&&(a.text+=l[0]),this.checkAndReturnWithFollowingPattern(a))}extractPrimaryTimeComponents(e,t,n=!1){let i=e.createParsingComponents(),s=0,a=null,o=parseInt(t[Dg]);if(o>100){if(this.strictMode||t[zi]!=null)return null;s=o%100,o=Math.floor(o/100)}if(o>24)return null;if(t[zi]!=null){if(t[zi].length==1&&!t[la])return null;s=parseInt(t[zi])}if(s>=60)return null;if(o>12&&(a=wt.Meridiem.PM),t[la]!=null){if(o>12)return null;let u=t[la][0].toLowerCase();u=="a"&&(a=wt.Meridiem.AM,o==12&&(o=0)),u=="p"&&(a=wt.Meridiem.PM,o!=12&&(o+=12))}if(i.assign("hour",o),i.assign("minute",s),a!==null?i.assign("meridiem",a):o<12?i.imply("meridiem",wt.Meridiem.AM):i.imply("meridiem",wt.Meridiem.PM),t[tu]!=null){let u=parseInt(t[tu].substring(0,3));if(u>=1e3)return null;i.assign("millisecond",u)}if(t[eu]!=null){let u=parseInt(t[eu]);if(u>=60)return null;i.assign("second",u)}return i}extractFollowingTimeComponents(e,t,n){let i=e.createParsingComponents();if(t[tu]!=null){let u=parseInt(t[tu].substring(0,3));if(u>=1e3)return null;i.assign("millisecond",u)}if(t[eu]!=null){let u=parseInt(t[eu]);if(u>=60)return null;i.assign("second",u)}let s=parseInt(t[Dg]),a=0,o=-1;if(t[zi]!=null?a=parseInt(t[zi]):s>100&&(a=s%100,s=Math.floor(s/100)),a>=60||s>24)return null;if(s>=12&&(o=wt.Meridiem.PM),t[la]!=null){if(s>12)return null;let u=t[la][0].toLowerCase();u=="a"&&(o=wt.Meridiem.AM,s==12&&(s=0,i.isCertain("day")||i.imply("day",i.get("day")+1))),u=="p"&&(o=wt.Meridiem.PM,s!=12&&(s+=12)),n.start.isCertain("meridiem")||(o==wt.Meridiem.AM?(n.start.imply("meridiem",wt.Meridiem.AM),n.start.get("hour")==12&&n.start.assign("hour",0)):(n.start.imply("meridiem",wt.Meridiem.PM),n.start.get("hour")!=12&&n.start.assign("hour",n.start.get("hour")+12)))}return i.assign("hour",s),i.assign("minute",a),o>=0?i.assign("meridiem",o):n.start.isCertain("meridiem")&&n.start.get("hour")>12?n.start.get("hour")-12>s?i.imply("meridiem",wt.Meridiem.AM):s<=12&&(i.assign("hour",s+12),i.assign("meridiem",wt.Meridiem.PM)):s>12?i.imply("meridiem",wt.Meridiem.PM):s<=12&&i.imply("meridiem",wt.Meridiem.AM),i.date().getTime()<n.start.date().getTime()&&i.imply("day",i.get("day")+1),i}checkAndReturnWithoutFollowingPattern(e){if(e.text.match(/^\d$/)||e.text.match(/^\d\d\d+$/)||e.text.match(/\d[apAP]$/))return null;let t=e.text.match(/[^\d:.](\d[\d.]+)$/);if(t){let n=t[1];if(this.strictMode||n.includes(".")&&!n.match(/\d(\.\d{2})+$/)||parseInt(n)>24)return null}return e}checkAndReturnWithFollowingPattern(e){if(e.text.match(/^\d+-\d+$/))return null;let t=e.text.match(/[^\d:.](\d[\d.]+)\s*-\s*(\d[\d.]+)$/);if(t){if(this.strictMode)return null;let n=t[1],i=t[2];if(i.includes(".")&&!i.match(/\d(\.\d{2})+$/))return null;let s=parseInt(i),a=parseInt(n);if(s>24||a>24)return null}return e}getPrimaryTimePatternThroughCache(){let e=this.primaryPrefix(),t=this.primarySuffix();return this.cachedPrimaryPrefix===e&&this.cachedPrimarySuffix===t?this.cachedPrimaryTimePattern:(this.cachedPrimaryTimePattern=lS(this.primaryPatternLeftBoundary(),e,t,this.patternFlags()),this.cachedPrimaryPrefix=e,this.cachedPrimarySuffix=t,this.cachedPrimaryTimePattern)}getFollowingTimePatternThroughCache(){let e=this.followingPhase(),t=this.followingSuffix();return this.cachedFollowingPhase===e&&this.cachedFollowingSuffix===t?this.cachedFollowingTimePatten:(this.cachedFollowingTimePatten=cS(e,t),this.cachedFollowingPhase=e,this.cachedFollowingSuffix=t,this.cachedFollowingTimePatten)}};ru.AbstractTimeExpressionParser=zc});var xg=k(Qc=>{"use strict";Object.defineProperty(Qc,"__esModule",{value:!0});var nu=ze(),dS=ci(),Kc=class extends dS.AbstractTimeExpressionParser{constructor(e){super(e)}followingPhase(){return"\\s*(?:\\-|\\\u2013|\\~|\\\u301C|to|\\?)\\s*"}primaryPrefix(){return"(?:(?:at|from)\\s*)??"}primarySuffix(){return"(?:\\s*(?:o\\W*clock|at\\s*night|in\\s*the\\s*(?:morning|afternoon)))?(?!/)(?=\\W|$)"}extractPrimaryTimeComponents(e,t){let n=super.extractPrimaryTimeComponents(e,t);if(n){if(t[0].endsWith("night")){let i=n.get("hour");i>=6&&i<12?(n.assign("hour",n.get("hour")+12),n.assign("meridiem",nu.Meridiem.PM)):i<6&&n.assign("meridiem",nu.Meridiem.AM)}if(t[0].endsWith("afternoon")){n.assign("meridiem",nu.Meridiem.PM);let i=n.get("hour");i>=0&&i<=6&&n.assign("hour",n.get("hour")+12)}t[0].endsWith("morning")&&(n.assign("meridiem",nu.Meridiem.AM),n.get("hour")<12&&n.assign("hour",n.get("hour")))}return n}};Qc.default=Kc});var sr=k(Ki=>{"use strict";Object.defineProperty(Ki,"__esModule",{value:!0});Ki.addImpliedTimeUnits=Ki.reverseTimeUnits=void 0;function fS(r){let e={};for(let t in r)e[t]=-r[t];return e}Ki.reverseTimeUnits=fS;function pS(r,e){let t=r.clone(),n=r.dayjs();for(let i in e)n=n.add(e[i],i);return("day"in e||"d"in e||"week"in e||"month"in e||"year"in e)&&(t.imply("day",n.date()),t.imply("month",n.month()+1),t.imply("year",n.year())),("second"in e||"minute"in e||"hour"in e)&&(t.imply("second",n.second()),t.imply("minute",n.minute()),t.imply("hour",n.hour())),t}Ki.addImpliedTimeUnits=pS});var Rg=k(Jc=>{"use strict";Object.defineProperty(Jc,"__esModule",{value:!0});var Zc=ut(),mS=We(),hS=V(),gS=sr(),yS=new RegExp(`(${Zc.TIME_UNITS_PATTERN})\\s{0,5}(?:ago|before|earlier)(?=(?:\\W|$))`,"i"),bS=new RegExp(`(${Zc.TIME_UNITS_PATTERN})\\s{0,5}ago(?=(?:\\W|$))`,"i"),Xc=class extends hS.AbstractParserWithWordBoundaryChecking{constructor(e){super(),this.strictMode=e}innerPattern(){return this.strictMode?bS:yS}innerExtract(e,t){let n=Zc.parseTimeUnits(t[1]),i=gS.reverseTimeUnits(n);return mS.ParsingComponents.createRelativeFromReference(e.reference,i)}};Jc.default=Xc});var Mg=k(rd=>{"use strict";Object.defineProperty(rd,"__esModule",{value:!0});var td=ut(),TS=We(),_S=V(),vS=new RegExp(`(${td.TIME_UNITS_PATTERN})\\s{0,5}(?:later|after|from now|henceforth|forward|out)(?=(?:\\W|$))`,"i"),wS=new RegExp("("+td.TIME_UNITS_PATTERN+")(later|from now)(?=(?:\\W|$))","i"),kS=1,ed=class extends _S.AbstractParserWithWordBoundaryChecking{constructor(e){super(),this.strictMode=e}innerPattern(){return this.strictMode?wS:vS}innerExtract(e,t){let n=td.parseTimeUnits(t[kS]);return TS.ParsingComponents.createRelativeFromReference(e.reference,n)}};rd.default=ed});var Xi=k(Qi=>{"use strict";Object.defineProperty(Qi,"__esModule",{value:!0});Qi.MergingRefiner=Qi.Filter=void 0;var nd=class{refine(e,t){return t.filter(n=>this.isValid(e,n))}};Qi.Filter=nd;var id=class{refine(e,t){if(t.length<2)return t;let n=[],i=t[0],s=null;for(let a=1;a<t.length;a++){s=t[a];let o=e.text.substring(i.index+i.text.length,s.index);if(!this.shouldMergeResults(o,i,s,e))n.push(i),i=s;else{let u=i,l=s,c=this.mergeResults(o,u,l,e);e.debug(()=>{console.log(`${this.constructor.name} merged ${u} and ${l} into ${c}`)}),i=c}}return i!=null&&n.push(i),n}};Qi.MergingRefiner=id});var Gr=k(ad=>{"use strict";Object.defineProperty(ad,"__esModule",{value:!0});var ES=Xi(),sd=class extends ES.MergingRefiner{shouldMergeResults(e,t,n){return!t.end&&!n.end&&e.match(this.patternBetween())!=null}mergeResults(e,t,n){if(!t.start.isOnlyWeekdayComponent()&&!n.start.isOnlyWeekdayComponent()&&(n.start.getCertainComponents().forEach(s=>{t.start.isCertain(s)||t.start.assign(s,n.start.get(s))}),t.start.getCertainComponents().forEach(s=>{n.start.isCertain(s)||n.start.assign(s,t.start.get(s))})),t.start.date().getTime()>n.start.date().getTime()){let s=t.start.dayjs(),a=n.start.dayjs();t.start.isOnlyWeekdayComponent()&&s.add(-7,"days").isBefore(a)?(s=s.add(-7,"days"),t.start.imply("day",s.date()),t.start.imply("month",s.month()+1),t.start.imply("year",s.year())):n.start.isOnlyWeekdayComponent()&&a.add(7,"days").isAfter(s)?(a=a.add(7,"days"),n.start.imply("day",a.date()),n.start.imply("month",a.month()+1),n.start.imply("year",a.year())):[n,t]=[t,n]}let i=t.clone();return i.start=t.start,i.end=n.start,i.index=Math.min(t.index,n.index),t.index<n.index?i.text=t.text+e+n.text:i.text=n.text+e+t.text,i}};ad.default=sd});var Cg=k(ca=>{"use strict";var SS=ca&&ca.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(ca,"__esModule",{value:!0});var OS=SS(Gr()),od=class extends OS.default{patternBetween(){return/^\s*(to|-)\s*$/i}};ca.default=od});var Ag=k(Zi=>{"use strict";Object.defineProperty(Zi,"__esModule",{value:!0});Zi.mergeDateTimeComponent=Zi.mergeDateTimeResult=void 0;var DS=ze();function xS(r,e){let t=r.clone(),n=r.start,i=e.start;if(t.start=ud(n,i),r.end!=null||e.end!=null){let s=r.end==null?r.start:r.end,a=e.end==null?e.start:e.end,o=ud(s,a);r.end==null&&o.date().getTime()<t.start.date().getTime()&&(o.isCertain("day")?o.assign("day",o.get("day")+1):o.imply("day",o.get("day")+1)),t.end=o}return t}Zi.mergeDateTimeResult=xS;function ud(r,e){let t=r.clone();return e.isCertain("hour")?(t.assign("hour",e.get("hour")),t.assign("minute",e.get("minute")),e.isCertain("second")?(t.assign("second",e.get("second")),e.isCertain("millisecond")?t.assign("millisecond",e.get("millisecond")):t.imply("millisecond",e.get("millisecond"))):(t.imply("second",e.get("second")),t.imply("millisecond",e.get("millisecond")))):(t.imply("hour",e.get("hour")),t.imply("minute",e.get("minute")),t.imply("second",e.get("second")),t.imply("millisecond",e.get("millisecond"))),e.isCertain("timezoneOffset")&&t.assign("timezoneOffset",e.get("timezoneOffset")),e.isCertain("meridiem")?t.assign("meridiem",e.get("meridiem")):e.get("meridiem")!=null&&t.get("meridiem")==null&&t.imply("meridiem",e.get("meridiem")),t.get("meridiem")==DS.Meridiem.PM&&t.get("hour")<12&&(e.isCertain("hour")?t.assign("hour",t.get("hour")+12):t.imply("hour",t.get("hour")+12)),t}Zi.mergeDateTimeComponent=ud});var cn=k(cd=>{"use strict";Object.defineProperty(cd,"__esModule",{value:!0});var RS=Xi(),Pg=Ag(),ld=class extends RS.MergingRefiner{shouldMergeResults(e,t,n){return(t.start.isOnlyDate()&&n.start.isOnlyTime()||n.start.isOnlyDate()&&t.start.isOnlyTime())&&e.match(this.patternBetween())!=null}mergeResults(e,t,n){let i=t.start.isOnlyDate()?Pg.mergeDateTimeResult(t,n):Pg.mergeDateTimeResult(n,t);return i.index=t.index,i.text=t.text+e+n.text,i}};cd.default=ld});var Ng=k(da=>{"use strict";var MS=da&&da.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(da,"__esModule",{value:!0});var CS=MS(cn()),dd=class extends CS.default{patternBetween(){return new RegExp("^\\s*(T|at|after|before|on|of|,|-)?\\s*$")}};da.default=dd});var Ig=k(pd=>{"use strict";Object.defineProperty(pd,"__esModule",{value:!0});var AS=new RegExp("^\\s*,?\\s*\\(?([A-Z]{2,4})\\)?(?=\\W|$)","i"),PS={ACDT:630,ACST:570,ADT:-180,AEDT:660,AEST:600,AFT:270,AKDT:-480,AKST:-540,ALMT:360,AMST:-180,AMT:-240,ANAST:720,ANAT:720,AQTT:300,ART:-180,AST:-240,AWDT:540,AWST:480,AZOST:0,AZOT:-60,AZST:300,AZT:240,BNT:480,BOT:-240,BRST:-120,BRT:-180,BST:60,BTT:360,CAST:480,CAT:120,CCT:390,CDT:-300,CEST:120,CET:60,CHADT:825,CHAST:765,CKT:-600,CLST:-180,CLT:-240,COT:-300,CST:-360,CVT:-60,CXT:420,ChST:600,DAVT:420,EASST:-300,EAST:-360,EAT:180,ECT:-300,EDT:-240,EEST:180,EET:120,EGST:0,EGT:-60,EST:-300,ET:-300,FJST:780,FJT:720,FKST:-180,FKT:-240,FNT:-120,GALT:-360,GAMT:-540,GET:240,GFT:-180,GILT:720,GMT:0,GST:240,GYT:-240,HAA:-180,HAC:-300,HADT:-540,HAE:-240,HAP:-420,HAR:-360,HAST:-600,HAT:-90,HAY:-480,HKT:480,HLV:-210,HNA:-240,HNC:-360,HNE:-300,HNP:-480,HNR:-420,HNT:-150,HNY:-540,HOVT:420,ICT:420,IDT:180,IOT:360,IRDT:270,IRKST:540,IRKT:540,IRST:210,IST:330,JST:540,KGT:360,KRAST:480,KRAT:480,KST:540,KUYT:240,LHDT:660,LHST:630,LINT:840,MAGST:720,MAGT:720,MART:-510,MAWT:300,MDT:-360,MESZ:120,MEZ:60,MHT:720,MMT:390,MSD:240,MSK:240,MST:-420,MUT:240,MVT:300,MYT:480,NCT:660,NDT:-90,NFT:690,NOVST:420,NOVT:360,NPT:345,NST:-150,NUT:-660,NZDT:780,NZST:720,OMSST:420,OMST:420,PDT:-420,PET:-300,PETST:720,PETT:720,PGT:600,PHOT:780,PHT:480,PKT:300,PMDT:-120,PMST:-180,PONT:660,PST:-480,PT:-480,PWT:540,PYST:-180,PYT:-240,RET:240,SAMT:240,SAST:120,SBT:660,SCT:240,SGT:480,SRT:-180,SST:-660,TAHT:-600,TFT:300,TJT:300,TKT:780,TLT:540,TMT:300,TVT:720,ULAT:480,UTC:0,UYST:-120,UYT:-180,UZT:300,VET:-210,VLAST:660,VLAT:660,VUT:660,WAST:120,WAT:60,WEST:60,WESZ:60,WET:0,WEZ:0,WFT:720,WGST:-120,WGT:-180,WIB:420,WIT:540,WITA:480,WST:780,WT:0,YAKST:600,YAKT:600,YAPT:600,YEKST:360,YEKT:360},fd=class{constructor(e){this.timezone=Object.assign(Object.assign({},PS),e)}refine(e,t){var n;let i=(n=e.option.timezones)!==null&&n!==void 0?n:{};return t.forEach(s=>{var a,o;let u=e.text.substring(s.index+s.text.length),l=AS.exec(u);if(!l)return;let c=l[1].toUpperCase(),d=(o=(a=i[c])!==null&&a!==void 0?a:this.timezone[c])!==null&&o!==void 0?o:null;if(d===null)return;e.debug(()=>{console.log(`Extracting timezone: '${c}' into: ${d} for: ${s.start}`)});let p=s.start.get("timezoneOffset");p!==null&&d!=p&&(s.start.isCertain("timezoneOffset")||c!=l[1])||s.start.isOnlyDate()&&c!=l[1]||(s.text+=l[0],s.start.isCertain("timezoneOffset")||s.start.assign("timezoneOffset",d),s.end!=null&&!s.end.isCertain("timezoneOffset")&&s.end.assign("timezoneOffset",d))}),t}};pd.default=fd});var iu=k(hd=>{"use strict";Object.defineProperty(hd,"__esModule",{value:!0});var NS=new RegExp("^\\s*(?:\\(?(?:GMT|UTC)\\s?)?([+-])(\\d{1,2})(?::?(\\d{2}))?\\)?","i"),IS=1,FS=2,LS=3,md=class{refine(e,t){return t.forEach(function(n){if(n.start.isCertain("timezoneOffset"))return;let i=e.text.substring(n.index+n.text.length),s=NS.exec(i);if(!s)return;e.debug(()=>{console.log(`Extracting timezone: '${s[0]}' into : ${n}`)});let a=parseInt(s[FS]),o=parseInt(s[LS]||"0"),u=a*60+o;u>14*60||(s[IS]==="-"&&(u=-u),n.end!=null&&n.end.assign("timezoneOffset",u),n.start.assign("timezoneOffset",u),n.text+=s[0])}),t}};hd.default=md});var Fg=k(yd=>{"use strict";Object.defineProperty(yd,"__esModule",{value:!0});var gd=class{refine(e,t){if(t.length<2)return t;let n=[],i=t[0];for(let s=1;s<t.length;s++){let a=t[s];a.index<i.index+i.text.length?a.text.length>i.text.length&&(i=a):(n.push(i),i=a)}return i!=null&&n.push(i),n}};yd.default=gd});var Lg=k(fa=>{"use strict";var US=fa&&fa.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(fa,"__esModule",{value:!0});var WS=US(we()),bd=class{refine(e,t){return e.option.forwardDate&&t.forEach(function(n){let i=WS.default(e.refDate);if(n.start.isOnlyDayMonthComponent()&&i.isAfter(n.start.dayjs()))for(let s=0;s<3&&i.isAfter(n.start.dayjs());s++)n.start.imply("year",n.start.get("year")+1),e.debug(()=>{console.log(`Forward yearly adjusted for ${n} (${n.start})`)}),n.end&&!n.end.isCertain("year")&&(n.end.imply("year",n.end.get("year")+1),e.debug(()=>{console.log(`Forward yearly adjusted for ${n} (${n.end})`)}));n.start.isOnlyWeekdayComponent()&&i.isAfter(n.start.dayjs())&&(i.day()>=n.start.get("weekday")?i=i.day(n.start.get("weekday")+7):i=i.day(n.start.get("weekday")),n.start.imply("day",i.date()),n.start.imply("month",i.month()+1),n.start.imply("year",i.year()),e.debug(()=>{console.log(`Forward weekly adjusted for ${n} (${n.start})`)}),n.end&&n.end.isOnlyWeekdayComponent()&&(i.day()>n.end.get("weekday")?i=i.day(n.end.get("weekday")+7):i=i.day(n.end.get("weekday")),n.end.imply("day",i.date()),n.end.imply("month",i.month()+1),n.end.imply("year",i.year()),e.debug(()=>{console.log(`Forward weekly adjusted for ${n} (${n.end})`)})))}),t}};fa.default=bd});var Ug=k(_d=>{"use strict";Object.defineProperty(_d,"__esModule",{value:!0});var qS=Xi(),Td=class extends qS.Filter{constructor(e){super(),this.strictMode=e}isValid(e,t){return t.text.replace(" ","").match(/^\d*(\.\d*)?$/)?(e.debug(()=>{console.log(`Removing unlikely result '${t.text}'`)}),!1):t.start.isValidDate()?t.end&&!t.end.isValidDate()?(e.debug(()=>{console.log(`Removing invalid result: ${t} (${t.end})`)}),!1):this.strictMode?this.isStrictModeValid(e,t):!0:(e.debug(()=>{console.log(`Removing invalid result: ${t} (${t.start})`)}),!1)}isStrictModeValid(e,t){return t.start.isOnlyWeekdayComponent()?(e.debug(()=>{console.log(`(Strict) Removing weekday only component: ${t} (${t.end})`)}),!1):t.start.isOnlyTime()&&(!t.start.isCertain("hour")||!t.start.isCertain("minute"))?(e.debug(()=>{console.log(`(Strict) Removing uncertain time component: ${t} (${t.end})`)}),!1):!0}};_d.default=Td});var kd=k(wd=>{"use strict";Object.defineProperty(wd,"__esModule",{value:!0});var $S=V(),jS=new RegExp("([0-9]{4})\\-([0-9]{1,2})\\-([0-9]{1,2})(?:T([0-9]{1,2}):([0-9]{1,2})(?::([0-9]{1,2})(?:\\.(\\d{1,4}))?)?(?:Z|([+-]\\d{2}):?(\\d{2})?)?)?(?=\\W|$)","i"),GS=1,YS=2,BS=3,Wg=4,HS=5,qg=6,$g=7,jg=8,Gg=9,vd=class extends $S.AbstractParserWithWordBoundaryChecking{innerPattern(){return jS}innerExtract(e,t){let n={};if(n.year=parseInt(t[GS]),n.month=parseInt(t[YS]),n.day=parseInt(t[BS]),t[Wg]!=null)if(n.hour=parseInt(t[Wg]),n.minute=parseInt(t[HS]),t[qg]!=null&&(n.second=parseInt(t[qg])),t[$g]!=null&&(n.millisecond=parseInt(t[$g])),t[jg]==null)n.timezoneOffset=0;else{let i=parseInt(t[jg]),s=0;t[Gg]!=null&&(s=parseInt(t[Gg]));let a=i*60;a<0?a-=s:a+=s,n.timezoneOffset=a}return n}};wd.default=vd});var Yg=k(Sd=>{"use strict";Object.defineProperty(Sd,"__esModule",{value:!0});var VS=Xi(),Ed=class extends VS.MergingRefiner{mergeResults(e,t,n){let i=n.clone();return i.index=t.index,i.text=t.text+e+i.text,i.start.assign("weekday",t.start.get("weekday")),i.end&&i.end.assign("weekday",t.start.get("weekday")),i}shouldMergeResults(e,t,n){return t.start.isOnlyWeekdayComponent()&&!t.start.isCertain("hour")&&n.start.isCertain("day")&&e.match(/^,?\s*$/)!=null}};Sd.default=Ed});var dn=k(Ji=>{"use strict";var di=Ji&&Ji.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(Ji,"__esModule",{value:!0});Ji.includeCommonConfiguration=void 0;var zS=di(Ig()),KS=di(iu()),Bg=di(Fg()),QS=di(Lg()),XS=di(Ug()),ZS=di(kd()),JS=di(Yg());function eO(r,e=!1){return r.parsers.unshift(new ZS.default),r.refiners.unshift(new JS.default),r.refiners.unshift(new zS.default),r.refiners.unshift(new KS.default),r.refiners.unshift(new Bg.default),r.refiners.push(new Bg.default),r.refiners.push(new QS.default),r.refiners.push(new XS.default(e)),r}Ji.includeCommonConfiguration=eO});var pn=k(Oe=>{"use strict";var tO=Oe&&Oe.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(Oe,"__esModule",{value:!0});Oe.noon=Oe.morning=Oe.midnight=Oe.yesterdayEvening=Oe.evening=Oe.lastNight=Oe.tonight=Oe.theDayAfter=Oe.tomorrow=Oe.theDayBefore=Oe.yesterday=Oe.today=Oe.now=void 0;var Yr=We(),es=tO(we()),fn=ir(),pa=ze();function rO(r){let e=es.default(r.instant),t=new Yr.ParsingComponents(r,{});return fn.assignSimilarDate(t,e),fn.assignSimilarTime(t,e),r.timezoneOffset!==null&&t.assign("timezoneOffset",e.utcOffset()),t}Oe.now=rO;function nO(r){let e=es.default(r.instant),t=new Yr.ParsingComponents(r,{});return fn.assignSimilarDate(t,e),fn.implySimilarTime(t,e),t}Oe.today=nO;function iO(r){return Hg(r,1)}Oe.yesterday=iO;function Hg(r,e){return Od(r,-e)}Oe.theDayBefore=Hg;function sO(r){return Od(r,1)}Oe.tomorrow=sO;function Od(r,e){let t=es.default(r.instant),n=new Yr.ParsingComponents(r,{});return t=t.add(e,"day"),fn.assignSimilarDate(n,t),fn.implySimilarTime(n,t),n}Oe.theDayAfter=Od;function aO(r,e=22){let t=es.default(r.instant),n=new Yr.ParsingComponents(r,{});return n.imply("hour",e),n.imply("meridiem",pa.Meridiem.PM),fn.assignSimilarDate(n,t),n}Oe.tonight=aO;function oO(r,e=0){let t=es.default(r.instant),n=new Yr.ParsingComponents(r,{});return t.hour()<6&&(t=t.add(-1,"day")),fn.assignSimilarDate(n,t),n.imply("hour",e),n}Oe.lastNight=oO;function uO(r,e=20){let t=new Yr.ParsingComponents(r,{});return t.imply("meridiem",pa.Meridiem.PM),t.imply("hour",e),t}Oe.evening=uO;function lO(r,e=20){let t=es.default(r.instant),n=new Yr.ParsingComponents(r,{});return t=t.add(-1,"day"),fn.assignSimilarDate(n,t),n.imply("hour",e),n.imply("meridiem",pa.Meridiem.PM),n}Oe.yesterdayEvening=lO;function cO(r){let e=new Yr.ParsingComponents(r,{});return e.imply("hour",0),e.imply("minute",0),e.imply("second",0),e}Oe.midnight=cO;function dO(r,e=6){let t=new Yr.ParsingComponents(r,{});return t.imply("meridiem",pa.Meridiem.AM),t.imply("hour",e),t}Oe.morning=dO;function fO(r){let e=new Yr.ParsingComponents(r,{});return e.imply("meridiem",pa.Meridiem.AM),e.imply("hour",12),e}Oe.noon=fO});var Vg=k(wr=>{"use strict";var pO=wr&&wr.__createBinding||(Object.create?function(r,e,t,n){n===void 0&&(n=t),Object.defineProperty(r,n,{enumerable:!0,get:function(){return e[t]}})}:function(r,e,t,n){n===void 0&&(n=t),r[n]=e[t]}),mO=wr&&wr.__setModuleDefault||(Object.create?function(r,e){Object.defineProperty(r,"default",{enumerable:!0,value:e})}:function(r,e){r.default=e}),hO=wr&&wr.__importStar||function(r){if(r&&r.__esModule)return r;var e={};if(r!=null)for(var t in r)t!=="default"&&Object.prototype.hasOwnProperty.call(r,t)&&pO(e,r,t);return mO(e,r),e},gO=wr&&wr.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(wr,"__esModule",{value:!0});var yO=gO(we()),bO=V(),TO=ir(),ma=hO(pn()),_O=/(now|today|tonight|tomorrow|tmr|tmrw|yesterday|last\s*night)(?=\W|$)/i,Dd=class extends bO.AbstractParserWithWordBoundaryChecking{innerPattern(e){return _O}innerExtract(e,t){let n=yO.default(e.refDate),i=t[0].toLowerCase(),s=e.createParsingComponents();switch(i){case"now":return ma.now(e.reference);case"today":return ma.today(e.reference);case"yesterday":return ma.yesterday(e.reference);case"tomorrow":case"tmr":case"tmrw":return ma.tomorrow(e.reference);case"tonight":return ma.tonight(e.reference);default:i.match(/last\s*night/)&&(n.hour()>6&&(n=n.add(-1,"day")),TO.assignSimilarDate(s,n),s.imply("hour",0));break}return s}};wr.default=Dd});var zg=k(ha=>{"use strict";var vO=ha&&ha.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(ha,"__esModule",{value:!0});var su=ze(),wO=V(),kO=vO(we()),EO=ir(),SO=/(?:this)?\s{0,3}(morning|afternoon|evening|night|midnight|noon)(?=\W|$)/i,xd=class extends wO.AbstractParserWithWordBoundaryChecking{innerPattern(){return SO}innerExtract(e,t){let n=kO.default(e.refDate),i=e.createParsingComponents();switch(t[1].toLowerCase()){case"afternoon":i.imply("meridiem",su.Meridiem.PM),i.imply("hour",15);break;case"evening":case"night":i.imply("meridiem",su.Meridiem.PM),i.imply("hour",20);break;case"midnight":EO.assignTheNextDay(i,n),i.imply("hour",0),i.imply("minute",0),i.imply("second",0);break;case"morning":i.imply("meridiem",su.Meridiem.AM),i.imply("hour",6);break;case"noon":i.imply("meridiem",su.Meridiem.AM),i.imply("hour",12);break}return i}};ha.default=xd});var fi=k(Wn=>{"use strict";var OO=Wn&&Wn.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(Wn,"__esModule",{value:!0});Wn.toDayJSClosestWeekday=Wn.toDayJSWeekday=void 0;var Kg=OO(we());function DO(r,e,t){if(!t)return Qg(r,e);let n=Kg.default(r);switch(t){case"this":n=n.day(e);break;case"next":n=n.day(e+7);break;case"last":n=n.day(e-7);break}return n}Wn.toDayJSWeekday=DO;function Qg(r,e){let t=Kg.default(r),n=t.day();return Math.abs(e-7-n)<Math.abs(e-n)?t=t.day(e-7):Math.abs(e+7-n)<Math.abs(e-n)?t=t.day(e+7):t=t.day(e),t}Wn.toDayJSClosestWeekday=Qg});var Zg=k(Md=>{"use strict";Object.defineProperty(Md,"__esModule",{value:!0});var Xg=ut(),xO=Re(),RO=V(),MO=fi(),CO=new RegExp(`(?:(?:\\,|\\(|\\\uFF08)\\s*)?(?:on\\s*?)?(?:(this|last|past|next)\\s*)?(${xO.matchAnyPattern(Xg.WEEKDAY_DICTIONARY)})(?:\\s*(?:\\,|\\)|\\\uFF09))?(?:\\s*(this|last|past|next)\\s*week)?(?=\\W|$)`,"i"),AO=1,PO=2,NO=3,Rd=class extends RO.AbstractParserWithWordBoundaryChecking{innerPattern(){return CO}innerExtract(e,t){let n=t[PO].toLowerCase(),i=Xg.WEEKDAY_DICTIONARY[n],s=t[AO],a=t[NO],o=s||a;o=o||"",o=o.toLowerCase();let u=null;o=="last"||o=="past"?u="last":o=="next"?u="next":o=="this"&&(u="this");let l=MO.toDayJSWeekday(e.refDate,i,u);return e.createParsingComponents().assign("weekday",i).imply("day",l.date()).imply("month",l.month()+1).imply("year",l.year())}};Md.default=Rd});var ty=k(ga=>{"use strict";var IO=ga&&ga.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(ga,"__esModule",{value:!0});var ey=ut(),Jg=We(),FO=IO(we()),LO=V(),UO=Re(),WO=new RegExp(`(this|last|past|next|after\\s*this)\\s*(${UO.matchAnyPattern(ey.TIME_UNIT_DICTIONARY)})(?=\\s*)(?=\\W|$)`,"i"),qO=1,$O=2,Cd=class extends LO.AbstractParserWithWordBoundaryChecking{innerPattern(){return WO}innerExtract(e,t){let n=t[qO].toLowerCase(),i=t[$O].toLowerCase(),s=ey.TIME_UNIT_DICTIONARY[i];if(n=="next"||n.startsWith("after")){let u={};return u[s]=1,Jg.ParsingComponents.createRelativeFromReference(e.reference,u)}if(n=="last"||n=="past"){let u={};return u[s]=-1,Jg.ParsingComponents.createRelativeFromReference(e.reference,u)}let a=e.createParsingComponents(),o=FO.default(e.reference.instant);return i.match(/week/i)?(o=o.add(-o.get("d"),"d"),a.imply("day",o.date()),a.imply("month",o.month()+1),a.imply("year",o.year())):i.match(/month/i)?(o=o.add(-o.date()+1,"d"),a.imply("day",o.date()),a.assign("year",o.year()),a.assign("month",o.month()+1)):i.match(/year/i)&&(o=o.add(-o.date()+1,"d"),o=o.add(-o.month(),"month"),a.imply("day",o.date()),a.imply("month",o.month()+1),a.assign("year",o.year())),a}};ga.default=Cd});var kr=k(ns=>{"use strict";Object.defineProperty(ns,"__esModule",{value:!0});ns.ParsingContext=ns.Chrono=void 0;var ts=We(),jO=Ad(),rs=class{constructor(e){e=e||jO.createCasualConfiguration(),this.parsers=[...e.parsers],this.refiners=[...e.refiners]}clone(){return new rs({parsers:[...this.parsers],refiners:[...this.refiners]})}parseDate(e,t,n){let i=this.parse(e,t,n);return i.length>0?i[0].start.date():null}parse(e,t,n){let i=new au(e,t,n),s=[];return this.parsers.forEach(a=>{let o=rs.executeParser(i,a);s=s.concat(o)}),s.sort((a,o)=>a.index-o.index),this.refiners.forEach(function(a){s=a.refine(i,s)}),s}static executeParser(e,t){let n=[],i=t.pattern(e),s=e.text,a=e.text,o=i.exec(a);for(;o;){let u=o.index+s.length-a.length;o.index=u;let l=t.extract(e,o);if(!l){a=s.substring(o.index+1),o=i.exec(a);continue}let c=null;l instanceof ts.ParsingResult?c=l:l instanceof ts.ParsingComponents?(c=e.createParsingResult(o.index,o[0]),c.start=l):c=e.createParsingResult(o.index,o[0],l),e.debug(()=>console.log(`${t.constructor.name} extracted result ${c}`)),n.push(c),a=s.substring(u+c.text.length),o=i.exec(a)}return n}};ns.Chrono=rs;var au=class{constructor(e,t,n){this.text=e,this.reference=new ts.ReferenceWithTimezone(t),this.option=n!=null?n:{},this.refDate=this.reference.instant}createParsingComponents(e){return e instanceof ts.ParsingComponents?e:new ts.ParsingComponents(this.reference,e)}createParsingResult(e,t,n,i){let s=typeof t=="string"?t:this.text.substring(e,t),a=n?this.createParsingComponents(n):null,o=i?this.createParsingComponents(i):null;return new ts.ParsingResult(this.reference,e,s,a,o)}debug(e){this.option.debug&&(this.option.debug instanceof Function?this.option.debug(e):this.option.debug.debug(e))}};ns.ParsingContext=au});var pi=k(Id=>{"use strict";Object.defineProperty(Id,"__esModule",{value:!0});var ry=ot(),GO=new RegExp("([^\\d]|^)([0-3]{0,1}[0-9]{1})[\\/\\.\\-]([0-3]{0,1}[0-9]{1})(?:[\\/\\.\\-]([0-9]{4}|[0-9]{2}))?(\\W|$)","i"),ou=1,ny=5,iy=2,sy=3,Pd=4,Nd=class{constructor(e){this.groupNumberMonth=e?sy:iy,this.groupNumberDay=e?iy:sy}pattern(){return GO}extract(e,t){if(t[ou]=="/"||t[ny]=="/"){t.index+=t[0].length;return}let n=t.index+t[ou].length,i=t[0].substr(t[ou].length,t[0].length-t[ou].length-t[ny].length);if(i.match(/^\d\.\d$/)||i.match(/^\d\.\d{1,2}\.\d{1,2}\s*$/)||!t[Pd]&&t[0].indexOf("/")<0)return;let s=e.createParsingResult(n,i),a=parseInt(t[this.groupNumberMonth]),o=parseInt(t[this.groupNumberDay]);if((a<1||a>12)&&a>12)if(o>=1&&o<=12&&a<=31)[o,a]=[a,o];else return null;if(o<1||o>31)return null;if(s.start.assign("day",o),s.start.assign("month",a),t[Pd]){let u=parseInt(t[Pd]),l=ry.findMostLikelyADYear(u);s.start.assign("year",l)}else{let u=ry.findYearClosestToRef(e.refDate,o,a);s.start.imply("year",u)}return s}};Id.default=Nd});var oy=k(Ld=>{"use strict";Object.defineProperty(Ld,"__esModule",{value:!0});var ay=ut(),YO=We(),BO=V(),HO=sr(),VO=new RegExp(`(this|last|past|next|after|\\+|-)\\s*(${ay.TIME_UNITS_PATTERN})(?=\\W|$)`,"i"),Fd=class extends BO.AbstractParserWithWordBoundaryChecking{innerPattern(){return VO}innerExtract(e,t){let n=t[1].toLowerCase(),i=ay.parseTimeUnits(t[2]);switch(n){case"last":case"past":case"-":i=HO.reverseTimeUnits(i);break}return YO.ParsingComponents.createRelativeFromReference(e.reference,i)}};Ld.default=Fd});var ly=k(qd=>{"use strict";Object.defineProperty(qd,"__esModule",{value:!0});var zO=Xi(),Ud=We(),KO=ut(),QO=sr();function uy(r){return r.text.match(/\s+(before|from)$/i)!=null}function XO(r){return r.text.match(/\s+(after|since)$/i)!=null}var Wd=class extends zO.MergingRefiner{patternBetween(){return/^\s*$/i}shouldMergeResults(e,t,n){return!e.match(this.patternBetween())||!uy(t)&&!XO(t)?!1:!!n.start.get("day")&&!!n.start.get("month")&&!!n.start.get("year")}mergeResults(e,t,n){let i=KO.parseTimeUnits(t.text);uy(t)&&(i=QO.reverseTimeUnits(i));let s=Ud.ParsingComponents.createRelativeFromReference(new Ud.ReferenceWithTimezone(n.start.date()),i);return new Ud.ParsingResult(n.reference,t.index,`${t.text}${e}${n.text}`,s)}};qd.default=Wd});var Ad=k(Ge=>{"use strict";var Xe=Ge&&Ge.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(Ge,"__esModule",{value:!0});Ge.createConfiguration=Ge.createCasualConfiguration=Ge.parseDate=Ge.parse=Ge.GB=Ge.strict=Ge.casual=void 0;var ZO=Xe(ug()),JO=Xe(mg()),e0=Xe(Tg()),t0=Xe(wg()),r0=Xe(Sg()),n0=Xe(Og()),i0=Xe(xg()),s0=Xe(Rg()),a0=Xe(Mg()),o0=Xe(Cg()),u0=Xe(Ng()),l0=dn(),c0=Xe(Vg()),d0=Xe(zg()),f0=Xe(Zg()),p0=Xe(ty()),$d=kr(),m0=Xe(pi()),h0=Xe(oy()),g0=Xe(ly());Ge.casual=new $d.Chrono(cy(!1));Ge.strict=new $d.Chrono(uu(!0,!1));Ge.GB=new $d.Chrono(uu(!1,!0));function y0(r,e,t){return Ge.casual.parse(r,e,t)}Ge.parse=y0;function b0(r,e,t){return Ge.casual.parseDate(r,e,t)}Ge.parseDate=b0;function cy(r=!1){let e=uu(!1,r);return e.parsers.unshift(new c0.default),e.parsers.unshift(new d0.default),e.parsers.unshift(new t0.default),e.parsers.unshift(new p0.default),e.parsers.unshift(new h0.default),e}Ge.createCasualConfiguration=cy;function uu(r=!0,e=!1){return l0.includeCommonConfiguration({parsers:[new m0.default(e),new ZO.default,new JO.default,new e0.default,new f0.default,new r0.default,new n0.default,new i0.default(r),new s0.default(r),new a0.default(r)],refiners:[new g0.default,new u0.default,new o0.default]},r)}Ge.createConfiguration=uu});var dy=k(Gd=>{"use strict";Object.defineProperty(Gd,"__esModule",{value:!0});var T0=ci(),jd=class extends T0.AbstractTimeExpressionParser{primaryPrefix(){return"(?:(?:um|von)\\s*)?"}followingPhase(){return"\\s*(?:\\-|\\\u2013|\\~|\\\u301C|bis)\\s*"}extractPrimaryTimeComponents(e,t){return t[0].match(/^\s*\d{4}\s*$/)?null:super.extractPrimaryTimeComponents(e,t)}};Gd.default=jd});var ya=k(Ae=>{"use strict";Object.defineProperty(Ae,"__esModule",{value:!0});Ae.parseTimeUnits=Ae.TIME_UNITS_PATTERN=Ae.parseYear=Ae.YEAR_PATTERN=Ae.parseNumberPattern=Ae.NUMBER_PATTERN=Ae.TIME_UNIT_DICTIONARY=Ae.INTEGER_WORD_DICTIONARY=Ae.MONTH_DICTIONARY=Ae.WEEKDAY_DICTIONARY=void 0;var Yd=Re(),_0=ot();Ae.WEEKDAY_DICTIONARY={sonntag:0,so:0,montag:1,mo:1,dienstag:2,di:2,mittwoch:3,mi:3,donnerstag:4,do:4,freitag:5,fr:5,samstag:6,sa:6};Ae.MONTH_DICTIONARY={januar:1,j\u00E4nner:1,janner:1,jan:1,"jan.":1,februar:2,feber:2,feb:2,"feb.":2,m\u00E4rz:3,maerz:3,m\u00E4r:3,"m\xE4r.":3,mrz:3,"mrz.":3,april:4,apr:4,"apr.":4,mai:5,juni:6,jun:6,"jun.":6,juli:7,jul:7,"jul.":7,august:8,aug:8,"aug.":8,september:9,sep:9,"sep.":9,sept:9,"sept.":9,oktober:10,okt:10,"okt.":10,november:11,nov:11,"nov.":11,dezember:12,dez:12,"dez.":12};Ae.INTEGER_WORD_DICTIONARY={eins:1,eine:1,einem:1,einen:1,einer:1,zwei:2,drei:3,vier:4,f\u00FCnf:5,fuenf:5,sechs:6,sieben:7,acht:8,neun:9,zehn:10,elf:11,zw\u00F6lf:12,zwoelf:12};Ae.TIME_UNIT_DICTIONARY={sek:"second",sekunde:"second",sekunden:"second",min:"minute",minute:"minute",minuten:"minute",h:"hour",std:"hour",stunde:"hour",stunden:"hour",tag:"d",tage:"d",tagen:"d",woche:"week",wochen:"week",monat:"month",monate:"month",monaten:"month",monats:"month",quartal:"quarter",quartals:"quarter",quartale:"quarter",quartalen:"quarter",a:"year",j:"year",jr:"year",jahr:"year",jahre:"year",jahren:"year",jahres:"year"};Ae.NUMBER_PATTERN=`(?:${Yd.matchAnyPattern(Ae.INTEGER_WORD_DICTIONARY)}|[0-9]+|[0-9]+\\.[0-9]+|half(?:\\s*an?)?|an?\\b(?:\\s*few)?|few|several|a?\\s*couple\\s*(?:of)?)`;function py(r){let e=r.toLowerCase();return Ae.INTEGER_WORD_DICTIONARY[e]!==void 0?Ae.INTEGER_WORD_DICTIONARY[e]:e==="a"||e==="an"?1:e.match(/few/)?3:e.match(/half/)?.5:e.match(/couple/)?2:e.match(/several/)?7:parseFloat(e)}Ae.parseNumberPattern=py;Ae.YEAR_PATTERN="(?:[0-9]{1,4}(?:\\s*[vn]\\.?\\s*(?:C(?:hr)?|(?:u\\.?|d\\.?(?:\\s*g\\.?)?)?\\s*Z)\\.?|\\s*(?:u\\.?|d\\.?(?:\\s*g\\.)?)\\s*Z\\.?)?)";function v0(r){if(/v/i.test(r))return-parseInt(r.replace(/[^0-9]+/gi,""));if(/n/i.test(r))return parseInt(r.replace(/[^0-9]+/gi,""));if(/z/i.test(r))return parseInt(r.replace(/[^0-9]+/gi,""));let e=parseInt(r);return _0.findMostLikelyADYear(e)}Ae.parseYear=v0;var my=`(${Ae.NUMBER_PATTERN})\\s{0,5}(${Yd.matchAnyPattern(Ae.TIME_UNIT_DICTIONARY)})\\s{0,5}`,fy=new RegExp(my,"i");Ae.TIME_UNITS_PATTERN=Yd.repeatedTimeunitPattern("",my);function w0(r){let e={},t=r,n=fy.exec(t);for(;n;)k0(e,n),t=t.substring(n[0].length),n=fy.exec(t);return e}Ae.parseTimeUnits=w0;function k0(r,e){let t=py(e[1]),n=Ae.TIME_UNIT_DICTIONARY[e[2].toLowerCase()];r[n]=t}});var gy=k(Hd=>{"use strict";Object.defineProperty(Hd,"__esModule",{value:!0});var hy=ya(),E0=Re(),S0=V(),O0=fi(),D0=new RegExp(`(?:(?:\\,|\\(|\\\uFF08)\\s*)?(?:a[mn]\\s*?)?(?:(diese[mn]|letzte[mn]|n(?:\xE4|ae)chste[mn])\\s*)?(${E0.matchAnyPattern(hy.WEEKDAY_DICTIONARY)})(?:\\s*(?:\\,|\\)|\\\uFF09))?(?:\\s*(diese|letzte|n(?:\xE4|ae)chste)\\s*woche)?(?=\\W|$)`,"i"),x0=1,R0=3,M0=2,Bd=class extends S0.AbstractParserWithWordBoundaryChecking{innerPattern(){return D0}innerExtract(e,t){let n=t[M0].toLowerCase(),i=hy.WEEKDAY_DICTIONARY[n],s=t[x0],a=t[R0],o=s||a;o=o||"",o=o.toLowerCase();let u=null;o.match(/letzte/)?u="last":o.match(/chste/)?u="next":o.match(/diese/)&&(u="this");let l=O0.toDayJSWeekday(e.refDate,i,u);return e.createParsingComponents().assign("weekday",i).imply("day",l.date()).imply("month",l.month()+1).imply("year",l.year())}};Hd.default=Bd});var _y=k(Vd=>{"use strict";Object.defineProperty(Vd,"__esModule",{value:!0});var qn=ze(),C0=new RegExp("(^|\\s|T)(?:(?:um|von)\\s*)?(\\d{1,2})(?:h|:)?(?:(\\d{1,2})(?:m|:)?)?(?:(\\d{1,2})(?:s)?)?(?:\\s*Uhr)?(?:\\s*(morgens|vormittags|nachmittags|abends|nachts|am\\s+(?:Morgen|Vormittag|Nachmittag|Abend)|in\\s+der\\s+Nacht))?(?=\\W|$)","i"),A0=new RegExp("^\\s*(\\-|\\\u2013|\\~|\\\u301C|bis(?:\\s+um)?|\\?)\\s*(\\d{1,2})(?:h|:)?(?:(\\d{1,2})(?:m|:)?)?(?:(\\d{1,2})(?:s)?)?(?:\\s*Uhr)?(?:\\s*(morgens|vormittags|nachmittags|abends|nachts|am\\s+(?:Morgen|Vormittag|Nachmittag|Abend)|in\\s+der\\s+Nacht))?(?=\\W|$)","i"),P0=2,yy=3,by=4,Ty=5,is=class{pattern(e){return C0}extract(e,t){let n=e.createParsingResult(t.index+t[1].length,t[0].substring(t[1].length));if(n.text.match(/^\d{4}$/)||(n.start=is.extractTimeComponent(n.start.clone(),t),!n.start))return t.index+=t[0].length,null;let i=e.text.substring(t.index+t[0].length),s=A0.exec(i);return s&&(n.end=is.extractTimeComponent(n.start.clone(),s),n.end&&(n.text+=s[0])),n}static extractTimeComponent(e,t){let n=0,i=0,s=null;if(n=parseInt(t[P0]),t[yy]!=null&&(i=parseInt(t[yy])),i>=60||n>24)return null;if(n>=12&&(s=qn.Meridiem.PM),t[Ty]!=null){if(n>12)return null;let a=t[Ty].toLowerCase();a.match(/morgen|vormittag/)&&(s=qn.Meridiem.AM,n==12&&(n=0)),a.match(/nachmittag|abend/)&&(s=qn.Meridiem.PM,n!=12&&(n+=12)),a.match(/nacht/)&&(n==12?(s=qn.Meridiem.AM,n=0):n<6?s=qn.Meridiem.AM:(s=qn.Meridiem.PM,n+=12))}if(e.assign("hour",n),e.assign("minute",i),s!==null?e.assign("meridiem",s):n<12?e.imply("meridiem",qn.Meridiem.AM):e.imply("meridiem",qn.Meridiem.PM),t[by]!=null){let a=parseInt(t[by]);if(a>=60)return null;e.assign("second",a)}return e}};Vd.default=is});var vy=k(ba=>{"use strict";var N0=ba&&ba.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(ba,"__esModule",{value:!0});var I0=N0(Gr()),zd=class extends I0.default{patternBetween(){return/^\s*(bis(?:\s*(?:am|zum))?|-)\s*$/i}};ba.default=zd});var wy=k(Ta=>{"use strict";var F0=Ta&&Ta.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(Ta,"__esModule",{value:!0});var L0=F0(cn()),Kd=class extends L0.default{patternBetween(){return new RegExp("^\\s*(T|um|am|,|-)?\\s*$")}};Ta.default=Kd});var Qd=k(va=>{"use strict";var U0=va&&va.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(va,"__esModule",{value:!0});var W0=U0(we()),mi=ze(),q0=V(),$0=ir(),j0=sr(),_a=class extends q0.AbstractParserWithWordBoundaryChecking{innerPattern(e){return/(diesen)?\s*(morgen|vormittag|mittags?|nachmittag|abend|nacht|mitternacht)(?=\W|$)/i}innerExtract(e,t){let n=W0.default(e.refDate),i=t[2].toLowerCase(),s=e.createParsingComponents();return $0.implySimilarTime(s,n),_a.extractTimeComponents(s,i)}static extractTimeComponents(e,t){switch(t){case"morgen":e.imply("hour",6),e.imply("minute",0),e.imply("second",0),e.imply("meridiem",mi.Meridiem.AM);break;case"vormittag":e.imply("hour",9),e.imply("minute",0),e.imply("second",0),e.imply("meridiem",mi.Meridiem.AM);break;case"mittag":case"mittags":e.imply("hour",12),e.imply("minute",0),e.imply("second",0),e.imply("meridiem",mi.Meridiem.AM);break;case"nachmittag":e.imply("hour",15),e.imply("minute",0),e.imply("second",0),e.imply("meridiem",mi.Meridiem.PM);break;case"abend":e.imply("hour",18),e.imply("minute",0),e.imply("second",0),e.imply("meridiem",mi.Meridiem.PM);break;case"nacht":e.imply("hour",22),e.imply("minute",0),e.imply("second",0),e.imply("meridiem",mi.Meridiem.PM);break;case"mitternacht":e.get("hour")>1&&(e=j0.addImpliedTimeUnits(e,{day:1})),e.imply("hour",0),e.imply("minute",0),e.imply("second",0),e.imply("meridiem",mi.Meridiem.AM);break}return e}};va.default=_a});var Sy=k(Er=>{"use strict";var G0=Er&&Er.__createBinding||(Object.create?function(r,e,t,n){n===void 0&&(n=t),Object.defineProperty(r,n,{enumerable:!0,get:function(){return e[t]}})}:function(r,e,t,n){n===void 0&&(n=t),r[n]=e[t]}),Y0=Er&&Er.__setModuleDefault||(Object.create?function(r,e){Object.defineProperty(r,"default",{enumerable:!0,value:e})}:function(r,e){r.default=e}),B0=Er&&Er.__importStar||function(r){if(r&&r.__esModule)return r;var e={};if(r!=null)for(var t in r)t!=="default"&&Object.prototype.hasOwnProperty.call(r,t)&&G0(e,r,t);return Y0(e,r),e},Ey=Er&&Er.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(Er,"__esModule",{value:!0});var H0=Ey(we()),V0=V(),hi=ir(),z0=Ey(Qd()),ky=B0(pn()),K0=new RegExp("(jetzt|heute|morgen|\xFCbermorgen|uebermorgen|gestern|vorgestern|letzte\\s*nacht)(?:\\s*(morgen|vormittag|mittags?|nachmittag|abend|nacht|mitternacht))?(?=\\W|$)","i"),Q0=1,X0=2,Xd=class extends V0.AbstractParserWithWordBoundaryChecking{innerPattern(e){return K0}innerExtract(e,t){let n=H0.default(e.refDate),i=(t[Q0]||"").toLowerCase(),s=(t[X0]||"").toLowerCase(),a=e.createParsingComponents();switch(i){case"jetzt":a=ky.now(e.reference);break;case"heute":a=ky.today(e.reference);break;case"morgen":hi.assignTheNextDay(a,n);break;case"\xFCbermorgen":case"uebermorgen":n=n.add(1,"day"),hi.assignTheNextDay(a,n);break;case"gestern":n=n.add(-1,"day"),hi.assignSimilarDate(a,n),hi.implySimilarTime(a,n);break;case"vorgestern":n=n.add(-2,"day"),hi.assignSimilarDate(a,n),hi.implySimilarTime(a,n);break;default:i.match(/letzte\s*nacht/)&&(n.hour()>6&&(n=n.add(-1,"day")),hi.assignSimilarDate(a,n),a.imply("hour",0));break}return s&&(a=z0.default.extractTimeComponents(a,s)),a}};Er.default=Xd});var Cy=k(Jd=>{"use strict";Object.defineProperty(Jd,"__esModule",{value:!0});var Z0=ot(),Ry=ya(),My=ya(),J0=Re(),e1=V(),t1=new RegExp(`(?:am\\s*?)?(?:den\\s*?)?([0-9]{1,2})\\.(?:\\s*(?:bis(?:\\s*(?:am|zum))?|\\-|\\\u2013|\\s)\\s*([0-9]{1,2})\\.?)?\\s*(${J0.matchAnyPattern(Ry.MONTH_DICTIONARY)})(?:(?:-|/|,?\\s*)(${My.YEAR_PATTERN}(?![^\\s]\\d)))?(?=\\W|$)`,"i"),Oy=1,Dy=2,r1=3,xy=4,Zd=class extends e1.AbstractParserWithWordBoundaryChecking{innerPattern(){return t1}innerExtract(e,t){let n=e.createParsingResult(t.index,t[0]),i=Ry.MONTH_DICTIONARY[t[r1].toLowerCase()],s=parseInt(t[Oy]);if(s>31)return t.index=t.index+t[Oy].length,null;if(n.start.assign("month",i),n.start.assign("day",s),t[xy]){let a=My.parseYear(t[xy]);n.start.assign("year",a)}else{let a=Z0.findYearClosestToRef(e.refDate,s,i);n.start.imply("year",a)}if(t[Dy]){let a=parseInt(t[Dy]);n.end=n.start.clone(),n.end.assign("day",a)}return n}};Jd.default=Zd});var Ay=k(tf=>{"use strict";Object.defineProperty(tf,"__esModule",{value:!0});var lu=ya(),n1=We(),i1=V(),s1=sr(),a1=Re(),ef=class extends i1.AbstractParserWithWordBoundaryChecking{constructor(){super()}innerPattern(){return new RegExp(`(?:\\s*((?:n\xE4chste|kommende|folgende|letzte|vergangene|vorige|vor(?:her|an)gegangene)(?:s|n|m|r)?|vor|in)\\s*)?(${lu.NUMBER_PATTERN})?(?:\\s*(n\xE4chste|kommende|folgende|letzte|vergangene|vorige|vor(?:her|an)gegangene)(?:s|n|m|r)?)?\\s*(${a1.matchAnyPattern(lu.TIME_UNIT_DICTIONARY)})`,"i")}innerExtract(e,t){let n=t[2]?lu.parseNumberPattern(t[2]):1,i=lu.TIME_UNIT_DICTIONARY[t[4].toLowerCase()],s={};s[i]=n;let a=t[1]||t[3]||"";if(a=a.toLowerCase(),!!a)return(/vor/.test(a)||/letzte/.test(a)||/vergangen/.test(a))&&(s=s1.reverseTimeUnits(s)),n1.ParsingComponents.createRelativeFromReference(e.reference,s)}};tf.default=ef});var Iy=k(Ze=>{"use strict";var Sr=Ze&&Ze.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(Ze,"__esModule",{value:!0});Ze.createConfiguration=Ze.createCasualConfiguration=Ze.parseDate=Ze.parse=Ze.strict=Ze.casual=void 0;var o1=dn(),Py=kr(),u1=Sr(pi()),l1=Sr(kd()),c1=Sr(dy()),d1=Sr(gy()),f1=Sr(_y()),p1=Sr(vy()),m1=Sr(wy()),h1=Sr(Sy()),g1=Sr(Qd()),y1=Sr(Cy()),b1=Sr(Ay());Ze.casual=new Py.Chrono(Ny());Ze.strict=new Py.Chrono(rf(!0));function T1(r,e,t){return Ze.casual.parse(r,e,t)}Ze.parse=T1;function _1(r,e,t){return Ze.casual.parseDate(r,e,t)}Ze.parseDate=_1;function Ny(r=!0){let e=rf(!1,r);return e.parsers.unshift(new g1.default),e.parsers.unshift(new h1.default),e.parsers.unshift(new b1.default),e}Ze.createCasualConfiguration=Ny;function rf(r=!0,e=!0){return o1.includeCommonConfiguration({parsers:[new l1.default,new u1.default(e),new c1.default,new f1.default,new y1.default,new d1.default],refiners:[new p1.default,new m1.default]},r)}Ze.createConfiguration=rf});var Ly=k(Or=>{"use strict";var v1=Or&&Or.__createBinding||(Object.create?function(r,e,t,n){n===void 0&&(n=t),Object.defineProperty(r,n,{enumerable:!0,get:function(){return e[t]}})}:function(r,e,t,n){n===void 0&&(n=t),r[n]=e[t]}),w1=Or&&Or.__setModuleDefault||(Object.create?function(r,e){Object.defineProperty(r,"default",{enumerable:!0,value:e})}:function(r,e){r.default=e}),k1=Or&&Or.__importStar||function(r){if(r&&r.__esModule)return r;var e={};if(r!=null)for(var t in r)t!=="default"&&Object.prototype.hasOwnProperty.call(r,t)&&v1(e,r,t);return w1(e,r),e},E1=Or&&Or.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(Or,"__esModule",{value:!0});var S1=E1(we()),O1=ze(),D1=V(),Fy=ir(),cu=k1(pn()),nf=class extends D1.AbstractParserWithWordBoundaryChecking{innerPattern(e){return/(maintenant|aujourd'hui|demain|hier|cette\s*nuit|la\s*veille)(?=\W|$)/i}innerExtract(e,t){let n=S1.default(e.refDate),i=t[0].toLowerCase(),s=e.createParsingComponents();switch(i){case"maintenant":return cu.now(e.reference);case"aujourd'hui":return cu.today(e.reference);case"hier":return cu.yesterday(e.reference);case"demain":return cu.tomorrow(e.reference);default:i.match(/cette\s*nuit/)?(Fy.assignSimilarDate(s,n),s.imply("hour",22),s.imply("meridiem",O1.Meridiem.PM)):i.match(/la\s*veille/)&&(n=n.add(-1,"day"),Fy.assignSimilarDate(s,n),s.imply("hour",0))}return s}};Or.default=nf});var Uy=k(af=>{"use strict";Object.defineProperty(af,"__esModule",{value:!0});var wa=ze(),x1=V(),sf=class extends x1.AbstractParserWithWordBoundaryChecking{innerPattern(e){return/(cet?)?\s*(matin|soir|après-midi|aprem|a midi|à minuit)(?=\W|$)/i}innerExtract(e,t){let n=t[2].toLowerCase(),i=e.createParsingComponents();switch(n){case"apr\xE8s-midi":case"aprem":i.imply("hour",14),i.imply("minute",0),i.imply("meridiem",wa.Meridiem.PM);break;case"soir":i.imply("hour",18),i.imply("minute",0),i.imply("meridiem",wa.Meridiem.PM);break;case"matin":i.imply("hour",8),i.imply("minute",0),i.imply("meridiem",wa.Meridiem.AM);break;case"a midi":i.imply("hour",12),i.imply("minute",0),i.imply("meridiem",wa.Meridiem.AM);break;case"\xE0 minuit":i.imply("hour",0),i.imply("meridiem",wa.Meridiem.AM);break}return i}};af.default=sf});var Wy=k(uf=>{"use strict";Object.defineProperty(uf,"__esModule",{value:!0});var R1=ci(),of=class extends R1.AbstractTimeExpressionParser{primaryPrefix(){return"(?:(?:[\xE0a])\\s*)?"}followingPhase(){return"\\s*(?:\\-|\\\u2013|\\~|\\\u301C|[\xE0a]|\\?)\\s*"}extractPrimaryTimeComponents(e,t){return t[0].match(/^\s*\d{4}\s*$/)?null:super.extractPrimaryTimeComponents(e,t)}};uf.default=of});var qy=k(ka=>{"use strict";var M1=ka&&ka.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(ka,"__esModule",{value:!0});var C1=M1(cn()),lf=class extends C1.default{patternBetween(){return new RegExp("^\\s*(T|\xE0|a|vers|de|,|-)?\\s*$")}};ka.default=lf});var $y=k(Ea=>{"use strict";var A1=Ea&&Ea.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(Ea,"__esModule",{value:!0});var P1=A1(Gr()),cf=class extends P1.default{patternBetween(){return/^\s*(à|a|-)\s*$/i}};Ea.default=cf});var $n=k(ke=>{"use strict";Object.defineProperty(ke,"__esModule",{value:!0});ke.parseTimeUnits=ke.TIME_UNITS_PATTERN=ke.parseYear=ke.YEAR_PATTERN=ke.parseOrdinalNumberPattern=ke.ORDINAL_NUMBER_PATTERN=ke.parseNumberPattern=ke.NUMBER_PATTERN=ke.TIME_UNIT_DICTIONARY=ke.INTEGER_WORD_DICTIONARY=ke.MONTH_DICTIONARY=ke.WEEKDAY_DICTIONARY=void 0;var df=Re();ke.WEEKDAY_DICTIONARY={dimanche:0,dim:0,lundi:1,lun:1,mardi:2,mar:2,mercredi:3,mer:3,jeudi:4,jeu:4,vendredi:5,ven:5,samedi:6,sam:6};ke.MONTH_DICTIONARY={janvier:1,jan:1,"jan.":1,f\u00E9vrier:2,f\u00E9v:2,"f\xE9v.":2,fevrier:2,fev:2,"fev.":2,mars:3,mar:3,"mar.":3,avril:4,avr:4,"avr.":4,mai:5,juin:6,jun:6,juillet:7,juil:7,jul:7,"jul.":7,ao\u00FBt:8,aout:8,septembre:9,sep:9,"sep.":9,sept:9,"sept.":9,octobre:10,oct:10,"oct.":10,novembre:11,nov:11,"nov.":11,d\u00E9cembre:12,decembre:12,dec:12,"dec.":12};ke.INTEGER_WORD_DICTIONARY={un:1,deux:2,trois:3,quatre:4,cinq:5,six:6,sept:7,huit:8,neuf:9,dix:10,onze:11,douze:12,treize:13};ke.TIME_UNIT_DICTIONARY={sec:"second",seconde:"second",secondes:"second",min:"minute",mins:"minute",minute:"minute",minutes:"minute",h:"hour",hr:"hour",hrs:"hour",heure:"hour",heures:"hour",jour:"d",jours:"d",semaine:"week",semaines:"week",mois:"month",trimestre:"quarter",trimestres:"quarter",ans:"year",ann\u00E9e:"year",ann\u00E9es:"year"};ke.NUMBER_PATTERN=`(?:${df.matchAnyPattern(ke.INTEGER_WORD_DICTIONARY)}|[0-9]+|[0-9]+\\.[0-9]+|une?\\b|quelques?|demi-?)`;function Gy(r){let e=r.toLowerCase();return ke.INTEGER_WORD_DICTIONARY[e]!==void 0?ke.INTEGER_WORD_DICTIONARY[e]:e==="une"||e==="un"?1:e.match(/quelques?/)?3:e.match(/demi-?/)?.5:parseFloat(e)}ke.parseNumberPattern=Gy;ke.ORDINAL_NUMBER_PATTERN="(?:[0-9]{1,2}(?:er)?)";function N1(r){let e=r.toLowerCase();return e=e.replace(/(?:er)$/i,""),parseInt(e)}ke.parseOrdinalNumberPattern=N1;ke.YEAR_PATTERN="(?:[1-9][0-9]{0,3}\\s*(?:AC|AD|p\\.\\s*C(?:hr?)?\\.\\s*n\\.)|[1-2][0-9]{3}|[5-9][0-9])";function I1(r){if(/AC/i.test(r))return r=r.replace(/BC/i,""),-parseInt(r);if(/AD/i.test(r)||/C/i.test(r))return r=r.replace(/[^\d]+/i,""),parseInt(r);let e=parseInt(r);return e<100&&(e>50?e=e+1900:e=e+2e3),e}ke.parseYear=I1;var Yy=`(${ke.NUMBER_PATTERN})\\s{0,5}(${df.matchAnyPattern(ke.TIME_UNIT_DICTIONARY)})\\s{0,5}`,jy=new RegExp(Yy,"i");ke.TIME_UNITS_PATTERN=df.repeatedTimeunitPattern("",Yy);function F1(r){let e={},t=r,n=jy.exec(t);for(;n;)L1(e,n),t=t.substring(n[0].length),n=jy.exec(t);return e}ke.parseTimeUnits=F1;function L1(r,e){let t=Gy(e[1]),n=ke.TIME_UNIT_DICTIONARY[e[2].toLowerCase()];r[n]=t}});var Hy=k(pf=>{"use strict";Object.defineProperty(pf,"__esModule",{value:!0});var By=$n(),U1=Re(),W1=V(),q1=fi(),$1=new RegExp(`(?:(?:\\,|\\(|\\\uFF08)\\s*)?(?:(?:ce)\\s*)?(${U1.matchAnyPattern(By.WEEKDAY_DICTIONARY)})(?:\\s*(?:\\,|\\)|\\\uFF09))?(?:\\s*(dernier|prochain)\\s*)?(?=\\W|\\d|$)`,"i"),j1=1,G1=2,ff=class extends W1.AbstractParserWithWordBoundaryChecking{innerPattern(){return $1}innerExtract(e,t){let n=t[j1].toLowerCase(),i=By.WEEKDAY_DICTIONARY[n];if(i===void 0)return null;let s=t[G1];s=s||"",s=s.toLowerCase();let a=null;s=="dernier"?a="last":s=="prochain"&&(a="next");let o=q1.toDayJSWeekday(e.refDate,i,a);return e.createParsingComponents().assign("weekday",i).imply("day",o.date()).imply("month",o.month()+1).imply("year",o.year())}};pf.default=ff});var Qy=k(mf=>{"use strict";Object.defineProperty(mf,"__esModule",{value:!0});var Sa=ze(),Y1=new RegExp("(^|\\s|T)(?:(?:[\xE0a])\\s*)?(\\d{1,2})(?:h|:)?(?:(\\d{1,2})(?:m|:)?)?(?:(\\d{1,2})(?:s|:)?)?(?:\\s*(A\\.M\\.|P\\.M\\.|AM?|PM?))?(?=\\W|$)","i"),B1=new RegExp("^\\s*(\\-|\\\u2013|\\~|\\\u301C|[\xE0a]|\\?)\\s*(\\d{1,2})(?:h|:)?(?:(\\d{1,2})(?:m|:)?)?(?:(\\d{1,2})(?:s|:)?)?(?:\\s*(A\\.M\\.|P\\.M\\.|AM?|PM?))?(?=\\W|$)","i"),H1=2,Vy=3,zy=4,Ky=5,ss=class{pattern(e){return Y1}extract(e,t){let n=e.createParsingResult(t.index+t[1].length,t[0].substring(t[1].length));if(n.text.match(/^\d{4}$/)||(n.start=ss.extractTimeComponent(n.start.clone(),t),!n.start))return t.index+=t[0].length,null;let i=e.text.substring(t.index+t[0].length),s=B1.exec(i);return s&&(n.end=ss.extractTimeComponent(n.start.clone(),s),n.end&&(n.text+=s[0])),n}static extractTimeComponent(e,t){let n=0,i=0,s=null;if(n=parseInt(t[H1]),t[Vy]!=null&&(i=parseInt(t[Vy])),i>=60||n>24)return null;if(n>=12&&(s=Sa.Meridiem.PM),t[Ky]!=null){if(n>12)return null;let a=t[Ky][0].toLowerCase();a=="a"&&(s=Sa.Meridiem.AM,n==12&&(n=0)),a=="p"&&(s=Sa.Meridiem.PM,n!=12&&(n+=12))}if(e.assign("hour",n),e.assign("minute",i),s!==null?e.assign("meridiem",s):n<12?e.imply("meridiem",Sa.Meridiem.AM):e.imply("meridiem",Sa.Meridiem.PM),t[zy]!=null){let a=parseInt(t[zy]);if(a>=60)return null;e.assign("second",a)}return e}};mf.default=ss});var rb=k(gf=>{"use strict";Object.defineProperty(gf,"__esModule",{value:!0});var V1=ot(),eb=$n(),tb=$n(),du=$n(),z1=Re(),K1=V(),Q1=new RegExp(`(?:on\\s*?)?(${du.ORDINAL_NUMBER_PATTERN})(?:\\s*(?:au|\\-|\\\u2013|jusqu'au?|\\s)\\s*(${du.ORDINAL_NUMBER_PATTERN}))?(?:-|/|\\s*(?:de)?\\s*)(${z1.matchAnyPattern(eb.MONTH_DICTIONARY)})(?:(?:-|/|,?\\s*)(${tb.YEAR_PATTERN}(?![^\\s]\\d)))?(?=\\W|$)`,"i"),Xy=1,Zy=2,X1=3,Jy=4,hf=class extends K1.AbstractParserWithWordBoundaryChecking{innerPattern(){return Q1}innerExtract(e,t){let n=e.createParsingResult(t.index,t[0]),i=eb.MONTH_DICTIONARY[t[X1].toLowerCase()],s=du.parseOrdinalNumberPattern(t[Xy]);if(s>31)return t.index=t.index+t[Xy].length,null;if(n.start.assign("month",i),n.start.assign("day",s),t[Jy]){let a=tb.parseYear(t[Jy]);n.start.assign("year",a)}else{let a=V1.findYearClosestToRef(e.refDate,s,i);n.start.imply("year",a)}if(t[Zy]){let a=du.parseOrdinalNumberPattern(t[Zy]);n.end=n.start.clone(),n.end.assign("day",a)}return n}};gf.default=hf});var ib=k(bf=>{"use strict";Object.defineProperty(bf,"__esModule",{value:!0});var nb=$n(),Z1=We(),J1=V(),eD=sr(),yf=class extends J1.AbstractParserWithWordBoundaryChecking{constructor(){super()}innerPattern(){return new RegExp(`il y a\\s*(${nb.TIME_UNITS_PATTERN})(?=(?:\\W|$))`,"i")}innerExtract(e,t){let n=nb.parseTimeUnits(t[1]),i=eD.reverseTimeUnits(n);return Z1.ParsingComponents.createRelativeFromReference(e.reference,i)}};bf.default=yf});var ab=k(_f=>{"use strict";Object.defineProperty(_f,"__esModule",{value:!0});var sb=$n(),tD=We(),rD=V(),Tf=class extends rD.AbstractParserWithWordBoundaryChecking{innerPattern(){return new RegExp(`(?:dans|en|pour|pendant|de)\\s*(${sb.TIME_UNITS_PATTERN})(?=\\W|$)`,"i")}innerExtract(e,t){let n=sb.parseTimeUnits(t[1]);return tD.ParsingComponents.createRelativeFromReference(e.reference,n)}};_f.default=Tf});var ob=k(wf=>{"use strict";Object.defineProperty(wf,"__esModule",{value:!0});var fu=$n(),nD=We(),iD=V(),sD=sr(),aD=Re(),vf=class extends iD.AbstractParserWithWordBoundaryChecking{constructor(){super()}innerPattern(){return new RegExp(`(?:les?|la|l'|du|des?)\\s*(${fu.NUMBER_PATTERN})?(?:\\s*(prochaine?s?|derni[e\xE8]re?s?|pass[\xE9e]e?s?|pr[\xE9e]c[\xE9e]dents?|suivante?s?))?\\s*(${aD.matchAnyPattern(fu.TIME_UNIT_DICTIONARY)})(?:\\s*(prochaine?s?|derni[e\xE8]re?s?|pass[\xE9e]e?s?|pr[\xE9e]c[\xE9e]dents?|suivante?s?))?`,"i")}innerExtract(e,t){let n=t[1]?fu.parseNumberPattern(t[1]):1,i=fu.TIME_UNIT_DICTIONARY[t[3].toLowerCase()],s={};s[i]=n;let a=t[2]||t[4]||"";if(a=a.toLowerCase(),!!a)return(/derni[eè]re?s?/.test(a)||/pass[ée]e?s?/.test(a)||/pr[ée]c[ée]dents?/.test(a))&&(s=sD.reverseTimeUnits(s)),nD.ParsingComponents.createRelativeFromReference(e.reference,s)}};wf.default=vf});var cb=k(Je=>{"use strict";var ar=Je&&Je.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(Je,"__esModule",{value:!0});Je.createConfiguration=Je.createCasualConfiguration=Je.parseDate=Je.parse=Je.strict=Je.casual=void 0;var oD=dn(),ub=kr(),uD=ar(Ly()),lD=ar(Uy()),cD=ar(pi()),dD=ar(Wy()),fD=ar(qy()),pD=ar($y()),mD=ar(Hy()),hD=ar(Qy()),gD=ar(rb()),yD=ar(ib()),bD=ar(ab()),TD=ar(ob());Je.casual=new ub.Chrono(lb());Je.strict=new ub.Chrono(kf(!0));function _D(r,e,t){return Je.casual.parse(r,e,t)}Je.parse=_D;function vD(r,e,t){return Je.casual.parseDate(r,e,t)}Je.parseDate=vD;function lb(r=!0){let e=kf(!1,r);return e.parsers.unshift(new uD.default),e.parsers.unshift(new lD.default),e.parsers.unshift(new TD.default),e}Je.createCasualConfiguration=lb;function kf(r=!0,e=!0){return oD.includeCommonConfiguration({parsers:[new cD.default(e),new gD.default,new dD.default,new hD.default,new yD.default,new bD.default,new mD.default],refiners:[new fD.default,new pD.default]},r)}Je.createConfiguration=kf});var db=k(pu=>{"use strict";Object.defineProperty(pu,"__esModule",{value:!0});pu.toHankaku=void 0;function wD(r){return String(r).replace(/\u2019/g,"'").replace(/\u201D/g,'"').replace(/\u3000/g," ").replace(/\uFFE5/g,"\xA5").replace(/[\uFF01\uFF03-\uFF06\uFF08\uFF09\uFF0C-\uFF19\uFF1C-\uFF1F\uFF21-\uFF3B\uFF3D\uFF3F\uFF41-\uFF5B\uFF5D\uFF5E]/g,kD)}pu.toHankaku=wD;function kD(r){return String.fromCharCode(r.charCodeAt(0)-65248)}});var pb=k(Oa=>{"use strict";var ED=Oa&&Oa.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(Oa,"__esModule",{value:!0});var Ef=db(),SD=ot(),OD=ED(we()),DD=/(?:(?:([同今本])|((昭和|平成|令和)?([0-9０-９]{1,4}|元)))年\s*)?([0-9０-９]{1,2})月\s*([0-9０-９]{1,2})日/i,fb=1,xD=2,Sf=3,RD=4,MD=5,CD=6,Of=class{pattern(){return DD}extract(e,t){let n=parseInt(Ef.toHankaku(t[MD])),i=parseInt(Ef.toHankaku(t[CD])),s=e.createParsingComponents({day:i,month:n});if(t[fb]&&t[fb].match("\u540C|\u4ECA|\u672C")){let a=OD.default(e.refDate);s.assign("year",a.year())}if(t[xD]){let a=t[RD],o=a=="\u5143"?1:parseInt(Ef.toHankaku(a));t[Sf]=="\u4EE4\u548C"?o+=2018:t[Sf]=="\u5E73\u6210"?o+=1988:t[Sf]=="\u662D\u548C"&&(o+=1925),s.assign("year",o)}else{let a=SD.findYearClosestToRef(e.refDate,i,n);s.imply("year",a)}return s}};Oa.default=Of});var mb=k(Da=>{"use strict";var AD=Da&&Da.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(Da,"__esModule",{value:!0});var PD=AD(Gr()),Df=class extends PD.default{patternBetween(){return/^\s*(から|ー|-)\s*$/i}};Da.default=Df});var gb=k(Dr=>{"use strict";var ND=Dr&&Dr.__createBinding||(Object.create?function(r,e,t,n){n===void 0&&(n=t),Object.defineProperty(r,n,{enumerable:!0,get:function(){return e[t]}})}:function(r,e,t,n){n===void 0&&(n=t),r[n]=e[t]}),ID=Dr&&Dr.__setModuleDefault||(Object.create?function(r,e){Object.defineProperty(r,"default",{enumerable:!0,value:e})}:function(r,e){r.default=e}),FD=Dr&&Dr.__importStar||function(r){if(r&&r.__esModule)return r;var e={};if(r!=null)for(var t in r)t!=="default"&&Object.prototype.hasOwnProperty.call(r,t)&&ND(e,r,t);return ID(e,r),e},LD=Dr&&Dr.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(Dr,"__esModule",{value:!0});var UD=LD(we()),hb=ze(),xf=FD(pn()),WD=/今日|当日|昨日|明日|今夜|今夕|今晩|今朝/i,Rf=class{pattern(){return WD}extract(e,t){let n=t[0],i=UD.default(e.refDate),s=e.createParsingComponents();switch(n){case"\u6628\u65E5":return xf.yesterday(e.reference);case"\u660E\u65E5":return xf.tomorrow(e.reference);case"\u4ECA\u65E5":case"\u5F53\u65E5":return xf.today(e.reference)}return n=="\u4ECA\u591C"||n=="\u4ECA\u5915"||n=="\u4ECA\u6669"?(s.imply("hour",22),s.assign("meridiem",hb.Meridiem.PM)):n.match("\u4ECA\u671D")&&(s.imply("hour",6),s.assign("meridiem",hb.Meridiem.AM)),s.assign("day",i.date()),s.assign("month",i.month()+1),s.assign("year",i.year()),s}};Dr.default=Rf});var Tb=k(et=>{"use strict";var Mf=et&&et.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(et,"__esModule",{value:!0});et.createConfiguration=et.createCasualConfiguration=et.parseDate=et.parse=et.strict=et.casual=void 0;var qD=Mf(pb()),$D=Mf(mb()),jD=Mf(gb()),yb=kr();et.casual=new yb.Chrono(bb());et.strict=new yb.Chrono(Cf());function GD(r,e,t){return et.casual.parse(r,e,t)}et.parse=GD;function YD(r,e,t){return et.casual.parseDate(r,e,t)}et.parseDate=YD;function bb(){let r=Cf();return r.parsers.unshift(new jD.default),r}et.createCasualConfiguration=bb;function Cf(){return{parsers:[new qD.default],refiners:[new $D.default]}}et.createConfiguration=Cf});var mu=k(Br=>{"use strict";Object.defineProperty(Br,"__esModule",{value:!0});Br.parseYear=Br.YEAR_PATTERN=Br.MONTH_DICTIONARY=Br.WEEKDAY_DICTIONARY=void 0;Br.WEEKDAY_DICTIONARY={domingo:0,dom:0,segunda:1,"segunda-feira":1,seg:1,ter\u00E7a:2,"ter\xE7a-feira":2,ter:2,quarta:3,"quarta-feira":3,qua:3,quinta:4,"quinta-feira":4,qui:4,sexta:5,"sexta-feira":5,sex:5,s\u00E1bado:6,sabado:6,sab:6};Br.MONTH_DICTIONARY={janeiro:1,jan:1,"jan.":1,fevereiro:2,fev:2,"fev.":2,mar\u00E7o:3,mar:3,"mar.":3,abril:4,abr:4,"abr.":4,maio:5,mai:5,"mai.":5,junho:6,jun:6,"jun.":6,julho:7,jul:7,"jul.":7,agosto:8,ago:8,"ago.":8,setembro:9,set:9,"set.":9,outubro:10,out:10,"out.":10,novembro:11,nov:11,"nov.":11,dezembro:12,dez:12,"dez.":12};Br.YEAR_PATTERN="[0-9]{1,4}(?![^\\s]\\d)(?:\\s*[a|d]\\.?\\s*c\\.?|\\s*a\\.?\\s*d\\.?)?";function BD(r){if(r.match(/^[0-9]{1,4}$/)){let e=parseInt(r);return e<100&&(e>50?e=e+1900:e=e+2e3),e}return r.match(/a\.?\s*c\.?/i)?(r=r.replace(/a\.?\s*c\.?/i,""),-parseInt(r)):parseInt(r)}Br.parseYear=BD});var vb=k(Pf=>{"use strict";Object.defineProperty(Pf,"__esModule",{value:!0});var _b=mu(),HD=Re(),VD=V(),zD=fi(),KD=new RegExp(`(?:(?:\\,|\\(|\\\uFF08)\\s*)?(?:(este|esta|passado|pr[o\xF3]ximo)\\s*)?(${HD.matchAnyPattern(_b.WEEKDAY_DICTIONARY)})(?:\\s*(?:\\,|\\)|\\\uFF09))?(?:\\s*(este|esta|passado|pr[\xF3o]ximo)\\s*semana)?(?=\\W|\\d|$)`,"i"),QD=1,XD=2,ZD=3,Af=class extends VD.AbstractParserWithWordBoundaryChecking{innerPattern(){return KD}innerExtract(e,t){let n=t[XD].toLowerCase(),i=_b.WEEKDAY_DICTIONARY[n];if(i===void 0)return null;let s=t[QD],a=t[ZD],o=s||a||"";o=o.toLowerCase();let u=null;o=="passado"?u="this":o=="pr\xF3ximo"||o=="proximo"?u="next":o=="este"&&(u="this");let l=zD.toDayJSWeekday(e.refDate,i,u);return e.createParsingComponents().assign("weekday",i).imply("day",l.date()).imply("month",l.month()+1).imply("year",l.year())}};Pf.default=Af});var wb=k(If=>{"use strict";Object.defineProperty(If,"__esModule",{value:!0});var JD=ci(),Nf=class extends JD.AbstractTimeExpressionParser{primaryPrefix(){return"(?:(?:ao?|\xE0s?|das|da|de|do)\\s*)?"}followingPhase(){return"\\s*(?:\\-|\\\u2013|\\~|\\\u301C|a(?:o)?|\\?)\\s*"}};If.default=Nf});var kb=k(xa=>{"use strict";var ex=xa&&xa.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(xa,"__esModule",{value:!0});var tx=ex(cn()),Ff=class extends tx.default{patternBetween(){return new RegExp("^\\s*(?:,|\xE0)?\\s*$")}};xa.default=Ff});var Eb=k(Ra=>{"use strict";var rx=Ra&&Ra.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(Ra,"__esModule",{value:!0});var nx=rx(Gr()),Lf=class extends nx.default{patternBetween(){return/^\s*(?:-)\s*$/i}};Ra.default=Lf});var Mb=k(Wf=>{"use strict";Object.defineProperty(Wf,"__esModule",{value:!0});var ix=ot(),xb=mu(),Rb=mu(),sx=Re(),ax=V(),ox=new RegExp(`([0-9]{1,2})(?:\xBA|\xAA|\xB0)?(?:\\s*(?:desde|de|\\-|\\\u2013|ao?|\\s)\\s*([0-9]{1,2})(?:\xBA|\xAA|\xB0)?)?\\s*(?:de)?\\s*(?:-|/|\\s*(?:de|,)?\\s*)(${sx.matchAnyPattern(xb.MONTH_DICTIONARY)})(?:\\s*(?:de|,)?\\s*(${Rb.YEAR_PATTERN}))?(?=\\W|$)`,"i"),Sb=1,Ob=2,ux=3,Db=4,Uf=class extends ax.AbstractParserWithWordBoundaryChecking{innerPattern(){return ox}innerExtract(e,t){let n=e.createParsingResult(t.index,t[0]),i=xb.MONTH_DICTIONARY[t[ux].toLowerCase()],s=parseInt(t[Sb]);if(s>31)return t.index=t.index+t[Sb].length,null;if(n.start.assign("month",i),n.start.assign("day",s),t[Db]){let a=Rb.parseYear(t[Db]);n.start.assign("year",a)}else{let a=ix.findYearClosestToRef(e.refDate,s,i);n.start.imply("year",a)}if(t[Ob]){let a=parseInt(t[Ob]);n.end=n.start.clone(),n.end.assign("day",a)}return n}};Wf.default=Uf});var Cb=k(mn=>{"use strict";var lx=mn&&mn.__createBinding||(Object.create?function(r,e,t,n){n===void 0&&(n=t),Object.defineProperty(r,n,{enumerable:!0,get:function(){return e[t]}})}:function(r,e,t,n){n===void 0&&(n=t),r[n]=e[t]}),cx=mn&&mn.__setModuleDefault||(Object.create?function(r,e){Object.defineProperty(r,"default",{enumerable:!0,value:e})}:function(r,e){r.default=e}),dx=mn&&mn.__importStar||function(r){if(r&&r.__esModule)return r;var e={};if(r!=null)for(var t in r)t!=="default"&&Object.prototype.hasOwnProperty.call(r,t)&&lx(e,r,t);return cx(e,r),e};Object.defineProperty(mn,"__esModule",{value:!0});var fx=V(),hu=dx(pn()),qf=class extends fx.AbstractParserWithWordBoundaryChecking{innerPattern(e){return/(agora|hoje|amanha|amanhã|ontem)(?=\W|$)/i}innerExtract(e,t){let n=t[0].toLowerCase(),i=e.createParsingComponents();switch(n){case"agora":return hu.now(e.reference);case"hoje":return hu.today(e.reference);case"amanha":case"amanh\xE3":return hu.tomorrow(e.reference);case"ontem":return hu.yesterday(e.reference)}return i}};mn.default=qf});var Ab=k(Ma=>{"use strict";var px=Ma&&Ma.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(Ma,"__esModule",{value:!0});var gu=ze(),mx=V(),hx=ir(),gx=px(we()),$f=class extends mx.AbstractParserWithWordBoundaryChecking{innerPattern(){return/(?:esta\s*)?(manha|manhã|tarde|meia-noite|meio-dia|noite)(?=\W|$)/i}innerExtract(e,t){let n=gx.default(e.refDate),i=e.createParsingComponents();switch(t[1].toLowerCase()){case"tarde":i.imply("meridiem",gu.Meridiem.PM),i.imply("hour",15);break;case"noite":i.imply("meridiem",gu.Meridiem.PM),i.imply("hour",22);break;case"manha":case"manh\xE3":i.imply("meridiem",gu.Meridiem.AM),i.imply("hour",6);break;case"meia-noite":hx.assignTheNextDay(i,n),i.imply("hour",0),i.imply("minute",0),i.imply("second",0);break;case"meio-dia":i.imply("meridiem",gu.Meridiem.AM),i.imply("hour",12);break}return i}};Ma.default=$f});var Ib=k(tt=>{"use strict";var jn=tt&&tt.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(tt,"__esModule",{value:!0});tt.createConfiguration=tt.createCasualConfiguration=tt.parseDate=tt.parse=tt.strict=tt.casual=void 0;var yx=dn(),Pb=kr(),bx=jn(pi()),Tx=jn(vb()),_x=jn(wb()),vx=jn(kb()),wx=jn(Eb()),kx=jn(Mb()),Ex=jn(Cb()),Sx=jn(Ab());tt.casual=new Pb.Chrono(Nb());tt.strict=new Pb.Chrono(jf(!0));function Ox(r,e,t){return tt.casual.parse(r,e,t)}tt.parse=Ox;function Dx(r,e,t){return tt.casual.parseDate(r,e,t)}tt.parseDate=Dx;function Nb(r=!0){let e=jf(!1,r);return e.parsers.push(new Ex.default),e.parsers.push(new Sx.default),e}tt.createCasualConfiguration=Nb;function jf(r=!0,e=!0){return yx.includeCommonConfiguration({parsers:[new bx.default(e),new Tx.default,new _x.default,new kx.default],refiners:[new vx.default,new wx.default]},r)}tt.createConfiguration=jf});var Fb=k(Ca=>{"use strict";var xx=Ca&&Ca.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(Ca,"__esModule",{value:!0});var Rx=xx(Gr()),Gf=class extends Rx.default{patternBetween(){return/^\s*(tot|-)\s*$/i}};Ca.default=Gf});var Lb=k(Aa=>{"use strict";var Mx=Aa&&Aa.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(Aa,"__esModule",{value:!0});var Cx=Mx(cn()),Yf=class extends Cx.default{patternBetween(){return new RegExp("^\\s*(om|na|voor|in de|,|-)?\\s*$")}};Aa.default=Yf});var Ub=k(hn=>{"use strict";var Ax=hn&&hn.__createBinding||(Object.create?function(r,e,t,n){n===void 0&&(n=t),Object.defineProperty(r,n,{enumerable:!0,get:function(){return e[t]}})}:function(r,e,t,n){n===void 0&&(n=t),r[n]=e[t]}),Px=hn&&hn.__setModuleDefault||(Object.create?function(r,e){Object.defineProperty(r,"default",{enumerable:!0,value:e})}:function(r,e){r.default=e}),Nx=hn&&hn.__importStar||function(r){if(r&&r.__esModule)return r;var e={};if(r!=null)for(var t in r)t!=="default"&&Object.prototype.hasOwnProperty.call(r,t)&&Ax(e,r,t);return Px(e,r),e};Object.defineProperty(hn,"__esModule",{value:!0});var Ix=V(),yu=Nx(pn()),Bf=class extends Ix.AbstractParserWithWordBoundaryChecking{innerPattern(e){return/(nu|vandaag|morgen|morgend|gisteren)(?=\W|$)/i}innerExtract(e,t){let n=t[0].toLowerCase(),i=e.createParsingComponents();switch(n){case"nu":return yu.now(e.reference);case"vandaag":return yu.today(e.reference);case"morgen":case"morgend":return yu.tomorrow(e.reference);case"gisteren":return yu.yesterday(e.reference)}return i}};hn.default=Bf});var Wb=k(Pa=>{"use strict";var Fx=Pa&&Pa.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(Pa,"__esModule",{value:!0});var bu=ze(),Lx=V(),Ux=Fx(we()),Wx=ir(),qx=1,$x=2,Hf=class extends Lx.AbstractParserWithWordBoundaryChecking{innerPattern(){return/(deze)?\s*(namiddag|avond|middernacht|ochtend|middag|'s middags|'s avonds|'s ochtends)(?=\W|$)/i}innerExtract(e,t){let n=Ux.default(e.refDate),i=e.createParsingComponents();switch(t[qx]==="deze"&&(i.assign("day",e.refDate.getDate()),i.assign("month",e.refDate.getMonth()+1),i.assign("year",e.refDate.getFullYear())),t[$x].toLowerCase()){case"namiddag":case"'s namiddags":i.imply("meridiem",bu.Meridiem.PM),i.imply("hour",15);break;case"avond":case"'s avonds'":i.imply("meridiem",bu.Meridiem.PM),i.imply("hour",20);break;case"middernacht":Wx.assignTheNextDay(i,n),i.imply("hour",0),i.imply("minute",0),i.imply("second",0);break;case"ochtend":case"'s ochtends":i.imply("meridiem",bu.Meridiem.AM),i.imply("hour",6);break;case"middag":case"'s middags":i.imply("meridiem",bu.Meridiem.AM),i.imply("hour",12);break}return i}};Pa.default=Hf});var Gt=k(ge=>{"use strict";Object.defineProperty(ge,"__esModule",{value:!0});ge.parseTimeUnits=ge.TIME_UNITS_PATTERN=ge.parseYear=ge.YEAR_PATTERN=ge.parseOrdinalNumberPattern=ge.ORDINAL_NUMBER_PATTERN=ge.parseNumberPattern=ge.NUMBER_PATTERN=ge.TIME_UNIT_DICTIONARY=ge.ORDINAL_WORD_DICTIONARY=ge.INTEGER_WORD_DICTIONARY=ge.MONTH_DICTIONARY=ge.WEEKDAY_DICTIONARY=void 0;var Tu=Re(),jx=ot();ge.WEEKDAY_DICTIONARY={zondag:0,zon:0,"zon.":0,zo:0,"zo.":0,maandag:1,ma:1,"ma.":1,dinsdag:2,din:2,"din.":2,di:2,"di.":2,woensdag:3,woe:3,"woe.":3,wo:3,"wo.":3,donderdag:4,dond:4,"dond.":4,do:4,"do.":4,vrijdag:5,vrij:5,"vrij.":5,vr:5,"vr.":5,zaterdag:6,zat:6,"zat.":6,za:6,"za.":6};ge.MONTH_DICTIONARY={januari:1,jan:1,"jan.":1,februari:2,feb:2,"feb.":2,maart:3,mar:3,"mar.":3,april:4,apr:4,"apr.":4,mei:5,juni:6,jun:6,"jun.":6,juli:7,jul:7,"jul.":7,augustus:8,aug:8,"aug.":8,september:9,sep:9,"sep.":9,sept:9,"sept.":9,oktober:10,okt:10,"okt.":10,november:11,nov:11,"nov.":11,december:12,dec:12,"dec.":12};ge.INTEGER_WORD_DICTIONARY={een:1,twee:2,drie:3,vier:4,vijf:5,zes:6,zeven:7,acht:8,negen:9,tien:10,elf:11,twaalf:12};ge.ORDINAL_WORD_DICTIONARY={eerste:1,tweede:2,derde:3,vierde:4,vijfde:5,zesde:6,zevende:7,achtste:8,negende:9,tiende:10,elfde:11,twaalfde:12,dertiende:13,veertiende:14,vijftiende:15,zestiende:16,zeventiende:17,achttiende:18,negentiende:19,twintigste:20,eenentwintigste:21,twee\u00EBntwintigste:22,drieentwintigste:23,vierentwintigste:24,vijfentwintigste:25,zesentwintigste:26,zevenentwintigste:27,achtentwintig:28,negenentwintig:29,dertigste:30,eenendertigste:31};ge.TIME_UNIT_DICTIONARY={sec:"second",second:"second",seconden:"second",min:"minute",mins:"minute",minute:"minute",minuut:"minute",minuten:"minute",minuutje:"minute",h:"hour",hr:"hour",hrs:"hour",uur:"hour",u:"hour",uren:"hour",dag:"d",dagen:"d",week:"week",weken:"week",maand:"month",maanden:"month",jaar:"year",jr:"year",jaren:"year"};ge.NUMBER_PATTERN=`(?:${Tu.matchAnyPattern(ge.INTEGER_WORD_DICTIONARY)}|[0-9]+|[0-9]+[\\.,][0-9]+|halve?|half|paar)`;function $b(r){let e=r.toLowerCase();return ge.INTEGER_WORD_DICTIONARY[e]!==void 0?ge.INTEGER_WORD_DICTIONARY[e]:e==="paar"?2:e==="half"||e.match(/halve?/)?.5:parseFloat(e.replace(",","."))}ge.parseNumberPattern=$b;ge.ORDINAL_NUMBER_PATTERN=`(?:${Tu.matchAnyPattern(ge.ORDINAL_WORD_DICTIONARY)}|[0-9]{1,2}(?:ste|de)?)`;function Gx(r){let e=r.toLowerCase();return ge.ORDINAL_WORD_DICTIONARY[e]!==void 0?ge.ORDINAL_WORD_DICTIONARY[e]:(e=e.replace(/(?:ste|de)$/i,""),parseInt(e))}ge.parseOrdinalNumberPattern=Gx;ge.YEAR_PATTERN="(?:[1-9][0-9]{0,3}\\s*(?:voor Christus|na Christus)|[1-2][0-9]{3}|[5-9][0-9])";function Yx(r){if(/voor Christus/i.test(r))return r=r.replace(/voor Christus/i,""),-parseInt(r);if(/na Christus/i.test(r))return r=r.replace(/na Christus/i,""),parseInt(r);let e=parseInt(r);return jx.findMostLikelyADYear(e)}ge.parseYear=Yx;var jb=`(${ge.NUMBER_PATTERN})\\s{0,5}(${Tu.matchAnyPattern(ge.TIME_UNIT_DICTIONARY)})\\s{0,5}`,qb=new RegExp(jb,"i");ge.TIME_UNITS_PATTERN=Tu.repeatedTimeunitPattern("(?:(?:binnen|in)\\s*)?",jb);function Bx(r){let e={},t=r,n=qb.exec(t);for(;n;)Hx(e,n),t=t.substring(n[0].length),n=qb.exec(t);return e}ge.parseTimeUnits=Bx;function Hx(r,e){let t=$b(e[1]),n=ge.TIME_UNIT_DICTIONARY[e[2].toLowerCase()];r[n]=t}});var Yb=k(zf=>{"use strict";Object.defineProperty(zf,"__esModule",{value:!0});var Gb=Gt(),Vx=We(),zx=V(),Vf=class extends zx.AbstractParserWithWordBoundaryChecking{innerPattern(){return new RegExp("(?:binnen|in|binnen de|voor)\\s*("+Gb.TIME_UNITS_PATTERN+")(?=\\W|$)","i")}innerExtract(e,t){let n=Gb.parseTimeUnits(t[1]);return Vx.ParsingComponents.createRelativeFromReference(e.reference,n)}};zf.default=Vf});var Hb=k(Qf=>{"use strict";Object.defineProperty(Qf,"__esModule",{value:!0});var Bb=Gt(),Kx=Re(),Qx=V(),Xx=fi(),Zx=new RegExp(`(?:(?:\\,|\\(|\\\uFF08)\\s*)?(?:op\\s*?)?(?:(deze|vorige|volgende)\\s*(?:week\\s*)?)?(${Kx.matchAnyPattern(Bb.WEEKDAY_DICTIONARY)})(?=\\W|$)`,"i"),Jx=1,eR=2,tR=3,Kf=class extends Qx.AbstractParserWithWordBoundaryChecking{innerPattern(){return Zx}innerExtract(e,t){let n=t[eR].toLowerCase(),i=Bb.WEEKDAY_DICTIONARY[n],s=t[Jx],a=t[tR],o=s||a;o=o||"",o=o.toLowerCase();let u=null;o=="vorige"?u="last":o=="volgende"?u="next":o=="deze"&&(u="this");let l=Xx.toDayJSWeekday(e.refDate,i,u);return e.createParsingComponents().assign("weekday",i).imply("day",l.date()).imply("month",l.month()+1).imply("year",l.year())}};Qf.default=Kf});var Zb=k(Zf=>{"use strict";Object.defineProperty(Zf,"__esModule",{value:!0});var rR=ot(),Qb=Gt(),_u=Gt(),Xb=Gt(),nR=Re(),iR=V(),sR=new RegExp(`(?:on\\s*?)?(${_u.ORDINAL_NUMBER_PATTERN})(?:\\s*(?:tot|\\-|\\\u2013|until|through|till|\\s)\\s*(${_u.ORDINAL_NUMBER_PATTERN}))?(?:-|/|\\s*(?:of)?\\s*)(`+nR.matchAnyPattern(Qb.MONTH_DICTIONARY)+`)(?:(?:-|/|,?\\s*)(${Xb.YEAR_PATTERN}(?![^\\s]\\d)))?(?=\\W|$)`,"i"),aR=3,Vb=1,zb=2,Kb=4,Xf=class extends iR.AbstractParserWithWordBoundaryChecking{innerPattern(){return sR}innerExtract(e,t){let n=Qb.MONTH_DICTIONARY[t[aR].toLowerCase()],i=_u.parseOrdinalNumberPattern(t[Vb]);if(i>31)return t.index=t.index+t[Vb].length,null;let s=e.createParsingComponents({day:i,month:n});if(t[Kb]){let u=Xb.parseYear(t[Kb]);s.assign("year",u)}else{let u=rR.findYearClosestToRef(e.refDate,i,n);s.imply("year",u)}if(!t[zb])return s;let a=_u.parseOrdinalNumberPattern(t[zb]),o=e.createParsingResult(t.index,t[0]);return o.start=s,o.end=s.clone(),o.end.assign("day",a),o}};Zf.default=Xf});var rT=k(ep=>{"use strict";Object.defineProperty(ep,"__esModule",{value:!0});var eT=Gt(),oR=ot(),uR=Re(),tT=Gt(),lR=V(),cR=new RegExp(`(${uR.matchAnyPattern(eT.MONTH_DICTIONARY)})\\s*(?:[,-]?\\s*(${tT.YEAR_PATTERN})?)?(?=[^\\s\\w]|\\s+[^0-9]|\\s+$|$)`,"i"),dR=1,Jb=2,Jf=class extends lR.AbstractParserWithWordBoundaryChecking{innerPattern(){return cR}innerExtract(e,t){let n=e.createParsingComponents();n.imply("day",1);let i=t[dR],s=eT.MONTH_DICTIONARY[i.toLowerCase()];if(n.assign("month",s),t[Jb]){let a=tT.parseYear(t[Jb]);n.assign("year",a)}else{let a=oR.findYearClosestToRef(e.refDate,1,s);n.imply("year",a)}return n}};ep.default=Jf});var nT=k(rp=>{"use strict";Object.defineProperty(rp,"__esModule",{value:!0});var fR=V(),pR=new RegExp("([0-9]|0[1-9]|1[012])/([0-9]{4})","i"),mR=1,hR=2,tp=class extends fR.AbstractParserWithWordBoundaryChecking{innerPattern(){return pR}innerExtract(e,t){let n=parseInt(t[hR]),i=parseInt(t[mR]);return e.createParsingComponents().imply("day",1).assign("month",i).assign("year",n)}};rp.default=tp});var iT=k(ip=>{"use strict";Object.defineProperty(ip,"__esModule",{value:!0});var gR=ci(),np=class extends gR.AbstractTimeExpressionParser{primaryPrefix(){return"(?:(?:om)\\s*)?"}followingPhase(){return"\\s*(?:\\-|\\\u2013|\\~|\\\u301C|om|\\?)\\s*"}primarySuffix(){return"(?:\\s*(?:uur))?(?!/)(?=\\W|$)"}extractPrimaryTimeComponents(e,t){return t[0].match(/^\s*\d{4}\s*$/)?null:super.extractPrimaryTimeComponents(e,t)}};ip.default=np});var oT=k(ap=>{"use strict";Object.defineProperty(ap,"__esModule",{value:!0});var aT=Gt(),yR=Re(),bR=V(),TR=new RegExp(`([0-9]{4})[\\.\\/\\s](?:(${yR.matchAnyPattern(aT.MONTH_DICTIONARY)})|([0-9]{1,2}))[\\.\\/\\s]([0-9]{1,2})(?=\\W|$)`,"i"),_R=1,vR=2,sT=3,wR=4,sp=class extends bR.AbstractParserWithWordBoundaryChecking{innerPattern(){return TR}innerExtract(e,t){let n=t[sT]?parseInt(t[sT]):aT.MONTH_DICTIONARY[t[vR].toLowerCase()];if(n<1||n>12)return null;let i=parseInt(t[_R]);return{day:parseInt(t[wR]),month:n,year:i}}};ap.default=sp});var uT=k(Na=>{"use strict";var kR=Na&&Na.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(Na,"__esModule",{value:!0});var ER=V(),vu=ze(),op=ir(),SR=kR(we()),OR=1,DR=2,up=class extends ER.AbstractParserWithWordBoundaryChecking{innerPattern(e){return/(gisteren|morgen|van)(ochtend|middag|namiddag|avond|nacht)(?=\W|$)/i}innerExtract(e,t){let n=t[OR].toLowerCase(),i=t[DR].toLowerCase(),s=e.createParsingComponents(),a=SR.default(e.refDate);switch(n){case"gisteren":op.assignSimilarDate(s,a.add(-1,"day"));break;case"van":op.assignSimilarDate(s,a);break;case"morgen":op.assignTheNextDay(s,a);break}switch(i){case"ochtend":s.imply("meridiem",vu.Meridiem.AM),s.imply("hour",6);break;case"middag":s.imply("meridiem",vu.Meridiem.AM),s.imply("hour",12);break;case"namiddag":s.imply("meridiem",vu.Meridiem.PM),s.imply("hour",15);break;case"avond":s.imply("meridiem",vu.Meridiem.PM),s.imply("hour",20);break}return s}};Na.default=up});var cT=k(cp=>{"use strict";Object.defineProperty(cp,"__esModule",{value:!0});var lT=Gt(),xR=We(),RR=V(),MR=sr(),CR=new RegExp(`(deze|vorige|afgelopen|komende|over|\\+|-)\\s*(${lT.TIME_UNITS_PATTERN})(?=\\W|$)`,"i"),lp=class extends RR.AbstractParserWithWordBoundaryChecking{innerPattern(){return CR}innerExtract(e,t){let n=t[1].toLowerCase(),i=lT.parseTimeUnits(t[2]);switch(n){case"vorige":case"afgelopen":case"-":i=MR.reverseTimeUnits(i);break}return xR.ParsingComponents.createRelativeFromReference(e.reference,i)}};cp.default=lp});var pT=k(Ia=>{"use strict";var AR=Ia&&Ia.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(Ia,"__esModule",{value:!0});var fT=Gt(),dT=We(),PR=AR(we()),NR=V(),IR=Re(),FR=new RegExp(`(dit|deze|komende|volgend|volgende|afgelopen|vorige)\\s*(${IR.matchAnyPattern(fT.TIME_UNIT_DICTIONARY)})(?=\\s*)(?=\\W|$)`,"i"),LR=1,UR=2,dp=class extends NR.AbstractParserWithWordBoundaryChecking{innerPattern(){return FR}innerExtract(e,t){let n=t[LR].toLowerCase(),i=t[UR].toLowerCase(),s=fT.TIME_UNIT_DICTIONARY[i];if(n=="volgend"||n=="volgende"||n=="komende"){let u={};return u[s]=1,dT.ParsingComponents.createRelativeFromReference(e.reference,u)}if(n=="afgelopen"||n=="vorige"){let u={};return u[s]=-1,dT.ParsingComponents.createRelativeFromReference(e.reference,u)}let a=e.createParsingComponents(),o=PR.default(e.reference.instant);return i.match(/week/i)?(o=o.add(-o.get("d"),"d"),a.imply("day",o.date()),a.imply("month",o.month()+1),a.imply("year",o.year())):i.match(/maand/i)?(o=o.add(-o.date()+1,"d"),a.imply("day",o.date()),a.assign("year",o.year()),a.assign("month",o.month()+1)):i.match(/jaar/i)&&(o=o.add(-o.date()+1,"d"),o=o.add(-o.month(),"month"),a.imply("day",o.date()),a.imply("month",o.month()+1),a.assign("year",o.year())),a}};Ia.default=dp});var mT=k(mp=>{"use strict";Object.defineProperty(mp,"__esModule",{value:!0});var pp=Gt(),WR=We(),qR=V(),$R=sr(),jR=new RegExp("("+pp.TIME_UNITS_PATTERN+")(?:geleden|voor|eerder)(?=(?:\\W|$))","i"),GR=new RegExp("("+pp.TIME_UNITS_PATTERN+")geleden(?=(?:\\W|$))","i"),fp=class extends qR.AbstractParserWithWordBoundaryChecking{constructor(e){super(),this.strictMode=e}innerPattern(){return this.strictMode?GR:jR}innerExtract(e,t){let n=pp.parseTimeUnits(t[1]),i=$R.reverseTimeUnits(n);return WR.ParsingComponents.createRelativeFromReference(e.reference,i)}};mp.default=fp});var hT=k(yp=>{"use strict";Object.defineProperty(yp,"__esModule",{value:!0});var gp=Gt(),YR=We(),BR=V(),HR=new RegExp("("+gp.TIME_UNITS_PATTERN+")(later|na|vanaf nu|voortaan|vooruit|uit)(?=(?:\\W|$))","i"),VR=new RegExp("("+gp.TIME_UNITS_PATTERN+")(later|vanaf nu)(?=(?:\\W|$))","i"),zR=1,hp=class extends BR.AbstractParserWithWordBoundaryChecking{constructor(e){super(),this.strictMode=e}innerPattern(){return this.strictMode?VR:HR}innerExtract(e,t){let n=gp.parseTimeUnits(t[zR]);return YR.ParsingComponents.createRelativeFromReference(e.reference,n)}};yp.default=hp});var TT=k(rt=>{"use strict";var lt=rt&&rt.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(rt,"__esModule",{value:!0});rt.createConfiguration=rt.createCasualConfiguration=rt.parseDate=rt.parse=rt.strict=rt.casual=void 0;var KR=dn(),gT=kr(),QR=lt(Fb()),XR=lt(Lb()),ZR=lt(Ub()),JR=lt(Wb()),eM=lt(pi()),tM=lt(Yb()),rM=lt(Hb()),nM=lt(Zb()),yT=lt(rT()),iM=lt(nT()),sM=lt(iT()),aM=lt(oT()),oM=lt(uT()),uM=lt(cT()),lM=lt(pT()),cM=lt(mT()),dM=lt(hT());rt.casual=new gT.Chrono(bT());rt.strict=new gT.Chrono(bp(!0));function fM(r,e,t){return rt.casual.parse(r,e,t)}rt.parse=fM;function pM(r,e,t){return rt.casual.parseDate(r,e,t)}rt.parseDate=pM;function bT(r=!0){let e=bp(!1,r);return e.parsers.unshift(new ZR.default),e.parsers.unshift(new JR.default),e.parsers.unshift(new oM.default),e.parsers.unshift(new yT.default),e.parsers.unshift(new lM.default),e.parsers.unshift(new uM.default),e}rt.createCasualConfiguration=bT;function bp(r=!0,e=!0){return KR.includeCommonConfiguration({parsers:[new eM.default(e),new tM.default,new nM.default,new yT.default,new rM.default,new aM.default,new iM.default,new sM.default(r),new cM.default(r),new dM.default(r)],refiners:[new XR.default,new QR.default]},r)}rt.createConfiguration=bp});var kT=k(Fa=>{"use strict";var mM=Fa&&Fa.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(Fa,"__esModule",{value:!0});var hM=mM(we()),gM=V(),yM=1,_T=2,bM=3,vT=4,wT=5,TM=6,Tp=class extends gM.AbstractParserWithWordBoundaryChecking{innerPattern(e){return new RegExp("(\u800C\u5BB6|\u7ACB(?:\u523B|\u5373)|\u5373\u523B)|(\u4ECA|\u660E|\u524D|\u5927\u524D|\u5F8C|\u5927\u5F8C|\u807D|\u6628|\u5C0B|\u7434)(\u65E9|\u671D|\u665A)|(\u4E0A(?:\u5348|\u665D)|\u671D(?:\u65E9)|\u65E9(?:\u4E0A)|\u4E0B(?:\u5348|\u665D)|\u664F(?:\u665D)|\u665A(?:\u4E0A)|\u591C(?:\u665A)?|\u4E2D(?:\u5348)|\u51CC(?:\u6668))|(\u4ECA|\u660E|\u524D|\u5927\u524D|\u5F8C|\u5927\u5F8C|\u807D|\u6628|\u5C0B|\u7434)(?:\u65E5|\u5929)(?:[\\s|,|\uFF0C]*)(?:(\u4E0A(?:\u5348|\u665D)|\u671D(?:\u65E9)|\u65E9(?:\u4E0A)|\u4E0B(?:\u5348|\u665D)|\u664F(?:\u665D)|\u665A(?:\u4E0A)|\u591C(?:\u665A)?|\u4E2D(?:\u5348)|\u51CC(?:\u6668)))?","i")}innerExtract(e,t){let n=t.index,i=e.createParsingResult(n,t[0]),s=hM.default(e.refDate),a=s;if(t[yM])i.start.imply("hour",s.hour()),i.start.imply("minute",s.minute()),i.start.imply("second",s.second()),i.start.imply("millisecond",s.millisecond());else if(t[_T]){let o=t[_T],u=t[bM];o=="\u660E"||o=="\u807D"?s.hour()>1&&(a=a.add(1,"day")):o=="\u6628"||o=="\u5C0B"||o=="\u7434"?a=a.add(-1,"day"):o=="\u524D"?a=a.add(-2,"day"):o=="\u5927\u524D"?a=a.add(-3,"day"):o=="\u5F8C"?a=a.add(2,"day"):o=="\u5927\u5F8C"&&(a=a.add(3,"day")),u=="\u65E9"||u=="\u671D"?i.start.imply("hour",6):u=="\u665A"&&(i.start.imply("hour",22),i.start.imply("meridiem",1))}else if(t[vT]){let u=t[vT][0];u=="\u65E9"||u=="\u671D"||u=="\u4E0A"?i.start.imply("hour",6):u=="\u4E0B"||u=="\u664F"?(i.start.imply("hour",15),i.start.imply("meridiem",1)):u=="\u4E2D"?(i.start.imply("hour",12),i.start.imply("meridiem",1)):u=="\u591C"||u=="\u665A"?(i.start.imply("hour",22),i.start.imply("meridiem",1)):u=="\u51CC"&&i.start.imply("hour",0)}else if(t[wT]){let o=t[wT];o=="\u660E"||o=="\u807D"?s.hour()>1&&(a=a.add(1,"day")):o=="\u6628"||o=="\u5C0B"||o=="\u7434"?a=a.add(-1,"day"):o=="\u524D"?a=a.add(-2,"day"):o=="\u5927\u524D"?a=a.add(-3,"day"):o=="\u5F8C"?a=a.add(2,"day"):o=="\u5927\u5F8C"&&(a=a.add(3,"day"));let u=t[TM];if(u){let l=u[0];l=="\u65E9"||l=="\u671D"||l=="\u4E0A"?i.start.imply("hour",6):l=="\u4E0B"||l=="\u664F"?(i.start.imply("hour",15),i.start.imply("meridiem",1)):l=="\u4E2D"?(i.start.imply("hour",12),i.start.imply("meridiem",1)):l=="\u591C"||l=="\u665A"?(i.start.imply("hour",22),i.start.imply("meridiem",1)):l=="\u51CC"&&i.start.imply("hour",0)}}return i.start.assign("day",a.date()),i.start.assign("month",a.month()+1),i.start.assign("year",a.year()),i}};Fa.default=Tp});var as=k(Ct=>{"use strict";Object.defineProperty(Ct,"__esModule",{value:!0});Ct.zhStringToYear=Ct.zhStringToNumber=Ct.WEEKDAY_OFFSET=Ct.NUMBER=void 0;Ct.NUMBER={\u96F6:0,\u4E00:1,\u4E8C:2,\u5169:2,\u4E09:3,\u56DB:4,\u4E94:5,\u516D:6,\u4E03:7,\u516B:8,\u4E5D:9,\u5341:10,\u5EFF:20,\u5345:30};Ct.WEEKDAY_OFFSET={\u5929:0,\u65E5:0,\u4E00:1,\u4E8C:2,\u4E09:3,\u56DB:4,\u4E94:5,\u516D:6};function _M(r){let e=0;for(let t=0;t<r.length;t++){let n=r[t];n==="\u5341"?e=e===0?Ct.NUMBER[n]:e*Ct.NUMBER[n]:e+=Ct.NUMBER[n]}return e}Ct.zhStringToNumber=_M;function vM(r){let e="";for(let t=0;t<r.length;t++){let n=r[t];e=e+Ct.NUMBER[n]}return parseInt(e)}Ct.zhStringToYear=vM});var ST=k(La=>{"use strict";var wM=La&&La.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(La,"__esModule",{value:!0});var kM=wM(we()),EM=V(),gi=as(),_p=1,ET=2,vp=3,wp=class extends EM.AbstractParserWithWordBoundaryChecking{innerPattern(){return new RegExp("(\\d{2,4}|["+Object.keys(gi.NUMBER).join("")+"]{4}|["+Object.keys(gi.NUMBER).join("")+"]{2})?(?:\\s*)(?:\u5E74)?(?:[\\s|,|\uFF0C]*)(\\d{1,2}|["+Object.keys(gi.NUMBER).join("")+"]{1,2})(?:\\s*)(?:\u6708)(?:\\s*)(\\d{1,2}|["+Object.keys(gi.NUMBER).join("")+"]{1,2})?(?:\\s*)(?:\u65E5|\u865F)?")}innerExtract(e,t){let n=kM.default(e.refDate),i=e.createParsingResult(t.index,t[0]),s=parseInt(t[ET]);if(isNaN(s)&&(s=gi.zhStringToNumber(t[ET])),i.start.assign("month",s),t[vp]){let a=parseInt(t[vp]);isNaN(a)&&(a=gi.zhStringToNumber(t[vp])),i.start.assign("day",a)}else i.start.imply("day",n.date());if(t[_p]){let a=parseInt(t[_p]);isNaN(a)&&(a=gi.zhStringToYear(t[_p])),i.start.assign("year",a)}else i.start.imply("year",n.year());return i}};La.default=wp});var DT=k(Ua=>{"use strict";var SM=Ua&&Ua.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(Ua,"__esModule",{value:!0});var OM=SM(we()),DM=V(),OT=as(),xM=new RegExp("(\\d+|["+Object.keys(OT.NUMBER).join("")+"]+|\u534A|\u5E7E)(?:\\s*)(?:\u500B)?(\u79D2(?:\u9418)?|\u5206\u9418|\u5C0F\u6642|\u9418|\u65E5|\u5929|\u661F\u671F|\u79AE\u62DC|\u6708|\u5E74)(?:(?:\u4E4B|\u904E)?\u5F8C|(?:\u4E4B)?\u5167)","i"),kp=1,RM=2,Ep=class extends DM.AbstractParserWithWordBoundaryChecking{innerPattern(){return xM}innerExtract(e,t){let n=e.createParsingResult(t.index,t[0]),i=parseInt(t[kp]);if(isNaN(i)&&(i=OT.zhStringToNumber(t[kp])),isNaN(i)){let u=t[kp];if(u==="\u5E7E")i=3;else if(u==="\u534A")i=.5;else return null}let s=OM.default(e.refDate),o=t[RM][0];return o.match(/[日天星禮月年]/)?(o=="\u65E5"||o=="\u5929"?s=s.add(i,"d"):o=="\u661F"||o=="\u79AE"?s=s.add(i*7,"d"):o=="\u6708"?s=s.add(i,"month"):o=="\u5E74"&&(s=s.add(i,"year")),n.start.assign("year",s.year()),n.start.assign("month",s.month()+1),n.start.assign("day",s.date()),n):(o=="\u79D2"?s=s.add(i,"second"):o=="\u5206"?s=s.add(i,"minute"):(o=="\u5C0F"||o=="\u9418")&&(s=s.add(i,"hour")),n.start.imply("year",s.year()),n.start.imply("month",s.month()+1),n.start.imply("day",s.date()),n.start.assign("hour",s.hour()),n.start.assign("minute",s.minute()),n.start.assign("second",s.second()),n)}};Ua.default=Ep});var RT=k(Wa=>{"use strict";var MM=Wa&&Wa.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(Wa,"__esModule",{value:!0});var CM=MM(we()),AM=V(),xT=as(),PM=new RegExp("(?<prefix>\u4E0A|\u4ECA|\u4E0B|\u9019|\u5462)(?:\u500B)?(?:\u661F\u671F|\u79AE\u62DC|\u9031)(?<weekday>"+Object.keys(xT.WEEKDAY_OFFSET).join("|")+")"),Sp=class extends AM.AbstractParserWithWordBoundaryChecking{innerPattern(){return PM}innerExtract(e,t){let n=e.createParsingResult(t.index,t[0]),i=t.groups.weekday,s=xT.WEEKDAY_OFFSET[i];if(s===void 0)return null;let a=null,o=t.groups.prefix;o=="\u4E0A"?a="last":o=="\u4E0B"?a="next":(o=="\u4ECA"||o=="\u9019"||o=="\u5462")&&(a="this");let u=CM.default(e.refDate),l=!1,c=u.day();return a=="last"||a=="past"?(u=u.day(s-7),l=!0):a=="next"?(u=u.day(s+7),l=!0):a=="this"?u=u.day(s):Math.abs(s-7-c)<Math.abs(s-c)?u=u.day(s-7):Math.abs(s+7-c)<Math.abs(s-c)?u=u.day(s+7):u=u.day(s),n.start.assign("weekday",s),l?(n.start.assign("day",u.date()),n.start.assign("month",u.month()+1),n.start.assign("year",u.year())):(n.start.imply("day",u.date()),n.start.imply("month",u.month()+1),n.start.imply("year",u.year())),n}};Wa.default=Sp});var MT=k(qa=>{"use strict";var NM=qa&&qa.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(qa,"__esModule",{value:!0});var IM=NM(we()),FM=V(),ur=as(),LM=new RegExp("(?:\u7531|\u5F9E|\u81EA)?(?:(\u4ECA|\u660E|\u524D|\u5927\u524D|\u5F8C|\u5927\u5F8C|\u807D|\u6628|\u5C0B|\u7434)(\u65E9|\u671D|\u665A)|(\u4E0A(?:\u5348|\u665D)|\u671D(?:\u65E9)|\u65E9(?:\u4E0A)|\u4E0B(?:\u5348|\u665D)|\u664F(?:\u665D)|\u665A(?:\u4E0A)|\u591C(?:\u665A)?|\u4E2D(?:\u5348)|\u51CC(?:\u6668))|(\u4ECA|\u660E|\u524D|\u5927\u524D|\u5F8C|\u5927\u5F8C|\u807D|\u6628|\u5C0B|\u7434)(?:\u65E5|\u5929)(?:[\\s,\uFF0C]*)(?:(\u4E0A(?:\u5348|\u665D)|\u671D(?:\u65E9)|\u65E9(?:\u4E0A)|\u4E0B(?:\u5348|\u665D)|\u664F(?:\u665D)|\u665A(?:\u4E0A)|\u591C(?:\u665A)?|\u4E2D(?:\u5348)|\u51CC(?:\u6668)))?)?(?:[\\s,\uFF0C]*)(?:(\\d+|["+Object.keys(ur.NUMBER).join("")+"]+)(?:\\s*)(?:\u9EDE|\u6642|:|\uFF1A)(?:\\s*)(\\d+|\u534A|\u6B63|\u6574|["+Object.keys(ur.NUMBER).join("")+"]+)?(?:\\s*)(?:\u5206|:|\uFF1A)?(?:\\s*)(\\d+|["+Object.keys(ur.NUMBER).join("")+"]+)?(?:\\s*)(?:\u79D2)?)(?:\\s*(A.M.|P.M.|AM?|PM?))?","i"),UM=new RegExp("(?:^\\s*(?:\u5230|\u81F3|\\-|\\\u2013|\\~|\\\u301C)\\s*)(?:(\u4ECA|\u660E|\u524D|\u5927\u524D|\u5F8C|\u5927\u5F8C|\u807D|\u6628|\u5C0B|\u7434)(\u65E9|\u671D|\u665A)|(\u4E0A(?:\u5348|\u665D)|\u671D(?:\u65E9)|\u65E9(?:\u4E0A)|\u4E0B(?:\u5348|\u665D)|\u664F(?:\u665D)|\u665A(?:\u4E0A)|\u591C(?:\u665A)?|\u4E2D(?:\u5348)|\u51CC(?:\u6668))|(\u4ECA|\u660E|\u524D|\u5927\u524D|\u5F8C|\u5927\u5F8C|\u807D|\u6628|\u5C0B|\u7434)(?:\u65E5|\u5929)(?:[\\s,\uFF0C]*)(?:(\u4E0A(?:\u5348|\u665D)|\u671D(?:\u65E9)|\u65E9(?:\u4E0A)|\u4E0B(?:\u5348|\u665D)|\u664F(?:\u665D)|\u665A(?:\u4E0A)|\u591C(?:\u665A)?|\u4E2D(?:\u5348)|\u51CC(?:\u6668)))?)?(?:[\\s,\uFF0C]*)(?:(\\d+|["+Object.keys(ur.NUMBER).join("")+"]+)(?:\\s*)(?:\u9EDE|\u6642|:|\uFF1A)(?:\\s*)(\\d+|\u534A|\u6B63|\u6574|["+Object.keys(ur.NUMBER).join("")+"]+)?(?:\\s*)(?:\u5206|:|\uFF1A)?(?:\\s*)(\\d+|["+Object.keys(ur.NUMBER).join("")+"]+)?(?:\\s*)(?:\u79D2)?)(?:\\s*(A.M.|P.M.|AM?|PM?))?","i"),wu=1,ku=2,Eu=3,Su=4,Ou=5,Du=6,or=7,os=8,xu=9,Op=class extends FM.AbstractParserWithWordBoundaryChecking{innerPattern(){return LM}innerExtract(e,t){if(t.index>0&&e.text[t.index-1].match(/\w/))return null;let n=IM.default(e.refDate),i=e.createParsingResult(t.index,t[0]),s=n.clone();if(t[wu]){var a=t[wu];a=="\u660E"||a=="\u807D"?n.hour()>1&&s.add(1,"day"):a=="\u6628"||a=="\u5C0B"||a=="\u7434"?s.add(-1,"day"):a=="\u524D"?s.add(-2,"day"):a=="\u5927\u524D"?s.add(-3,"day"):a=="\u5F8C"?s.add(2,"day"):a=="\u5927\u5F8C"&&s.add(3,"day"),i.start.assign("day",s.date()),i.start.assign("month",s.month()+1),i.start.assign("year",s.year())}else if(t[Su]){var o=t[Su];o=="\u660E"||o=="\u807D"?s.add(1,"day"):o=="\u6628"||o=="\u5C0B"||o=="\u7434"?s.add(-1,"day"):o=="\u524D"?s.add(-2,"day"):o=="\u5927\u524D"?s.add(-3,"day"):o=="\u5F8C"?s.add(2,"day"):o=="\u5927\u5F8C"&&s.add(3,"day"),i.start.assign("day",s.date()),i.start.assign("month",s.month()+1),i.start.assign("year",s.year())}else i.start.imply("day",s.date()),i.start.imply("month",s.month()+1),i.start.imply("year",s.year());let u=0,l=0,c=-1;if(t[os]){var d=parseInt(t[os]);if(isNaN(d)&&(d=ur.zhStringToNumber(t[os])),d>=60)return null;i.start.assign("second",d)}if(u=parseInt(t[Du]),isNaN(u)&&(u=ur.zhStringToNumber(t[Du])),t[or]?t[or]=="\u534A"?l=30:t[or]=="\u6B63"||t[or]=="\u6574"?l=0:(l=parseInt(t[or]),isNaN(l)&&(l=ur.zhStringToNumber(t[or]))):u>100&&(l=u%100,u=Math.floor(u/100)),l>=60||u>24)return null;if(u>=12&&(c=1),t[xu]){if(u>12)return null;var p=t[xu][0].toLowerCase();p=="a"&&(c=0,u==12&&(u=0)),p=="p"&&(c=1,u!=12&&(u+=12))}else if(t[ku]){var m=t[ku],y=m[0];y=="\u671D"||y=="\u65E9"?(c=0,u==12&&(u=0)):y=="\u665A"&&(c=1,u!=12&&(u+=12))}else if(t[Eu]){var _=t[Eu],b=_[0];b=="\u4E0A"||b=="\u671D"||b=="\u65E9"||b=="\u51CC"?(c=0,u==12&&(u=0)):(b=="\u4E0B"||b=="\u664F"||b=="\u665A")&&(c=1,u!=12&&(u+=12))}else if(t[Ou]){var E=t[Ou],R=E[0];R=="\u4E0A"||R=="\u671D"||R=="\u65E9"||R=="\u51CC"?(c=0,u==12&&(u=0)):(R=="\u4E0B"||R=="\u664F"||R=="\u665A")&&(c=1,u!=12&&(u+=12))}if(i.start.assign("hour",u),i.start.assign("minute",l),c>=0?i.start.assign("meridiem",c):u<12?i.start.imply("meridiem",0):i.start.imply("meridiem",1),t=UM.exec(e.text.substring(i.index+i.text.length)),!t)return i.text.match(/^\d+$/)?null:i;let S=s.clone();if(i.end=e.createParsingComponents(),t[wu]){var a=t[wu];a=="\u660E"||a=="\u807D"?n.hour()>1&&S.add(1,"day"):a=="\u6628"||a=="\u5C0B"||a=="\u7434"?S.add(-1,"day"):a=="\u524D"?S.add(-2,"day"):a=="\u5927\u524D"?S.add(-3,"day"):a=="\u5F8C"?S.add(2,"day"):a=="\u5927\u5F8C"&&S.add(3,"day"),i.end.assign("day",S.date()),i.end.assign("month",S.month()+1),i.end.assign("year",S.year())}else if(t[Su]){var o=t[Su];o=="\u660E"||o=="\u807D"?S.add(1,"day"):o=="\u6628"||o=="\u5C0B"||o=="\u7434"?S.add(-1,"day"):o=="\u524D"?S.add(-2,"day"):o=="\u5927\u524D"?S.add(-3,"day"):o=="\u5F8C"?S.add(2,"day"):o=="\u5927\u5F8C"&&S.add(3,"day"),i.end.assign("day",S.date()),i.end.assign("month",S.month()+1),i.end.assign("year",S.year())}else i.end.imply("day",S.date()),i.end.imply("month",S.month()+1),i.end.imply("year",S.year());if(u=0,l=0,c=-1,t[os]){var d=parseInt(t[os]);if(isNaN(d)&&(d=ur.zhStringToNumber(t[os])),d>=60)return null;i.end.assign("second",d)}if(u=parseInt(t[Du]),isNaN(u)&&(u=ur.zhStringToNumber(t[Du])),t[or]?t[or]=="\u534A"?l=30:t[or]=="\u6B63"||t[or]=="\u6574"?l=0:(l=parseInt(t[or]),isNaN(l)&&(l=ur.zhStringToNumber(t[or]))):u>100&&(l=u%100,u=Math.floor(u/100)),l>=60||u>24)return null;if(u>=12&&(c=1),t[xu]){if(u>12)return null;var p=t[xu][0].toLowerCase();p=="a"&&(c=0,u==12&&(u=0)),p=="p"&&(c=1,u!=12&&(u+=12)),i.start.isCertain("meridiem")||(c==0?(i.start.imply("meridiem",0),i.start.get("hour")==12&&i.start.assign("hour",0)):(i.start.imply("meridiem",1),i.start.get("hour")!=12&&i.start.assign("hour",i.start.get("hour")+12)))}else if(t[ku]){var m=t[ku],y=m[0];y=="\u671D"||y=="\u65E9"?(c=0,u==12&&(u=0)):y=="\u665A"&&(c=1,u!=12&&(u+=12))}else if(t[Eu]){var _=t[Eu],b=_[0];b=="\u4E0A"||b=="\u671D"||b=="\u65E9"||b=="\u51CC"?(c=0,u==12&&(u=0)):(b=="\u4E0B"||b=="\u664F"||b=="\u665A")&&(c=1,u!=12&&(u+=12))}else if(t[Ou]){var E=t[Ou],R=E[0];R=="\u4E0A"||R=="\u671D"||R=="\u65E9"||R=="\u51CC"?(c=0,u==12&&(u=0)):(R=="\u4E0B"||R=="\u664F"||R=="\u665A")&&(c=1,u!=12&&(u+=12))}return i.text=i.text+t[0],i.end.assign("hour",u),i.end.assign("minute",l),c>=0?i.end.assign("meridiem",c):i.start.isCertain("meridiem")&&i.start.get("meridiem")==1&&i.start.get("hour")>u?i.end.imply("meridiem",0):u>12&&i.end.imply("meridiem",1),i.end.date().getTime()<i.start.date().getTime()&&i.end.imply("day",i.end.get("day")+1),i}};qa.default=Op});var AT=k($a=>{"use strict";var WM=$a&&$a.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty($a,"__esModule",{value:!0});var qM=WM(we()),$M=V(),CT=as(),jM=new RegExp("(?:\u661F\u671F|\u79AE\u62DC|\u9031)(?<weekday>"+Object.keys(CT.WEEKDAY_OFFSET).join("|")+")"),Dp=class extends $M.AbstractParserWithWordBoundaryChecking{innerPattern(){return jM}innerExtract(e,t){let n=e.createParsingResult(t.index,t[0]),i=t.groups.weekday,s=CT.WEEKDAY_OFFSET[i];if(s===void 0)return null;let a=qM.default(e.refDate),o=!1,u=a.day();return Math.abs(s-7-u)<Math.abs(s-u)?a=a.day(s-7):Math.abs(s+7-u)<Math.abs(s-u)?a=a.day(s+7):a=a.day(s),n.start.assign("weekday",s),o?(n.start.assign("day",a.date()),n.start.assign("month",a.month()+1),n.start.assign("year",a.year())):(n.start.imply("day",a.date()),n.start.imply("month",a.month()+1),n.start.imply("year",a.year())),n}};$a.default=Dp});var PT=k(ja=>{"use strict";var GM=ja&&ja.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(ja,"__esModule",{value:!0});var YM=GM(Gr()),xp=class extends YM.default{patternBetween(){return/^\s*(至|到|\-|\~|～|－|ー)\s*$/i}};ja.default=xp});var NT=k(Ga=>{"use strict";var BM=Ga&&Ga.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(Ga,"__esModule",{value:!0});var HM=BM(cn()),Rp=class extends HM.default{patternBetween(){return/^\s*$/i}};Ga.default=Rp});var IT=k(Ye=>{"use strict";var gn=Ye&&Ye.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(Ye,"__esModule",{value:!0});Ye.createConfiguration=Ye.createCasualConfiguration=Ye.parseDate=Ye.parse=Ye.strict=Ye.casual=Ye.hant=void 0;var Mp=kr(),VM=gn(iu()),zM=dn(),KM=gn(kT()),QM=gn(ST()),XM=gn(DT()),ZM=gn(RT()),JM=gn(MT()),eC=gn(AT()),tC=gn(PT()),rC=gn(NT());Ye.hant=new Mp.Chrono(Cp());Ye.casual=new Mp.Chrono(Cp());Ye.strict=new Mp.Chrono(Ap());function nC(r,e,t){return Ye.casual.parse(r,e,t)}Ye.parse=nC;function iC(r,e,t){return Ye.casual.parseDate(r,e,t)}Ye.parseDate=iC;function Cp(){let r=Ap();return r.parsers.unshift(new KM.default),r}Ye.createCasualConfiguration=Cp;function Ap(){let r=zM.includeCommonConfiguration({parsers:[new QM.default,new ZM.default,new eC.default,new JM.default,new XM.default],refiners:[new tC.default,new rC.default]});return r.refiners=r.refiners.filter(e=>!(e instanceof VM.default)),r}Ye.createConfiguration=Ap});var WT=k(Ya=>{"use strict";var sC=Ya&&Ya.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(Ya,"__esModule",{value:!0});var aC=sC(we()),oC=V(),uC=1,FT=2,lC=3,LT=4,UT=5,cC=6,Pp=class extends oC.AbstractParserWithWordBoundaryChecking{innerPattern(e){return new RegExp("(\u73B0\u5728|\u7ACB(?:\u523B|\u5373)|\u5373\u523B)|(\u4ECA|\u660E|\u524D|\u5927\u524D|\u540E|\u5927\u540E|\u6628)(\u65E9|\u665A)|(\u4E0A(?:\u5348)|\u65E9(?:\u4E0A)|\u4E0B(?:\u5348)|\u665A(?:\u4E0A)|\u591C(?:\u665A)?|\u4E2D(?:\u5348)|\u51CC(?:\u6668))|(\u4ECA|\u660E|\u524D|\u5927\u524D|\u540E|\u5927\u540E|\u6628)(?:\u65E5|\u5929)(?:[\\s|,|\uFF0C]*)(?:(\u4E0A(?:\u5348)|\u65E9(?:\u4E0A)|\u4E0B(?:\u5348)|\u665A(?:\u4E0A)|\u591C(?:\u665A)?|\u4E2D(?:\u5348)|\u51CC(?:\u6668)))?","i")}innerExtract(e,t){let n=t.index,i=e.createParsingResult(n,t[0]),s=aC.default(e.refDate),a=s;if(t[uC])i.start.imply("hour",s.hour()),i.start.imply("minute",s.minute()),i.start.imply("second",s.second()),i.start.imply("millisecond",s.millisecond());else if(t[FT]){let o=t[FT],u=t[lC];o=="\u660E"?s.hour()>1&&(a=a.add(1,"day")):o=="\u6628"?a=a.add(-1,"day"):o=="\u524D"?a=a.add(-2,"day"):o=="\u5927\u524D"?a=a.add(-3,"day"):o=="\u540E"?a=a.add(2,"day"):o=="\u5927\u540E"&&(a=a.add(3,"day")),u=="\u65E9"?i.start.imply("hour",6):u=="\u665A"&&(i.start.imply("hour",22),i.start.imply("meridiem",1))}else if(t[LT]){let u=t[LT][0];u=="\u65E9"||u=="\u4E0A"?i.start.imply("hour",6):u=="\u4E0B"?(i.start.imply("hour",15),i.start.imply("meridiem",1)):u=="\u4E2D"?(i.start.imply("hour",12),i.start.imply("meridiem",1)):u=="\u591C"||u=="\u665A"?(i.start.imply("hour",22),i.start.imply("meridiem",1)):u=="\u51CC"&&i.start.imply("hour",0)}else if(t[UT]){let o=t[UT];o=="\u660E"?s.hour()>1&&(a=a.add(1,"day")):o=="\u6628"?a=a.add(-1,"day"):o=="\u524D"?a=a.add(-2,"day"):o=="\u5927\u524D"?a=a.add(-3,"day"):o=="\u540E"?a=a.add(2,"day"):o=="\u5927\u540E"&&(a=a.add(3,"day"));let u=t[cC];if(u){let l=u[0];l=="\u65E9"||l=="\u4E0A"?i.start.imply("hour",6):l=="\u4E0B"?(i.start.imply("hour",15),i.start.imply("meridiem",1)):l=="\u4E2D"?(i.start.imply("hour",12),i.start.imply("meridiem",1)):l=="\u591C"||l=="\u665A"?(i.start.imply("hour",22),i.start.imply("meridiem",1)):l=="\u51CC"&&i.start.imply("hour",0)}}return i.start.assign("day",a.date()),i.start.assign("month",a.month()+1),i.start.assign("year",a.year()),i}};Ya.default=Pp});var us=k(At=>{"use strict";Object.defineProperty(At,"__esModule",{value:!0});At.zhStringToYear=At.zhStringToNumber=At.WEEKDAY_OFFSET=At.NUMBER=void 0;At.NUMBER={\u96F6:0,"\u3007":0,\u4E00:1,\u4E8C:2,\u4E24:2,\u4E09:3,\u56DB:4,\u4E94:5,\u516D:6,\u4E03:7,\u516B:8,\u4E5D:9,\u5341:10};At.WEEKDAY_OFFSET={\u5929:0,\u65E5:0,\u4E00:1,\u4E8C:2,\u4E09:3,\u56DB:4,\u4E94:5,\u516D:6};function dC(r){let e=0;for(let t=0;t<r.length;t++){let n=r[t];n==="\u5341"?e=e===0?At.NUMBER[n]:e*At.NUMBER[n]:e+=At.NUMBER[n]}return e}At.zhStringToNumber=dC;function fC(r){let e="";for(let t=0;t<r.length;t++){let n=r[t];e=e+At.NUMBER[n]}return parseInt(e)}At.zhStringToYear=fC});var $T=k(Ba=>{"use strict";var pC=Ba&&Ba.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(Ba,"__esModule",{value:!0});var mC=pC(we()),hC=V(),yi=us(),Np=1,qT=2,Ip=3,Fp=class extends hC.AbstractParserWithWordBoundaryChecking{innerPattern(){return new RegExp("(\\d{2,4}|["+Object.keys(yi.NUMBER).join("")+"]{4}|["+Object.keys(yi.NUMBER).join("")+"]{2})?(?:\\s*)(?:\u5E74)?(?:[\\s|,|\uFF0C]*)(\\d{1,2}|["+Object.keys(yi.NUMBER).join("")+"]{1,3})(?:\\s*)(?:\u6708)(?:\\s*)(\\d{1,2}|["+Object.keys(yi.NUMBER).join("")+"]{1,3})?(?:\\s*)(?:\u65E5|\u53F7)?")}innerExtract(e,t){let n=mC.default(e.refDate),i=e.createParsingResult(t.index,t[0]),s=parseInt(t[qT]);if(isNaN(s)&&(s=yi.zhStringToNumber(t[qT])),i.start.assign("month",s),t[Ip]){let a=parseInt(t[Ip]);isNaN(a)&&(a=yi.zhStringToNumber(t[Ip])),i.start.assign("day",a)}else i.start.imply("day",n.date());if(t[Np]){let a=parseInt(t[Np]);isNaN(a)&&(a=yi.zhStringToYear(t[Np])),i.start.assign("year",a)}else i.start.imply("year",n.year());return i}};Ba.default=Fp});var GT=k(Ha=>{"use strict";var gC=Ha&&Ha.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(Ha,"__esModule",{value:!0});var yC=gC(we()),bC=V(),jT=us(),TC=new RegExp("(\\d+|["+Object.keys(jT.NUMBER).join("")+"]+|\u534A|\u51E0)(?:\\s*)(?:\u4E2A)?(\u79D2(?:\u949F)?|\u5206\u949F|\u5C0F\u65F6|\u949F|\u65E5|\u5929|\u661F\u671F|\u793C\u62DC|\u6708|\u5E74)(?:(?:\u4E4B|\u8FC7)?\u540E|(?:\u4E4B)?\u5185)","i"),Lp=1,_C=2,Up=class extends bC.AbstractParserWithWordBoundaryChecking{innerPattern(){return TC}innerExtract(e,t){let n=e.createParsingResult(t.index,t[0]),i=parseInt(t[Lp]);if(isNaN(i)&&(i=jT.zhStringToNumber(t[Lp])),isNaN(i)){let u=t[Lp];if(u==="\u51E0")i=3;else if(u==="\u534A")i=.5;else return null}let s=yC.default(e.refDate),o=t[_C][0];return o.match(/[日天星礼月年]/)?(o=="\u65E5"||o=="\u5929"?s=s.add(i,"d"):o=="\u661F"||o=="\u793C"?s=s.add(i*7,"d"):o=="\u6708"?s=s.add(i,"month"):o=="\u5E74"&&(s=s.add(i,"year")),n.start.assign("year",s.year()),n.start.assign("month",s.month()+1),n.start.assign("day",s.date()),n):(o=="\u79D2"?s=s.add(i,"second"):o=="\u5206"?s=s.add(i,"minute"):(o=="\u5C0F"||o=="\u949F")&&(s=s.add(i,"hour")),n.start.imply("year",s.year()),n.start.imply("month",s.month()+1),n.start.imply("day",s.date()),n.start.assign("hour",s.hour()),n.start.assign("minute",s.minute()),n.start.assign("second",s.second()),n)}};Ha.default=Up});var BT=k(Va=>{"use strict";var vC=Va&&Va.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(Va,"__esModule",{value:!0});var wC=vC(we()),kC=V(),YT=us(),EC=new RegExp("(?<prefix>\u4E0A|\u4E0B|\u8FD9)(?:\u4E2A)?(?:\u661F\u671F|\u793C\u62DC|\u5468)(?<weekday>"+Object.keys(YT.WEEKDAY_OFFSET).join("|")+")"),Wp=class extends kC.AbstractParserWithWordBoundaryChecking{innerPattern(){return EC}innerExtract(e,t){let n=e.createParsingResult(t.index,t[0]),i=t.groups.weekday,s=YT.WEEKDAY_OFFSET[i];if(s===void 0)return null;let a=null,o=t.groups.prefix;o=="\u4E0A"?a="last":o=="\u4E0B"?a="next":o=="\u8FD9"&&(a="this");let u=wC.default(e.refDate),l=!1,c=u.day();return a=="last"||a=="past"?(u=u.day(s-7),l=!0):a=="next"?(u=u.day(s+7),l=!0):a=="this"?u=u.day(s):Math.abs(s-7-c)<Math.abs(s-c)?u=u.day(s-7):Math.abs(s+7-c)<Math.abs(s-c)?u=u.day(s+7):u=u.day(s),n.start.assign("weekday",s),l?(n.start.assign("day",u.date()),n.start.assign("month",u.month()+1),n.start.assign("year",u.year())):(n.start.imply("day",u.date()),n.start.imply("month",u.month()+1),n.start.imply("year",u.year())),n}};Va.default=Wp});var HT=k(za=>{"use strict";var SC=za&&za.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(za,"__esModule",{value:!0});var OC=SC(we()),DC=V(),cr=us(),xC=new RegExp("(?:\u4ECE|\u81EA)?(?:(\u4ECA|\u660E|\u524D|\u5927\u524D|\u540E|\u5927\u540E|\u6628)(\u65E9|\u671D|\u665A)|(\u4E0A(?:\u5348)|\u65E9(?:\u4E0A)|\u4E0B(?:\u5348)|\u665A(?:\u4E0A)|\u591C(?:\u665A)?|\u4E2D(?:\u5348)|\u51CC(?:\u6668))|(\u4ECA|\u660E|\u524D|\u5927\u524D|\u540E|\u5927\u540E|\u6628)(?:\u65E5|\u5929)(?:[\\s,\uFF0C]*)(?:(\u4E0A(?:\u5348)|\u65E9(?:\u4E0A)|\u4E0B(?:\u5348)|\u665A(?:\u4E0A)|\u591C(?:\u665A)?|\u4E2D(?:\u5348)|\u51CC(?:\u6668)))?)?(?:[\\s,\uFF0C]*)(?:(\\d+|["+Object.keys(cr.NUMBER).join("")+"]+)(?:\\s*)(?:\u70B9|\u65F6|:|\uFF1A)(?:\\s*)(\\d+|\u534A|\u6B63|\u6574|["+Object.keys(cr.NUMBER).join("")+"]+)?(?:\\s*)(?:\u5206|:|\uFF1A)?(?:\\s*)(\\d+|["+Object.keys(cr.NUMBER).join("")+"]+)?(?:\\s*)(?:\u79D2)?)(?:\\s*(A.M.|P.M.|AM?|PM?))?","i"),RC=new RegExp("(?:^\\s*(?:\u5230|\u81F3|\\-|\\\u2013|\\~|\\\u301C)\\s*)(?:(\u4ECA|\u660E|\u524D|\u5927\u524D|\u540E|\u5927\u540E|\u6628)(\u65E9|\u671D|\u665A)|(\u4E0A(?:\u5348)|\u65E9(?:\u4E0A)|\u4E0B(?:\u5348)|\u665A(?:\u4E0A)|\u591C(?:\u665A)?|\u4E2D(?:\u5348)|\u51CC(?:\u6668))|(\u4ECA|\u660E|\u524D|\u5927\u524D|\u540E|\u5927\u540E|\u6628)(?:\u65E5|\u5929)(?:[\\s,\uFF0C]*)(?:(\u4E0A(?:\u5348)|\u65E9(?:\u4E0A)|\u4E0B(?:\u5348)|\u665A(?:\u4E0A)|\u591C(?:\u665A)?|\u4E2D(?:\u5348)|\u51CC(?:\u6668)))?)?(?:[\\s,\uFF0C]*)(?:(\\d+|["+Object.keys(cr.NUMBER).join("")+"]+)(?:\\s*)(?:\u70B9|\u65F6|:|\uFF1A)(?:\\s*)(\\d+|\u534A|\u6B63|\u6574|["+Object.keys(cr.NUMBER).join("")+"]+)?(?:\\s*)(?:\u5206|:|\uFF1A)?(?:\\s*)(\\d+|["+Object.keys(cr.NUMBER).join("")+"]+)?(?:\\s*)(?:\u79D2)?)(?:\\s*(A.M.|P.M.|AM?|PM?))?","i"),Ru=1,Mu=2,Cu=3,Au=4,Pu=5,Nu=6,lr=7,ls=8,Iu=9,qp=class extends DC.AbstractParserWithWordBoundaryChecking{innerPattern(){return xC}innerExtract(e,t){if(t.index>0&&e.text[t.index-1].match(/\w/))return null;let n=OC.default(e.refDate),i=e.createParsingResult(t.index,t[0]),s=n.clone();if(t[Ru]){let c=t[Ru];c=="\u660E"?n.hour()>1&&s.add(1,"day"):c=="\u6628"?s.add(-1,"day"):c=="\u524D"?s.add(-2,"day"):c=="\u5927\u524D"?s.add(-3,"day"):c=="\u540E"?s.add(2,"day"):c=="\u5927\u540E"&&s.add(3,"day"),i.start.assign("day",s.date()),i.start.assign("month",s.month()+1),i.start.assign("year",s.year())}else if(t[Au]){let c=t[Au];c=="\u660E"?s.add(1,"day"):c=="\u6628"?s.add(-1,"day"):c=="\u524D"?s.add(-2,"day"):c=="\u5927\u524D"?s.add(-3,"day"):c=="\u540E"?s.add(2,"day"):c=="\u5927\u540E"&&s.add(3,"day"),i.start.assign("day",s.date()),i.start.assign("month",s.month()+1),i.start.assign("year",s.year())}else i.start.imply("day",s.date()),i.start.imply("month",s.month()+1),i.start.imply("year",s.year());let a=0,o=0,u=-1;if(t[ls]){let c=parseInt(t[ls]);if(isNaN(c)&&(c=cr.zhStringToNumber(t[ls])),c>=60)return null;i.start.assign("second",c)}if(a=parseInt(t[Nu]),isNaN(a)&&(a=cr.zhStringToNumber(t[Nu])),t[lr]?t[lr]=="\u534A"?o=30:t[lr]=="\u6B63"||t[lr]=="\u6574"?o=0:(o=parseInt(t[lr]),isNaN(o)&&(o=cr.zhStringToNumber(t[lr]))):a>100&&(o=a%100,a=Math.floor(a/100)),o>=60||a>24)return null;if(a>=12&&(u=1),t[Iu]){if(a>12)return null;let c=t[Iu][0].toLowerCase();c=="a"&&(u=0,a==12&&(a=0)),c=="p"&&(u=1,a!=12&&(a+=12))}else if(t[Mu]){let d=t[Mu][0];d=="\u65E9"?(u=0,a==12&&(a=0)):d=="\u665A"&&(u=1,a!=12&&(a+=12))}else if(t[Cu]){let d=t[Cu][0];d=="\u4E0A"||d=="\u65E9"||d=="\u51CC"?(u=0,a==12&&(a=0)):(d=="\u4E0B"||d=="\u665A")&&(u=1,a!=12&&(a+=12))}else if(t[Pu]){let d=t[Pu][0];d=="\u4E0A"||d=="\u65E9"||d=="\u51CC"?(u=0,a==12&&(a=0)):(d=="\u4E0B"||d=="\u665A")&&(u=1,a!=12&&(a+=12))}if(i.start.assign("hour",a),i.start.assign("minute",o),u>=0?i.start.assign("meridiem",u):a<12?i.start.imply("meridiem",0):i.start.imply("meridiem",1),t=RC.exec(e.text.substring(i.index+i.text.length)),!t)return i.text.match(/^\d+$/)?null:i;let l=s.clone();if(i.end=e.createParsingComponents(),t[Ru]){let c=t[Ru];c=="\u660E"?n.hour()>1&&l.add(1,"day"):c=="\u6628"?l.add(-1,"day"):c=="\u524D"?l.add(-2,"day"):c=="\u5927\u524D"?l.add(-3,"day"):c=="\u540E"?l.add(2,"day"):c=="\u5927\u540E"&&l.add(3,"day"),i.end.assign("day",l.date()),i.end.assign("month",l.month()+1),i.end.assign("year",l.year())}else if(t[Au]){let c=t[Au];c=="\u660E"?l.add(1,"day"):c=="\u6628"?l.add(-1,"day"):c=="\u524D"?l.add(-2,"day"):c=="\u5927\u524D"?l.add(-3,"day"):c=="\u540E"?l.add(2,"day"):c=="\u5927\u540E"&&l.add(3,"day"),i.end.assign("day",l.date()),i.end.assign("month",l.month()+1),i.end.assign("year",l.year())}else i.end.imply("day",l.date()),i.end.imply("month",l.month()+1),i.end.imply("year",l.year());if(a=0,o=0,u=-1,t[ls]){let c=parseInt(t[ls]);if(isNaN(c)&&(c=cr.zhStringToNumber(t[ls])),c>=60)return null;i.end.assign("second",c)}if(a=parseInt(t[Nu]),isNaN(a)&&(a=cr.zhStringToNumber(t[Nu])),t[lr]?t[lr]=="\u534A"?o=30:t[lr]=="\u6B63"||t[lr]=="\u6574"?o=0:(o=parseInt(t[lr]),isNaN(o)&&(o=cr.zhStringToNumber(t[lr]))):a>100&&(o=a%100,a=Math.floor(a/100)),o>=60||a>24)return null;if(a>=12&&(u=1),t[Iu]){if(a>12)return null;let c=t[Iu][0].toLowerCase();c=="a"&&(u=0,a==12&&(a=0)),c=="p"&&(u=1,a!=12&&(a+=12)),i.start.isCertain("meridiem")||(u==0?(i.start.imply("meridiem",0),i.start.get("hour")==12&&i.start.assign("hour",0)):(i.start.imply("meridiem",1),i.start.get("hour")!=12&&i.start.assign("hour",i.start.get("hour")+12)))}else if(t[Mu]){let d=t[Mu][0];d=="\u65E9"?(u=0,a==12&&(a=0)):d=="\u665A"&&(u=1,a!=12&&(a+=12))}else if(t[Cu]){let d=t[Cu][0];d=="\u4E0A"||d=="\u65E9"||d=="\u51CC"?(u=0,a==12&&(a=0)):(d=="\u4E0B"||d=="\u665A")&&(u=1,a!=12&&(a+=12))}else if(t[Pu]){let d=t[Pu][0];d=="\u4E0A"||d=="\u65E9"||d=="\u51CC"?(u=0,a==12&&(a=0)):(d=="\u4E0B"||d=="\u665A")&&(u=1,a!=12&&(a+=12))}return i.text=i.text+t[0],i.end.assign("hour",a),i.end.assign("minute",o),u>=0?i.end.assign("meridiem",u):i.start.isCertain("meridiem")&&i.start.get("meridiem")==1&&i.start.get("hour")>a?i.end.imply("meridiem",0):a>12&&i.end.imply("meridiem",1),i.end.date().getTime()<i.start.date().getTime()&&i.end.imply("day",i.end.get("day")+1),i}};za.default=qp});var zT=k(Ka=>{"use strict";var MC=Ka&&Ka.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(Ka,"__esModule",{value:!0});var CC=MC(we()),AC=V(),VT=us(),PC=new RegExp("(?:\u661F\u671F|\u793C\u62DC|\u5468)(?<weekday>"+Object.keys(VT.WEEKDAY_OFFSET).join("|")+")"),$p=class extends AC.AbstractParserWithWordBoundaryChecking{innerPattern(){return PC}innerExtract(e,t){let n=e.createParsingResult(t.index,t[0]),i=t.groups.weekday,s=VT.WEEKDAY_OFFSET[i];if(s===void 0)return null;let a=CC.default(e.refDate),o=!1,u=a.day();return Math.abs(s-7-u)<Math.abs(s-u)?a=a.day(s-7):Math.abs(s+7-u)<Math.abs(s-u)?a=a.day(s+7):a=a.day(s),n.start.assign("weekday",s),o?(n.start.assign("day",a.date()),n.start.assign("month",a.month()+1),n.start.assign("year",a.year())):(n.start.imply("day",a.date()),n.start.imply("month",a.month()+1),n.start.imply("year",a.year())),n}};Ka.default=$p});var KT=k(Qa=>{"use strict";var NC=Qa&&Qa.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(Qa,"__esModule",{value:!0});var IC=NC(Gr()),jp=class extends IC.default{patternBetween(){return/^\s*(至|到|-|~|～|－|ー)\s*$/i}};Qa.default=jp});var QT=k(Xa=>{"use strict";var FC=Xa&&Xa.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(Xa,"__esModule",{value:!0});var LC=FC(cn()),Gp=class extends LC.default{patternBetween(){return/^\s*$/i}};Xa.default=Gp});var XT=k(Be=>{"use strict";var yn=Be&&Be.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(Be,"__esModule",{value:!0});Be.createConfiguration=Be.createCasualConfiguration=Be.parseDate=Be.parse=Be.strict=Be.casual=Be.hans=void 0;var Yp=kr(),UC=yn(iu()),WC=dn(),qC=yn(WT()),$C=yn($T()),jC=yn(GT()),GC=yn(BT()),YC=yn(HT()),BC=yn(zT()),HC=yn(KT()),VC=yn(QT());Be.hans=new Yp.Chrono(Bp());Be.casual=new Yp.Chrono(Bp());Be.strict=new Yp.Chrono(Hp());function zC(r,e,t){return Be.casual.parse(r,e,t)}Be.parse=zC;function KC(r,e,t){return Be.casual.parseDate(r,e,t)}Be.parseDate=KC;function Bp(){let r=Hp();return r.parsers.unshift(new qC.default),r}Be.createCasualConfiguration=Bp;function Hp(){let r=WC.includeCommonConfiguration({parsers:[new $C.default,new GC.default,new BC.default,new YC.default,new jC.default],refiners:[new HC.default,new VC.default]});return r.refiners=r.refiners.filter(e=>!(e instanceof UC.default)),r}Be.createConfiguration=Hp});var JT=k(Yt=>{"use strict";var ZT=Yt&&Yt.__createBinding||(Object.create?function(r,e,t,n){n===void 0&&(n=t),Object.defineProperty(r,n,{enumerable:!0,get:function(){return e[t]}})}:function(r,e,t,n){n===void 0&&(n=t),r[n]=e[t]}),QC=Yt&&Yt.__setModuleDefault||(Object.create?function(r,e){Object.defineProperty(r,"default",{enumerable:!0,value:e})}:function(r,e){r.default=e}),XC=Yt&&Yt.__exportStar||function(r,e){for(var t in r)t!=="default"&&!Object.prototype.hasOwnProperty.call(e,t)&&ZT(e,r,t)},ZC=Yt&&Yt.__importStar||function(r){if(r&&r.__esModule)return r;var e={};if(r!=null)for(var t in r)t!=="default"&&Object.prototype.hasOwnProperty.call(r,t)&&ZT(e,r,t);return QC(e,r),e};Object.defineProperty(Yt,"__esModule",{value:!0});Yt.hans=void 0;XC(IT(),Yt);Yt.hans=ZC(XT())});var Pt=k(se=>{"use strict";Object.defineProperty(se,"__esModule",{value:!0});se.parseTimeUnits=se.TIME_UNITS_PATTERN=se.parseYear=se.YEAR_PATTERN=se.parseOrdinalNumberPattern=se.ORDINAL_NUMBER_PATTERN=se.parseNumberPattern=se.NUMBER_PATTERN=se.TIME_UNIT_DICTIONARY=se.ORDINAL_WORD_DICTIONARY=se.INTEGER_WORD_DICTIONARY=se.MONTH_DICTIONARY=se.FULL_MONTH_NAME_DICTIONARY=se.WEEKDAY_DICTIONARY=se.REGEX_PARTS=void 0;var Fu=Re(),JC=ot();se.REGEX_PARTS={leftBoundary:"([^\\p{L}\\p{N}_]|^)",rightBoundary:"(?=[^\\p{L}\\p{N}_]|$)",flags:"iu"};se.WEEKDAY_DICTIONARY={\u0432\u043E\u0441\u043A\u0440\u0435\u0441\u0435\u043D\u044C\u0435:0,\u0432\u043E\u0441\u043A\u0440\u0435\u0441\u0435\u043D\u044C\u044F:0,\u0432\u0441\u043A:0,"\u0432\u0441\u043A.":0,\u043F\u043E\u043D\u0435\u0434\u0435\u043B\u044C\u043D\u0438\u043A:1,\u043F\u043E\u043D\u0435\u0434\u0435\u043B\u044C\u043D\u0438\u043A\u0430:1,\u043F\u043D:1,"\u043F\u043D.":1,\u0432\u0442\u043E\u0440\u043D\u0438\u043A:2,\u0432\u0442\u043E\u0440\u043D\u0438\u043A\u0430:2,\u0432\u0442:2,"\u0432\u0442.":2,\u0441\u0440\u0435\u0434\u0430:3,\u0441\u0440\u0435\u0434\u044B:3,\u0441\u0440\u0435\u0434\u0443:3,\u0441\u0440:3,"\u0441\u0440.":3,\u0447\u0435\u0442\u0432\u0435\u0440\u0433:4,\u0447\u0435\u0442\u0432\u0435\u0440\u0433\u0430:4,\u0447\u0442:4,"\u0447\u0442.":4,\u043F\u044F\u0442\u043D\u0438\u0446\u0430:5,\u043F\u044F\u0442\u043D\u0438\u0446\u0443:5,\u043F\u044F\u0442\u043D\u0438\u0446\u044B:5,\u043F\u0442:5,"\u043F\u0442.":5,\u0441\u0443\u0431\u0431\u043E\u0442\u0430:6,\u0441\u0443\u0431\u0431\u043E\u0442\u0443:6,\u0441\u0443\u0431\u0431\u043E\u0442\u044B:6,\u0441\u0431:6,"\u0441\u0431.":6};se.FULL_MONTH_NAME_DICTIONARY={\u044F\u043D\u0432\u0430\u0440\u044C:1,\u044F\u043D\u0432\u0430\u0440\u044F:1,\u044F\u043D\u0432\u0430\u0440\u0435:1,\u0444\u0435\u0432\u0440\u044F\u043B\u044C:2,\u0444\u0435\u0432\u0440\u044F\u043B\u044F:2,\u0444\u0435\u0432\u0440\u044F\u043B\u0435:2,\u043C\u0430\u0440\u0442:3,\u043C\u0430\u0440\u0442\u0430:3,\u043C\u0430\u0440\u0442\u0435:3,\u0430\u043F\u0440\u0435\u043B\u044C:4,\u0430\u043F\u0440\u0435\u043B\u044F:4,\u0430\u043F\u0440\u0435\u043B\u0435:4,\u043C\u0430\u0439:5,\u043C\u0430\u044F:5,\u043C\u0430\u0435:5,\u0438\u044E\u043D\u044C:6,\u0438\u044E\u043D\u044F:6,\u0438\u044E\u043D\u0435:6,\u0438\u044E\u043B\u044C:7,\u0438\u044E\u043B\u044F:7,\u0438\u044E\u043B\u0435:7,\u0430\u0432\u0433\u0443\u0441\u0442:8,\u0430\u0432\u0433\u0443\u0441\u0442\u0430:8,\u0430\u0432\u0433\u0443\u0441\u0442\u0435:8,\u0441\u0435\u043D\u0442\u044F\u0431\u0440\u044C:9,\u0441\u0435\u043D\u0442\u044F\u0431\u0440\u044F:9,\u0441\u0435\u043D\u0442\u044F\u0431\u0440\u0435:9,\u043E\u043A\u0442\u044F\u0431\u0440\u044C:10,\u043E\u043A\u0442\u044F\u0431\u0440\u044F:10,\u043E\u043A\u0442\u044F\u0431\u0440\u0435:10,\u043D\u043E\u044F\u0431\u0440\u044C:11,\u043D\u043E\u044F\u0431\u0440\u044F:11,\u043D\u043E\u044F\u0431\u0440\u0435:11,\u0434\u0435\u043A\u0430\u0431\u0440\u044C:12,\u0434\u0435\u043A\u0430\u0431\u0440\u044F:12,\u0434\u0435\u043A\u0430\u0431\u0440\u0435:12};se.MONTH_DICTIONARY=Object.assign(Object.assign({},se.FULL_MONTH_NAME_DICTIONARY),{\u044F\u043D\u0432:1,"\u044F\u043D\u0432.":1,\u0444\u0435\u0432:2,"\u0444\u0435\u0432.":2,\u043C\u0430\u0440:3,"\u043C\u0430\u0440.":3,\u0430\u043F\u0440:4,"\u0430\u043F\u0440.":4,\u0430\u0432\u0433:8,"\u0430\u0432\u0433.":8,\u0441\u0435\u043D:9,"\u0441\u0435\u043D.":9,\u043E\u043A\u0442:10,"\u043E\u043A\u0442.":10,\u043D\u043E\u044F:11,"\u043D\u043E\u044F.":11,\u0434\u0435\u043A:12,"\u0434\u0435\u043A.":12});se.INTEGER_WORD_DICTIONARY={\u043E\u0434\u0438\u043D:1,\u043E\u0434\u043D\u0430:1,\u043E\u0434\u043D\u043E\u0439:1,\u043E\u0434\u043D\u0443:1,\u0434\u0432\u0435:2,\u0434\u0432\u0430:2,\u0434\u0432\u0443\u0445:2,\u0442\u0440\u0438:3,\u0442\u0440\u0435\u0445:3,\u0442\u0440\u0451\u0445:3,\u0447\u0435\u0442\u044B\u0440\u0435:4,\u0447\u0435\u0442\u044B\u0440\u0435\u0445:4,\u0447\u0435\u0442\u044B\u0440\u0451\u0445:4,\u043F\u044F\u0442\u044C:5,\u043F\u044F\u0442\u0438:5,\u0448\u0435\u0441\u0442\u044C:6,\u0448\u0435\u0441\u0442\u0438:6,\u0441\u0435\u043C\u044C:7,\u0441\u0435\u043C\u0438:7,\u0432\u043E\u0441\u0435\u043C\u044C:8,\u0432\u043E\u0441\u0435\u043C\u044C\u043C\u0438:8,\u0434\u0435\u0432\u044F\u0442\u044C:9,\u0434\u0435\u0432\u044F\u0442\u0438:9,\u0434\u0435\u0441\u044F\u0442\u044C:10,\u0434\u0435\u0441\u044F\u0442\u0438:10,\u043E\u0434\u0438\u043D\u043D\u0430\u0434\u0446\u0430\u0442\u044C:11,\u043E\u0434\u0438\u043D\u043D\u0430\u0434\u0446\u0430\u0442\u0438:11,\u0434\u0432\u0435\u043D\u0430\u0434\u0446\u0430\u0442\u044C:12,\u0434\u0432\u0435\u043D\u0430\u0434\u0446\u0430\u0442\u0438:12};se.ORDINAL_WORD_DICTIONARY={\u043F\u0435\u0440\u0432\u043E\u0435:1,\u043F\u0435\u0440\u0432\u043E\u0433\u043E:1,\u0432\u0442\u043E\u0440\u043E\u0435:2,\u0432\u0442\u043E\u0440\u043E\u0433\u043E:2,\u0442\u0440\u0435\u0442\u044C\u0435:3,\u0442\u0440\u0435\u0442\u044C\u0435\u0433\u043E:3,\u0447\u0435\u0442\u0432\u0435\u0440\u0442\u043E\u0435:4,\u0447\u0435\u0442\u0432\u0435\u0440\u0442\u043E\u0433\u043E:4,\u043F\u044F\u0442\u043E\u0435:5,\u043F\u044F\u0442\u043E\u0433\u043E:5,\u0448\u0435\u0441\u0442\u043E\u0435:6,\u0448\u0435\u0441\u0442\u043E\u0433\u043E:6,\u0441\u0435\u0434\u044C\u043C\u043E\u0435:7,\u0441\u0435\u0434\u044C\u043C\u043E\u0433\u043E:7,\u0432\u043E\u0441\u044C\u043C\u043E\u0435:8,\u0432\u043E\u0441\u044C\u043C\u043E\u0433\u043E:8,\u0434\u0435\u0432\u044F\u0442\u043E\u0435:9,\u0434\u0435\u0432\u044F\u0442\u043E\u0433\u043E:9,\u0434\u0435\u0441\u044F\u0442\u043E\u0435:10,\u0434\u0435\u0441\u044F\u0442\u043E\u0433\u043E:10,\u043E\u0434\u0438\u043D\u043D\u0430\u0434\u0446\u0430\u0442\u043E\u0435:11,\u043E\u0434\u0438\u043D\u043D\u0430\u0434\u0446\u0430\u0442\u043E\u0433\u043E:11,\u0434\u0432\u0435\u043D\u0430\u0434\u0446\u0430\u0442\u043E\u0435:12,\u0434\u0432\u0435\u043D\u0430\u0434\u0446\u0430\u0442\u043E\u0433\u043E:12,\u0442\u0440\u0438\u043D\u0430\u0434\u0446\u0430\u0442\u043E\u0435:13,\u0442\u0440\u0438\u043D\u0430\u0434\u0446\u0430\u0442\u043E\u0433\u043E:13,\u0447\u0435\u0442\u044B\u0440\u043D\u0430\u0434\u0446\u0430\u0442\u043E\u0435:14,\u0447\u0435\u0442\u044B\u0440\u043D\u0430\u0434\u0446\u0430\u0442\u043E\u0433\u043E:14,\u043F\u044F\u0442\u043D\u0430\u0434\u0446\u0430\u0442\u043E\u0435:15,\u043F\u044F\u0442\u043D\u0430\u0434\u0446\u0430\u0442\u043E\u0433\u043E:15,\u0448\u0435\u0441\u0442\u043D\u0430\u0434\u0446\u0430\u0442\u043E\u0435:16,\u0448\u0435\u0441\u0442\u043D\u0430\u0434\u0446\u0430\u0442\u043E\u0433\u043E:16,\u0441\u0435\u043C\u043D\u0430\u0434\u0446\u0430\u0442\u043E\u0435:17,\u0441\u0435\u043C\u043D\u0430\u0434\u0446\u0430\u0442\u043E\u0433\u043E:17,\u0432\u043E\u0441\u0435\u043C\u043D\u0430\u0434\u0446\u0430\u0442\u043E\u0435:18,\u0432\u043E\u0441\u0435\u043C\u043D\u0430\u0434\u0446\u0430\u0442\u043E\u0433\u043E:18,\u0434\u0435\u0432\u044F\u0442\u043D\u0430\u0434\u0446\u0430\u0442\u043E\u0435:19,\u0434\u0435\u0432\u044F\u0442\u043D\u0430\u0434\u0446\u0430\u0442\u043E\u0433\u043E:19,\u0434\u0432\u0430\u0434\u0446\u0430\u0442\u043E\u0435:20,\u0434\u0432\u0430\u0434\u0446\u0430\u0442\u043E\u0433\u043E:20,"\u0434\u0432\u0430\u0434\u0446\u0430\u0442\u044C \u043F\u0435\u0440\u0432\u043E\u0435":21,"\u0434\u0432\u0430\u0434\u0446\u0430\u0442\u044C \u043F\u0435\u0440\u0432\u043E\u0433\u043E":21,"\u0434\u0432\u0430\u0434\u0446\u0430\u0442\u044C \u0432\u0442\u043E\u0440\u043E\u0435":22,"\u0434\u0432\u0430\u0434\u0446\u0430\u0442\u044C \u0432\u0442\u043E\u0440\u043E\u0433\u043E":22,"\u0434\u0432\u0430\u0434\u0446\u0430\u0442\u044C \u0442\u0440\u0435\u0442\u044C\u0435":23,"\u0434\u0432\u0430\u0434\u0446\u0430\u0442\u044C \u0442\u0440\u0435\u0442\u044C\u0435\u0433\u043E":23,"\u0434\u0432\u0430\u0434\u0446\u0430\u0442\u044C \u0447\u0435\u0442\u0432\u0435\u0440\u0442\u043E\u0435":24,"\u0434\u0432\u0430\u0434\u0446\u0430\u0442\u044C \u0447\u0435\u0442\u0432\u0435\u0440\u0442\u043E\u0433\u043E":24,"\u0434\u0432\u0430\u0434\u0446\u0430\u0442\u044C \u043F\u044F\u0442\u043E\u0435":25,"\u0434\u0432\u0430\u0434\u0446\u0430\u0442\u044C \u043F\u044F\u0442\u043E\u0433\u043E":25,"\u0434\u0432\u0430\u0434\u0446\u0430\u0442\u044C \u0448\u0435\u0441\u0442\u043E\u0435":26,"\u0434\u0432\u0430\u0434\u0446\u0430\u0442\u044C \u0448\u0435\u0441\u0442\u043E\u0433\u043E":26,"\u0434\u0432\u0430\u0434\u0446\u0430\u0442\u044C \u0441\u0435\u0434\u044C\u043C\u043E\u0435":27,"\u0434\u0432\u0430\u0434\u0446\u0430\u0442\u044C \u0441\u0435\u0434\u044C\u043C\u043E\u0433\u043E":27,"\u0434\u0432\u0430\u0434\u0446\u0430\u0442\u044C \u0432\u043E\u0441\u044C\u043C\u043E\u0435":28,"\u0434\u0432\u0430\u0434\u0446\u0430\u0442\u044C \u0432\u043E\u0441\u044C\u043C\u043E\u0433\u043E":28,"\u0434\u0432\u0430\u0434\u0446\u0430\u0442\u044C \u0434\u0435\u0432\u044F\u0442\u043E\u0435":29,"\u0434\u0432\u0430\u0434\u0446\u0430\u0442\u044C \u0434\u0435\u0432\u044F\u0442\u043E\u0433\u043E":29,\u0442\u0440\u0438\u0434\u0446\u0430\u0442\u043E\u0435:30,\u0442\u0440\u0438\u0434\u0446\u0430\u0442\u043E\u0433\u043E:30,"\u0442\u0440\u0438\u0434\u0446\u0430\u0442\u044C \u043F\u0435\u0440\u0432\u043E\u0435":31,"\u0442\u0440\u0438\u0434\u0446\u0430\u0442\u044C \u043F\u0435\u0440\u0432\u043E\u0433\u043E":31};se.TIME_UNIT_DICTIONARY={\u0441\u0435\u043A:"second",\u0441\u0435\u043A\u0443\u043D\u0434\u0430:"second",\u0441\u0435\u043A\u0443\u043D\u0434:"second",\u0441\u0435\u043A\u0443\u043D\u0434\u044B:"second",\u0441\u0435\u043A\u0443\u043D\u0434\u0443:"second",\u0441\u0435\u043A\u0443\u043D\u0434\u043E\u0447\u043A\u0430:"second",\u0441\u0435\u043A\u0443\u043D\u0434\u043E\u0447\u043A\u0438:"second",\u0441\u0435\u043A\u0443\u043D\u0434\u043E\u0447\u0435\u043A:"second",\u0441\u0435\u043A\u0443\u043D\u0434\u043E\u0447\u043A\u0443:"second",\u043C\u0438\u043D:"minute",\u043C\u0438\u043D\u0443\u0442\u0430:"minute",\u043C\u0438\u043D\u0443\u0442:"minute",\u043C\u0438\u043D\u0443\u0442\u044B:"minute",\u043C\u0438\u043D\u0443\u0442\u0443:"minute",\u043C\u0438\u043D\u0443\u0442\u043E\u043A:"minute",\u043C\u0438\u043D\u0443\u0442\u043A\u0438:"minute",\u043C\u0438\u043D\u0443\u0442\u043A\u0443:"minute",\u0447\u0430\u0441:"hour",\u0447\u0430\u0441\u043E\u0432:"hour",\u0447\u0430\u0441\u0430:"hour",\u0447\u0430\u0441\u0443:"hour",\u0447\u0430\u0441\u0438\u043A\u043E\u0432:"hour",\u0447\u0430\u0441\u0438\u043A\u0430:"hour",\u0447\u0430\u0441\u0438\u043A\u0435:"hour",\u0447\u0430\u0441\u0438\u043A:"hour",\u0434\u0435\u043D\u044C:"d",\u0434\u043D\u044F:"d",\u0434\u043D\u0435\u0439:"d",\u0441\u0443\u0442\u043E\u043A:"d",\u0441\u0443\u0442\u043A\u0438:"d",\u043D\u0435\u0434\u0435\u043B\u044F:"week",\u043D\u0435\u0434\u0435\u043B\u0435:"week",\u043D\u0435\u0434\u0435\u043B\u0438:"week",\u043D\u0435\u0434\u0435\u043B\u044E:"week",\u043D\u0435\u0434\u0435\u043B\u044C:"week",\u043D\u0435\u0434\u0435\u043B\u044C\u043A\u0435:"week",\u043D\u0435\u0434\u0435\u043B\u044C\u043A\u0438:"week",\u043D\u0435\u0434\u0435\u043B\u0435\u043A:"week",\u043C\u0435\u0441\u044F\u0446:"month",\u043C\u0435\u0441\u044F\u0446\u0435:"month",\u043C\u0435\u0441\u044F\u0446\u0435\u0432:"month",\u043C\u0435\u0441\u044F\u0446\u0430:"month",\u043A\u0432\u0430\u0440\u0442\u0430\u043B:"quarter",\u043A\u0432\u0430\u0440\u0442\u0430\u043B\u0435:"quarter",\u043A\u0432\u0430\u0440\u0442\u0430\u043B\u043E\u0432:"quarter",\u0433\u043E\u0434:"year",\u0433\u043E\u0434\u0430:"year",\u0433\u043E\u0434\u0443:"year",\u0433\u043E\u0434\u043E\u0432:"year",\u043B\u0435\u0442:"year",\u0433\u043E\u0434\u0438\u043A:"year",\u0433\u043E\u0434\u0438\u043A\u0430:"year",\u0433\u043E\u0434\u0438\u043A\u043E\u0432:"year"};se.NUMBER_PATTERN=`(?:${Fu.matchAnyPattern(se.INTEGER_WORD_DICTIONARY)}|[0-9]+|[0-9]+\\.[0-9]+|\u043F\u043E\u043B|\u043D\u0435\u0441\u043A\u043E\u043B\u044C\u043A\u043E|\u043F\u0430\u0440(?:\u044B|\u0443)|\\s{0,3})`;function t_(r){let e=r.toLowerCase();return se.INTEGER_WORD_DICTIONARY[e]!==void 0?se.INTEGER_WORD_DICTIONARY[e]:e.match(/несколько/)?3:e.match(/пол/)?.5:e.match(/пар/)?2:e===""?1:parseFloat(e)}se.parseNumberPattern=t_;se.ORDINAL_NUMBER_PATTERN=`(?:${Fu.matchAnyPattern(se.ORDINAL_WORD_DICTIONARY)}|[0-9]{1,2}(?:\u0433\u043E|\u043E\u0433\u043E|\u0435|\u043E\u0435)?)`;function eA(r){let e=r.toLowerCase();return se.ORDINAL_WORD_DICTIONARY[e]!==void 0?se.ORDINAL_WORD_DICTIONARY[e]:(e=e.replace(/(?:st|nd|rd|th)$/i,""),parseInt(e))}se.parseOrdinalNumberPattern=eA;var Vp="(?:\\s+(?:\u0433\u043E\u0434\u0443|\u0433\u043E\u0434\u0430|\u0433\u043E\u0434|\u0433|\u0433.))?";se.YEAR_PATTERN=`(?:[1-9][0-9]{0,3}${Vp}\\s*(?:\u043D.\u044D.|\u0434\u043E \u043D.\u044D.|\u043D. \u044D.|\u0434\u043E \u043D. \u044D.)|[1-2][0-9]{3}${Vp}|[5-9][0-9]${Vp})`;function tA(r){if(/(год|года|г|г.)/i.test(r)&&(r=r.replace(/(год|года|г|г.)/i,"")),/(до н.э.|до н. э.)/i.test(r))return r=r.replace(/(до н.э.|до н. э.)/i,""),-parseInt(r);if(/(н. э.|н.э.)/i.test(r))return r=r.replace(/(н. э.|н.э.)/i,""),parseInt(r);let e=parseInt(r);return JC.findMostLikelyADYear(e)}se.parseYear=tA;var r_=`(${se.NUMBER_PATTERN})\\s{0,3}(${Fu.matchAnyPattern(se.TIME_UNIT_DICTIONARY)})`,e_=new RegExp(r_,"i");se.TIME_UNITS_PATTERN=Fu.repeatedTimeunitPattern("(?:(?:\u043E\u043A\u043E\u043B\u043E|\u043F\u0440\u0438\u043C\u0435\u0440\u043D\u043E)\\s{0,3})?",r_);function rA(r){let e={},t=r,n=e_.exec(t);for(;n;)nA(e,n),t=t.substring(n[0].length).trim(),n=e_.exec(t);return e}se.parseTimeUnits=rA;function nA(r,e){let t=t_(e[1]),n=se.TIME_UNIT_DICTIONARY[e[2].toLowerCase()];r[n]=t}});var i_=k(Kp=>{"use strict";Object.defineProperty(Kp,"__esModule",{value:!0});var Za=Pt(),iA=We(),sA=V(),n_=`(?:(?:\u043E\u043A\u043E\u043B\u043E|\u043F\u0440\u0438\u043C\u0435\u0440\u043D\u043E)\\s*(?:~\\s*)?)?(${Za.TIME_UNITS_PATTERN})${Za.REGEX_PARTS.rightBoundary}`,aA=new RegExp(`(?:\u0432 \u0442\u0435\u0447\u0435\u043D\u0438\u0435|\u0432 \u0442\u0435\u0447\u0435\u043D\u0438\u0438)\\s*${n_}`,Za.REGEX_PARTS.flags),oA=new RegExp(n_,"i"),zp=class extends sA.AbstractParserWithWordBoundaryChecking{patternLeftBoundary(){return Za.REGEX_PARTS.leftBoundary}innerPattern(e){return e.option.forwardDate?oA:aA}innerExtract(e,t){let n=Za.parseTimeUnits(t[1]);return iA.ParsingComponents.createRelativeFromReference(e.reference,n)}};Kp.default=zp});var l_=k(Xp=>{"use strict";Object.defineProperty(Xp,"__esModule",{value:!0});var uA=ot(),Ja=Pt(),u_=Pt(),Lu=Pt(),lA=Re(),cA=V(),dA=new RegExp(`(?:\u0441)?\\s*(${Lu.ORDINAL_NUMBER_PATTERN})(?:\\s{0,3}(?:\u043F\u043E|-|\u2013|\u0434\u043E)?\\s{0,3}(${Lu.ORDINAL_NUMBER_PATTERN}))?(?:-|\\/|\\s{0,3}(?:of)?\\s{0,3})(${lA.matchAnyPattern(Ja.MONTH_DICTIONARY)})(?:(?:-|\\/|,?\\s{0,3})(${u_.YEAR_PATTERN}(?![^\\s]\\d)))?${Ja.REGEX_PARTS.rightBoundary}`,Ja.REGEX_PARTS.flags),s_=1,a_=2,fA=3,o_=4,Qp=class extends cA.AbstractParserWithWordBoundaryChecking{patternLeftBoundary(){return Ja.REGEX_PARTS.leftBoundary}innerPattern(){return dA}innerExtract(e,t){let n=e.createParsingResult(t.index,t[0]),i=Ja.MONTH_DICTIONARY[t[fA].toLowerCase()],s=Lu.parseOrdinalNumberPattern(t[s_]);if(s>31)return t.index=t.index+t[s_].length,null;if(n.start.assign("month",i),n.start.assign("day",s),t[o_]){let a=u_.parseYear(t[o_]);n.start.assign("year",a)}else{let a=uA.findYearClosestToRef(e.refDate,s,i);n.start.imply("year",a)}if(t[a_]){let a=Lu.parseOrdinalNumberPattern(t[a_]);n.end=n.start.clone(),n.end.assign("day",a)}return n}};Xp.default=Qp});var f_=k(Jp=>{"use strict";Object.defineProperty(Jp,"__esModule",{value:!0});var eo=Pt(),pA=ot(),mA=Re(),d_=Pt(),hA=V(),gA=new RegExp(`((?:\u0432)\\s*)?(${mA.matchAnyPattern(eo.MONTH_DICTIONARY)})\\s*(?:[,-]?\\s*(${d_.YEAR_PATTERN})?)?(?=[^\\s\\w]|\\s+[^0-9]|\\s+$|$)`,eo.REGEX_PARTS.flags),yA=2,c_=3,Zp=class extends hA.AbstractParserWithWordBoundaryChecking{patternLeftBoundary(){return eo.REGEX_PARTS.leftBoundary}innerPattern(){return gA}innerExtract(e,t){let n=t[yA].toLowerCase();if(t[0].length<=3&&!eo.FULL_MONTH_NAME_DICTIONARY[n])return null;let i=e.createParsingResult(t.index,t.index+t[0].length);i.start.imply("day",1);let s=eo.MONTH_DICTIONARY[n];if(i.start.assign("month",s),t[c_]){let a=d_.parseYear(t[c_]);i.start.assign("year",a)}else{let a=pA.findYearClosestToRef(e.refDate,1,s);i.start.imply("year",a)}return i}};Jp.default=Zp});var m_=k(tm=>{"use strict";Object.defineProperty(tm,"__esModule",{value:!0});var Uu=ze(),bA=ci(),p_=Pt(),em=class extends bA.AbstractTimeExpressionParser{constructor(e){super(e)}patternFlags(){return p_.REGEX_PARTS.flags}primaryPatternLeftBoundary(){return"(^|\\s|T|(?:[^\\p{L}\\p{N}_]))"}followingPhase(){return"\\s*(?:\\-|\\\u2013|\\~|\\\u301C|\u0434\u043E|\u0438|\u043F\u043E|\\?)\\s*"}primaryPrefix(){return"(?:(?:\u0432|\u0441)\\s*)??"}primarySuffix(){return`(?:\\s*(?:\u0443\u0442\u0440\u0430|\u0432\u0435\u0447\u0435\u0440\u0430|\u043F\u043E\u0441\u043B\u0435 \u043F\u043E\u043B\u0443\u0434\u043D\u044F))?(?!\\/)${p_.REGEX_PARTS.rightBoundary}`}extractPrimaryTimeComponents(e,t){let n=super.extractPrimaryTimeComponents(e,t);if(n){if(t[0].endsWith("\u0432\u0435\u0447\u0435\u0440\u0430")){let i=n.get("hour");i>=6&&i<12?(n.assign("hour",n.get("hour")+12),n.assign("meridiem",Uu.Meridiem.PM)):i<6&&n.assign("meridiem",Uu.Meridiem.AM)}if(t[0].endsWith("\u043F\u043E\u0441\u043B\u0435 \u043F\u043E\u043B\u0443\u0434\u043D\u044F")){n.assign("meridiem",Uu.Meridiem.PM);let i=n.get("hour");i>=0&&i<=6&&n.assign("hour",n.get("hour")+12)}t[0].endsWith("\u0443\u0442\u0440\u0430")&&(n.assign("meridiem",Uu.Meridiem.AM),n.get("hour")<12&&n.assign("hour",n.get("hour")))}return n}};tm.default=em});var h_=k(nm=>{"use strict";Object.defineProperty(nm,"__esModule",{value:!0});var Wu=Pt(),TA=We(),_A=V(),vA=sr(),wA=new RegExp(`(${Wu.TIME_UNITS_PATTERN})\\s{0,5}\u043D\u0430\u0437\u0430\u0434(?=(?:\\W|$))`,Wu.REGEX_PARTS.flags),rm=class extends _A.AbstractParserWithWordBoundaryChecking{patternLeftBoundary(){return Wu.REGEX_PARTS.leftBoundary}innerPattern(){return wA}innerExtract(e,t){let n=Wu.parseTimeUnits(t[1]),i=vA.reverseTimeUnits(n);return TA.ParsingComponents.createRelativeFromReference(e.reference,i)}};nm.default=rm});var g_=k(to=>{"use strict";var kA=to&&to.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(to,"__esModule",{value:!0});var EA=kA(Gr()),im=class extends EA.default{patternBetween(){return/^\s*(и до|и по|до|по|-)\s*$/i}};to.default=im});var y_=k(ro=>{"use strict";var SA=ro&&ro.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(ro,"__esModule",{value:!0});var OA=SA(cn()),sm=class extends OA.default{patternBetween(){return new RegExp("^\\s*(T|\u0432|,|-)?\\s*$")}};ro.default=sm});var b_=k(bn=>{"use strict";var DA=bn&&bn.__createBinding||(Object.create?function(r,e,t,n){n===void 0&&(n=t),Object.defineProperty(r,n,{enumerable:!0,get:function(){return e[t]}})}:function(r,e,t,n){n===void 0&&(n=t),r[n]=e[t]}),xA=bn&&bn.__setModuleDefault||(Object.create?function(r,e){Object.defineProperty(r,"default",{enumerable:!0,value:e})}:function(r,e){r.default=e}),RA=bn&&bn.__importStar||function(r){if(r&&r.__esModule)return r;var e={};if(r!=null)for(var t in r)t!=="default"&&Object.prototype.hasOwnProperty.call(r,t)&&DA(e,r,t);return xA(e,r),e};Object.defineProperty(bn,"__esModule",{value:!0});var MA=V(),no=RA(pn()),am=Pt(),CA=new RegExp(`(?:\u0441|\u0441\u043E)?\\s*(\u0441\u0435\u0433\u043E\u0434\u043D\u044F|\u0432\u0447\u0435\u0440\u0430|\u0437\u0430\u0432\u0442\u0440\u0430|\u043F\u043E\u0441\u043B\u0435\u0437\u0430\u0432\u0442\u0440\u0430|\u043F\u043E\u0437\u0430\u0432\u0447\u0435\u0440\u0430)${am.REGEX_PARTS.rightBoundary}`,am.REGEX_PARTS.flags),om=class extends MA.AbstractParserWithWordBoundaryChecking{patternLeftBoundary(){return am.REGEX_PARTS.leftBoundary}innerPattern(e){return CA}innerExtract(e,t){let n=t[1].toLowerCase(),i=e.createParsingComponents();switch(n){case"\u0441\u0435\u0433\u043E\u0434\u043D\u044F":return no.today(e.reference);case"\u0432\u0447\u0435\u0440\u0430":return no.yesterday(e.reference);case"\u0437\u0430\u0432\u0442\u0440\u0430":return no.tomorrow(e.reference);case"\u043F\u043E\u0441\u043B\u0435\u0437\u0430\u0432\u0442\u0440\u0430":return no.theDayAfter(e.reference,2);case"\u043F\u043E\u0437\u0430\u0432\u0447\u0435\u0440\u0430":return no.theDayBefore(e.reference,2)}return i}};bn.default=om});var T_=k(xr=>{"use strict";var AA=xr&&xr.__createBinding||(Object.create?function(r,e,t,n){n===void 0&&(n=t),Object.defineProperty(r,n,{enumerable:!0,get:function(){return e[t]}})}:function(r,e,t,n){n===void 0&&(n=t),r[n]=e[t]}),PA=xr&&xr.__setModuleDefault||(Object.create?function(r,e){Object.defineProperty(r,"default",{enumerable:!0,value:e})}:function(r,e){r.default=e}),NA=xr&&xr.__importStar||function(r){if(r&&r.__esModule)return r;var e={};if(r!=null)for(var t in r)t!=="default"&&Object.prototype.hasOwnProperty.call(r,t)&&AA(e,r,t);return PA(e,r),e},IA=xr&&xr.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(xr,"__esModule",{value:!0});var FA=V(),bi=NA(pn()),LA=ir(),UA=IA(we()),um=Pt(),WA=new RegExp(`(\u0441\u0435\u0439\u0447\u0430\u0441|\u043F\u0440\u043E\u0448\u043B\u044B\u043C\\s*\u0432\u0435\u0447\u0435\u0440\u043E\u043C|\u043F\u0440\u043E\u0448\u043B\u043E\u0439\\s*\u043D\u043E\u0447\u044C\u044E|\u0441\u043B\u0435\u0434\u0443\u044E\u0449\u0435\u0439\\s*\u043D\u043E\u0447\u044C\u044E|\u0441\u0435\u0433\u043E\u0434\u043D\u044F\\s*\u043D\u043E\u0447\u044C\u044E|\u044D\u0442\u043E\u0439\\s*\u043D\u043E\u0447\u044C\u044E|\u043D\u043E\u0447\u044C\u044E|\u044D\u0442\u0438\u043C \u0443\u0442\u0440\u043E\u043C|\u0443\u0442\u0440\u043E\u043C|\u0443\u0442\u0440\u0430|\u0432\\s*\u043F\u043E\u043B\u0434\u0435\u043D\u044C|\u0432\u0435\u0447\u0435\u0440\u043E\u043C|\u0432\u0435\u0447\u0435\u0440\u0430|\u0432\\s*\u043F\u043E\u043B\u043D\u043E\u0447\u044C)${um.REGEX_PARTS.rightBoundary}`,um.REGEX_PARTS.flags),lm=class extends FA.AbstractParserWithWordBoundaryChecking{patternLeftBoundary(){return um.REGEX_PARTS.leftBoundary}innerPattern(){return WA}innerExtract(e,t){let n=UA.default(e.refDate),i=t[0].toLowerCase(),s=e.createParsingComponents();if(i==="\u0441\u0435\u0439\u0447\u0430\u0441")return bi.now(e.reference);if(i==="\u0432\u0435\u0447\u0435\u0440\u043E\u043C"||i==="\u0432\u0435\u0447\u0435\u0440\u0430")return bi.evening(e.reference);if(i.endsWith("\u0443\u0442\u0440\u043E\u043C")||i.endsWith("\u0443\u0442\u0440\u0430"))return bi.morning(e.reference);if(i.match(/в\s*полдень/))return bi.noon(e.reference);if(i.match(/прошлой\s*ночью/))return bi.lastNight(e.reference);if(i.match(/прошлым\s*вечером/))return bi.yesterdayEvening(e.reference);if(i.match(/следующей\s*ночью/)){let a=n.hour()<22?1:2;n=n.add(a,"day"),LA.assignSimilarDate(s,n),s.imply("hour",0)}return i.match(/в\s*полночь/)||i.endsWith("\u043D\u043E\u0447\u044C\u044E")?bi.midnight(e.reference):s}};xr.default=lm});var __=k(dm=>{"use strict";Object.defineProperty(dm,"__esModule",{value:!0});var io=Pt(),qA=Re(),$A=V(),jA=fi(),GA=new RegExp(`(?:(?:,|\\(|\uFF08)\\s*)?(?:\u0432\\s*?)?(?:(\u044D\u0442\u0443|\u044D\u0442\u043E\u0442|\u043F\u0440\u043E\u0448\u043B\u044B\u0439|\u043F\u0440\u043E\u0448\u043B\u0443\u044E|\u0441\u043B\u0435\u0434\u0443\u044E\u0449\u0438\u0439|\u0441\u043B\u0435\u0434\u0443\u044E\u0449\u0443\u044E|\u0441\u043B\u0435\u0434\u0443\u044E\u0449\u0435\u0433\u043E)\\s*)?(${qA.matchAnyPattern(io.WEEKDAY_DICTIONARY)})(?:\\s*(?:,|\\)|\uFF09))?(?:\\s*\u043D\u0430\\s*(\u044D\u0442\u043E\u0439|\u043F\u0440\u043E\u0448\u043B\u043E\u0439|\u0441\u043B\u0435\u0434\u0443\u044E\u0449\u0435\u0439)\\s*\u043D\u0435\u0434\u0435\u043B\u0435)?${io.REGEX_PARTS.rightBoundary}`,io.REGEX_PARTS.flags),YA=1,BA=2,HA=3,cm=class extends $A.AbstractParserWithWordBoundaryChecking{innerPattern(){return GA}patternLeftBoundary(){return io.REGEX_PARTS.leftBoundary}innerExtract(e,t){let n=t[BA].toLowerCase(),i=io.WEEKDAY_DICTIONARY[n],s=t[YA],a=t[HA],o=s||a;o=o||"",o=o.toLowerCase();let u=null;o=="\u043F\u0440\u043E\u0448\u043B\u044B\u0439"||o=="\u043F\u0440\u043E\u0448\u043B\u0443\u044E"||o=="\u043F\u0440\u043E\u0448\u043B\u043E\u0439"?u="last":o=="\u0441\u043B\u0435\u0434\u0443\u044E\u0449\u0438\u0439"||o=="\u0441\u043B\u0435\u0434\u0443\u044E\u0449\u0443\u044E"||o=="\u0441\u043B\u0435\u0434\u0443\u044E\u0449\u0435\u0439"||o=="\u0441\u043B\u0435\u0434\u0443\u044E\u0449\u0435\u0433\u043E"?u="next":(o=="\u044D\u0442\u043E\u0442"||o=="\u044D\u0442\u0443"||o=="\u044D\u0442\u043E\u0439")&&(u="this");let l=jA.toDayJSWeekday(e.refDate,i,u);return e.createParsingComponents().assign("weekday",i).imply("day",l.date()).imply("month",l.month()+1).imply("year",l.year())}};dm.default=cm});var w_=k(ao=>{"use strict";var VA=ao&&ao.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(ao,"__esModule",{value:!0});var so=Pt(),v_=We(),zA=VA(we()),KA=V(),QA=Re(),XA=new RegExp(`(\u0432 \u043F\u0440\u043E\u0448\u043B\u043E\u043C|\u043D\u0430 \u043F\u0440\u043E\u0448\u043B\u043E\u0439|\u043D\u0430 \u0441\u043B\u0435\u0434\u0443\u044E\u0449\u0435\u0439|\u0432 \u0441\u043B\u0435\u0434\u0443\u044E\u0449\u0435\u043C|\u043D\u0430 \u044D\u0442\u043E\u0439|\u0432 \u044D\u0442\u043E\u043C)\\s*(${QA.matchAnyPattern(so.TIME_UNIT_DICTIONARY)})(?=\\s*)${so.REGEX_PARTS.rightBoundary}`,so.REGEX_PARTS.flags),ZA=1,JA=2,fm=class extends KA.AbstractParserWithWordBoundaryChecking{patternLeftBoundary(){return so.REGEX_PARTS.leftBoundary}innerPattern(){return XA}innerExtract(e,t){let n=t[ZA].toLowerCase(),i=t[JA].toLowerCase(),s=so.TIME_UNIT_DICTIONARY[i];if(n=="\u043D\u0430 \u0441\u043B\u0435\u0434\u0443\u044E\u0449\u0435\u0439"||n=="\u0432 \u0441\u043B\u0435\u0434\u0443\u044E\u0449\u0435\u043C"){let u={};return u[s]=1,v_.ParsingComponents.createRelativeFromReference(e.reference,u)}if(n=="\u0432 \u043F\u0440\u043E\u0448\u043B\u043E\u043C"||n=="\u043D\u0430 \u043F\u0440\u043E\u0448\u043B\u043E\u0439"){let u={};return u[s]=-1,v_.ParsingComponents.createRelativeFromReference(e.reference,u)}let a=e.createParsingComponents(),o=zA.default(e.reference.instant);return s.match(/week/i)?(o=o.add(-o.get("d"),"d"),a.imply("day",o.date()),a.imply("month",o.month()+1),a.imply("year",o.year())):s.match(/month/i)?(o=o.add(-o.date()+1,"d"),a.imply("day",o.date()),a.assign("year",o.year()),a.assign("month",o.month()+1)):s.match(/year/i)&&(o=o.add(-o.date()+1,"d"),o=o.add(-o.month(),"month"),a.imply("day",o.date()),a.imply("month",o.month()+1),a.assign("year",o.year())),a}};ao.default=fm});var k_=k(mm=>{"use strict";Object.defineProperty(mm,"__esModule",{value:!0});var oo=Pt(),eP=We(),tP=V(),rP=sr(),nP=new RegExp(`(\u044D\u0442\u0438|\u043F\u043E\u0441\u043B\u0435\u0434\u043D\u0438\u0435|\u043F\u0440\u043E\u0448\u043B\u044B\u0435|\u0441\u043B\u0435\u0434\u0443\u044E\u0449\u0438\u0435|\u043F\u043E\u0441\u043B\u0435|\u0447\u0435\u0440\u0435\u0437|\\+|-)\\s*(${oo.TIME_UNITS_PATTERN})${oo.REGEX_PARTS.rightBoundary}`,oo.REGEX_PARTS.flags),pm=class extends tP.AbstractParserWithWordBoundaryChecking{patternLeftBoundary(){return oo.REGEX_PARTS.leftBoundary}innerPattern(){return nP}innerExtract(e,t){let n=t[1].toLowerCase(),i=oo.parseTimeUnits(t[2]);switch(n){case"\u043F\u043E\u0441\u043B\u0435\u0434\u043D\u0438\u0435":case"\u043F\u0440\u043E\u0448\u043B\u044B\u0435":case"-":i=rP.reverseTimeUnits(i);break}return eP.ParsingComponents.createRelativeFromReference(e.reference,i)}};mm.default=pm});var O_=k(nt=>{"use strict";var Bt=nt&&nt.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(nt,"__esModule",{value:!0});nt.createConfiguration=nt.createCasualConfiguration=nt.parseDate=nt.parse=nt.strict=nt.casual=void 0;var iP=Bt(i_()),sP=Bt(l_()),aP=Bt(f_()),oP=Bt(m_()),uP=Bt(h_()),lP=Bt(g_()),cP=Bt(y_()),dP=dn(),fP=Bt(b_()),pP=Bt(T_()),mP=Bt(__()),hP=Bt(w_()),E_=kr(),gP=Bt(pi()),yP=Bt(k_());nt.casual=new E_.Chrono(S_());nt.strict=new E_.Chrono(hm(!0));function bP(r,e,t){return nt.casual.parse(r,e,t)}nt.parse=bP;function TP(r,e,t){return nt.casual.parseDate(r,e,t)}nt.parseDate=TP;function S_(){let r=hm(!1);return r.parsers.unshift(new fP.default),r.parsers.unshift(new pP.default),r.parsers.unshift(new aP.default),r.parsers.unshift(new hP.default),r.parsers.unshift(new yP.default),r}nt.createCasualConfiguration=S_;function hm(r=!0){return dP.includeCommonConfiguration({parsers:[new gP.default(!0),new iP.default,new sP.default,new mP.default,new oP.default(r),new uP.default],refiners:[new cP.default,new lP.default]},r)}nt.createConfiguration=hm});var ze=k(pe=>{"use strict";var _P=pe&&pe.__createBinding||(Object.create?function(r,e,t,n){n===void 0&&(n=t),Object.defineProperty(r,n,{enumerable:!0,get:function(){return e[t]}})}:function(r,e,t,n){n===void 0&&(n=t),r[n]=e[t]}),vP=pe&&pe.__setModuleDefault||(Object.create?function(r,e){Object.defineProperty(r,"default",{enumerable:!0,value:e})}:function(r,e){r.default=e}),Gn=pe&&pe.__importStar||function(r){if(r&&r.__esModule)return r;var e={};if(r!=null)for(var t in r)t!=="default"&&Object.prototype.hasOwnProperty.call(r,t)&&_P(e,r,t);return vP(e,r),e};Object.defineProperty(pe,"__esModule",{value:!0});pe.parseDate=pe.parse=pe.casual=pe.strict=pe.ru=pe.zh=pe.nl=pe.pt=pe.ja=pe.fr=pe.de=pe.Meridiem=pe.Chrono=pe.en=void 0;var gm=Gn(Ad());pe.en=gm;var wP=kr();Object.defineProperty(pe,"Chrono",{enumerable:!0,get:function(){return wP.Chrono}});var kP;(function(r){r[r.AM=0]="AM",r[r.PM=1]="PM"})(kP=pe.Meridiem||(pe.Meridiem={}));var EP=Gn(Iy());pe.de=EP;var SP=Gn(cb());pe.fr=SP;var OP=Gn(Tb());pe.ja=OP;var DP=Gn(Ib());pe.pt=DP;var xP=Gn(TT());pe.nl=xP;var RP=Gn(JT());pe.zh=RP;var MP=Gn(O_());pe.ru=MP;pe.strict=gm.strict;pe.casual=gm.casual;function CP(r,e,t){return pe.casual.parse(r,e,t)}pe.parse=CP;function AP(r,e,t){return pe.casual.parseDate(r,e,t)}pe.parseDate=AP});var Mv=k((xv,Rv)=>{(function(r){var e=Object.hasOwnProperty,t=Array.isArray?Array.isArray:function(h){return Object.prototype.toString.call(h)==="[object Array]"},n=10,i=typeof process=="object"&&typeof process.nextTick=="function",s=typeof Symbol=="function",a=typeof Reflect=="object",o=typeof setImmediate=="function",u=o?setImmediate:setTimeout,l=s?a&&typeof Reflect.ownKeys=="function"?Reflect.ownKeys:function(f){var h=Object.getOwnPropertyNames(f);return h.push.apply(h,Object.getOwnPropertySymbols(f)),h}:Object.keys;function c(){this._events={},this._conf&&d.call(this,this._conf)}function d(f){f&&(this._conf=f,f.delimiter&&(this.delimiter=f.delimiter),f.maxListeners!==r&&(this._maxListeners=f.maxListeners),f.wildcard&&(this.wildcard=f.wildcard),f.newListener&&(this._newListener=f.newListener),f.removeListener&&(this._removeListener=f.removeListener),f.verboseMemoryLeak&&(this.verboseMemoryLeak=f.verboseMemoryLeak),f.ignoreErrors&&(this.ignoreErrors=f.ignoreErrors),this.wildcard&&(this.listenerTree={}))}function p(f,h){var g="(node) warning: possible EventEmitter memory leak detected. "+f+" listeners added. Use emitter.setMaxListeners() to increase limit.";if(this.verboseMemoryLeak&&(g+=" Event name: "+h+"."),typeof process!="undefined"&&process.emitWarning){var T=new Error(g);T.name="MaxListenersExceededWarning",T.emitter=this,T.count=f,process.emitWarning(T)}else console.error(g),console.trace&&console.trace()}var m=function(f,h,g){var T=arguments.length;switch(T){case 0:return[];case 1:return[f];case 2:return[f,h];case 3:return[f,h,g];default:for(var w=new Array(T);T--;)w[T]=arguments[T];return w}};function y(f,h){for(var g={},T,w=f.length,O=h?h.length:0,M=0;M<w;M++)T=f[M],g[T]=M<O?h[M]:r;return g}function _(f,h,g){this._emitter=f,this._target=h,this._listeners={},this._listenersCount=0;var T,w;if((g.on||g.off)&&(T=g.on,w=g.off),h.addEventListener?(T=h.addEventListener,w=h.removeEventListener):h.addListener?(T=h.addListener,w=h.removeListener):h.on&&(T=h.on,w=h.off),!T&&!w)throw Error("target does not implement any known event API");if(typeof T!="function")throw TypeError("on method must be a function");if(typeof w!="function")throw TypeError("off method must be a function");this._on=T,this._off=w;var O=f._observers;O?O.push(this):f._observers=[this]}Object.assign(_.prototype,{subscribe:function(f,h,g){var T=this,w=this._target,O=this._emitter,M=this._listeners,A=function(){var v=m.apply(null,arguments),x={data:v,name:h,original:f};if(g){var N=g.call(w,x);N!==!1&&O.emit.apply(O,[x.name].concat(v));return}O.emit.apply(O,[h].concat(v))};if(M[f])throw Error("Event '"+f+"' is already listening");this._listenersCount++,O._newListener&&O._removeListener&&!T._onNewListener?(this._onNewListener=function(v){v===h&&M[f]===null&&(M[f]=A,T._on.call(w,f,A))},O.on("newListener",this._onNewListener),this._onRemoveListener=function(v){v===h&&!O.hasListeners(v)&&M[f]&&(M[f]=null,T._off.call(w,f,A))},M[f]=null,O.on("removeListener",this._onRemoveListener)):(M[f]=A,T._on.call(w,f,A))},unsubscribe:function(f){var h=this,g=this._listeners,T=this._emitter,w,O,M=this._off,A=this._target,v;if(f&&typeof f!="string")throw TypeError("event must be a string");function x(){h._onNewListener&&(T.off("newListener",h._onNewListener),T.off("removeListener",h._onRemoveListener),h._onNewListener=null,h._onRemoveListener=null);var N=te.call(T,h);T._observers.splice(N,1)}if(f){if(w=g[f],!w)return;M.call(A,f,w),delete g[f],--this._listenersCount||x()}else{for(O=l(g),v=O.length;v-- >0;)f=O[v],M.call(A,f,g[f]);this._listeners={},this._listenersCount=0,x()}}});function b(f,h,g,T){var w=Object.assign({},h);if(!f)return w;if(typeof f!="object")throw TypeError("options must be an object");var O=Object.keys(f),M=O.length,A,v,x;function N(le){throw Error('Invalid "'+A+'" option value'+(le?". Reason: "+le:""))}for(var re=0;re<M;re++){if(A=O[re],!T&&!e.call(h,A))throw Error('Unknown "'+A+'" option');v=f[A],v!==r&&(x=g[A],w[A]=x?x(v,N):v)}return w}function E(f,h){return(typeof f!="function"||!f.hasOwnProperty("prototype"))&&h("value must be a constructor"),f}function R(f){var h="value must be type of "+f.join("|"),g=f.length,T=f[0],w=f[1];return g===1?function(O,M){if(typeof O===T)return O;M(h)}:g===2?function(O,M){var A=typeof O;if(A===T||A===w)return O;M(h)}:function(O,M){for(var A=typeof O,v=g;v-- >0;)if(A===f[v])return O;M(h)}}var S=R(["function"]),F=R(["object","function"]);function q(f,h,g){var T,w,O=0,M,A=new f(function(v,x,N){g=b(g,{timeout:0,overload:!1},{timeout:function(Ne,Ce){return Ne*=1,(typeof Ne!="number"||Ne<0||!Number.isFinite(Ne))&&Ce("timeout must be a positive number"),Ne}}),T=!g.overload&&typeof f.prototype.cancel=="function"&&typeof N=="function";function re(){w&&(w=null),O&&(clearTimeout(O),O=0)}var le=function(Ne){re(),v(Ne)},fe=function(Ne){re(),x(Ne)};T?h(le,fe,N):(w=[function(Ne){fe(Ne||Error("canceled"))}],h(le,fe,function(Ne){if(M)throw Error("Unable to subscribe on cancel event asynchronously");if(typeof Ne!="function")throw TypeError("onCancel callback must be a function");w.push(Ne)}),M=!0),g.timeout>0&&(O=setTimeout(function(){var Ne=Error("timeout");Ne.code="ETIMEDOUT",O=0,A.cancel(Ne),x(Ne)},g.timeout))});return T||(A.cancel=function(v){if(!!w){for(var x=w.length,N=1;N<x;N++)w[N](v);w[0](v),w=null}}),A}function te(f){var h=this._observers;if(!h)return-1;for(var g=h.length,T=0;T<g;T++)if(h[T]._target===f)return T;return-1}function G(f,h,g,T,w){if(!g)return null;if(T===0){var O=typeof h;if(O==="string"){var M,A,v=0,x=0,N=this.delimiter,re=N.length;if((A=h.indexOf(N))!==-1){M=new Array(5);do M[v++]=h.slice(x,A),x=A+re;while((A=h.indexOf(N,x))!==-1);M[v++]=h.slice(x),h=M,w=v}else h=[h],w=1}else O==="object"?w=h.length:(h=[h],w=1)}var le=null,fe,Ne,Ce,Mt,an,Wr=h[T],Y=h[T+1],jt,Qe;if(T===w)g._listeners&&(typeof g._listeners=="function"?(f&&f.push(g._listeners),le=[g]):(f&&f.push.apply(f,g._listeners),le=[g]));else if(Wr==="*"){for(jt=l(g),A=jt.length;A-- >0;)fe=jt[A],fe!=="_listeners"&&(Qe=G(f,h,g[fe],T+1,w),Qe&&(le?le.push.apply(le,Qe):le=Qe));return le}else if(Wr==="**"){for(an=T+1===w||T+2===w&&Y==="*",an&&g._listeners&&(le=G(f,h,g,w,w)),jt=l(g),A=jt.length;A-- >0;)fe=jt[A],fe!=="_listeners"&&(fe==="*"||fe==="**"?(g[fe]._listeners&&!an&&(Qe=G(f,h,g[fe],w,w),Qe&&(le?le.push.apply(le,Qe):le=Qe)),Qe=G(f,h,g[fe],T,w)):fe===Y?Qe=G(f,h,g[fe],T+2,w):Qe=G(f,h,g[fe],T,w),Qe&&(le?le.push.apply(le,Qe):le=Qe));return le}else g[Wr]&&(le=G(f,h,g[Wr],T+1,w));if(Ne=g["*"],Ne&&G(f,h,Ne,T+1,w),Ce=g["**"],Ce)if(T<w)for(Ce._listeners&&G(f,h,Ce,w,w),jt=l(Ce),A=jt.length;A-- >0;)fe=jt[A],fe!=="_listeners"&&(fe===Y?G(f,h,Ce[fe],T+2,w):fe===Wr?G(f,h,Ce[fe],T+1,w):(Mt={},Mt[fe]=Ce[fe],G(f,h,{"**":Mt},T+1,w)));else Ce._listeners?G(f,h,Ce,w,w):Ce["*"]&&Ce["*"]._listeners&&G(f,h,Ce["*"],w,w);return le}function H(f,h,g){var T=0,w=0,O,M=this.delimiter,A=M.length,v;if(typeof f=="string")if((O=f.indexOf(M))!==-1){v=new Array(5);do v[T++]=f.slice(w,O),w=O+A;while((O=f.indexOf(M,w))!==-1);v[T++]=f.slice(w)}else v=[f],T=1;else v=f,T=f.length;if(T>1){for(O=0;O+1<T;O++)if(v[O]==="**"&&v[O+1]==="**")return}var x=this.listenerTree,N;for(O=0;O<T;O++)if(N=v[O],x=x[N]||(x[N]={}),O===T-1)return x._listeners?(typeof x._listeners=="function"&&(x._listeners=[x._listeners]),g?x._listeners.unshift(h):x._listeners.push(h),!x._listeners.warned&&this._maxListeners>0&&x._listeners.length>this._maxListeners&&(x._listeners.warned=!0,p.call(this,x._listeners.length,N))):x._listeners=h,!0;return!0}function be(f,h,g,T){for(var w=l(f),O=w.length,M,A,v,x=f._listeners,N;O-- >0;)A=w[O],M=f[A],A==="_listeners"?v=g:v=g?g.concat(A):[A],N=T||typeof A=="symbol",x&&h.push(N?v:v.join(this.delimiter)),typeof M=="object"&&be.call(this,M,h,v,N);return h}function Pe(f){for(var h=l(f),g=h.length,T,w,O;g-- >0;)w=h[g],T=f[w],T&&(O=!0,w!=="_listeners"&&!Pe(T)&&delete f[w]);return O}function j(f,h,g){this.emitter=f,this.event=h,this.listener=g}j.prototype.off=function(){return this.emitter.off(this.event,this.listener),this};function $(f,h,g){if(g===!0)w=!0;else if(g===!1)T=!0;else{if(!g||typeof g!="object")throw TypeError("options should be an object or true");var T=g.async,w=g.promisify,O=g.nextTick,M=g.objectify}if(T||O||w){var A=h,v=h._origin||h;if(O&&!i)throw Error("process.nextTick is not supported");w===r&&(w=h.constructor.name==="AsyncFunction"),h=function(){var x=arguments,N=this,re=this.event;return w?O?Promise.resolve():new Promise(function(le){u(le)}).then(function(){return N.event=re,A.apply(N,x)}):(O?process.nextTick:u)(function(){N.event=re,A.apply(N,x)})},h._async=!0,h._origin=v}return[h,M?new j(this,f,h):this]}function D(f){this._events={},this._newListener=!1,this._removeListener=!1,this.verboseMemoryLeak=!1,d.call(this,f)}D.EventEmitter2=D,D.prototype.listenTo=function(f,h,g){if(typeof f!="object")throw TypeError("target musts be an object");var T=this;g=b(g,{on:r,off:r,reducers:r},{on:S,off:S,reducers:F});function w(O){if(typeof O!="object")throw TypeError("events must be an object");var M=g.reducers,A=te.call(T,f),v;A===-1?v=new _(T,f,g):v=T._observers[A];for(var x=l(O),N=x.length,re,le=typeof M=="function",fe=0;fe<N;fe++)re=x[fe],v.subscribe(re,O[re]||re,le?M:M&&M[re])}return t(h)?w(y(h)):w(typeof h=="string"?y(h.split(/\s+/)):h),this},D.prototype.stopListeningTo=function(f,h){var g=this._observers;if(!g)return!1;var T=g.length,w,O=!1;if(f&&typeof f!="object")throw TypeError("target should be an object");for(;T-- >0;)w=g[T],(!f||w._target===f)&&(w.unsubscribe(h),O=!0);return O},D.prototype.delimiter=".",D.prototype.setMaxListeners=function(f){f!==r&&(this._maxListeners=f,this._conf||(this._conf={}),this._conf.maxListeners=f)},D.prototype.getMaxListeners=function(){return this._maxListeners},D.prototype.event="",D.prototype.once=function(f,h,g){return this._once(f,h,!1,g)},D.prototype.prependOnceListener=function(f,h,g){return this._once(f,h,!0,g)},D.prototype._once=function(f,h,g,T){return this._many(f,1,h,g,T)},D.prototype.many=function(f,h,g,T){return this._many(f,h,g,!1,T)},D.prototype.prependMany=function(f,h,g,T){return this._many(f,h,g,!0,T)},D.prototype._many=function(f,h,g,T,w){var O=this;if(typeof g!="function")throw new Error("many only accepts instances of Function");function M(){return--h===0&&O.off(f,M),g.apply(this,arguments)}return M._origin=g,this._on(f,M,T,w)},D.prototype.emit=function(){if(!this._events&&!this._all)return!1;this._events||c.call(this);var f=arguments[0],h,g=this.wildcard,T,w,O,M,A;if(f==="newListener"&&!this._newListener&&!this._events.newListener)return!1;if(g&&(h=f,f!=="newListener"&&f!=="removeListener"&&typeof f=="object")){if(w=f.length,s){for(O=0;O<w;O++)if(typeof f[O]=="symbol"){A=!0;break}}A||(f=f.join(this.delimiter))}var v=arguments.length,x;if(this._all&&this._all.length)for(x=this._all.slice(),O=0,w=x.length;O<w;O++)switch(this.event=f,v){case 1:x[O].call(this,f);break;case 2:x[O].call(this,f,arguments[1]);break;case 3:x[O].call(this,f,arguments[1],arguments[2]);break;default:x[O].apply(this,arguments)}if(g)x=[],G.call(this,x,h,this.listenerTree,0,w);else if(x=this._events[f],typeof x=="function"){switch(this.event=f,v){case 1:x.call(this);break;case 2:x.call(this,arguments[1]);break;case 3:x.call(this,arguments[1],arguments[2]);break;default:for(T=new Array(v-1),M=1;M<v;M++)T[M-1]=arguments[M];x.apply(this,T)}return!0}else x&&(x=x.slice());if(x&&x.length){if(v>3)for(T=new Array(v-1),M=1;M<v;M++)T[M-1]=arguments[M];for(O=0,w=x.length;O<w;O++)switch(this.event=f,v){case 1:x[O].call(this);break;case 2:x[O].call(this,arguments[1]);break;case 3:x[O].call(this,arguments[1],arguments[2]);break;default:x[O].apply(this,T)}return!0}else if(!this.ignoreErrors&&!this._all&&f==="error")throw arguments[1]instanceof Error?arguments[1]:new Error("Uncaught, unspecified 'error' event.");return!!this._all},D.prototype.emitAsync=function(){if(!this._events&&!this._all)return!1;this._events||c.call(this);var f=arguments[0],h=this.wildcard,g,T,w,O,M,A;if(f==="newListener"&&!this._newListener&&!this._events.newListener)return Promise.resolve([!1]);if(h&&(g=f,f!=="newListener"&&f!=="removeListener"&&typeof f=="object")){if(O=f.length,s){for(M=0;M<O;M++)if(typeof f[M]=="symbol"){T=!0;break}}T||(f=f.join(this.delimiter))}var v=[],x=arguments.length,N;if(this._all)for(M=0,O=this._all.length;M<O;M++)switch(this.event=f,x){case 1:v.push(this._all[M].call(this,f));break;case 2:v.push(this._all[M].call(this,f,arguments[1]));break;case 3:v.push(this._all[M].call(this,f,arguments[1],arguments[2]));break;default:v.push(this._all[M].apply(this,arguments))}if(h?(N=[],G.call(this,N,g,this.listenerTree,0)):N=this._events[f],typeof N=="function")switch(this.event=f,x){case 1:v.push(N.call(this));break;case 2:v.push(N.call(this,arguments[1]));break;case 3:v.push(N.call(this,arguments[1],arguments[2]));break;default:for(w=new Array(x-1),A=1;A<x;A++)w[A-1]=arguments[A];v.push(N.apply(this,w))}else if(N&&N.length){if(N=N.slice(),x>3)for(w=new Array(x-1),A=1;A<x;A++)w[A-1]=arguments[A];for(M=0,O=N.length;M<O;M++)switch(this.event=f,x){case 1:v.push(N[M].call(this));break;case 2:v.push(N[M].call(this,arguments[1]));break;case 3:v.push(N[M].call(this,arguments[1],arguments[2]));break;default:v.push(N[M].apply(this,w))}}else if(!this.ignoreErrors&&!this._all&&f==="error")return arguments[1]instanceof Error?Promise.reject(arguments[1]):Promise.reject("Uncaught, unspecified 'error' event.");return Promise.all(v)},D.prototype.on=function(f,h,g){return this._on(f,h,!1,g)},D.prototype.prependListener=function(f,h,g){return this._on(f,h,!0,g)},D.prototype.onAny=function(f){return this._onAny(f,!1)},D.prototype.prependAny=function(f){return this._onAny(f,!0)},D.prototype.addListener=D.prototype.on,D.prototype._onAny=function(f,h){if(typeof f!="function")throw new Error("onAny only accepts instances of Function");return this._all||(this._all=[]),h?this._all.unshift(f):this._all.push(f),this},D.prototype._on=function(f,h,g,T){if(typeof f=="function")return this._onAny(f,h),this;if(typeof h!="function")throw new Error("on only accepts instances of Function");this._events||c.call(this);var w=this,O;return T!==r&&(O=$.call(this,f,h,T),h=O[0],w=O[1]),this._newListener&&this.emit("newListener",f,h),this.wildcard?(H.call(this,f,h,g),w):(this._events[f]?(typeof this._events[f]=="function"&&(this._events[f]=[this._events[f]]),g?this._events[f].unshift(h):this._events[f].push(h),!this._events[f].warned&&this._maxListeners>0&&this._events[f].length>this._maxListeners&&(this._events[f].warned=!0,p.call(this,this._events[f].length,f))):this._events[f]=h,w)},D.prototype.off=function(f,h){if(typeof h!="function")throw new Error("removeListener only takes instances of Function");var g,T=[];if(this.wildcard){var w=typeof f=="string"?f.split(this.delimiter):f.slice();if(T=G.call(this,null,w,this.listenerTree,0),!T)return this}else{if(!this._events[f])return this;g=this._events[f],T.push({_listeners:g})}for(var O=0;O<T.length;O++){var M=T[O];if(g=M._listeners,t(g)){for(var A=-1,v=0,x=g.length;v<x;v++)if(g[v]===h||g[v].listener&&g[v].listener===h||g[v]._origin&&g[v]._origin===h){A=v;break}if(A<0)continue;return this.wildcard?M._listeners.splice(A,1):this._events[f].splice(A,1),g.length===0&&(this.wildcard?delete M._listeners:delete this._events[f]),this._removeListener&&this.emit("removeListener",f,h),this}else(g===h||g.listener&&g.listener===h||g._origin&&g._origin===h)&&(this.wildcard?delete M._listeners:delete this._events[f],this._removeListener&&this.emit("removeListener",f,h))}return this.listenerTree&&Pe(this.listenerTree),this},D.prototype.offAny=function(f){var h=0,g=0,T;if(f&&this._all&&this._all.length>0){for(T=this._all,h=0,g=T.length;h<g;h++)if(f===T[h])return T.splice(h,1),this._removeListener&&this.emit("removeListenerAny",f),this}else{if(T=this._all,this._removeListener)for(h=0,g=T.length;h<g;h++)this.emit("removeListenerAny",T[h]);this._all=[]}return this},D.prototype.removeListener=D.prototype.off,D.prototype.removeAllListeners=function(f){if(f===r)return!this._events||c.call(this),this;if(this.wildcard){var h=G.call(this,null,f,this.listenerTree,0),g,T;if(!h)return this;for(T=0;T<h.length;T++)g=h[T],g._listeners=null;this.listenerTree&&Pe(this.listenerTree)}else this._events&&(this._events[f]=null);return this},D.prototype.listeners=function(f){var h=this._events,g,T,w,O,M;if(f===r){if(this.wildcard)throw Error("event name required for wildcard emitter");if(!h)return[];for(g=l(h),O=g.length,w=[];O-- >0;)T=h[g[O]],typeof T=="function"?w.push(T):w.push.apply(w,T);return w}else{if(this.wildcard){if(M=this.listenerTree,!M)return[];var A=[],v=typeof f=="string"?f.split(this.delimiter):f.slice();return G.call(this,A,v,M,0),A}return h?(T=h[f],T?typeof T=="function"?[T]:T:[]):[]}},D.prototype.eventNames=function(f){var h=this._events;return this.wildcard?be.call(this,this.listenerTree,[],null,f):h?l(h):[]},D.prototype.listenerCount=function(f){return this.listeners(f).length},D.prototype.hasListeners=function(f){if(this.wildcard){var h=[],g=typeof f=="string"?f.split(this.delimiter):f.slice();return G.call(this,h,g,this.listenerTree,0),h.length>0}var T=this._events,w=this._all;return!!(w&&w.length||T&&(f===r?l(T).length:T[f]))},D.prototype.listenersAny=function(){return this._all?this._all:[]},D.prototype.waitFor=function(f,h){var g=this,T=typeof h;return T==="number"?h={timeout:h}:T==="function"&&(h={filter:h}),h=b(h,{timeout:0,filter:r,handleError:!1,Promise,overload:!1},{filter:S,Promise:E}),q(h.Promise,function(w,O,M){function A(){var v=h.filter;if(!(v&&!v.apply(g,arguments)))if(g.off(f,A),h.handleError){var x=arguments[0];x?O(x):w(m.apply(null,arguments).slice(1))}else w(m.apply(null,arguments))}M(function(){g.off(f,A)}),g._on(f,A,!1)},{timeout:h.timeout,overload:h.overload})};function B(f,h,g){g=b(g,{Promise,timeout:0,overload:!1},{Promise:E});var T=g.Promise;return q(T,function(w,O,M){var A;if(typeof f.addEventListener=="function"){A=function(){w(m.apply(null,arguments))},M(function(){f.removeEventListener(h,A)}),f.addEventListener(h,A,{once:!0});return}var v=function(){x&&f.removeListener("error",x),w(m.apply(null,arguments))},x;h!=="error"&&(x=function(N){f.removeListener(h,v),O(N)},f.once("error",x)),M(function(){x&&f.removeListener("error",x),f.removeListener(h,v)}),f.once(h,v)},{timeout:g.timeout,overload:g.overload})}var I=D.prototype;if(Object.defineProperties(D,{defaultMaxListeners:{get:function(){return I._maxListeners},set:function(f){if(typeof f!="number"||f<0||Number.isNaN(f))throw TypeError("n must be a non-negative number");I._maxListeners=f},enumerable:!0},once:{value:B,writable:!0,configurable:!0}}),Object.defineProperties(I,{_maxListeners:{value:n,writable:!0,configurable:!0},_observers:{value:null,writable:!0,configurable:!0}}),typeof define=="function"&&define.amd)define(function(){return D});else if(typeof xv=="object")Rv.exports=D;else{var Z=new Function("","return this")();Z.EventEmitter2=D}})()});var Lw=k(ph=>{"use strict";Object.defineProperty(ph,"__esModule",{value:!0});var Iw=Symbol("MustacheDataPath");function Nw({target:r,propertyName:e}){return[...r[Iw]||[],e]}function Fw(r,e){return typeof r!="object"?r:new Proxy(r,{get(t,n){let i=t[n];if(i===void 0&&!(n in t)){let s=Nw({target:t,propertyName:n});if(e!=null&&e.handleError)return e.handleError(s),i;throw Error(`Missing Mustache data property: ${s.join(" > ")}`)}return i&&typeof i=="object"?(i[Iw]=Nw({target:t,propertyName:n}),Fw(i,e)):i}})}ph.default=Fw});var tn=k(Fr=>{"use strict";Fr.__esModule=!0;Fr.Tokens=Fr.StructuralCharacters=Fr.Operators=void 0;var uF;(function(r){r.AND="AND",r.OR="OR",r.XOR="XOR",r.NOT="NOT"})(uF=Fr.Operators||(Fr.Operators={}));var lF;(function(r){r.OPEN_PARENTHESIS="(",r.CLOSE_PARENTHESIS=")"})(lF=Fr.StructuralCharacters||(Fr.StructuralCharacters={}));var cF;(function(r){r.IDENTIFIER="IDENTIFIER",r.OPERATOR="OPERATOR",r.STRUCTURAL_CHARACTER="STRUCTURAL_CHARACTER",r.EOF="EOF",r.COMMENT="COMMENT"})(cF=Fr.Tokens||(Fr.Tokens={}))});var _h=k(Us=>{"use strict";Us.__esModule=!0;Us.VALID_TOKENS=Us.OPERATOR_PRECEDENCE=void 0;var qe=tn();Us.OPERATOR_PRECEDENCE={NOT:0,XOR:1,AND:2,OR:3};Us.VALID_TOKENS={identifierOnly:[{name:qe.Tokens.IDENTIFIER},{name:qe.Tokens.STRUCTURAL_CHARACTER,value:qe.StructuralCharacters.OPEN_PARENTHESIS}],identifierOrNot:[{name:qe.Tokens.IDENTIFIER},{name:qe.Tokens.STRUCTURAL_CHARACTER,value:qe.StructuralCharacters.OPEN_PARENTHESIS},{name:qe.Tokens.OPERATOR,value:qe.Operators.NOT}],binaryOperator:[{name:qe.Tokens.OPERATOR,value:qe.Operators.AND},{name:qe.Tokens.OPERATOR,value:qe.Operators.OR},{name:qe.Tokens.OPERATOR,value:qe.Operators.XOR}],binaryOperatorOrClose:[{name:qe.Tokens.OPERATOR,value:qe.Operators.AND},{name:qe.Tokens.OPERATOR,value:qe.Operators.OR},{name:qe.Tokens.OPERATOR,value:qe.Operators.XOR},{name:qe.Tokens.STRUCTURAL_CHARACTER,value:qe.StructuralCharacters.CLOSE_PARENTHESIS}]}});var vh=k(Tt=>{"use strict";Tt.__esModule=!0;Tt.ESCAPE_CHARACTER=Tt.EOL=Tt.COMMENT_DELIMITER=Tt.QUOTED_IDENTIFIER_DELIMITER=Tt.SEPARATORS=Tt.OPERATORS=Tt.STRUCTURAL_CHARACTERS=void 0;var Ws=tn();Tt.STRUCTURAL_CHARACTERS={"(":Ws.StructuralCharacters.OPEN_PARENTHESIS,")":Ws.StructuralCharacters.CLOSE_PARENTHESIS};Tt.OPERATORS={AND:Ws.Operators.AND,OR:Ws.Operators.OR,XOR:Ws.Operators.XOR,NOT:Ws.Operators.NOT};Tt.SEPARATORS=new Set([32,9,10,13].map(function(r){return String.fromCodePoint(r)}));Tt.QUOTED_IDENTIFIER_DELIMITER=String.fromCodePoint(34);Tt.COMMENT_DELIMITER=String.fromCodePoint(35);Tt.EOL=String.fromCodePoint(10);Tt.ESCAPE_CHARACTER=String.fromCodePoint(92)});var Gw=k(_r=>{"use strict";var wh=_r&&_r.__assign||function(){return wh=Object.assign||function(r){for(var e,t=1,n=arguments.length;t<n;t++){e=arguments[t];for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(r[i]=e[i])}return r},wh.apply(this,arguments)};_r.__esModule=!0;_r.getQuotedIdentifier=_r.getComment=_r.createResult=void 0;var jw=tn(),qi=vh(),dF=function(r,e,t){return{token:wh({name:r},e!==null?{value:e}:{}),remainingString:t}};_r.createResult=dF;var fF=function(r){for(var e=r.length,t=0;t<r.length;t+=1){var n=r[t];if(n===qi.EOL){e=t;break}}return(0,_r.createResult)(jw.Tokens.COMMENT,r.slice(0,e),r.slice(e+1))};_r.getComment=fF;var pF=function(r){for(var e=!1,t="",n=null,i=0;i<r.length;i+=1){var s=r[i];if(n===null)s===qi.QUOTED_IDENTIFIER_DELIMITER?e?(t=t.slice(0,-1)+qi.QUOTED_IDENTIFIER_DELIMITER,e=!1):n=i:(s===qi.ESCAPE_CHARACTER?e=!0:e=!1,t=t+=s);else{if(!qi.SEPARATORS.has(s)&&!qi.STRUCTURAL_CHARACTERS[s])throw new Error("Unexpected character: ".concat(s," Expected ) character or separator"));break}}if(n===null)throw new Error("Unexpected end of expression: expected ".concat(qi.QUOTED_IDENTIFIER_DELIMITER," character"));return(0,_r.createResult)(jw.Tokens.IDENTIFIER,t,r.slice(n+1))};_r.getQuotedIdentifier=pF});var Yw=k(Nl=>{"use strict";Nl.__esModule=!0;Nl.lex=void 0;var qs=tn(),Jt=vh(),$s=Gw(),mF=function(r){for(var e=null,t=null,n=null,i=0;i<r.length;i+=1){var s=r[i];if(e===null){if(!Jt.SEPARATORS.has(s)){var a=Jt.STRUCTURAL_CHARACTERS[s];if(a){var o=r[i+1];if(a===qs.StructuralCharacters.CLOSE_PARENTHESIS&&o&&!Jt.SEPARATORS.has(o)&&o!==qs.StructuralCharacters.CLOSE_PARENTHESIS)throw new Error("Unexpected character: ".concat(o,". A closing parenthesis should be followed by another closing parenthesis or whitespace"));return(0,$s.createResult)(qs.Tokens.STRUCTURAL_CHARACTER,Jt.STRUCTURAL_CHARACTERS[s],r.slice(i+1))}if(s===Jt.QUOTED_IDENTIFIER_DELIMITER)return(0,$s.getQuotedIdentifier)(r.slice(i+1));if(s===Jt.COMMENT_DELIMITER)return(0,$s.getComment)(r.slice(i+1));e=i}}else if(Jt.SEPARATORS.has(s)||Jt.STRUCTURAL_CHARACTERS[s]){t=i,n=s;break}else if(s===Jt.QUOTED_IDENTIFIER_DELIMITER||s===Jt.COMMENT_DELIMITER)throw new Error("Unexpected character: ".concat(s))}if(e!==null){t=t!=null?t:r.length;var u=r.slice(e,t),l=r.slice(t);if(Jt.OPERATORS[u]){if(n&&!Jt.SEPARATORS.has(n))throw new Error("Unexpected character: ".concat(n,". Operators should be separated using whitespace"));return(0,$s.createResult)(qs.Tokens.OPERATOR,Jt.OPERATORS[u],l)}else return(0,$s.createResult)(qs.Tokens.IDENTIFIER,u,l)}return(0,$s.createResult)(qs.Tokens.EOF,null,"")};Nl.lex=mF});var Hw=k(er=>{"use strict";var Bw=er&&er.__spreadArray||function(r,e,t){if(t||arguments.length===2)for(var n=0,i=e.length,s;n<i;n++)(s||!(n in e))&&(s||(s=Array.prototype.slice.call(e,0,n)),s[n]=e[n]);return r.concat(s||Array.prototype.slice.call(e))};er.__esModule=!0;er.validateToken=er.previousOperatorTakesPrecedent=er.getValue=er.newTokenGenerator=void 0;var hF=Yw(),js=tn(),Il=_h(),gF=function(r){var e=r;return function(t,n){for(n===void 0&&(n=!1);;){var i=(0,hF.lex)(e),s=i.token,a=i.remainingString;if(e=a,s.name!==js.Tokens.COMMENT)return(0,er.validateToken)(s,t,n),s}}};er.newTokenGenerator=gF;var yF=function(r,e){var t=r(Il.VALID_TOKENS.identifierOrNot),n=t.value===js.Operators.NOT;n&&(t=r(Il.VALID_TOKENS.identifierOnly));var i=t.name===js.Tokens.STRUCTURAL_CHARACTER?e(r,!0):[t];return n?Bw(Bw([],i,!0),[{name:js.Tokens.OPERATOR,value:js.Operators.NOT}],!1):i};er.getValue=yF;var bF=function(r,e){return Il.OPERATOR_PRECEDENCE[r]<=Il.OPERATOR_PRECEDENCE[e]};er.previousOperatorTakesPrecedent=bF;var TF=function(r,e,t){if(t===void 0&&(t=!1),r.name===js.Tokens.EOF){if(t)return;throw new Error("Unexpected end of expression")}for(var n=0,i=e;n<i.length;n++){var s=i[n];if(s.name===r.name&&(!s.value||s.value===r.value))return}throw new TypeError("Invalid token")};er.validateToken=TF});var Eh=k(Gs=>{"use strict";var rn=Gs&&Gs.__spreadArray||function(r,e,t){if(t||arguments.length===2)for(var n=0,i=e.length,s;n<i;n++)(s||!(n in e))&&(s||(s=Array.prototype.slice.call(e,0,n)),s[n]=e[n]);return r.concat(s||Array.prototype.slice.call(e))};Gs.__esModule=!0;Gs.parse=void 0;var Vw=tn(),zw=_h(),Fl=Hw(),_F=function(r){if(typeof r!="string")throw new Error("Expected string but received ".concat(typeof r));var e=(0,Fl.newTokenGenerator)(r);return kh(e)};Gs.parse=_F;var kh=function(r,e){e===void 0&&(e=!1);for(var t=rn([],(0,Fl.getValue)(r,kh),!0),n=[];;){var i=e?zw.VALID_TOKENS.binaryOperatorOrClose:zw.VALID_TOKENS.binaryOperator,s=r(i,!e);if(s.name===Vw.Tokens.EOF||s.name===Vw.Tokens.STRUCTURAL_CHARACTER)return rn(rn([],t,!0),rn([],n,!0).reverse(),!0);for(;n.length;){var a=n[n.length-1]||null;if(a&&(0,Fl.previousOperatorTakesPrecedent)(a.value,s.value))t=rn(rn([],t,!0),[a],!1),n=n.slice(0,-1);else break}n=rn(rn([],n,!0),[s],!1),t=rn(rn([],t,!0),(0,Fl.getValue)(r,kh),!0)}}});var Sh=k(_t=>{"use strict";_t.__esModule=!0;_t.throwInvalidExpression=_t.isOperator=_t.isIdentifier=_t.notUtil=_t.xorUtil=_t.orUtil=_t.andUtil=void 0;var Kw=tn(),vF=function(r,e){return r&&e};_t.andUtil=vF;var wF=function(r,e){return r||e};_t.orUtil=wF;var kF=function(r,e){return r!==e};_t.xorUtil=kF;var EF=function(r){return!r};_t.notUtil=EF;var SF=function(r){var e=r.name,t=r.value;return e===Kw.Tokens.IDENTIFIER&&typeof t=="string"};_t.isIdentifier=SF;var OF=function(r){var e=r.name,t=r.value;return e===Kw.Tokens.OPERATOR&&typeof t=="string"};_t.isOperator=OF;var DF=function(r){throw new TypeError("Invalid postfix expression: ".concat(r))};_t.throwInvalidExpression=DF});var Qw=k(Ll=>{"use strict";var Fo;Ll.__esModule=!0;Ll.OPERATOR_MAP=void 0;var Oh=tn(),Dh=Sh();Ll.OPERATOR_MAP=(Fo={},Fo[Oh.Operators.AND]=Dh.andUtil,Fo[Oh.Operators.OR]=Dh.orUtil,Fo[Oh.Operators.XOR]=Dh.xorUtil,Fo)});var Zw=k(vr=>{"use strict";var Ys=vr&&vr.__spreadArray||function(r,e,t){if(t||arguments.length===2)for(var n=0,i=e.length,s;n<i;n++)(s||!(n in e))&&(s||(s=Array.prototype.slice.call(e,0,n)),s[n]=e[n]);return r.concat(s||Array.prototype.slice.call(e))};vr.__esModule=!0;vr.evaluateExpression=vr.evaluate=vr.getEvaluator=void 0;var xF=Eh(),Xw=tn(),RF=Qw(),$i=Sh(),MF=function(r){var e=(0,xF.parse)(r);return function(t){return(0,vr.evaluate)(e,t)}};vr.getEvaluator=MF;var CF=function(r,e){if(!Array.isArray(r))throw new Error("".concat(r," should be an array. evaluate takes in a parsed expression. Use in combination with parse or use getEvaluator"));var t=r.reduce(function(n,i,s){if(!(i&&((0,$i.isIdentifier)(i)||(0,$i.isOperator)(i))))throw new Error("Invalid token: ".concat(i,". Found in parsed expression at index ").concat(s));if(i.name===Xw.Tokens.IDENTIFIER)return Ys(Ys([],n,!0),[Boolean(e[i.value])],!1);var a=n[n.length-2],o=n[n.length-1];if(i.value===Xw.Operators.NOT)return o===void 0&&(0,$i.throwInvalidExpression)("missing identifier"),Ys(Ys([],n.slice(0,-1),!0),[(0,$i.notUtil)(o)],!1);(o===void 0||a===void 0)&&(0,$i.throwInvalidExpression)("missing identifier");var u=RF.OPERATOR_MAP[i.value];return u||(0,$i.throwInvalidExpression)("unknown operator"),Ys(Ys([],n.slice(0,-2),!0),[u(a,o)],!1)},[]);return t.length!==1&&(0,$i.throwInvalidExpression)("too many identifiers after evaluation"),t[0]};vr.evaluate=CF;var AF=function(r,e){return(0,vr.getEvaluator)(r)(e)};vr.evaluateExpression=AF});var ek=k(nn=>{"use strict";var xh=nn&&nn.__createBinding||(Object.create?function(r,e,t,n){n===void 0&&(n=t);var i=Object.getOwnPropertyDescriptor(e,t);(!i||("get"in i?!e.__esModule:i.writable||i.configurable))&&(i={enumerable:!0,get:function(){return e[t]}}),Object.defineProperty(r,n,i)}:function(r,e,t,n){n===void 0&&(n=t),r[n]=e[t]});nn.__esModule=!0;nn.parse=nn.evaluate=nn.getEvaluator=void 0;var Jw=Zw();xh(nn,Jw,"getEvaluator");xh(nn,Jw,"evaluate");var PF=Eh();xh(nn,PF,"parse")});var ZF={};dE(ZF,{default:()=>fc});module.exports=fE(ZF);var Yk=require("obsidian");var bs=require("obsidian");var eL=new Error("timeout while waiting for mutex to become available"),tL=new Error("mutex already locked"),pE=new Error("request for lock canceled"),mE=function(r,e,t,n){function i(s){return s instanceof t?s:new t(function(a){a(s)})}return new(t||(t=Promise))(function(s,a){function o(c){try{l(n.next(c))}catch(d){a(d)}}function u(c){try{l(n.throw(c))}catch(d){a(d)}}function l(c){c.done?s(c.value):i(c.value).then(o,u)}l((n=n.apply(r,e||[])).next())})},Oc=class{constructor(e,t=pE){this._value=e,this._cancelError=t,this._weightedQueues=[],this._weightedWaiters=[]}acquire(e=1){if(e<=0)throw new Error(`invalid weight ${e}: must be positive`);return new Promise((t,n)=>{this._weightedQueues[e-1]||(this._weightedQueues[e-1]=[]),this._weightedQueues[e-1].push({resolve:t,reject:n}),this._dispatch()})}runExclusive(e,t=1){return mE(this,void 0,void 0,function*(){let[n,i]=yield this.acquire(t);try{return yield e(n)}finally{i()}})}waitForUnlock(e=1){if(e<=0)throw new Error(`invalid weight ${e}: must be positive`);return new Promise(t=>{this._weightedWaiters[e-1]||(this._weightedWaiters[e-1]=[]),this._weightedWaiters[e-1].push(t),this._dispatch()})}isLocked(){return this._value<=0}getValue(){return this._value}setValue(e){this._value=e,this._dispatch()}release(e=1){if(e<=0)throw new Error(`invalid weight ${e}: must be positive`);this._value+=e,this._dispatch()}cancel(){this._weightedQueues.forEach(e=>e.forEach(t=>t.reject(this._cancelError))),this._weightedQueues=[]}_dispatch(){var e;for(let t=this._value;t>0;t--){let n=(e=this._weightedQueues[t-1])===null||e===void 0?void 0:e.shift();if(!n)continue;let i=this._value,s=t;this._value-=t,t=this._value+1,n.resolve([i,this._newReleaser(s)])}this._drainUnlockWaiters()}_newReleaser(e){let t=!1;return()=>{t||(t=!0,this.release(e))}}_drainUnlockWaiters(){for(let e=this._value;e>0;e--)!this._weightedWaiters[e-1]||(this._weightedWaiters[e-1].forEach(t=>t()),this._weightedWaiters[e-1]=[])}},hE=function(r,e,t,n){function i(s){return s instanceof t?s:new t(function(a){a(s)})}return new(t||(t=Promise))(function(s,a){function o(c){try{l(n.next(c))}catch(d){a(d)}}function u(c){try{l(n.throw(c))}catch(d){a(d)}}function l(c){c.done?s(c.value):i(c.value).then(o,u)}l((n=n.apply(r,e||[])).next())})},Vo=class{constructor(e){this._semaphore=new Oc(1,e)}acquire(){return hE(this,void 0,void 0,function*(){let[,e]=yield this._semaphore.acquire();return e})}runExclusive(e){return this._semaphore.runExclusive(()=>e())}isLocked(){return this._semaphore.isLocked()}waitForUnlock(){return this._semaphore.waitForUnlock()}release(){this._semaphore.isLocked()&&this._semaphore.release()}cancel(){return this._semaphore.cancel()}};var zo=require("obsidian"),at=class{constructor(e,t={}){this._frontmatter={tags:[]};this._tags=[];var i,s;this._path=e,this._cachedMetadata=t;let n=t.frontmatter;if(n!==void 0&&(this._frontmatter=JSON.parse(JSON.stringify(n)),this._frontmatter.tags=(i=(0,zo.parseFrontMatterTags)(n))!=null?i:[]),Object.keys(t).length!==0){let a=(s=(0,zo.getAllTags)(this.cachedMetadata))!=null?s:[];this._tags=[...new Set(a)]}}get path(){return this._path}get tags(){return this._tags}get cachedMetadata(){return this._cachedMetadata}get frontmatter(){return this._frontmatter}rawFrontmatterIdenticalTo(e){let t=this.cachedMetadata.frontmatter,n=e.cachedMetadata.frontmatter;return t===n?!0:!t||!n?!1:JSON.stringify(t)===JSON.stringify(n)}get pathWithoutExtension(){return this.withoutExtension(this.path)}withoutExtension(e){return e.replace(/\.md$/,"")}get root(){let e=this.path.replace(/\\/g,"/");e.charAt(0)==="/"&&(e=e.substring(1));let t=e.indexOf("/");return t==-1?"/":e.substring(0,t+1)}get folder(){let e=this.path,t=this.filename,n=e.substring(0,e.lastIndexOf(t));return n===""?"/":n}get filename(){let e=this.path.match(/([^/]+)$/);return e!==null?e[1]:""}get filenameWithoutExtension(){return this.withoutExtension(this.filename)}hasProperty(e){let t=this.findKeyInFrontmatter(e);if(t===void 0)return!1;let n=this.frontmatter[t];return!(n===null||n===void 0)}property(e){let t=this.findKeyInFrontmatter(e);if(t===void 0)return null;let n=this.frontmatter[t];return n===void 0?null:Array.isArray(n)?n.filter(i=>i!==null):n}findKeyInFrontmatter(e){let t=e.toLowerCase();return Object.keys(this.frontmatter).find(n=>n.toLowerCase()===t)}};var Vi=class{constructor(e,t){this.parent=null;this.children=[];this.originalMarkdown=e,this.parent=t,t!==null&&t.children.push(this)}get root(){return this.parent===null?this:this.parent.root}get isRoot(){return this.parent===null}};var qu=oa(ze());var kt=class{constructor(e,t){this.start=e,this.end=t,t.isBefore(e)&&(this.start=t,this.end=e),this.start=this.start.startOf("day"),this.end=this.end.startOf("day")}static buildRelative(e){let t=e==="week"?"isoWeek":e;return new kt(window.moment().startOf(t).startOf("day"),window.moment().endOf(t).startOf("day"))}static buildInvalid(){return new kt(window.moment.invalid(),window.moment.invalid())}isValid(){return this.start.isValid()&&this.end.isValid()}moveToPrevious(e){let t=window.moment.duration(1,e);this.start.subtract(t),this.end.subtract(t),(e==="month"||e==="quarter")&&(this.end=this.end.endOf(e).startOf("day"))}moveToNext(e){let t=window.moment.duration(1,e);this.start.add(t),this.end.add(t),(e==="month"||e==="quarter")&&(this.end=this.end.endOf(e).startOf("day"))}};var Ht=class{static parseDate(e,t=!1){return window.moment(qu.parseDate(e,void 0,{forwardDate:t})).startOf("day")}static parseDateRange(e,t=!1){let n=[Ht.parseRelativeDateRange,Ht.parseNumberedDateRange,Ht.parseAbsoluteDateRange];for(let i of n){let s=i(e,t);if(s.isValid())return s}return kt.buildInvalid()}static parseAbsoluteDateRange(e,t){let n=qu.parse(e,void 0,{forwardDate:t});if(n.length===0)return kt.buildInvalid();let i=n[0].start,s=n[1]&&n[1].start?n[1].start:i,a=window.moment(i.date()),o=window.moment(s.date());return new kt(a,o)}static parseRelativeDateRange(e,t){let n=/(last|this|next) (week|month|quarter|year)/,i=e.match(n);if(i&&i.length===3){let s=i[1],a=i[2],o=kt.buildRelative(a);switch(s){case"last":o.moveToPrevious(a);break;case"next":o.moveToNext(a);break}return o}return kt.buildInvalid()}static parseNumberedDateRange(e,t){let n=[[/^\s*[0-9]{4}\s*$/,"YYYY","year"],[/^\s*[0-9]{4}-Q[1-4]\s*$/,"YYYY-Q","quarter"],[/^\s*[0-9]{4}-[0-9]{2}\s*$/,"YYYY-MM","month"],[/^\s*[0-9]{4}-W[0-9]{2}\s*$/,"YYYY-WW","isoWeek"]];for(let[i,s,a]of n){let o=e.match(i);if(o){let u=o[0].trim();return new kt(window.moment(u,s).startOf(a),window.moment(u,s).endOf(a))}}return kt.buildInvalid()}};var PP={td:"today",tm:"tomorrow",yd:"yesterday",tw:"this week",nw:"next week",weekend:"sat",we:"sat"};function $u(r){for(let[e,t]of Object.entries(PP))r=r.replace(RegExp(`\\b${e}\\s`,"i"),t);return r}var ym=oa(ze());function dr(r,e){return r!==null&&e===null?-1:r===null&&e!==null?1:r!==null&&e!==null?r.isValid()&&!e.isValid()?1:!r.isValid()&&e.isValid()?-1:r.isAfter(e)?1:r.isBefore(e)?-1:0:0}function NP(r,e,t=void 0){if(!e)return`<i>no ${r} date</i>`;let n=ym.parseDate(e,t,{forwardDate:t!=null});return n!==null?window.moment(n).format("YYYY-MM-DD"):`<i>invalid ${r} date</i>`}function D_(r,e,t){return NP(r,e,t?new Date:void 0)}function Ti(r,e){let t=null,n=ym.parseDate(r,new Date,{forwardDate:e});return n!==null&&(t=window.moment(n)),t}var fr=class{constructor({startDate:e=null,scheduledDate:t=null,dueDate:n=null}){this.startDate=e!=null?e:null,this.scheduledDate=t!=null?t:null,this.dueDate=n!=null?n:null,this.referenceDate=this.getReferenceDate()}getReferenceDate(){return this.dueDate?window.moment(this.dueDate):this.scheduledDate?window.moment(this.scheduledDate):this.startDate?window.moment(this.startDate):null}isIdenticalTo(e){return!(dr(this.startDate,e.startDate)!==0||dr(this.scheduledDate,e.scheduledDate)!==0||dr(this.dueDate,e.dueDate)!==0)}next(e){return this.referenceDate===null?new fr({startDate:null,scheduledDate:null,dueDate:null}):new fr({startDate:this.nextOccurrenceDate(this.startDate,e),scheduledDate:this.nextOccurrenceDate(this.scheduledDate,e),dueDate:this.nextOccurrenceDate(this.dueDate,e)})}nextOccurrenceDate(e,t){if(e===null)return null;let n=window.moment.duration(e.diff(this.referenceDate)),i=window.moment(t);return i.add(Math.round(n.asDays()),"days"),i}};var ju=["MO","TU","WE","TH","FR","SA","SU"],He=function(){function r(e,t){if(t===0)throw new Error("Can't create weekday with n == 0");this.weekday=e,this.n=t}return r.fromStr=function(e){return new r(ju.indexOf(e))},r.prototype.nth=function(e){return this.n===e?this:new r(this.weekday,e)},r.prototype.equals=function(e){return this.weekday===e.weekday&&this.n===e.n},r.prototype.toString=function(){var e=ju[this.weekday];return this.n&&(e=(this.n>0?"+":"")+String(this.n)+e),e},r.prototype.getJsWeekday=function(){return this.weekday===6?0:this.weekday+1},r}();var Fe=function(r){return r!=null},Vt=function(r){return typeof r=="number"},bm=function(r){return typeof r=="string"&&ju.includes(r)},ct=Array.isArray,pr=function(r,e){e===void 0&&(e=r),arguments.length===1&&(e=r,r=0);for(var t=[],n=r;n<e;n++)t.push(n);return t};var ye=function(r,e){var t=0,n=[];if(ct(r))for(;t<e;t++)n[t]=[].concat(r);else for(;t<e;t++)n[t]=r;return n},x_=function(r){return ct(r)?r:[r]};function _i(r,e,t){t===void 0&&(t=" ");var n=String(r);return e=e>>0,n.length>e?String(n):(e=e-n.length,e>t.length&&(t+=ye(t,e/t.length)),t.slice(0,e)+String(n))}var R_=function(r,e,t){var n=r.split(e);return t?n.slice(0,t).concat([n.slice(t).join(e)]):n},mt=function(r,e){var t=r%e;return t*e<0?t+e:t},Gu=function(r,e){return{div:Math.floor(r/e),mod:mt(r,e)}},zt=function(r){return!Fe(r)||r.length===0},$e=function(r){return!zt(r)},Te=function(r,e){return $e(r)&&r.indexOf(e)!==-1};var Hr=function(r,e,t,n,i,s){return n===void 0&&(n=0),i===void 0&&(i=0),s===void 0&&(s=0),new Date(Date.UTC(r,e-1,t,n,i,s))},IP=[31,28,31,30,31,30,31,31,30,31,30,31],A_=1e3*60*60*24,Yu=9999,P_=Hr(1970,1,1),FP=[6,0,1,2,3,4,5];var cs=function(r){return r%4===0&&r%100!==0||r%400===0},Tm=function(r){return r instanceof Date},vi=function(r){return Tm(r)&&!isNaN(r.getTime())},M_=function(r){return r.getTimezoneOffset()*60*1e3},LP=function(r,e){var t=r.getTime()-M_(r),n=e.getTime()-M_(e),i=t-n;return Math.round(i/A_)},uo=function(r){return LP(r,P_)},Bu=function(r){return new Date(P_.getTime()+r*A_)},UP=function(r){var e=r.getUTCMonth();return e===1&&cs(r.getUTCFullYear())?29:IP[e]},Tn=function(r){return FP[r.getUTCDay()]},_m=function(r,e){var t=Hr(r,e+1,1);return[Tn(t),UP(t)]},Hu=function(r,e){return e=e||r,new Date(Date.UTC(r.getUTCFullYear(),r.getUTCMonth(),r.getUTCDate(),e.getHours(),e.getMinutes(),e.getSeconds(),e.getMilliseconds()))},Vu=function(r){var e=new Date(r.getTime());return e},vm=function(r){for(var e=[],t=0;t<r.length;t++)e.push(Vu(r[t]));return e},_n=function(r){r.sort(function(e,t){return e.getTime()-t.getTime()})},ds=function(r,e){e===void 0&&(e=!0);var t=new Date(r);return[_i(t.getUTCFullYear().toString(),4,"0"),_i(t.getUTCMonth()+1,2,"0"),_i(t.getUTCDate(),2,"0"),"T",_i(t.getUTCHours(),2,"0"),_i(t.getUTCMinutes(),2,"0"),_i(t.getUTCSeconds(),2,"0"),e?"Z":""].join("")},lo=function(r){var e=/^(\d{4})(\d{2})(\d{2})(T(\d{2})(\d{2})(\d{2})Z?)?$/,t=e.exec(r);if(!t)throw new Error("Invalid UNTIL value: ".concat(r));return new Date(Date.UTC(parseInt(t[1],10),parseInt(t[2],10)-1,parseInt(t[3],10),parseInt(t[5],10)||0,parseInt(t[6],10)||0,parseInt(t[7],10)||0))},C_=function(r,e){var t=r.toLocaleString("sv-SE",{timeZone:e});return t.replace(" ","T")+"Z"},N_=function(r,e){var t=Intl.DateTimeFormat().resolvedOptions().timeZone,n=new Date(C_(r,t)),i=new Date(C_(r,e!=null?e:"UTC")),s=i.getTime()-n.getTime();return new Date(r.getTime()-s)};var WP=function(){function r(e,t){this.minDate=null,this.maxDate=null,this._result=[],this.total=0,this.method=e,this.args=t,e==="between"?(this.maxDate=t.inc?t.before:new Date(t.before.getTime()-1),this.minDate=t.inc?t.after:new Date(t.after.getTime()+1)):e==="before"?this.maxDate=t.inc?t.dt:new Date(t.dt.getTime()-1):e==="after"&&(this.minDate=t.inc?t.dt:new Date(t.dt.getTime()+1))}return r.prototype.accept=function(e){++this.total;var t=this.minDate&&e<this.minDate,n=this.maxDate&&e>this.maxDate;if(this.method==="between"){if(t)return!0;if(n)return!1}else if(this.method==="before"){if(n)return!1}else if(this.method==="after")return t?!0:(this.add(e),!1);return this.add(e)},r.prototype.add=function(e){return this._result.push(e),!0},r.prototype.getValue=function(){var e=this._result;switch(this.method){case"all":case"between":return e;case"before":case"after":default:return e.length?e[e.length-1]:null}},r.prototype.clone=function(){return new r(this.method,this.args)},r}(),vn=WP;var wm=function(r,e){return wm=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,n){t.__proto__=n}||function(t,n){for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(t[i]=n[i])},wm(r,e)};function fs(r,e){if(typeof e!="function"&&e!==null)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");wm(r,e);function t(){this.constructor=r}r.prototype=e===null?Object.create(e):(t.prototype=e.prototype,new t)}var dt=function(){return dt=Object.assign||function(e){for(var t,n=1,i=arguments.length;n<i;n++){t=arguments[n];for(var s in t)Object.prototype.hasOwnProperty.call(t,s)&&(e[s]=t[s])}return e},dt.apply(this,arguments)};function L(r,e,t){if(t||arguments.length===2)for(var n=0,i=e.length,s;n<i;n++)(s||!(n in e))&&(s||(s=Array.prototype.slice.call(e,0,n)),s[n]=e[n]);return r.concat(s||Array.prototype.slice.call(e))}var qP=function(r){fs(e,r);function e(t,n,i){var s=r.call(this,t,n)||this;return s.iterator=i,s}return e.prototype.add=function(t){return this.iterator(t,this._result.length)?(this._result.push(t),!0):!1},e}(vn),km=qP;var $P={dayNames:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],monthNames:["January","February","March","April","May","June","July","August","September","October","November","December"],tokens:{SKIP:/^[ \r\n\t]+|^\.$/,number:/^[1-9][0-9]*/,numberAsText:/^(one|two|three)/i,every:/^every/i,"day(s)":/^days?/i,"weekday(s)":/^weekdays?/i,"week(s)":/^weeks?/i,"hour(s)":/^hours?/i,"minute(s)":/^minutes?/i,"month(s)":/^months?/i,"year(s)":/^years?/i,on:/^(on|in)/i,at:/^(at)/i,the:/^the/i,first:/^first/i,second:/^second/i,third:/^third/i,nth:/^([1-9][0-9]*)(\.|th|nd|rd|st)/i,last:/^last/i,for:/^for/i,"time(s)":/^times?/i,until:/^(un)?til/i,monday:/^mo(n(day)?)?/i,tuesday:/^tu(e(s(day)?)?)?/i,wednesday:/^we(d(n(esday)?)?)?/i,thursday:/^th(u(r(sday)?)?)?/i,friday:/^fr(i(day)?)?/i,saturday:/^sa(t(urday)?)?/i,sunday:/^su(n(day)?)?/i,january:/^jan(uary)?/i,february:/^feb(ruary)?/i,march:/^mar(ch)?/i,april:/^apr(il)?/i,may:/^may/i,june:/^june?/i,july:/^july?/i,august:/^aug(ust)?/i,september:/^sep(t(ember)?)?/i,october:/^oct(ober)?/i,november:/^nov(ember)?/i,december:/^dec(ember)?/i,comma:/^(,\s*|(and|or)\s*)+/i}},wi=$P;var I_=function(r,e){return r.indexOf(e)!==-1},jP=function(r){return r.toString()},GP=function(r,e,t){return"".concat(e," ").concat(t,", ").concat(r)},YP=function(){function r(e,t,n,i){if(t===void 0&&(t=jP),n===void 0&&(n=wi),i===void 0&&(i=GP),this.text=[],this.language=n||wi,this.gettext=t,this.dateFormatter=i,this.rrule=e,this.options=e.options,this.origOptions=e.origOptions,this.origOptions.bymonthday){var s=[].concat(this.options.bymonthday),a=[].concat(this.options.bynmonthday);s.sort(function(c,d){return c-d}),a.sort(function(c,d){return d-c}),this.bymonthday=s.concat(a),this.bymonthday.length||(this.bymonthday=null)}if(Fe(this.origOptions.byweekday)){var o=ct(this.origOptions.byweekday)?this.origOptions.byweekday:[this.origOptions.byweekday],u=String(o);this.byweekday={allWeeks:o.filter(function(c){return!c.n}),someWeeks:o.filter(function(c){return Boolean(c.n)}),isWeekdays:u.indexOf("MO")!==-1&&u.indexOf("TU")!==-1&&u.indexOf("WE")!==-1&&u.indexOf("TH")!==-1&&u.indexOf("FR")!==-1&&u.indexOf("SA")===-1&&u.indexOf("SU")===-1,isEveryDay:u.indexOf("MO")!==-1&&u.indexOf("TU")!==-1&&u.indexOf("WE")!==-1&&u.indexOf("TH")!==-1&&u.indexOf("FR")!==-1&&u.indexOf("SA")!==-1&&u.indexOf("SU")!==-1};var l=function(c,d){return c.weekday-d.weekday};this.byweekday.allWeeks.sort(l),this.byweekday.someWeeks.sort(l),this.byweekday.allWeeks.length||(this.byweekday.allWeeks=null),this.byweekday.someWeeks.length||(this.byweekday.someWeeks=null)}else this.byweekday=null}return r.isFullyConvertible=function(e){var t=!0;if(!(e.options.freq in r.IMPLEMENTED)||e.origOptions.until&&e.origOptions.count)return!1;for(var n in e.origOptions){if(I_(["dtstart","wkst","freq"],n))return!0;if(!I_(r.IMPLEMENTED[e.options.freq],n))return!1}return t},r.prototype.isFullyConvertible=function(){return r.isFullyConvertible(this.rrule)},r.prototype.toString=function(){var e=this.gettext;if(!(this.options.freq in r.IMPLEMENTED))return e("RRule error: Unable to fully convert this rrule to text");if(this.text=[e("every")],this[z.FREQUENCIES[this.options.freq]](),this.options.until){this.add(e("until"));var t=this.options.until;this.add(this.dateFormatter(t.getUTCFullYear(),this.language.monthNames[t.getUTCMonth()],t.getUTCDate()))}else this.options.count&&this.add(e("for")).add(this.options.count.toString()).add(this.plural(this.options.count)?e("times"):e("time"));return this.isFullyConvertible()||this.add(e("(~ approximate)")),this.text.join("")},r.prototype.HOURLY=function(){var e=this.gettext;this.options.interval!==1&&this.add(this.options.interval.toString()),this.add(this.plural(this.options.interval)?e("hours"):e("hour"))},r.prototype.MINUTELY=function(){var e=this.gettext;this.options.interval!==1&&this.add(this.options.interval.toString()),this.add(this.plural(this.options.interval)?e("minutes"):e("minute"))},r.prototype.DAILY=function(){var e=this.gettext;this.options.interval!==1&&this.add(this.options.interval.toString()),this.byweekday&&this.byweekday.isWeekdays?this.add(this.plural(this.options.interval)?e("weekdays"):e("weekday")):this.add(this.plural(this.options.interval)?e("days"):e("day")),this.origOptions.bymonth&&(this.add(e("in")),this._bymonth()),this.bymonthday?this._bymonthday():this.byweekday?this._byweekday():this.origOptions.byhour&&this._byhour()},r.prototype.WEEKLY=function(){var e=this.gettext;this.options.interval!==1&&this.add(this.options.interval.toString()).add(this.plural(this.options.interval)?e("weeks"):e("week")),this.byweekday&&this.byweekday.isWeekdays?this.options.interval===1?this.add(this.plural(this.options.interval)?e("weekdays"):e("weekday")):this.add(e("on")).add(e("weekdays")):this.byweekday&&this.byweekday.isEveryDay?this.add(this.plural(this.options.interval)?e("days"):e("day")):(this.options.interval===1&&this.add(e("week")),this.origOptions.bymonth&&(this.add(e("in")),this._bymonth()),this.bymonthday?this._bymonthday():this.byweekday&&this._byweekday())},r.prototype.MONTHLY=function(){var e=this.gettext;this.origOptions.bymonth?(this.options.interval!==1&&(this.add(this.options.interval.toString()).add(e("months")),this.plural(this.options.interval)&&this.add(e("in"))),this._bymonth()):(this.options.interval!==1&&this.add(this.options.interval.toString()),this.add(this.plural(this.options.interval)?e("months"):e("month"))),this.bymonthday?this._bymonthday():this.byweekday&&this.byweekday.isWeekdays?this.add(e("on")).add(e("weekdays")):this.byweekday&&this._byweekday()},r.prototype.YEARLY=function(){var e=this.gettext;this.origOptions.bymonth?(this.options.interval!==1&&(this.add(this.options.interval.toString()),this.add(e("years"))),this._bymonth()):(this.options.interval!==1&&this.add(this.options.interval.toString()),this.add(this.plural(this.options.interval)?e("years"):e("year"))),this.bymonthday?this._bymonthday():this.byweekday&&this._byweekday(),this.options.byyearday&&this.add(e("on the")).add(this.list(this.options.byyearday,this.nth,e("and"))).add(e("day")),this.options.byweekno&&this.add(e("in")).add(this.plural(this.options.byweekno.length)?e("weeks"):e("week")).add(this.list(this.options.byweekno,void 0,e("and")))},r.prototype._bymonthday=function(){var e=this.gettext;this.byweekday&&this.byweekday.allWeeks?this.add(e("on")).add(this.list(this.byweekday.allWeeks,this.weekdaytext,e("or"))).add(e("the")).add(this.list(this.bymonthday,this.nth,e("or"))):this.add(e("on the")).add(this.list(this.bymonthday,this.nth,e("and")))},r.prototype._byweekday=function(){var e=this.gettext;this.byweekday.allWeeks&&!this.byweekday.isWeekdays&&this.add(e("on")).add(this.list(this.byweekday.allWeeks,this.weekdaytext)),this.byweekday.someWeeks&&(this.byweekday.allWeeks&&this.add(e("and")),this.add(e("on the")).add(this.list(this.byweekday.someWeeks,this.weekdaytext,e("and"))))},r.prototype._byhour=function(){var e=this.gettext;this.add(e("at")).add(this.list(this.origOptions.byhour,void 0,e("and")))},r.prototype._bymonth=function(){this.add(this.list(this.options.bymonth,this.monthtext,this.gettext("and")))},r.prototype.nth=function(e){e=parseInt(e.toString(),10);var t,n=this.gettext;if(e===-1)return n("last");var i=Math.abs(e);switch(i){case 1:case 21:case 31:t=i+n("st");break;case 2:case 22:t=i+n("nd");break;case 3:case 23:t=i+n("rd");break;default:t=i+n("th")}return e<0?t+" "+n("last"):t},r.prototype.monthtext=function(e){return this.language.monthNames[e-1]},r.prototype.weekdaytext=function(e){var t=Vt(e)?(e+1)%7:e.getJsWeekday();return(e.n?this.nth(e.n)+" ":"")+this.language.dayNames[t]},r.prototype.plural=function(e){return e%100!==1},r.prototype.add=function(e){return this.text.push(" "),this.text.push(e),this},r.prototype.list=function(e,t,n,i){var s=this;i===void 0&&(i=","),ct(e)||(e=[e]);var a=function(u,l,c){for(var d="",p=0;p<u.length;p++)p!==0&&(p===u.length-1?d+=" "+c+" ":d+=l+" "),d+=u[p];return d};t=t||function(u){return u.toString()};var o=function(u){return t&&t.call(s,u)};return n?a(e.map(o),i,n):e.map(o).join(i+" ")},r}(),Vr=YP;var BP=function(){function r(e){this.done=!0,this.rules=e}return r.prototype.start=function(e){return this.text=e,this.done=!1,this.nextSymbol()},r.prototype.isDone=function(){return this.done&&this.symbol===null},r.prototype.nextSymbol=function(){var e,t;this.symbol=null,this.value=null;do{if(this.done)return!1;var n=void 0;e=null;for(var i in this.rules){n=this.rules[i];var s=n.exec(this.text);s&&(e===null||s[0].length>e[0].length)&&(e=s,t=i)}if(e!=null&&(this.text=this.text.substr(e[0].length),this.text===""&&(this.done=!0)),e==null){this.done=!0,this.symbol=null,this.value=null;return}}while(t==="SKIP");return this.symbol=t,this.value=e,!0},r.prototype.accept=function(e){if(this.symbol===e){if(this.value){var t=this.value;return this.nextSymbol(),t}return this.nextSymbol(),!0}return!1},r.prototype.acceptNumber=function(){return this.accept("number")},r.prototype.expect=function(e){if(this.accept(e))return!0;throw new Error("expected "+e+" but found "+this.symbol)},r}();function co(r,e){e===void 0&&(e=wi);var t={},n=new BP(e.tokens);if(!n.start(r))return null;return i(),t;function i(){n.expect("every");var p=n.acceptNumber();if(p&&(t.interval=parseInt(p[0],10)),n.isDone())throw new Error("Unexpected end");switch(n.symbol){case"day(s)":t.freq=z.DAILY,n.nextSymbol()&&(a(),d());break;case"weekday(s)":t.freq=z.WEEKLY,t.byweekday=[z.MO,z.TU,z.WE,z.TH,z.FR],n.nextSymbol(),d();break;case"week(s)":t.freq=z.WEEKLY,n.nextSymbol()&&(s(),d());break;case"hour(s)":t.freq=z.HOURLY,n.nextSymbol()&&(s(),d());break;case"minute(s)":t.freq=z.MINUTELY,n.nextSymbol()&&(s(),d());break;case"month(s)":t.freq=z.MONTHLY,n.nextSymbol()&&(s(),d());break;case"year(s)":t.freq=z.YEARLY,n.nextSymbol()&&(s(),d());break;case"monday":case"tuesday":case"wednesday":case"thursday":case"friday":case"saturday":case"sunday":t.freq=z.WEEKLY;var m=n.symbol.substr(0,2).toUpperCase();if(t.byweekday=[z[m]],!n.nextSymbol())return;for(;n.accept("comma");){if(n.isDone())throw new Error("Unexpected end");var y=u();if(!y)throw new Error("Unexpected symbol "+n.symbol+", expected weekday");t.byweekday.push(z[y]),n.nextSymbol()}c(),d();break;case"january":case"february":case"march":case"april":case"may":case"june":case"july":case"august":case"september":case"october":case"november":case"december":if(t.freq=z.YEARLY,t.bymonth=[o()],!n.nextSymbol())return;for(;n.accept("comma");){if(n.isDone())throw new Error("Unexpected end");var _=o();if(!_)throw new Error("Unexpected symbol "+n.symbol+", expected month");t.bymonth.push(_),n.nextSymbol()}s(),d();break;default:throw new Error("Unknown symbol")}}function s(){var p=n.accept("on"),m=n.accept("the");if(!!(p||m))do{var y=l(),_=u(),b=o();if(y)_?(n.nextSymbol(),t.byweekday||(t.byweekday=[]),t.byweekday.push(z[_].nth(y))):(t.bymonthday||(t.bymonthday=[]),t.bymonthday.push(y),n.accept("day(s)"));else if(_)n.nextSymbol(),t.byweekday||(t.byweekday=[]),t.byweekday.push(z[_]);else if(n.symbol==="weekday(s)")n.nextSymbol(),t.byweekday||(t.byweekday=[z.MO,z.TU,z.WE,z.TH,z.FR]);else if(n.symbol==="week(s)"){n.nextSymbol();var E=n.acceptNumber();if(!E)throw new Error("Unexpected symbol "+n.symbol+", expected week number");for(t.byweekno=[parseInt(E[0],10)];n.accept("comma");){if(E=n.acceptNumber(),!E)throw new Error("Unexpected symbol "+n.symbol+"; expected monthday");t.byweekno.push(parseInt(E[0],10))}}else if(b)n.nextSymbol(),t.bymonth||(t.bymonth=[]),t.bymonth.push(b);else return}while(n.accept("comma")||n.accept("the")||n.accept("on"))}function a(){var p=n.accept("at");if(!!p)do{var m=n.acceptNumber();if(!m)throw new Error("Unexpected symbol "+n.symbol+", expected hour");for(t.byhour=[parseInt(m[0],10)];n.accept("comma");){if(m=n.acceptNumber(),!m)throw new Error("Unexpected symbol "+n.symbol+"; expected hour");t.byhour.push(parseInt(m[0],10))}}while(n.accept("comma")||n.accept("at"))}function o(){switch(n.symbol){case"january":return 1;case"february":return 2;case"march":return 3;case"april":return 4;case"may":return 5;case"june":return 6;case"july":return 7;case"august":return 8;case"september":return 9;case"october":return 10;case"november":return 11;case"december":return 12;default:return!1}}function u(){switch(n.symbol){case"monday":case"tuesday":case"wednesday":case"thursday":case"friday":case"saturday":case"sunday":return n.symbol.substr(0,2).toUpperCase();default:return!1}}function l(){switch(n.symbol){case"last":return n.nextSymbol(),-1;case"first":return n.nextSymbol(),1;case"second":return n.nextSymbol(),n.accept("last")?-2:2;case"third":return n.nextSymbol(),n.accept("last")?-3:3;case"nth":var p=parseInt(n.value[1],10);if(p<-366||p>366)throw new Error("Nth out of range: "+p);return n.nextSymbol(),n.accept("last")?-p:p;default:return!1}}function c(){n.accept("on"),n.accept("the");var p=l();if(!!p)for(t.bymonthday=[p],n.nextSymbol();n.accept("comma");){if(p=l(),!p)throw new Error("Unexpected symbol "+n.symbol+"; expected monthday");t.bymonthday.push(p),n.nextSymbol()}}function d(){if(n.symbol==="until"){var p=Date.parse(n.text);if(!p)throw new Error("Cannot parse until date:"+n.text);t.until=new Date(p)}else n.accept("for")&&(t.count=parseInt(n.value[0],10),n.expect("number"))}}var me;(function(r){r[r.YEARLY=0]="YEARLY",r[r.MONTHLY=1]="MONTHLY",r[r.WEEKLY=2]="WEEKLY",r[r.DAILY=3]="DAILY",r[r.HOURLY=4]="HOURLY",r[r.MINUTELY=5]="MINUTELY",r[r.SECONDLY=6]="SECONDLY"})(me||(me={}));function fo(r){return r<me.HOURLY}var F_=function(r,e){return e===void 0&&(e=wi),new z(co(r,e)||void 0)},ps=["count","until","interval","byweekday","bymonthday","bymonth"];Vr.IMPLEMENTED=[];Vr.IMPLEMENTED[me.HOURLY]=ps;Vr.IMPLEMENTED[me.MINUTELY]=ps;Vr.IMPLEMENTED[me.DAILY]=["byhour"].concat(ps);Vr.IMPLEMENTED[me.WEEKLY]=ps;Vr.IMPLEMENTED[me.MONTHLY]=ps;Vr.IMPLEMENTED[me.YEARLY]=["byweekno","byyearday"].concat(ps);var L_=function(r,e,t,n){return new Vr(r,e,t,n).toString()},U_=Vr.isFullyConvertible;var ms=function(){function r(e,t,n,i){this.hour=e,this.minute=t,this.second=n,this.millisecond=i||0}return r.prototype.getHours=function(){return this.hour},r.prototype.getMinutes=function(){return this.minute},r.prototype.getSeconds=function(){return this.second},r.prototype.getMilliseconds=function(){return this.millisecond},r.prototype.getTime=function(){return(this.hour*60*60+this.minute*60+this.second)*1e3+this.millisecond},r}();var W_=function(r){fs(e,r);function e(t,n,i,s,a,o,u){var l=r.call(this,s,a,o,u)||this;return l.year=t,l.month=n,l.day=i,l}return e.fromDate=function(t){return new this(t.getUTCFullYear(),t.getUTCMonth()+1,t.getUTCDate(),t.getUTCHours(),t.getUTCMinutes(),t.getUTCSeconds(),t.valueOf()%1e3)},e.prototype.getWeekday=function(){return Tn(new Date(this.getTime()))},e.prototype.getTime=function(){return new Date(Date.UTC(this.year,this.month-1,this.day,this.hour,this.minute,this.second,this.millisecond)).getTime()},e.prototype.getDay=function(){return this.day},e.prototype.getMonth=function(){return this.month},e.prototype.getYear=function(){return this.year},e.prototype.addYears=function(t){this.year+=t},e.prototype.addMonths=function(t){if(this.month+=t,this.month>12){var n=Math.floor(this.month/12),i=mt(this.month,12);this.month=i,this.year+=n,this.month===0&&(this.month=12,--this.year)}},e.prototype.addWeekly=function(t,n){n>this.getWeekday()?this.day+=-(this.getWeekday()+1+(6-n))+t*7:this.day+=-(this.getWeekday()-n)+t*7,this.fixDay()},e.prototype.addDaily=function(t){this.day+=t,this.fixDay()},e.prototype.addHours=function(t,n,i){for(n&&(this.hour+=Math.floor((23-this.hour)/t)*t);;){this.hour+=t;var s=Gu(this.hour,24),a=s.div,o=s.mod;if(a&&(this.hour=o,this.addDaily(a)),zt(i)||Te(i,this.hour))break}},e.prototype.addMinutes=function(t,n,i,s){for(n&&(this.minute+=Math.floor((1439-(this.hour*60+this.minute))/t)*t);;){this.minute+=t;var a=Gu(this.minute,60),o=a.div,u=a.mod;if(o&&(this.minute=u,this.addHours(o,!1,i)),(zt(i)||Te(i,this.hour))&&(zt(s)||Te(s,this.minute)))break}},e.prototype.addSeconds=function(t,n,i,s,a){for(n&&(this.second+=Math.floor((86399-(this.hour*3600+this.minute*60+this.second))/t)*t);;){this.second+=t;var o=Gu(this.second,60),u=o.div,l=o.mod;if(u&&(this.second=l,this.addMinutes(u,!1,i,s)),(zt(i)||Te(i,this.hour))&&(zt(s)||Te(s,this.minute))&&(zt(a)||Te(a,this.second)))break}},e.prototype.fixDay=function(){if(!(this.day<=28)){var t=_m(this.year,this.month-1)[1];if(!(this.day<=t))for(;this.day>t;){if(this.day-=t,++this.month,this.month===13&&(this.month=1,++this.year,this.year>Yu))return;t=_m(this.year,this.month-1)[1]}}},e.prototype.add=function(t,n){var i=t.freq,s=t.interval,a=t.wkst,o=t.byhour,u=t.byminute,l=t.bysecond;switch(i){case me.YEARLY:return this.addYears(s);case me.MONTHLY:return this.addMonths(s);case me.WEEKLY:return this.addWeekly(s,a);case me.DAILY:return this.addDaily(s);case me.HOURLY:return this.addHours(s,n,o);case me.MINUTELY:return this.addMinutes(s,n,o,u);case me.SECONDLY:return this.addSeconds(s,n,o,u,l)}},e}(ms);function Em(r){for(var e=[],t=Object.keys(r),n=0,i=t;n<i.length;n++){var s=i[n];Te(j_,s)||e.push(s),Tm(r[s])&&!vi(r[s])&&e.push(s)}if(e.length)throw new Error("Invalid options: "+e.join(", "));return dt({},r)}function q_(r){var e=dt(dt({},po),Em(r));if(Fe(e.byeaster)&&(e.freq=z.YEARLY),!(Fe(e.freq)&&z.FREQUENCIES[e.freq]))throw new Error("Invalid frequency: ".concat(e.freq," ").concat(r.freq));if(e.dtstart||(e.dtstart=new Date(new Date().setMilliseconds(0))),Fe(e.wkst)?Vt(e.wkst)||(e.wkst=e.wkst.weekday):e.wkst=z.MO.weekday,Fe(e.bysetpos)){Vt(e.bysetpos)&&(e.bysetpos=[e.bysetpos]);for(var t=0;t<e.bysetpos.length;t++){var n=e.bysetpos[t];if(n===0||!(n>=-366&&n<=366))throw new Error("bysetpos must be between 1 and 366, or between -366 and -1")}}if(!(Boolean(e.byweekno)||$e(e.byweekno)||$e(e.byyearday)||Boolean(e.bymonthday)||$e(e.bymonthday)||Fe(e.byweekday)||Fe(e.byeaster)))switch(e.freq){case z.YEARLY:e.bymonth||(e.bymonth=e.dtstart.getUTCMonth()+1),e.bymonthday=e.dtstart.getUTCDate();break;case z.MONTHLY:e.bymonthday=e.dtstart.getUTCDate();break;case z.WEEKLY:e.byweekday=[Tn(e.dtstart)];break}if(Fe(e.bymonth)&&!ct(e.bymonth)&&(e.bymonth=[e.bymonth]),Fe(e.byyearday)&&!ct(e.byyearday)&&Vt(e.byyearday)&&(e.byyearday=[e.byyearday]),!Fe(e.bymonthday))e.bymonthday=[],e.bynmonthday=[];else if(ct(e.bymonthday)){for(var i=[],s=[],t=0;t<e.bymonthday.length;t++){var n=e.bymonthday[t];n>0?i.push(n):n<0&&s.push(n)}e.bymonthday=i,e.bynmonthday=s}else e.bymonthday<0?(e.bynmonthday=[e.bymonthday],e.bymonthday=[]):(e.bynmonthday=[],e.bymonthday=[e.bymonthday]);if(Fe(e.byweekno)&&!ct(e.byweekno)&&(e.byweekno=[e.byweekno]),!Fe(e.byweekday))e.bynweekday=null;else if(Vt(e.byweekday))e.byweekday=[e.byweekday],e.bynweekday=null;else if(bm(e.byweekday))e.byweekday=[He.fromStr(e.byweekday).weekday],e.bynweekday=null;else if(e.byweekday instanceof He)!e.byweekday.n||e.freq>z.MONTHLY?(e.byweekday=[e.byweekday.weekday],e.bynweekday=null):(e.bynweekday=[[e.byweekday.weekday,e.byweekday.n]],e.byweekday=null);else{for(var a=[],o=[],t=0;t<e.byweekday.length;t++){var u=e.byweekday[t];if(Vt(u)){a.push(u);continue}else if(bm(u)){a.push(He.fromStr(u).weekday);continue}!u.n||e.freq>z.MONTHLY?a.push(u.weekday):o.push([u.weekday,u.n])}e.byweekday=$e(a)?a:null,e.bynweekday=$e(o)?o:null}return Fe(e.byhour)?Vt(e.byhour)&&(e.byhour=[e.byhour]):e.byhour=e.freq<z.HOURLY?[e.dtstart.getUTCHours()]:null,Fe(e.byminute)?Vt(e.byminute)&&(e.byminute=[e.byminute]):e.byminute=e.freq<z.MINUTELY?[e.dtstart.getUTCMinutes()]:null,Fe(e.bysecond)?Vt(e.bysecond)&&(e.bysecond=[e.bysecond]):e.bysecond=e.freq<z.SECONDLY?[e.dtstart.getUTCSeconds()]:null,{parsedOptions:e}}function $_(r){var e=r.dtstart.getTime()%1e3;if(!fo(r.freq))return[];var t=[];return r.byhour.forEach(function(n){r.byminute.forEach(function(i){r.bysecond.forEach(function(s){t.push(new ms(n,i,s,e))})})}),t}function ho(r){var e=r.split(`
`).map(HP).filter(function(t){return t!==null});return dt(dt({},e[0]),e[1])}function mo(r){var e={},t=/DTSTART(?:;TZID=([^:=]+?))?(?::|=)([^;\s]+)/i.exec(r);if(!t)return e;var n=t[1],i=t[2];return n&&(e.tzid=n),e.dtstart=lo(i),e}function HP(r){if(r=r.replace(/^\s+|\s+$/,""),!r.length)return null;var e=/^([A-Z]+?)[:;]/.exec(r.toUpperCase());if(!e)return G_(r);var t=e[1];switch(t.toUpperCase()){case"RRULE":case"EXRULE":return G_(r);case"DTSTART":return mo(r);default:throw new Error("Unsupported RFC prop ".concat(t," in ").concat(r))}}function G_(r){var e=r.replace(/^RRULE:/i,""),t=mo(e),n=r.replace(/^(?:RRULE|EXRULE):/i,"").split(";");return n.forEach(function(i){var s=i.split("="),a=s[0],o=s[1];switch(a.toUpperCase()){case"FREQ":t.freq=me[o.toUpperCase()];break;case"WKST":t.wkst=mr[o.toUpperCase()];break;case"COUNT":case"INTERVAL":case"BYSETPOS":case"BYMONTH":case"BYMONTHDAY":case"BYYEARDAY":case"BYWEEKNO":case"BYHOUR":case"BYMINUTE":case"BYSECOND":var u=VP(o),l=a.toLowerCase();t[l]=u;break;case"BYWEEKDAY":case"BYDAY":t.byweekday=zP(o);break;case"DTSTART":case"TZID":var c=mo(r);t.tzid=c.tzid,t.dtstart=c.dtstart;break;case"UNTIL":t.until=lo(o);break;case"BYEASTER":t.byeaster=Number(o);break;default:throw new Error("Unknown RRULE property '"+a+"'")}}),t}function VP(r){if(r.indexOf(",")!==-1){var e=r.split(",");return e.map(Y_)}return Y_(r)}function Y_(r){return/^[+-]?\d+$/.test(r)?Number(r):r}function zP(r){var e=r.split(",");return e.map(function(t){if(t.length===2)return mr[t];var n=t.match(/^([+-]?\d{1,2})([A-Z]{2})$/);if(!n||n.length<3)throw new SyntaxError("Invalid weekday string: ".concat(t));var i=Number(n[1]),s=n[2],a=mr[s].weekday;return new He(a,i)})}var ki=function(){function r(e,t){if(isNaN(e.getTime()))throw new RangeError("Invalid date passed to DateWithZone");this.date=e,this.tzid=t}return Object.defineProperty(r.prototype,"isUTC",{get:function(){return!this.tzid||this.tzid.toUpperCase()==="UTC"},enumerable:!1,configurable:!0}),r.prototype.toString=function(){var e=ds(this.date.getTime(),this.isUTC);return this.isUTC?":".concat(e):";TZID=".concat(this.tzid,":").concat(e)},r.prototype.getTime=function(){return this.date.getTime()},r.prototype.rezonedDate=function(){return this.isUTC?this.date:N_(this.date,this.tzid)},r}();function go(r){for(var e=[],t="",n=Object.keys(r),i=Object.keys(po),s=0;s<n.length;s++)if(n[s]!=="tzid"&&!!Te(i,n[s])){var a=n[s].toUpperCase(),o=r[n[s]],u="";if(!(!Fe(o)||ct(o)&&!o.length)){switch(a){case"FREQ":u=z.FREQUENCIES[r.freq];break;case"WKST":Vt(o)?u=new He(o).toString():u=o.toString();break;case"BYWEEKDAY":a="BYDAY",u=x_(o).map(function(m){return m instanceof He?m:ct(m)?new He(m[0],m[1]):new He(m)}).toString();break;case"DTSTART":t=KP(o,r.tzid);break;case"UNTIL":u=ds(o,!r.tzid);break;default:if(ct(o)){for(var l=[],c=0;c<o.length;c++)l[c]=String(o[c]);u=l.toString()}else u=String(o)}u&&e.push([a,u])}}var d=e.map(function(m){var y=m[0],_=m[1];return"".concat(y,"=").concat(_.toString())}).join(";"),p="";return d!==""&&(p="RRULE:".concat(d)),[t,p].filter(function(m){return!!m}).join(`
`)}function KP(r,e){return r?"DTSTART"+new ki(new Date(r),e).toString():""}function QP(r,e){return Array.isArray(r)?!Array.isArray(e)||r.length!==e.length?!1:r.every(function(t,n){return t.getTime()===e[n].getTime()}):r instanceof Date?e instanceof Date&&r.getTime()===e.getTime():r===e}var B_=function(){function r(){this.all=!1,this.before=[],this.after=[],this.between=[]}return r.prototype._cacheAdd=function(e,t,n){t&&(t=t instanceof Date?Vu(t):vm(t)),e==="all"?this.all=t:(n._value=t,this[e].push(n))},r.prototype._cacheGet=function(e,t){var n=!1,i=t?Object.keys(t):[],s=function(c){for(var d=0;d<i.length;d++){var p=i[d];if(!QP(t[p],c[p]))return!0}return!1},a=this[e];if(e==="all")n=this.all;else if(ct(a))for(var o=0;o<a.length;o++){var u=a[o];if(!(i.length&&s(u))){n=u._value;break}}if(!n&&this.all){for(var l=new vn(e,t),o=0;o<this.all.length&&l.accept(this.all[o]);o++);n=l.getValue(),this._cacheAdd(e,n,t)}return ct(n)?vm(n):n instanceof Date?Vu(n):n},r}();var H_=L(L(L(L(L(L(L(L(L(L(L(L(L([],ye(1,31),!0),ye(2,28),!0),ye(3,31),!0),ye(4,30),!0),ye(5,31),!0),ye(6,30),!0),ye(7,31),!0),ye(8,31),!0),ye(9,30),!0),ye(10,31),!0),ye(11,30),!0),ye(12,31),!0),ye(1,7),!0),V_=L(L(L(L(L(L(L(L(L(L(L(L(L([],ye(1,31),!0),ye(2,29),!0),ye(3,31),!0),ye(4,30),!0),ye(5,31),!0),ye(6,30),!0),ye(7,31),!0),ye(8,31),!0),ye(9,30),!0),ye(10,31),!0),ye(11,30),!0),ye(12,31),!0),ye(1,7),!0),XP=pr(1,29),ZP=pr(1,30),Yn=pr(1,31),ht=pr(1,32),z_=L(L(L(L(L(L(L(L(L(L(L(L(L([],ht,!0),ZP,!0),ht,!0),Yn,!0),ht,!0),Yn,!0),ht,!0),ht,!0),Yn,!0),ht,!0),Yn,!0),ht,!0),ht.slice(0,7),!0),K_=L(L(L(L(L(L(L(L(L(L(L(L(L([],ht,!0),XP,!0),ht,!0),Yn,!0),ht,!0),Yn,!0),ht,!0),ht,!0),Yn,!0),ht,!0),Yn,!0),ht,!0),ht.slice(0,7),!0),JP=pr(-28,0),eN=pr(-29,0),Bn=pr(-30,0),gt=pr(-31,0),Q_=L(L(L(L(L(L(L(L(L(L(L(L(L([],gt,!0),eN,!0),gt,!0),Bn,!0),gt,!0),Bn,!0),gt,!0),gt,!0),Bn,!0),gt,!0),Bn,!0),gt,!0),gt.slice(0,7),!0),X_=L(L(L(L(L(L(L(L(L(L(L(L(L([],gt,!0),JP,!0),gt,!0),Bn,!0),gt,!0),Bn,!0),gt,!0),gt,!0),Bn,!0),gt,!0),Bn,!0),gt,!0),gt.slice(0,7),!0),Z_=[0,31,60,91,121,152,182,213,244,274,305,335,366],J_=[0,31,59,90,120,151,181,212,243,273,304,334,365],Sm=function(){for(var r=[],e=0;e<55;e++)r=r.concat(pr(7));return r}();function ev(r,e){var t=Hr(r,1,1),n=cs(r)?366:365,i=cs(r+1)?366:365,s=uo(t),a=Tn(t),o=dt(dt({yearlen:n,nextyearlen:i,yearordinal:s,yearweekday:a},tN(r)),{wnomask:null});if(zt(e.byweekno))return o;o.wnomask=ye(0,n+7);var u,l,c=u=mt(7-a+e.wkst,7);c>=4?(c=0,l=o.yearlen+mt(a-e.wkst,7)):l=n-c;for(var d=Math.floor(l/7),p=mt(l,7),m=Math.floor(d+p/4),y=0;y<e.byweekno.length;y++){var _=e.byweekno[y];if(_<0&&(_+=m+1),_>0&&_<=m){var b=void 0;_>1?(b=c+(_-1)*7,c!==u&&(b-=7-u)):b=c;for(var E=0;E<7&&(o.wnomask[b]=1,b++,o.wdaymask[b]!==e.wkst);E++);}}if(Te(e.byweekno,1)){var b=c+m*7;if(c!==u&&(b-=7-u),b<n)for(var y=0;y<7&&(o.wnomask[b]=1,b+=1,o.wdaymask[b]!==e.wkst);y++);}if(c){var R=void 0;if(Te(e.byweekno,-1))R=-1;else{var S=Tn(Hr(r-1,1,1)),F=mt(7-S.valueOf()+e.wkst,7),q=cs(r-1)?366:365,te=void 0;F>=4?(F=0,te=q+mt(S-e.wkst,7)):te=n-c,R=Math.floor(52+mt(te,7)/4)}if(Te(e.byweekno,R))for(var b=0;b<c;b++)o.wnomask[b]=1}return o}function tN(r){var e=cs(r)?366:365,t=Hr(r,1,1),n=Tn(t);return e===365?{mmask:H_,mdaymask:K_,nmdaymask:X_,wdaymask:Sm.slice(n),mrange:J_}:{mmask:V_,mdaymask:z_,nmdaymask:Q_,wdaymask:Sm.slice(n),mrange:Z_}}function tv(r,e,t,n,i,s){var a={lastyear:r,lastmonth:e,nwdaymask:[]},o=[];if(s.freq===z.YEARLY)if(zt(s.bymonth))o=[[0,t]];else for(var u=0;u<s.bymonth.length;u++)e=s.bymonth[u],o.push(n.slice(e-1,e+1));else s.freq===z.MONTHLY&&(o=[n.slice(e-1,e+1)]);if(zt(o))return a;a.nwdaymask=ye(0,t);for(var u=0;u<o.length;u++)for(var l=o[u],c=l[0],d=l[1]-1,p=0;p<s.bynweekday.length;p++){var m=void 0,y=s.bynweekday[p],_=y[0],b=y[1];b<0?(m=d+(b+1)*7,m-=mt(i[m]-_,7)):(m=c+(b-1)*7,m+=mt(7-i[m]+_,7)),c<=m&&m<=d&&(a.nwdaymask[m]=1)}return a}function rv(r,e){e===void 0&&(e=0);var t=r%19,n=Math.floor(r/100),i=r%100,s=Math.floor(n/4),a=n%4,o=Math.floor((n+8)/25),u=Math.floor((n-o+1)/3),l=Math.floor(19*t+n-s-u+15)%30,c=Math.floor(i/4),d=i%4,p=Math.floor(32+2*a+2*c-l-d)%7,m=Math.floor((t+11*l+22*p)/451),y=Math.floor((l+p-7*m+114)/31),_=(l+p-7*m+114)%31+1,b=Date.UTC(r,y-1,_+e),E=Date.UTC(r,0,1);return[Math.ceil((b-E)/(1e3*60*60*24))]}var rN=function(){function r(e){this.options=e}return r.prototype.rebuild=function(e,t){var n=this.options;if(e!==this.lastyear&&(this.yearinfo=ev(e,n)),$e(n.bynweekday)&&(t!==this.lastmonth||e!==this.lastyear)){var i=this.yearinfo,s=i.yearlen,a=i.mrange,o=i.wdaymask;this.monthinfo=tv(e,t,s,a,o,n)}Fe(n.byeaster)&&(this.eastermask=rv(e,n.byeaster))},Object.defineProperty(r.prototype,"lastyear",{get:function(){return this.monthinfo?this.monthinfo.lastyear:null},enumerable:!1,configurable:!0}),Object.defineProperty(r.prototype,"lastmonth",{get:function(){return this.monthinfo?this.monthinfo.lastmonth:null},enumerable:!1,configurable:!0}),Object.defineProperty(r.prototype,"yearlen",{get:function(){return this.yearinfo.yearlen},enumerable:!1,configurable:!0}),Object.defineProperty(r.prototype,"yearordinal",{get:function(){return this.yearinfo.yearordinal},enumerable:!1,configurable:!0}),Object.defineProperty(r.prototype,"mrange",{get:function(){return this.yearinfo.mrange},enumerable:!1,configurable:!0}),Object.defineProperty(r.prototype,"wdaymask",{get:function(){return this.yearinfo.wdaymask},enumerable:!1,configurable:!0}),Object.defineProperty(r.prototype,"mmask",{get:function(){return this.yearinfo.mmask},enumerable:!1,configurable:!0}),Object.defineProperty(r.prototype,"wnomask",{get:function(){return this.yearinfo.wnomask},enumerable:!1,configurable:!0}),Object.defineProperty(r.prototype,"nwdaymask",{get:function(){return this.monthinfo?this.monthinfo.nwdaymask:[]},enumerable:!1,configurable:!0}),Object.defineProperty(r.prototype,"nextyearlen",{get:function(){return this.yearinfo.nextyearlen},enumerable:!1,configurable:!0}),Object.defineProperty(r.prototype,"mdaymask",{get:function(){return this.yearinfo.mdaymask},enumerable:!1,configurable:!0}),Object.defineProperty(r.prototype,"nmdaymask",{get:function(){return this.yearinfo.nmdaymask},enumerable:!1,configurable:!0}),r.prototype.ydayset=function(){return[pr(this.yearlen),0,this.yearlen]},r.prototype.mdayset=function(e,t){for(var n=this.mrange[t-1],i=this.mrange[t],s=ye(null,this.yearlen),a=n;a<i;a++)s[a]=a;return[s,n,i]},r.prototype.wdayset=function(e,t,n){for(var i=ye(null,this.yearlen+7),s=uo(Hr(e,t,n))-this.yearordinal,a=s,o=0;o<7&&(i[s]=s,++s,this.wdaymask[s]!==this.options.wkst);o++);return[i,a,s]},r.prototype.ddayset=function(e,t,n){var i=ye(null,this.yearlen),s=uo(Hr(e,t,n))-this.yearordinal;return i[s]=s,[i,s,s+1]},r.prototype.htimeset=function(e,t,n,i){var s=this,a=[];return this.options.byminute.forEach(function(o){a=a.concat(s.mtimeset(e,o,n,i))}),_n(a),a},r.prototype.mtimeset=function(e,t,n,i){var s=this.options.bysecond.map(function(a){return new ms(e,t,a,i)});return _n(s),s},r.prototype.stimeset=function(e,t,n,i){return[new ms(e,t,n,i)]},r.prototype.getdayset=function(e){switch(e){case me.YEARLY:return this.ydayset.bind(this);case me.MONTHLY:return this.mdayset.bind(this);case me.WEEKLY:return this.wdayset.bind(this);case me.DAILY:return this.ddayset.bind(this);default:return this.ddayset.bind(this)}},r.prototype.gettimeset=function(e){switch(e){case me.HOURLY:return this.htimeset.bind(this);case me.MINUTELY:return this.mtimeset.bind(this);case me.SECONDLY:return this.stimeset.bind(this)}},r}(),nv=rN;function iv(r,e,t,n,i,s){for(var a=[],o=0;o<r.length;o++){var u=void 0,l=void 0,c=r[o];c<0?(u=Math.floor(c/e.length),l=mt(c,e.length)):(u=Math.floor((c-1)/e.length),l=mt(c-1,e.length));for(var d=[],p=t;p<n;p++){var m=s[p];!Fe(m)||d.push(m)}var y=void 0;u<0?y=d.slice(u)[0]:y=d[u];var _=e[l],b=Bu(i.yearordinal+y),E=Hu(b,_);Te(a,E)||a.push(E)}return _n(a),a}function zu(r,e){var t=e.dtstart,n=e.freq,i=e.interval,s=e.until,a=e.bysetpos,o=e.count;if(o===0||i===0)return wn(r);var u=W_.fromDate(t),l=new nv(e);l.rebuild(u.year,u.month);for(var c=sN(l,u,e);;){var d=l.getdayset(n)(u.year,u.month,u.day),p=d[0],m=d[1],y=d[2],_=iN(p,m,y,l,e);if($e(a))for(var b=iv(a,c,m,y,l,p),E=0;E<b.length;E++){var R=b[E];if(s&&R>s)return wn(r);if(R>=t){var S=sv(R,e);if(!r.accept(S)||o&&(--o,!o))return wn(r)}}else for(var E=m;E<y;E++){var F=p[E];if(!!Fe(F))for(var q=Bu(l.yearordinal+F),te=0;te<c.length;te++){var G=c[te],R=Hu(q,G);if(s&&R>s)return wn(r);if(R>=t){var S=sv(R,e);if(!r.accept(S)||o&&(--o,!o))return wn(r)}}}if(e.interval===0||(u.add(e,_),u.year>Yu))return wn(r);fo(n)||(c=l.gettimeset(n)(u.hour,u.minute,u.second,0)),l.rebuild(u.year,u.month)}}function nN(r,e,t){var n=t.bymonth,i=t.byweekno,s=t.byweekday,a=t.byeaster,o=t.bymonthday,u=t.bynmonthday,l=t.byyearday;return $e(n)&&!Te(n,r.mmask[e])||$e(i)&&!r.wnomask[e]||$e(s)&&!Te(s,r.wdaymask[e])||$e(r.nwdaymask)&&!r.nwdaymask[e]||a!==null&&!Te(r.eastermask,e)||($e(o)||$e(u))&&!Te(o,r.mdaymask[e])&&!Te(u,r.nmdaymask[e])||$e(l)&&(e<r.yearlen&&!Te(l,e+1)&&!Te(l,-r.yearlen+e)||e>=r.yearlen&&!Te(l,e+1-r.yearlen)&&!Te(l,-r.nextyearlen+e-r.yearlen))}function sv(r,e){return new ki(r,e.tzid).rezonedDate()}function wn(r){return r.getValue()}function iN(r,e,t,n,i){for(var s=!1,a=e;a<t;a++){var o=r[a];s=nN(n,o,i),s&&(r[o]=null)}return s}function sN(r,e,t){var n=t.freq,i=t.byhour,s=t.byminute,a=t.bysecond;return fo(n)?$_(t):n>=z.HOURLY&&$e(i)&&!Te(i,e.hour)||n>=z.MINUTELY&&$e(s)&&!Te(s,e.minute)||n>=z.SECONDLY&&$e(a)&&!Te(a,e.second)?[]:r.gettimeset(n)(e.hour,e.minute,e.second,e.millisecond)}var mr={MO:new He(0),TU:new He(1),WE:new He(2),TH:new He(3),FR:new He(4),SA:new He(5),SU:new He(6)},po={freq:me.YEARLY,dtstart:null,interval:1,wkst:mr.MO,count:null,until:null,tzid:null,bysetpos:null,bymonth:null,bymonthday:null,bynmonthday:null,byyearday:null,byweekno:null,byweekday:null,bynweekday:null,byhour:null,byminute:null,bysecond:null,byeaster:null},j_=Object.keys(po),z=function(){function r(e,t){e===void 0&&(e={}),t===void 0&&(t=!1),this._cache=t?null:new B_,this.origOptions=Em(e);var n=q_(e).parsedOptions;this.options=n}return r.parseText=function(e,t){return co(e,t)},r.fromText=function(e,t){return F_(e,t)},r.fromString=function(e){return new r(r.parseString(e)||void 0)},r.prototype._iter=function(e){return zu(e,this.options)},r.prototype._cacheGet=function(e,t){return this._cache?this._cache._cacheGet(e,t):!1},r.prototype._cacheAdd=function(e,t,n){if(!!this._cache)return this._cache._cacheAdd(e,t,n)},r.prototype.all=function(e){if(e)return this._iter(new km("all",{},e));var t=this._cacheGet("all");return t===!1&&(t=this._iter(new vn("all",{})),this._cacheAdd("all",t)),t},r.prototype.between=function(e,t,n,i){if(n===void 0&&(n=!1),!vi(e)||!vi(t))throw new Error("Invalid date passed in to RRule.between");var s={before:t,after:e,inc:n};if(i)return this._iter(new km("between",s,i));var a=this._cacheGet("between",s);return a===!1&&(a=this._iter(new vn("between",s)),this._cacheAdd("between",a,s)),a},r.prototype.before=function(e,t){if(t===void 0&&(t=!1),!vi(e))throw new Error("Invalid date passed in to RRule.before");var n={dt:e,inc:t},i=this._cacheGet("before",n);return i===!1&&(i=this._iter(new vn("before",n)),this._cacheAdd("before",i,n)),i},r.prototype.after=function(e,t){if(t===void 0&&(t=!1),!vi(e))throw new Error("Invalid date passed in to RRule.after");var n={dt:e,inc:t},i=this._cacheGet("after",n);return i===!1&&(i=this._iter(new vn("after",n)),this._cacheAdd("after",i,n)),i},r.prototype.count=function(){return this.all().length},r.prototype.toString=function(){return go(this.origOptions)},r.prototype.toText=function(e,t,n){return L_(this,e,t,n)},r.prototype.isFullyConvertibleToText=function(){return U_(this)},r.prototype.clone=function(){return new r(this.origOptions)},r.FREQUENCIES=["YEARLY","MONTHLY","WEEKLY","DAILY","HOURLY","MINUTELY","SECONDLY"],r.YEARLY=me.YEARLY,r.MONTHLY=me.MONTHLY,r.WEEKLY=me.WEEKLY,r.DAILY=me.DAILY,r.HOURLY=me.HOURLY,r.MINUTELY=me.MINUTELY,r.SECONDLY=me.SECONDLY,r.MO=mr.MO,r.TU=mr.TU,r.WE=mr.WE,r.TH=mr.TH,r.FR=mr.FR,r.SA=mr.SA,r.SU=mr.SU,r.parseString=ho,r.optionsToString=go,r}();function av(r,e,t,n,i,s){var a={},o=r.accept;function u(p,m){t.forEach(function(y){y.between(p,m,!0).forEach(function(_){a[Number(_)]=!0})})}i.forEach(function(p){var m=new ki(p,s).rezonedDate();a[Number(m)]=!0}),r.accept=function(p){var m=Number(p);return isNaN(m)?o.call(this,p):!a[m]&&(u(new Date(m-1),new Date(m+1)),!a[m])?(a[m]=!0,o.call(this,p)):!0},r.method==="between"&&(u(r.args.after,r.args.before),r.accept=function(p){var m=Number(p);return a[m]?!0:(a[m]=!0,o.call(this,p))});for(var l=0;l<n.length;l++){var c=new ki(n[l],s).rezonedDate();if(!r.accept(new Date(c.getTime())))break}e.forEach(function(p){zu(r,p.options)});var d=r._result;switch(_n(d),r.method){case"all":case"between":return d;case"before":return d.length&&d[d.length-1]||null;case"after":default:return d.length&&d[0]||null}}var ov={dtstart:null,cache:!1,unfold:!1,forceset:!1,compatible:!1,tzid:null};function aN(r,e){var t=[],n=[],i=[],s=[],a=mo(r),o=a.dtstart,u=a.tzid,l=dN(r,e.unfold);return l.forEach(function(c){var d;if(!!c){var p=cN(c),m=p.name,y=p.parms,_=p.value;switch(m.toUpperCase()){case"RRULE":if(y.length)throw new Error("unsupported RRULE parm: ".concat(y.join(",")));t.push(ho(c));break;case"RDATE":var b=(d=/RDATE(?:;TZID=([^:=]+))?/i.exec(c))!==null&&d!==void 0?d:[],E=b[1];E&&!u&&(u=E),n=n.concat(uv(_,y));break;case"EXRULE":if(y.length)throw new Error("unsupported EXRULE parm: ".concat(y.join(",")));i.push(ho(_));break;case"EXDATE":s=s.concat(uv(_,y));break;case"DTSTART":break;default:throw new Error("unsupported property: "+m)}}}),{dtstart:o,tzid:u,rrulevals:t,rdatevals:n,exrulevals:i,exdatevals:s}}function oN(r,e){var t=aN(r,e),n=t.rrulevals,i=t.rdatevals,s=t.exrulevals,a=t.exdatevals,o=t.dtstart,u=t.tzid,l=e.cache===!1;if(e.compatible&&(e.forceset=!0,e.unfold=!0),e.forceset||n.length>1||i.length||s.length||a.length){var c=new Dm(l);return c.dtstart(o),c.tzid(u||void 0),n.forEach(function(p){c.rrule(new z(Om(p,o,u),l))}),i.forEach(function(p){c.rdate(p)}),s.forEach(function(p){c.exrule(new z(Om(p,o,u),l))}),a.forEach(function(p){c.exdate(p)}),e.compatible&&e.dtstart&&c.rdate(o),c}var d=n[0]||{};return new z(Om(d,d.dtstart||e.dtstart||o,d.tzid||e.tzid||u),l)}function Ku(r,e){return e===void 0&&(e={}),oN(r,uN(e))}function Om(r,e,t){return dt(dt({},r),{dtstart:e,tzid:t})}function uN(r){var e=[],t=Object.keys(r),n=Object.keys(ov);if(t.forEach(function(i){Te(n,i)||e.push(i)}),e.length)throw new Error("Invalid options: "+e.join(", "));return dt(dt({},ov),r)}function lN(r){if(r.indexOf(":")===-1)return{name:"RRULE",value:r};var e=R_(r,":",1),t=e[0],n=e[1];return{name:t,value:n}}function cN(r){var e=lN(r),t=e.name,n=e.value,i=t.split(";");if(!i)throw new Error("empty property name");return{name:i[0].toUpperCase(),parms:i.slice(1),value:n}}function dN(r,e){if(e===void 0&&(e=!1),r=r&&r.trim(),!r)throw new Error("Invalid empty string");if(!e)return r.split(/\s/);for(var t=r.split(`
`),n=0;n<t.length;){var i=t[n]=t[n].replace(/\s+$/g,"");i?n>0&&i[0]===" "?(t[n-1]+=i.slice(1),t.splice(n,1)):n+=1:t.splice(n,1)}return t}function fN(r){r.forEach(function(e){if(!/(VALUE=DATE(-TIME)?)|(TZID=)/.test(e))throw new Error("unsupported RDATE/EXDATE parm: "+e)})}function uv(r,e){return fN(e),r.split(",").map(function(t){return lo(t)})}function lv(r){var e=this;return function(t){if(t!==void 0&&(e["_".concat(r)]=t),e["_".concat(r)]!==void 0)return e["_".concat(r)];for(var n=0;n<e._rrule.length;n++){var i=e._rrule[n].origOptions[r];if(i)return i}}}var Dm=function(r){fs(e,r);function e(t){t===void 0&&(t=!1);var n=r.call(this,{},t)||this;return n.dtstart=lv.apply(n,["dtstart"]),n.tzid=lv.apply(n,["tzid"]),n._rrule=[],n._rdate=[],n._exrule=[],n._exdate=[],n}return e.prototype._iter=function(t){return av(t,this._rrule,this._exrule,this._rdate,this._exdate,this.tzid())},e.prototype.rrule=function(t){cv(t,this._rrule)},e.prototype.exrule=function(t){cv(t,this._exrule)},e.prototype.rdate=function(t){dv(t,this._rdate)},e.prototype.exdate=function(t){dv(t,this._exdate)},e.prototype.rrules=function(){return this._rrule.map(function(t){return Ku(t.toString())})},e.prototype.exrules=function(){return this._exrule.map(function(t){return Ku(t.toString())})},e.prototype.rdates=function(){return this._rdate.map(function(t){return new Date(t.getTime())})},e.prototype.exdates=function(){return this._exdate.map(function(t){return new Date(t.getTime())})},e.prototype.valueOf=function(){var t=[];return!this._rrule.length&&this._dtstart&&(t=t.concat(go({dtstart:this._dtstart}))),this._rrule.forEach(function(n){t=t.concat(n.toString().split(`
`))}),this._exrule.forEach(function(n){t=t.concat(n.toString().split(`
`).map(function(i){return i.replace(/^RRULE:/,"EXRULE:")}).filter(function(i){return!/^DTSTART/.test(i)}))}),this._rdate.length&&t.push(fv("RDATE",this._rdate,this.tzid())),this._exdate.length&&t.push(fv("EXDATE",this._exdate,this.tzid())),t},e.prototype.toString=function(){return this.valueOf().join(`
`)},e.prototype.clone=function(){var t=new e(!!this._cache);return this._rrule.forEach(function(n){return t.rrule(n.clone())}),this._exrule.forEach(function(n){return t.exrule(n.clone())}),this._rdate.forEach(function(n){return t.rdate(new Date(n.getTime()))}),this._exdate.forEach(function(n){return t.exdate(new Date(n.getTime()))}),t},e}(z);function cv(r,e){if(!(r instanceof z))throw new TypeError(String(r)+" is not RRule instance");Te(e.map(String),String(r))||e.push(r)}function dv(r,e){if(!(r instanceof Date))throw new TypeError(String(r)+" is not Date instance");Te(e.map(Number),Number(r))||(e.push(r),_n(e))}function fv(r,e,t){var n=!t||t.toUpperCase()==="UTC",i=n?"".concat(r,":"):"".concat(r,";TZID=").concat(t,":"),s=e.map(function(a){return ds(a.valueOf(),n)}).join(",");return"".concat(i).concat(s)}var it=class{constructor({rrule:e,baseOnToday:t,occurrence:n}){this.rrule=e,this.baseOnToday=t,this.occurrence=n}static fromText({recurrenceRuleText:e,occurrence:t}){try{let n=e.match(/^([a-zA-Z0-9, !]+?)( when done)?$/i);if(n==null)return null;let i=n[1].trim(),s=n[2]!==void 0,a=z.parseText(i);if(a!==null){let o=t.referenceDate;!s&&o!==null?a.dtstart=window.moment(o).startOf("day").utc(!0).toDate():a.dtstart=window.moment().startOf("day").utc(!0).toDate();let u=new z(a);return new it({rrule:u,baseOnToday:s,occurrence:t})}}catch(n){n instanceof Error&&console.log(n.message)}return null}toText(){let e=this.rrule.toText();return this.baseOnToday&&(e+=" when done"),e}next(e=window.moment()){let t=this.nextReferenceDate(e);return t===null?null:this.occurrence.next(t)}identicalTo(e){return this.baseOnToday!==e.baseOnToday||!this.occurrence.isIdenticalTo(e.occurrence)?!1:this.toText()===e.toText()}nextReferenceDate(e){return this.baseOnToday?this.nextReferenceDateFromToday(e.clone()).toDate():this.nextReferenceDateFromOriginalReferenceDate().toDate()}nextReferenceDateFromToday(e){let t=new z(he(K({},this.rrule.origOptions),{dtstart:e.startOf("day").utc(!0).toDate()}));return this.nextAfter(e.endOf("day"),t)}nextReferenceDateFromOriginalReferenceDate(){var t;let e=window.moment((t=this.occurrence.referenceDate)!=null?t:void 0).endOf("day");return this.nextAfter(e,this.rrule)}nextAfter(e,t){e.utc(!0);let n=window.moment(t.after(e.toDate())),i=this.toText(),s=i.match(/every( \d+)? month(s)?(.*)?/);s!==null&&(i.includes(" on ")||(n=it.nextAfterMonths(e,n,t,s[1])));let a=i.match(/every( \d+)? year(s)?(.*)?/);return a!==null&&(n=it.nextAfterYears(e,n,t,a[1])),it.addTimezone(n)}static nextAfterMonths(e,t,n,i){let s=1;for(i!==void 0&&(s=Number.parseInt(i.trim(),10));it.isSkippingTooManyMonths(e,t,s);)t=it.fromOneDayEarlier(e,n);return t}static isSkippingTooManyMonths(e,t,n){let i=t.month()-e.month();return i+=(t.year()-e.year())*12,i>n}static nextAfterYears(e,t,n,i){let s=1;for(i!==void 0&&(s=Number.parseInt(i.trim(),10));it.isSkippingTooManyYears(e,t,s);)t=it.fromOneDayEarlier(e,n);return t}static isSkippingTooManyYears(e,t,n){return t.year()-e.year()>n}static fromOneDayEarlier(e,t){e.subtract(1,"days").endOf("day");let n=t.origOptions;return n.dtstart=e.startOf("day").toDate(),t=new z(n),window.moment(t.after(e.toDate()))}static addTimezone(e){return window.moment.utc(e).local(!0).startOf("day")}};var hs=(m=>(m.Description="description",m.Id="id",m.DependsOn="dependsOn",m.Priority="priority",m.RecurrenceRule="recurrenceRule",m.OnCompletion="onCompletion",m.CreatedDate="createdDate",m.StartDate="startDate",m.ScheduledDate="scheduledDate",m.DueDate="dueDate",m.CancelledDate="cancelledDate",m.DoneDate="doneDate",m.BlockLink="blockLink",m))(hs||{}),Qu=Object.values(hs),zr=class{constructor(){this.visible={};this.tagsVisible=!0;Qu.forEach(e=>{this.visible[e]=!0})}isShown(e){return this.visible[e]}areTagsShown(){return this.tagsVisible}hide(e){this.visible[e]=!1}setVisibility(e,t){this.visible[e]=t}setTagsVisibility(e){this.tagsVisible=e}get shownComponents(){return Qu.filter(e=>this.visible[e])}get hiddenComponents(){return Qu.filter(e=>!this.visible[e])}get toggleableComponents(){return Qu.filter(e=>e!=="description"&&e!=="blockLink")}toggleVisibilityExceptDescriptionAndBlockLink(){this.toggleableComponents.forEach(e=>{this.visible[e]=!this.visible[e]}),this.setTagsVisibility(!this.areTagsShown())}};var Nt=(a=>(a.TODO="TODO",a.DONE="DONE",a.IN_PROGRESS="IN_PROGRESS",a.CANCELLED="CANCELLED",a.NON_TASK="NON_TASK",a.EMPTY="EMPTY",a))(Nt||{}),Ke=class{constructor(e,t,n,i,s="TODO"){this.symbol=e,this.name=t,this.nextStatusSymbol=n,this.availableAsCommand=i,this.type=s}};function pv(r){let e=r.trim().toLowerCase();return e==="delete"?"delete":e==="keep"?"keep":""}function pN(r,e){return r.filter(t=>t!==e)}function mN(r,e){let t=r.status,n=e.status,i=n.type===t.type;return n.type!=="DONE"||i}function mv(r,e){let t=e.length;if(r.onCompletion===""||r.onCompletion==="keep"||t===0)return e;let n=e[t-1];if(mN(r,n))return e;let s=r.onCompletion;return s==="delete"?pN(e,n):(console.log(`OnCompletion action ${s} not yet implemented.`),e)}var Kt=class{},J=Kt;J.dateFormat="YYYY-MM-DD",J.dateTimeFormat="YYYY-MM-DD HH:mm",J.indentationRegex=/^([\s\t>]*)/,J.listMarkerRegex=/([-*+]|[0-9]+\.)/,J.checkboxRegex=/\[(.)\]/u,J.afterCheckboxRegex=/ *(.*)/u,J.taskRegex=new RegExp(Kt.indentationRegex.source+Kt.listMarkerRegex.source+" +"+Kt.checkboxRegex.source+Kt.afterCheckboxRegex.source,"u"),J.nonTaskRegex=new RegExp(Kt.indentationRegex.source+Kt.listMarkerRegex.source+"? *("+Kt.checkboxRegex.source+")?"+Kt.afterCheckboxRegex.source,"u"),J.listItemRegex=new RegExp(Kt.indentationRegex.source+Kt.listMarkerRegex.source),J.blockLinkRegex=/ \^[a-zA-Z0-9-]+$/u,J.hashTags=/(^|\s)#[^ !@#$%^&*(),.?":{}|<>]+/g,J.hashTagsFromEnd=new RegExp(Kt.hashTags.source+"$");var Ei=/[a-zA-Z0-9-_]+/,xm=new RegExp(Ei.source+"( *, *"+Ei.source+" *)*"),yo={prioritySymbols:{Highest:"\u{1F53A}",High:"\u23EB",Medium:"\u{1F53C}",Low:"\u{1F53D}",Lowest:"\u23EC",None:""},startDateSymbol:"\u{1F6EB}",createdDateSymbol:"\u2795",scheduledDateSymbol:"\u23F3",dueDateSymbol:"\u{1F4C5}",doneDateSymbol:"\u2705",cancelledDateSymbol:"\u274C",recurrenceSymbol:"\u{1F501}",onCompletionSymbol:"\u{1F3C1}",dependsOnSymbol:"\u26D4",idSymbol:"\u{1F194}",TaskFormatRegularExpressions:{priorityRegex:/([🔺⏫🔼🔽⏬])\uFE0F?$/u,startDateRegex:/🛫 *(\d{4}-\d{2}-\d{2})$/u,createdDateRegex:/➕ *(\d{4}-\d{2}-\d{2})$/u,scheduledDateRegex:/[⏳⌛] *(\d{4}-\d{2}-\d{2})$/u,dueDateRegex:/[📅📆🗓] *(\d{4}-\d{2}-\d{2})$/u,doneDateRegex:/✅ *(\d{4}-\d{2}-\d{2})$/u,cancelledDateRegex:/❌ *(\d{4}-\d{2}-\d{2})$/u,recurrenceRegex:/🔁 ?([a-zA-Z0-9, !]+)$/iu,onCompletionRegex:/🏁 ?([a-zA-Z]+)$/iu,dependsOnRegex:new RegExp("\u26D4\uFE0F? *("+xm.source+")$","iu"),idRegex:new RegExp("\u{1F194} *("+Ei.source+")$","iu")}};function Xu(r,e,t){return t?r?" "+e:` ${e} ${t}`:""}function gs(r,e,t){return t?r?" "+e:` ${e} ${t.format(J.dateFormat)}`:""}function hv(){let r=[];return Object.values(yo.prioritySymbols).forEach(e=>{e.length>0&&r.push(e)}),Object.values(yo).forEach(e=>{typeof e=="string"&&r.push(e)}),r}var Si=class{constructor(e){this.symbols=e}serialize(e){let t=new zr,n="",i=!1;for(let s of t.shownComponents)n+=this.componentToString(e,i,s);return n}componentToString(e,t,n){var _;let{prioritySymbols:i,startDateSymbol:s,createdDateSymbol:a,scheduledDateSymbol:o,doneDateSymbol:u,cancelledDateSymbol:l,recurrenceSymbol:c,onCompletionSymbol:d,dueDateSymbol:p,dependsOnSymbol:m,idSymbol:y}=this.symbols;switch(n){case"description":return e.description;case"priority":{let b="";return e.priority==="0"?b=" "+i.Highest:e.priority==="1"?b=" "+i.High:e.priority==="2"?b=" "+i.Medium:e.priority==="4"?b=" "+i.Low:e.priority==="5"&&(b=" "+i.Lowest),b}case"startDate":return gs(t,s,e.startDate);case"createdDate":return gs(t,a,e.createdDate);case"scheduledDate":return e.scheduledDateIsInferred?"":gs(t,o,e.scheduledDate);case"doneDate":return gs(t,u,e.doneDate);case"cancelledDate":return gs(t,l,e.cancelledDate);case"dueDate":return gs(t,p,e.dueDate);case"recurrenceRule":return e.recurrence?Xu(t,c,e.recurrence.toText()):"";case"onCompletion":return e.onCompletion===""?"":Xu(t,d,e.onCompletion);case"dependsOn":return e.dependsOn.length===0?"":Xu(t,m,e.dependsOn.join(","));case"id":return Xu(t,y,e.id);case"blockLink":return(_=e.blockLink)!=null?_:"";default:throw new Error(`Don't know how to render task component of type '${n}'`)}}parsePriority(e){let{prioritySymbols:t}=this.symbols;switch(e){case t.Lowest:return"5";case t.Low:return"4";case t.Medium:return"2";case t.High:return"1";case t.Highest:return"0";default:return"3"}}deserialize(e){let{TaskFormatRegularExpressions:t}=this.symbols,n,i="3",s=null,a=null,o=null,u=null,l=null,c=null,d="",p=null,m="",y="",_=[],b="",E=20,R=0;do{n=!1;let S=e.match(t.priorityRegex);S!==null&&(i=this.parsePriority(S[1]),e=e.replace(t.priorityRegex,"").trim(),n=!0);let F=e.match(t.doneDateRegex);F!==null&&(u=window.moment(F[1],J.dateFormat),e=e.replace(t.doneDateRegex,"").trim(),n=!0);let q=e.match(t.cancelledDateRegex);q!==null&&(l=window.moment(q[1],J.dateFormat),e=e.replace(t.cancelledDateRegex,"").trim(),n=!0);let te=e.match(t.dueDateRegex);te!==null&&(o=window.moment(te[1],J.dateFormat),e=e.replace(t.dueDateRegex,"").trim(),n=!0);let G=e.match(t.scheduledDateRegex);G!==null&&(a=window.moment(G[1],J.dateFormat),e=e.replace(t.scheduledDateRegex,"").trim(),n=!0);let H=e.match(t.startDateRegex);H!==null&&(s=window.moment(H[1],J.dateFormat),e=e.replace(t.startDateRegex,"").trim(),n=!0);let be=e.match(t.createdDateRegex);be!==null&&(c=window.moment(be[1],J.dateFormat),e=e.replace(t.createdDateRegex,"").trim(),n=!0);let Pe=e.match(t.recurrenceRegex);Pe!==null&&(d=Pe[1].trim(),e=e.replace(t.recurrenceRegex,"").trim(),n=!0);let j=e.match(t.onCompletionRegex);if(j!=null){e=e.replace(t.onCompletionRegex,"").trim();let I=j[1];m=pv(I),n=!0}let $=e.match(J.hashTagsFromEnd);if($!=null){e=e.replace(J.hashTagsFromEnd,"").trim(),n=!0;let I=$[0].trim();b=b.length>0?[I,b].join(" "):I}let D=e.match(t.idRegex);D!=null&&(e=e.replace(t.idRegex,"").trim(),y=D[1].trim(),n=!0);let B=e.match(t.dependsOnRegex);B!=null&&(e=e.replace(t.dependsOnRegex,"").trim(),_=B[1].replace(/ /g,"").split(",").filter(I=>I!==""),n=!0),R++}while(n&&R<=E);return d.length>0&&(p=it.fromText({recurrenceRuleText:d,occurrence:new fr({startDate:s,scheduledDate:a,dueDate:o})})),b.length>0&&(e+=" "+b),{description:e,priority:i,startDate:s,createdDate:c,scheduledDate:a,dueDate:o,doneDate:u,cancelledDate:l,recurrence:p,onCompletion:m,id:y,dependsOn:_,tags:ae.extractHashtags(e)}}};function To(r){let e="",t=!0;for(;t;)e=Math.random().toString(36).substring(2,6+2),r.includes(e)||(t=!1);return e}function Zu(r,e){return r.id!==""?r:new ae(he(K({},r),{id:To(e)}))}function gv(r,e){let t=r;if(!r.dependsOn.includes(e.id)){let n=[...r.dependsOn,e.id];t=new ae(he(K({},r),{dependsOn:n}))}return t}function yv(r,e){let t=r;if(r.dependsOn.includes(e.id)){let n=r.dependsOn.filter(i=>i!==e.id);t=new ae(he(K({},r),{dependsOn:n}))}return t}function Hn(r){return r.replace(/([.*+?^${}()|[\]/\\])/g,"\\$1")}var Vn=class{constructor(){this._globalFilter="";this._removeGlobalFilter=!1}static getInstance(){return Vn.instance||(Vn.instance=new Vn),Vn.instance}get(){return this._globalFilter}set(e){this._globalFilter=e}reset(){this.set(Vn.empty)}isEmpty(){return this.get()===Vn.empty}equals(e){return this.get()===e}includedIn(e){let t=this.get();return e.includes(t)}prependTo(e){return this.get()+" "+e}removeAsWordFromDependingOnSettings(e){return this.getRemoveGlobalFilter()?this.removeAsWordFrom(e):e}getRemoveGlobalFilter(){return this._removeGlobalFilter}setRemoveGlobalFilter(e){this._removeGlobalFilter=e}removeAsWordFrom(e){if(this.isEmpty())return e;let t=RegExp("(^|\\s)"+Hn(this.get())+"($|\\s)","ug");return e.search(t)>-1&&(e=e.replace(t,"$1$2").replace("  "," ").trim()),e}removeAsSubstringFrom(e){let t=this.get();return e.replace(t,"").trim()}},_e=Vn;_e.empty="";var Tv=require("obsidian");var gN=20;function Oi(r){return _e.getInstance().removeAsWordFrom(r.description)}function yN(r,e){if(r==="")return e;let t=(0,Tv.prepareSimpleSearch)(r),n=-4;return e.map(a=>{let o=t(Oi(a));return o&&o.score>n?{item:a,match:o}:null}).filter(Boolean).sort((a,o)=>o.match.score-a.match.score).map(a=>a.item)}function Ju(r,e,t,n,i){let s=yN(r,e);return s=s.filter(a=>!(a.isDone||a.description.includes("<%")&&a.description.includes("%>")||a.description===(t==null?void 0:t.description)&&a.taskLocation.path===(t==null?void 0:t.taskLocation.path)&&a.originalMarkdown===(t==null?void 0:t.originalMarkdown)||(n==null?void 0:n.includes(a))||(i==null?void 0:i.includes(a)))),t&&s.sort((a,o)=>{let u=a.taskLocation.path===t.taskLocation.path,l=o.taskLocation.path===t.taskLocation.path;return u&&l?Math.abs(a.taskLocation.lineNumber-t.taskLocation.lineNumber)-Math.abs(o.taskLocation.lineNumber-t.taskLocation.lineNumber):u?-1:l?1:0}),s.slice(0,gN)}var Rm=5,bN=!0;globalThis.SHOW_DEPENDENCY_SUGGESTIONS=bN;function _v(r){return globalThis.SHOW_DEPENDENCY_SUGGESTIONS&&r}function Mm(r,e,t){let n=[r.startDateSymbol,r.scheduledDateSymbol,r.dueDateSymbol].join("|");return(i,s,a,o,u,l)=>{let c=[],{postfix:d,insertSkip:p}=TN(t,i,s),m={line:i,cursorPos:s,settings:a,dataviewMode:t,postfix:d,insertSkip:p};return c=c.concat(SN(n,e,m)),c=c.concat(ON(r.recurrenceSymbol,m)),_v(u)&&(c=c.concat(xN(r.idSymbol,o,m)),c=c.concat(RN(r.dependsOnSymbol,o,m,l))),c=c.concat(DN(r.onCompletionSymbol,e,m)),c=c.concat(_N(r,u,m)),c.length>0&&!c.some(y=>y.suggestionType==="match")&&(t||c.unshift({suggestionType:"empty",displayText:"\u23CE",appendText:`
`})),c=c.slice(0,a.autoSuggestMaxItems),c}}function TN(r,e,t){let n=CN(e.substring(0,t),[["(",")"],["[","]"]])=="("?")":"]",i=r?n+" ":" ",s=r&&e.length>t&&e.charAt(t)===n?1:0;return{postfix:i,insertSkip:s}}function _N(r,e,t){let n=[],i=t.line;Di(n,i,r.dueDateSymbol,"due date"),Di(n,i,r.startDateSymbol,"start date"),Di(n,i,r.scheduledDateSymbol,"scheduled date"),vN(n,r,t),Di(n,i,r.recurrenceSymbol,"recurring (repeat)"),wN(n,r,t),_v(e)&&(Di(n,i,r.idSymbol,"id"),Di(n,i,r.dependsOnSymbol,"depends on id")),Di(n,i,r.onCompletionSymbol,"on completion");let s=kN(n,t);return s.length===0&&t.settings.autoSuggestMinMatch===0?n:s}function Di(r,e,t,n){e.includes(t)||r.push({displayText:`${t} ${n}`,appendText:`${t} `})}function vN(r,e,t){if(!(i=>Object.values(e.prioritySymbols).some(s=>s.length>0&&i.includes(s)))(t.line)){let i=e.prioritySymbols,s=["High","Medium","Low","Highest","Lowest"];for(let a of s){let o=i[a];r.push({displayText:t.dataviewMode?`${o} priority`:`${o} ${a.toLowerCase()} priority`,appendText:`${o}${t.postfix}`,insertSkip:t.dataviewMode?t.insertSkip:void 0})}}}function wN(r,e,t){if(!t.line.includes(e.createdDateSymbol)){let i=Ht.parseDate("today",!0).format(J.dateFormat);r.push({textToMatch:`${e.createdDateSymbol} created`,displayText:`${e.createdDateSymbol} created today (${i})`,appendText:`${e.createdDateSymbol} ${i}`+t.postfix,insertSkip:t.dataviewMode?t.insertSkip:void 0})}}function kN(r,e){let t=ys(/([a-zA-Z'_-]*)/g,e),n=[];if(t&&t.length>0){let i=t[0];if(i.length>=Math.max(1,e.settings.autoSuggestMinMatch)){let s=r.filter(a=>{var u;return((u=a.textToMatch)!=null?u:a.displayText).toLowerCase().includes(i.toLowerCase())});for(let a of s){let o=e.dataviewMode&&(a.displayText.includes("priority")||a.displayText.includes("created"))?i.length+e.insertSkip:i.length;n.push({suggestionType:"match",displayText:a.displayText,appendText:a.appendText,insertAt:t.index,insertSkip:o})}}}return n}function vv(r,e){let t=`${e}`,n=`${r} ${e}`;return{displayText:t,appendText:n}}function EN(r,e){let n=`${Ht.parseDate(e,!0).format(J.dateFormat)}`,i=`${e} (${n})`,s=`${r} ${n}`;return{displayText:i,appendText:s}}function SN(r,e,t){let n=["today","tomorrow","Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday","next week","next month","next year"],i=[],s=new RegExp(`(${r})\\s*([0-9a-zA-Z ]*)`,"ug"),a=ys(s,t);if(a&&a.length>=2){let o=a[2];if(o.length<t.settings.autoSuggestMinMatch)return[];let u=o&&o.length>1?Ht.parseDate($u(o),!0):null;if(u!=null&&u.isValid()){let c=u.format(J.dateFormat);el(t,a,[c],vv,i)}let l=Cm(n,o,e,!0);el(t,a,l,EN,i)}return i}function ON(r,e){var a;let t=["every","every day","every week","every month","every month on the","every year","every week on Sunday","every week on Monday","every week on Tuesday","every week on Wednesday","every week on Thursday","every week on Friday","every week on Saturday"],n=[],i=new RegExp(`(${r})\\s*([0-9a-zA-Z ]*)`,"ug"),s=ys(i,e);if(s&&s.length>=2){let o=s[1],u=s[2];if(u.length<e.settings.autoSuggestMinMatch)return[];if(u.length>0){let p=(a=it.fromText({recurrenceRuleText:u,occurrence:new fr({startDate:null,scheduledDate:null,dueDate:null})}))==null?void 0:a.toText();if(p){let m=`${o} ${p}`+e.postfix;if(n.push({suggestionType:"match",displayText:`\u2705 ${p}`,appendText:m,insertAt:s.index,insertSkip:Am(s[0],e)}),s[0]==m)return[]}}let l=e.settings.autoSuggestMaxItems/2,c=Cm(t,u,l,!1);c.length===0&&u.trim().length===0&&(c=t.slice(0,l)),el(e,s,c,(p,m)=>{let y=`${m}`,_=`${p} ${m}`;return{displayText:y,appendText:_}},n)}return n}function DN(r,e,t){let n=["delete","keep"],i=[],s=new RegExp(`(${r})\\s*([0-9a-zA-Z ]*)`,"ug"),a=ys(s,t);if(a&&a.length>=2){let o=a[2];if(o.length<t.settings.autoSuggestMinMatch)return[];let u=Cm(n,o,e,!0);el(t,a,u,vv,i)}return i}function xN(r,e,t){let n=[],i=new RegExp(`(${r})\\s*(${Ei.source})?`,"ug"),s=ys(i,t);if(s&&s[0].trim().length<=r.length){let a=To(e.map(o=>o.id));n.push({suggestionType:"match",displayText:"generate unique id",appendText:`${r} ${a}`+t.postfix,insertAt:s.index,insertSkip:Am(s[0],t)})}return n}function RN(r,e,t,n){let i=[],s=t.dataviewMode?Hn("()[]"):hv(),a=new RegExp(`(${r})([0-9a-zA-Z-_ ^,]*,)*([^,${s}]*)`,"ug"),o=ys(a,t);if(o&&o.length>=1){let u=o[2]||"",l=o[3],c=[];if(u){let d=u.split(",").map(p=>p.trim());c=e.filter(p=>p.id&&d.includes(p.id))}if(l.length>=t.settings.autoSuggestMinMatch){let d=Ju(l.trim(),e,n,[],c);for(let p of d)i.push({suggestionType:"match",displayText:`${p.descriptionWithoutTags} - From: ${p.filename}.md`,appendText:`${r}${u}`,insertAt:o.index,insertSkip:r.length+u.length+l.length,taskItDependsOn:p})}}return i}function Cm(r,e,t,n){let s=r.filter(a=>e&&e.length>=1&&a.toLowerCase().includes(e.toLowerCase())).slice(0,t);return n&&s.length===0&&(s=r.slice(0,t)),s}function el(r,e,t,n,i){let s=e[1];for(let a of t){let{displayText:o,appendText:u}=n(s,a);i.push({suggestionType:"match",displayText:o,appendText:u+r.postfix,insertAt:e.index,insertSkip:Am(e[0],r)})}}function ys(r,e){let t=e.line.matchAll(r),n=e.cursorPos;for(let i of t)if((i==null?void 0:i.index)&&i.index<n&&n<=i.index+i[0].length)return i}function MN(r,e){if(e.length===0)return!1;let t=Object.fromEntries(e.map(([i,s])=>[i,0])),n=Object.fromEntries(e.map(([i,s])=>[s,i]));for(let i of r)i in t?t[i]++:i in n&&(t[n[i]]=Math.max(0,t[n[i]]-1));return Object.values(t).some(i=>i>0)}function CN(r,e){if(e.length===0)return null;let t=Object.fromEntries(e.map(([s,a])=>[s,0])),n=Object.fromEntries(e.map(([s,a])=>[a,s])),i=[];for(let s=0;s<r.length;s++){let a=r[s];if(a in t)t[a]++,i.push({bracket:a,idx:s});else if(a in n){if(t[n[a]]>=1){for(let o=i.length-1;o>=0;o--)if(i[o].bracket==n[a]){i.splice(o,1);break}}t[n[a]]=Math.max(0,t[n[a]]-1)}}return i.length>0?i[i.length-1].bracket:null}function wv(r,e){return(t,n,i,s,a)=>MN(t.slice(0,n),e)?r(t,n,i,s,a):[]}function kv(r,e,t){let n=_e.getInstance().includedIn(r),i=AN(t,e,n);return typeof i=="boolean"?i:n&&PN(r,e.ch)}function AN(r,e,t){var n,i;return(i=(n=r==null?void 0:r.editorComponent)==null?void 0:n.showTasksPluginAutoSuggest)==null?void 0:i.call(n,e,r,t)}function PN(r,e){if(r.length===0)return!1;let t=ae.extractTaskComponents(r);if(!t)return!1;let n=t.indentation+t.listMarker+" ["+t.status.symbol+"] ";return e>=n.length}function Am(r,e){return e.dataviewMode?r.length+e.insertSkip:r.length}function Rr(r){let e=["(?:",/(?=[^\]]+\])\[/,"|",/(?=[^)]+\))\(/,")",/ */,r,/ */,/[)\]]/,/(?: *,)?/,/$/].map(t=>t instanceof RegExp?t.source:t).join("");return new RegExp(e,r.flags)}var Pm={prioritySymbols:{Highest:"priority:: highest",High:"priority:: high",Medium:"priority:: medium",Low:"priority:: low",Lowest:"priority:: lowest",None:""},startDateSymbol:"start::",createdDateSymbol:"created::",scheduledDateSymbol:"scheduled::",dueDateSymbol:"due::",doneDateSymbol:"completion::",cancelledDateSymbol:"cancelled::",recurrenceSymbol:"repeat::",onCompletionSymbol:"onCompletion::",idSymbol:"id::",dependsOnSymbol:"dependsOn::",TaskFormatRegularExpressions:{priorityRegex:Rr(/priority:: *(highest|high|medium|low|lowest)/),startDateRegex:Rr(/start:: *(\d{4}-\d{2}-\d{2})/),createdDateRegex:Rr(/created:: *(\d{4}-\d{2}-\d{2})/),scheduledDateRegex:Rr(/scheduled:: *(\d{4}-\d{2}-\d{2})/),dueDateRegex:Rr(/due:: *(\d{4}-\d{2}-\d{2})/),doneDateRegex:Rr(/completion:: *(\d{4}-\d{2}-\d{2})/),cancelledDateRegex:Rr(/cancelled:: *(\d{4}-\d{2}-\d{2})/),recurrenceRegex:Rr(/repeat:: *([a-zA-Z0-9, !]+)/),onCompletionRegex:Rr(/onCompletion:: *([a-zA-Z]+)/),dependsOnRegex:Rr(new RegExp("dependsOn:: *("+xm.source+")")),idRegex:Rr(new RegExp("id:: *("+Ei.source+")"))}},tl=class extends Si{constructor(){super(Pm)}parsePriority(e){switch(e){case"highest":return"0";case"high":return"1";case"medium":return"2";case"low":return"4";case"lowest":return"5";default:return"3"}}componentToString(e,t,n){let i=super.componentToString(e,t,n),s=["blockLink","description"];return i!==""&&!s.includes(n)?`  [${i.trim()}]`:i}};var Et=class{get symbol(){return this.configuration.symbol}get name(){return this.configuration.name}get nextStatusSymbol(){return this.configuration.nextStatusSymbol}get nextSymbol(){return this.configuration.nextStatusSymbol}get availableAsCommand(){return this.configuration.availableAsCommand}get type(){return this.configuration.type}get typeGroupText(){let e=this.type,t;switch(e){case"IN_PROGRESS":t="1";break;case"TODO":t="2";break;case"DONE":t="3";break;case"CANCELLED":t="4";break;case"NON_TASK":t="5";break;case"EMPTY":t="6";break}return`%%${t}%%${e}`}constructor(e){this.configuration=e}static makeDone(){return new Et(new Ke("x","Done"," ",!0,"DONE"))}static makeEmpty(){return new Et(new Ke("","EMPTY","",!0,"EMPTY"))}static makeTodo(){return new Et(new Ke(" ","Todo","x",!0,"TODO"))}static makeCancelled(){return new Et(new Ke("-","Cancelled"," ",!0,"CANCELLED"))}static makeInProgress(){return new Et(new Ke("/","In Progress","x",!0,"IN_PROGRESS"))}static makeNonTask(){return new Et(new Ke("Q","Non-Task","A",!0,"NON_TASK"))}static getTypeForUnknownSymbol(e){switch(e){case"x":case"X":return"DONE";case"/":return"IN_PROGRESS";case"-":return"CANCELLED";case"":return"EMPTY";case" ":default:return"TODO"}}static getTypeFromStatusTypeString(e){return Nt[e]||"TODO"}static createUnknownStatus(e){return new Et(new Ke(e,"Unknown","x",!1,"TODO"))}static createFromImportedValue(e){let t=e[0],n=Et.getTypeFromStatusTypeString(e[3]);return new Et(new Ke(t,e[1],e[2],!1,n))}isCompleted(){return this.type==="DONE"}isCancelled(){return this.type==="CANCELLED"}identicalTo(e){let t=["symbol","name","nextStatusSymbol","availableAsCommand","type"];for(let n of t)if(this[n]!==e[n])return!1;return!0}previewText(){let e="";return Et.tasksPluginCanCreateCommandsForStatuses()&&this.availableAsCommand&&(e=" Available as a command."),`- [${this.symbol}] => [${this.nextStatusSymbol}], name: '${this.name}', type: '${this.configuration.type}'.${e}`}static tasksPluginCanCreateCommandsForStatuses(){return!1}},ne=Et;ne.DONE=Et.makeDone(),ne.EMPTY=Et.makeEmpty(),ne.TODO=Et.makeTodo();var rl=class{constructor(e=!1,t=!1,n=!1){this.ignoreSortInstructions=e,this.showTaskHiddenData=t,this.recordTimings=n}};var Le=class{constructor(){this.coreStatuses=[ne.makeTodo().configuration,ne.makeDone().configuration],this.customStatuses=[ne.makeInProgress().configuration,ne.makeCancelled().configuration]}static addStatus(e,t){e.push(t)}static replaceStatus(e,t,n){let i=this.findStatusIndex(t,e);return i<=-1?!1:(e.splice(i,1,n),!0)}static findStatusIndex(e,t){let n=new ne(e);return t.findIndex(i=>new ne(i).previewText()==n.previewText())}static deleteStatus(e,t){let n=this.findStatusIndex(t,e);return n<=-1?!1:(e.splice(n,1),!0)}static deleteAllCustomStatuses(e){e.customStatuses.splice(0)}static resetAllCustomStatuses(e){Le.deleteAllCustomStatuses(e),new Le().customStatuses.forEach(n=>{Le.addStatus(e.customStatuses,n)})}static bulkAddStatusCollection(e,t){let n=[];return t.forEach(i=>{e.customStatuses.find(a=>a.symbol==i[0]&&a.name==i[1]&&a.nextStatusSymbol==i[2])?n.push(`The status ${i[1]} (${i[0]}) is already added.`):Le.addStatus(e.customStatuses,ne.createFromImportedValue(i))}),n}static allStatuses(e){return e.coreStatuses.concat(e.customStatuses)}static applyToStatusRegistry(e,t){t.clearStatuses(),Le.allStatuses(e).forEach(n=>{t.add(n)})}};var Ev=[{index:9999,internalName:"INTERNAL_TESTING_ENABLED_BY_DEFAULT",displayName:"Test Item. Used to validate the Feature Framework.",description:"Description",enabledByDefault:!0,stable:!1}];var kn=class{constructor(e,t,n,i,s,a){this.internalName=e;this.index=t;this.description=n;this.displayName=i;this.enabledByDefault=s;this.stable=a}static get values(){let e=[];return Ev.forEach(t=>{e=[...e,new kn(t.internalName,t.index,t.description,t.displayName,t.enabledByDefault,t.stable)]}),e}static get settingsFlags(){let e={};return kn.values.forEach(t=>{e[t.internalName]=t.enabledByDefault}),e}static fromString(e){for(let t of kn.values)if(e===t.internalName)return t;throw new RangeError(`Illegal argument passed to fromString(): ${e} does not correspond to any available Feature ${this.prototype.constructor.name}`)}};var Mr={tasksPluginEmoji:{displayName:"Tasks Emoji Format",taskSerializer:new Si(yo),buildSuggestions:Mm(yo,Rm,!1)},dataview:{displayName:"Dataview",taskSerializer:new tl,buildSuggestions:wv(Mm(Pm,Rm,!0),[["(",")"],["[","]"]])}},Im={globalQuery:"",globalFilter:"",removeGlobalFilter:!1,taskFormat:"tasksPluginEmoji",setCreatedDate:!1,setDoneDate:!0,setCancelledDate:!0,autoSuggestInEditor:!0,autoSuggestMinMatch:0,autoSuggestMaxItems:20,provideAccessKeys:!0,useFilenameAsScheduledDate:!1,filenameAsScheduledDateFormat:"",filenameAsDateFolders:[],recurrenceOnNextLine:!1,statusSettings:new Le,features:kn.settingsFlags,generalSettings:{},headingOpened:{},debugSettings:new rl,loggingOptions:{minLevels:{"":"info",tasks:"info","tasks.Cache":"info","tasks.Events":"info","tasks.File":"info","tasks.Query":"info","tasks.Task":"info"}}},En=K({},Im);function Nm(r,e){for(let t in r)e[t]===void 0&&(e[t]=r[t])}var X=()=>(Nm(kn.settingsFlags,En.features),Nm(Im.loggingOptions.minLevels,En.loggingOptions.minLevels),Nm(Im.debugSettings,En.debugSettings),En.statusSettings.customStatuses.forEach((r,e,t)=>{var i,s;let n=ne.getTypeFromStatusTypeString(r.type);t[e]=new Ke((i=r.symbol)!=null?i:" ",r.name,(s=r.nextStatusSymbol)!=null?s:"x",r.availableAsCommand,n)}),K({},En)),Ve=r=>(En=K(K({},En),r),X());var xi=(r,e)=>(En.generalSettings[r]=e,X()),Sv=r=>{var e;return(e=En.features[r])!=null?e:!1};function _o(){return Mr[X().taskFormat]}function nl(r){let t={"<":"&lt;",">":"&gt;","&":"&amp;",'"':"&quot;"}[r];return t!==void 0?t:r}function Ov(r){let e=[...r],t="";return e.forEach(n=>{t+=nl(n)}),t}var De=class{constructor(){this._registeredStatuses=[];this.addDefaultStatusTypes()}set(e){this.clearStatuses(),e.forEach(t=>{this.add(t)})}get registeredStatuses(){return this._registeredStatuses.filter(({symbol:e})=>e!==ne.EMPTY.symbol)}static getInstance(){return De.instance||(De.instance=new De),De.instance}add(e){this.hasSymbol(e.symbol)||(e instanceof ne?this._registeredStatuses.push(e):this._registeredStatuses.push(new ne(e)))}bySymbol(e){return this.hasSymbol(e)?this.getSymbol(e):ne.EMPTY}bySymbolOrCreate(e){return this.hasSymbol(e)?this.getSymbol(e):ne.createUnknownStatus(e)}byName(e){return this._registeredStatuses.filter(({name:t})=>t===e).length>0?this._registeredStatuses.filter(({name:t})=>t===e)[0]:ne.EMPTY}resetToDefaultStatuses(){this.clearStatuses(),this.addDefaultStatusTypes()}clearStatuses(){this._registeredStatuses=[]}getNextStatus(e){if(e.nextStatusSymbol!==""){let t=this.bySymbol(e.nextStatusSymbol);if(t!==null)return t}return ne.EMPTY}getNextStatusOrCreate(e){let t=this.getNextStatus(e);return t.type!=="EMPTY"?t:ne.createUnknownStatus(e.nextStatusSymbol)}getNextRecurrenceStatusOrCreate(e){let t=this.getNextStatusOrCreate(e),n=this.getNextRecurrenceStatusOfType(t,"TODO");if(n)return n;let i=this.getNextRecurrenceStatusOfType(t,"IN_PROGRESS");return i||this.bySymbolOrCreate(" ")}getNextRecurrenceStatusOfType(e,t){if(e.type===t)return e;let n=e;for(let i=0;i<this.registeredStatuses.length-1;i++)if(n=this.getNextStatusOrCreate(n),n.type===t)return n}findUnknownStatuses(e){let t=e.filter(s=>!this.hasSymbol(s.symbol)),n=new De,i=[];return t.forEach(s=>{if(n.hasSymbol(s.symbol))return;let a=De.copyStatusWithNewName(s,`Unknown (${s.symbol})`);i.push(a),n.add(a)}),i.sort((s,a)=>s.symbol.localeCompare(a.symbol,void 0,{numeric:!0}))}static copyStatusWithNewName(e,t){let n=new Ke(e.symbol,t,e.nextStatusSymbol,e.availableAsCommand,e.type);return new ne(n)}getSymbol(e){return this._registeredStatuses.filter(({symbol:t})=>t===e)[0]}hasSymbol(e){return this._registeredStatuses.find(t=>t.symbol===e)!==void 0}addDefaultStatusTypes(){[ne.makeTodo(),ne.makeInProgress(),ne.makeDone(),ne.makeCancelled()].forEach(t=>{this.add(t)})}mermaidDiagram(e=!1){let t=this.registeredStatuses,n="mermaid",i=[],s=[];return t.forEach((a,o)=>{let u=this.getMermaidNodeLabel(a,e);i.push(`${o+1}${u}`);let l=this.getNextStatus(a);if(this.addEdgeIfNotToInternal(t,l,s,o,!1),a.type==="DONE"){let c=this.getNextRecurrenceStatusOrCreate(a);c.symbol!==l.symbol&&this.addEdgeIfNotToInternal(t,c,s,o,!0)}}),`
\`\`\`${n}
flowchart LR

classDef TODO        stroke:#f33,stroke-width:3px;
classDef DONE        stroke:#0c0,stroke-width:3px;
classDef IN_PROGRESS stroke:#fa0,stroke-width:3px;
classDef CANCELLED   stroke:#ddd,stroke-width:3px;
classDef NON_TASK    stroke:#99e,stroke-width:3px;

${i.join(`
`)}
${s.join(`
`)}

linkStyle default stroke:gray
\`\`\`
`}addEdgeIfNotToInternal(e,t,n,i,s){let a=e.findIndex(l=>l.symbol===t.symbol),o=a!==-1,u=t.type!=="EMPTY";if(o&&u){let l;s?l='-. "\u{1F501}" .-> ':l=" --> ";let c=`${i+1}${l}${a+1}`;n.push(c)}}getMermaidNodeLabel(e,t){let n=Ov(e.name),i=e.type;if(t){let s=nl(e.symbol),a=nl(e.nextStatusSymbol),o=`[${s}] -> [${a}]`,u=`'${n}'`,l=`(${i})`;return`["${u}<br>${o}<br>${l}"]:::${i}`}else return`["${n}"]:::${i}`}};var Dv=require("obsidian");var Kr=class{constructor(e,t){this.name=e,this.sortOrder=t}get groupText(){return this.name!==""?`%%${this.sortOrder}%% ${this.name}`:""}};var It=class{constructor(e){this._date=null;this._date=e}get moment(){return this._date}formatAsDate(e=""){return this.format(J.dateFormat,e)}formatAsDateAndTime(e=""){return this.format(J.dateTimeFormat,e)}format(e,t=""){return this._date?this._date.format(e):t}toISOString(e){return this._date?this._date.toISOString(e):""}get category(){let e=window.moment(),t=this.moment;return t?t.isBefore(e,"day")?new Kr("Overdue",1):t.isSame(e,"day")?new Kr("Today",2):t.isValid()?new Kr("Future",3):new Kr("Invalid date",0):new Kr("Undated",4)}get fromNow(){let e=this.moment;if(!e)return new Kr("",0);let t=this.fromNowOrder(e);return new Kr(e.fromNow(),t)}fromNowOrder(e){if(!e.isValid())return 0;let t=window.moment(),n=e.isSameOrBefore(t,"day"),i=this.fromNowStartDateOfGroup(e,n,t);return Number((n?1:3)+i.format("YYYYMMDD"))}fromNowStartDateOfGroup(e,t,n){let i=e.fromNow(!0).split(" "),s,a=Number(i[0]);isNaN(a)?s=1:s=a;let o=i[1];return t?n.subtract(s,o):n.add(s,o)}postpone(e="days",t=1){if(!this._date)throw new Dv.Notice("Cannot postpone a null date");let n=window.moment().startOf("day");return this._date.isSameOrAfter(n,"day")?this._date.clone().add(t,e):n.add(t,e)}};var Sn=class{static priorityNameUsingNone(e){let t="ERROR";switch(e){case"1":t="High";break;case"0":t="Highest";break;case"2":t="Medium";break;case"3":t="None";break;case"4":t="Low";break;case"5":t="Lowest";break}return t}static priorityNameUsingNormal(e){return Sn.priorityNameUsingNone(e).replace("None","Normal")}};var IN=require("obsidian"),Cv=oa(Mv());var Fm=class extends Cv.EventEmitter2{constructor(){super(...arguments);this.options={minLevels:{"":"info",tasks:"info"}};this.consoleLoggerRegistered=!1;this.arrAvg=t=>t.reduce((n,i)=>n+i,0)/t.length}configure(t){return this.options=Object.assign({},this.options,t),this}getLogger(t){let n="none",i="";for(let s in this.options.minLevels)t.startsWith(s)&&s.length>=i.length&&(n=this.options.minLevels[s],i=s);return new Lm(this,t,n)}onLogEntry(t){return this.on("log",t),this}registerConsoleLogger(){return this.consoleLoggerRegistered?this:(this.onLogEntry(t=>{let n=`[${window.moment().format("YYYY-MM-DD-HH:mm:ss.SSS")}][${t.level}][${t.module}]`;switch(t.traceId&&(n+=`[${t.traceId}]`),n+=` ${t.message}`,t.objects===void 0&&(t.objects=""),t.level){case"trace":console.trace(n,t.objects);break;case"debug":console.debug(n,t.objects);break;case"info":console.info(n,t.objects);break;case"warn":console.warn(n,t.objects);break;case"error":console.error(n,t.objects);break;default:console.log(`{${t.level}} ${n}`,t.objects)}}),this.consoleLoggerRegistered=!0,this)}},St=new Fm,Lm=class{constructor(e,t,n){this.levels={trace:1,debug:2,info:3,warn:4,error:5};this.logManager=e,this.module=t,this.minLevel=this.levelToInt(n)}levelToInt(e){return e.toLowerCase()in this.levels?this.levels[e.toLowerCase()]:99}log(e,t,n){if(this.levelToInt(e)<this.minLevel)return;let s={level:e,module:this.module,message:t,objects:n,traceId:void 0};this.logManager.emit("log",s)}trace(e,t){this.log("trace",e,t)}debug(e,t){this.log("debug",e,t)}info(e,t){this.log("info",e,t)}warn(e,t){this.log("warn",e,t)}error(e,t){this.log("error",e,t)}logWithId(e,t,n,i){if(this.levelToInt(e)<this.minLevel)return;let a={level:e,module:this.module,message:n,objects:i,traceId:t};this.logManager.emit("log",a)}traceWithId(e,t,n){this.logWithId("trace",e,t,n)}debugWithId(e,t,n){this.logWithId("debug",e,t,n)}infoWithId(e,t,n){this.logWithId("info",e,t,n)}warnWithId(e,t,n){this.logWithId("warn",e,t,n)}errorWithId(e,t,n){this.logWithId("error",e,t,n)}};function Um(r,e){let t=St.getLogger("tasks");switch(r){case"trace":t.trace(e);break;case"debug":t.debug(e);break;case"info":t.info(e);break;case"warn":t.warn(e);break;case"error":t.error(e);break;default:break}}function il(r,e,t){r.debug(`${e}: task line number: ${t.taskLocation.lineNumber}. file path: "${t.path}"`),r.debug(`${e} original: ${t.originalMarkdown}`)}function sl(r,e,t){t.map((n,i)=>{r.debug(`${e} ==> ${i+1}   : ${n.toFileLineString()}`)})}var yt=class{static fromPath(e){let{useFilenameAsScheduledDate:t,filenameAsDateFolders:n}=X();return!t||!this.matchesAnyFolder(n,e)?null:this.extractDateFromPath(e)}static matchesAnyFolder(e,t){return e.length===0?!0:e.some(n=>t.startsWith(n+"/"))}static extractDateFromPath(e){let t=Math.max(0,e.lastIndexOf("/")+1),n=e.lastIndexOf("."),i=e.substring(t,n),{filenameAsScheduledDateFormat:s}=X();if(s!==""){let o=window.moment(i,s,!0);if(o.isValid())return o}let a=/(\d{4})-(\d{2})-(\d{2})/.exec(i);if(a||(a=/(\d{4})(\d{2})(\d{2})/.exec(i)),a){let o=window.moment([parseInt(a[1]),parseInt(a[2])-1,parseInt(a[3])]);if(o.isValid())return o}return null}static canApplyFallback({startDate:e,scheduledDate:t,dueDate:n}){return e===null&&n===null&&t===null}static updateTaskPath(e,t,n){let i=e.scheduledDate,s=e.scheduledDateIsInferred;return n===null?s&&(s=!1,i=null):s?i=n:this.canApplyFallback(e)&&(i=n,s=!0),new ae(he(K({},e),{taskLocation:t,scheduledDate:i,scheduledDateIsInferred:s}))}static removeInferredStatusIfNeeded(e,t){let n=e.scheduledDateIsInferred?e.scheduledDate:null;return t.map(i=>(n!==null&&!n.isSame(i.scheduledDate,"day")&&(i=new ae(he(K({},i),{scheduledDateIsInferred:!1}))),i))}};var Cr=class{static calculate(e){var n,i,s;let t=0;if((n=e.dueDate)!=null&&n.isValid()){let a=window.moment().startOf("day"),o=Math.round(a.diff(e.dueDate)/Cr.milliSecondsPerDay),u;o>=7?u=1:o>=-14?u=(o+14)*.8/21+.2:u=.2,t+=u*Cr.dueCoefficient}switch((i=e.scheduledDate)!=null&&i.isValid()&&window.moment().isSameOrAfter(e.scheduledDate)&&(t+=1*Cr.scheduledCoefficient),(s=e.startDate)!=null&&s.isValid()&&window.moment().isBefore(e.startDate)&&(t+=1*Cr.startedCoefficient),e.priority){case"0":t+=1.5*Cr.priorityCoefficient;break;case"1":t+=1*Cr.priorityCoefficient;break;case"2":t+=.65*Cr.priorityCoefficient;break;case"3":t+=.325*Cr.priorityCoefficient;break;case"5":t-=.3*Cr.priorityCoefficient;break}return t}},On=Cr;On.dueCoefficient=12,On.scheduledCoefficient=5,On.startedCoefficient=-3,On.priorityCoefficient=6,On.milliSecondsPerDay=1e3*60*60*24;var ae=class extends Vi{constructor({status:t,description:n,taskLocation:i,indentation:s,listMarker:a,priority:o,createdDate:u,startDate:l,scheduledDate:c,dueDate:d,doneDate:p,cancelledDate:m,recurrence:y,onCompletion:_,dependsOn:b,id:E,blockLink:R,tags:S,originalMarkdown:F,scheduledDateIsInferred:q,parent:te=null}){super(F,te);this._urgency=null;this.status=t,this.description=n,this.indentation=s,this.listMarker=a,this.taskLocation=i,this.tags=S,this.priority=o,this.createdDate=u,this.startDate=l,this.scheduledDate=c,this.dueDate=d,this.doneDate=p,this.cancelledDate=m,this.recurrence=y,this.onCompletion=_,this.dependsOn=b,this.id=E,this.blockLink=R,this.scheduledDateIsInferred=q}static fromLine({line:t,taskLocation:n,fallbackDate:i}){let s=ae.extractTaskComponents(t);return s===null||!_e.getInstance().includedIn(s.body)?null:ae.parseTaskSignifiers(t,n,i)}static parseTaskSignifiers(t,n,i){let s=ae.extractTaskComponents(t);if(s===null)return null;let{taskSerializer:a}=_o(),o=a.deserialize(s.body),u=!1;return yt.canApplyFallback(o)&&i!==null&&(o.scheduledDate=i,u=!0),o.tags=o.tags.map(l=>l.trim()),o.tags=o.tags.filter(l=>!_e.getInstance().equals(l)),new ae(he(K(K({},s),o),{taskLocation:n,originalMarkdown:t,scheduledDateIsInferred:u}))}static extractTaskComponents(t){let n=t.match(J.taskRegex);if(n===null)return null;let i=n[1],s=n[2],a=n[3],o=De.getInstance().bySymbolOrCreate(a),u=n[4].trim(),l=u.match(J.blockLinkRegex),c=l!==null?l[0]:"";return c!==""&&(u=u.replace(J.blockLinkRegex,"").trim()),{indentation:i,listMarker:s,status:o,body:u,blockLink:c}}toString(){return _o().taskSerializer.serialize(this)}toFileLineString(){return`${this.indentation}${this.listMarker} [${this.status.symbol}] ${this.toString()}`}toggle(){let t=St.getLogger("tasks.Task"),n="toggle()";il(t,n,this);let i=De.getInstance().getNextStatusOrCreate(this.status),s=this.handleNewStatus(i);return sl(t,n,s),s}handleNewStatus(t,n=window.moment()){if(t.identicalTo(this.status))return[this];let{setDoneDate:i}=X(),s=this.newDate(t,"DONE",this.doneDate,i,n),{setCancelledDate:a}=X(),o=this.newDate(t,"CANCELLED",this.cancelledDate,a,n),u=new ae(he(K({},this),{status:t,doneDate:s,cancelledDate:o})),l=!t.isCompleted(),c=this.status.isCompleted(),d=this.recurrence===null;if(l||c||d)return[u];let m=this.recurrence.next(n);return m===null?[u]:[this.createNextOccurrence(t,m),u]}newDate(t,n,i,s,a){let o=null;return t.type===n&&(this.status.type!==n?s&&(o=a):o=i),o}createNextOccurrence(t,n){let{setCreatedDate:i}=X(),s=null;i&&(s=window.moment());let a=null,o=null,l=De.getInstance().getNextRecurrenceStatusOrCreate(t);return new ae(he(K(K({},this),n),{status:l,blockLink:"",id:"",dependsOn:[],createdDate:s,cancelledDate:a,doneDate:o}))}toggleWithRecurrenceInUsersOrder(){let t=this.toggle();return this.putRecurrenceInUsersOrder(t)}handleNewStatusWithRecurrenceInUsersOrder(t,n=window.moment()){St.getLogger("tasks.Task").debug(`changed task ${this.taskLocation.path} ${this.taskLocation.lineNumber} ${this.originalMarkdown} status to '${t.symbol}'`);let s=this.handleNewStatus(t,n);return this.putRecurrenceInUsersOrder(s)}putRecurrenceInUsersOrder(t){let n=mv(this,t),{recurrenceOnNextLine:i}=X();return i?n.reverse():n}get isDone(){return this.status.type==="DONE"||this.status.type==="CANCELLED"||this.status.type==="NON_TASK"}isBlocked(t){if(this.dependsOn.length===0||this.isDone)return!1;for(let n of this.dependsOn)if(!!t.find(s=>s.id===n&&!s.isDone))return!0;return!1}isBlocking(t){return this.id===""||this.isDone?!1:t.some(n=>n.isDone?!1:n.dependsOn.includes(this.id))}get priorityNumber(){return Number.parseInt(this.priority)}get priorityNameGroupText(){let t=Sn.priorityNameUsingNormal(this.priority);return`%%${this.priority}%%${t} priority`}get descriptionWithoutTags(){return this.description.replace(J.hashTags,"").trim()}get priorityName(){return Sn.priorityNameUsingNormal(this.priority)}get urgency(){return this._urgency===null&&(this._urgency=On.calculate(this)),this._urgency}get path(){return this.taskLocation.path}get cancelled(){return new It(this.cancelledDate)}get created(){return new It(this.createdDate)}get done(){return new It(this.doneDate)}get due(){return new It(this.dueDate)}get scheduled(){return new It(this.scheduledDate)}get start(){return new It(this.startDate)}get happensDates(){return Array.of(this.startDate,this.scheduledDate,this.dueDate)}get happens(){let t=this.happensDates,n=Array.from(t).sort(dr);for(let i of n)if(i!=null&&i.isValid())return new It(i);return new It(null)}get isRecurring(){return this.recurrence!==null}get recurrenceRule(){return this.recurrence?this.recurrence.toText():""}get heading(){return this.precedingHeader}get hasHeading(){return this.precedingHeader!==null}get file(){return this.taskLocation.tasksFile}get filename(){let t=this.path.match(/([^/]+)\.md$/);return t!==null?t[1]:null}get lineNumber(){return this.taskLocation.lineNumber}get sectionStart(){return this.taskLocation.sectionStart}get sectionIndex(){return this.taskLocation.sectionIndex}get precedingHeader(){return this.taskLocation.precedingHeader}getLinkText({isFilenameUnique:t}){let n;return t?n=this.filename:n="/"+this.path,n===null?null:(this.precedingHeader!==null&&this.precedingHeader!==n&&(n=n+" > "+this.precedingHeader),n)}static tasksListsIdentical(t,n){return t.length!==n.length?!1:t.every((i,s)=>i.identicalTo(n[s]))}identicalTo(t){var i,s;let n=["description","path","indentation","listMarker","lineNumber","sectionStart","sectionIndex","precedingHeader","priority","blockLink","scheduledDateIsInferred","id","dependsOn","onCompletion"];for(let a of n)if(((i=this[a])==null?void 0:i.toString())!==((s=t[a])==null?void 0:s.toString()))return!1;if(!this.status.identicalTo(t.status)||this.tags.length!==t.tags.length||!this.tags.every(function(a,o){return a===t.tags[o]}))return!1;n=ae.allDateFields();for(let a of n){let o=this[a],u=t[a];if(dr(o,u)!==0)return!1}return this.recurrenceIdenticalTo(t)?this.file.rawFrontmatterIdenticalTo(t.file):!1}recurrenceIdenticalTo(t){let n=this.recurrence,i=t.recurrence;return!(n===null&&i!==null||n!==null&&i===null||n&&i&&!n.identicalTo(i))}static allDateFields(){return["createdDate","startDate","scheduledDate","dueDate","doneDate","cancelledDate"]}static extractHashtags(t){var n,i;return(i=(n=t.match(J.hashTags))==null?void 0:n.map(s=>s.trim()))!=null?i:[]}};var vo=class{constructor(e){this.fetch=e;this._value=void 0}get value(){return this._value===void 0&&(this._value=this.fetch()),this._value}};var ft=class{constructor(e,t,n,i,s){this._tasksFile=e,this._lineNumber=t,this._sectionStart=n,this._sectionIndex=i,this._precedingHeader=s}static fromUnknownPosition(e){return new ft(e,0,0,0,null)}fromRenamedFile(e){return new ft(e,this.lineNumber,this.sectionStart,this.sectionIndex,this.precedingHeader)}get tasksFile(){return this._tasksFile}get path(){return this._tasksFile.path}get lineNumber(){return this._lineNumber}get sectionStart(){return this._sectionStart}get sectionIndex(){return this._sectionIndex}get precedingHeader(){return this._precedingHeader}get hasKnownPath(){return this.path!==""}};function FN(r,e,t,n,i,s){var y,_;let a=new at(r,i),o=[],u=e.split(`
`),l=u.length,c=new vo(()=>yt.fromPath(r)),d=null,p=0,m=new Map;for(let b of t)if(b.task!==void 0){let E=b.position.start.line;if(E>=l)return n.debug(`${r} Obsidian gave us a line number ${E} past the end of the file. ${l}.`),o;if((d===null||d.position.end.line<E)&&(d=Ts.getSection(E,i.sections),p=0),d===null)continue;let R=u[E];if(R===void 0){n.debug(`${r}: line ${E} - ignoring 'undefined' line.`);continue}let S;try{if(S=ae.fromLine({line:R,taskLocation:new ft(a,E,d.position.start.line,p,Ts.getPrecedingHeader(E,i.headings)),fallbackDate:c.value}),S!==null){let F=(y=m.get(b.parent))!=null?y:null;F!==null&&(S=new ae(he(K({},S),{parent:F}))),m.set(E,S)}}catch(F){s(F,r,b,R);continue}S!==null&&(p++,o.push(S))}else{let E=b.position.start.line,R=(_=m.get(b.parent))!=null?_:null;m.set(E,new Vi(u[E],R))}return o}var Ts=class{constructor({metadataCache:e,vault:t,events:n}){this.logger=St.getLogger("tasks.Cache");this.logger.debug("Creating Cache object"),this.metadataCache=e,this.metadataCacheEventReferences=[],this.vault=t,this.vaultEventReferences=[],this.events=n,this.eventsEventReferences=[],this.tasksMutex=new Vo,this.state="Cold",this.logger.debug("Cache.constructor(): state = Cold"),this.tasks=[],this.loadedAfterFirstResolve=!1,this.subscribeToCache(),this.subscribeToVault(),this.subscribeToEvents(),this.loadVault()}unload(){this.logger.info("Unloading Cache");for(let e of this.metadataCacheEventReferences)this.metadataCache.offref(e);for(let e of this.vaultEventReferences)this.vault.offref(e);for(let e of this.eventsEventReferences)this.events.off(e)}getTasks(){return this.tasks}getState(){return this.state}notifySubscribers(){this.logger.debug("Cache.notifySubscribers()"),this.events.triggerCacheUpdate({tasks:this.tasks,state:this.state})}subscribeToCache(){this.logger.debug("Cache.subscribeToCache()");let e=this.metadataCache.on("resolved",()=>P(this,null,function*(){this.loadedAfterFirstResolve||(this.loadedAfterFirstResolve=!0,this.loadVault())}));this.metadataCacheEventReferences.push(e);let t=this.metadataCache.on("changed",n=>{this.tasksMutex.runExclusive(()=>{this.indexFile(n)})});this.metadataCacheEventReferences.push(t)}subscribeToVault(){this.logger.debug("Cache.subscribeToVault()");let{useFilenameAsScheduledDate:e}=X(),t=this.vault.on("create",s=>{s instanceof bs.TFile&&(this.logger.debug(`Cache.subscribeToVault.createdEventReference() ${s.path}`),this.tasksMutex.runExclusive(()=>{this.indexFile(s)}))});this.vaultEventReferences.push(t);let n=this.vault.on("delete",s=>{s instanceof bs.TFile&&(this.logger.debug(`Cache.subscribeToVault.deletedEventReference() ${s.path}`),this.tasksMutex.runExclusive(()=>{this.tasks=this.tasks.filter(a=>a.path!==s.path),this.notifySubscribers()}))});this.vaultEventReferences.push(n);let i=this.vault.on("rename",(s,a)=>{s instanceof bs.TFile&&(this.logger.debug(`Cache.subscribeToVault.renamedEventReference() ${s.path}`),this.tasksMutex.runExclusive(()=>{let o=this.metadataCache.getFileCache(s),u=new at(s.path,o!=null?o:void 0),l=new vo(()=>yt.fromPath(s.path));this.tasks=this.tasks.map(c=>{if(c.path!==a)return c;let d=c.taskLocation.fromRenamedFile(u);return e?yt.updateTaskPath(c,d,l.value):new ae(he(K({},c),{taskLocation:d}))}),this.notifySubscribers()}))});this.vaultEventReferences.push(i)}subscribeToEvents(){this.logger.debug("Cache.subscribeToEvents()");let e=this.events.onRequestCacheUpdate(t=>{t({tasks:this.tasks,state:this.state})});this.eventsEventReferences.push(e)}loadVault(){return this.logger.debug("Cache.loadVault()"),this.tasksMutex.runExclusive(()=>P(this,null,function*(){this.state="Initializing",this.logger.debug("Cache.loadVault(): state = Initializing"),yield Promise.all(this.vault.getMarkdownFiles().map(e=>this.indexFile(e))),this.state="Warm",this.logger.debug("Cache.loadVault(): state = Warm"),this.notifySubscribers()}))}indexFile(e){return P(this,null,function*(){let t=this.metadataCache.getFileCache(e);if(t==null)return;if(!e.path.endsWith(".md")){this.logger.debug("indexFile: skipping non-markdown file: "+e.path);return}this.logger.debug("Cache.indexFile: "+e.path);let n=this.tasks.filter(a=>a.path===e.path),i=t.listItems,s=[];if(i!==void 0){let a=yield this.vault.cachedRead(e);s=this.getTasksFromFileContent(a,i,t,e.path,this.reportTaskParsingErrorToUser,this.logger)}ae.tasksListsIdentical(n,s)||(this.tasks=this.tasks.filter(a=>a.path!==e.path),this.tasks.push(...s),this.logger.debug("Cache.indexFile: "+e.path+`: read ${s.length} task(s)`),this.notifySubscribers())})}getTasksFromFileContent(e,t,n,i,s,a){return FN(i,e,t,a,n,s)}reportTaskParsingErrorToUser(e,t,n,i){let s=`There was an error reading one of the tasks in this vault.
The following task has been ignored, to prevent Tasks queries getting stuck with 'Loading Tasks ...'
Error: ${e}
File: ${t}
Line number: ${n.position.start.line}
Task line: ${i}

Please create a bug report for this message at
https://github.com/obsidian-tasks-group/obsidian-tasks/issues/new/choose
to help us find and fix the underlying issue.

Include:
- either a screenshot of the error popup, or copy the text from the console, if on a desktop machine.
- the output from running the Obsidian command 'Show debug info'

The error popup will only be shown when Tasks is starting up, but if the error persists,
it will be shown in the console every time this file is edited during the Obsidian
session.
`;this.logger.error(s),e instanceof Error&&this.logger.error(e.stack?e.stack:"Cannot determine stack"),this.state==="Initializing"&&new bs.Notice(s,1e4)}static getSection(e,t){if(t===void 0)return null;for(let n of t)if(n.position.start.line<=e&&n.position.end.line>=e)return n;return null}static getPrecedingHeader(e,t){if(t===void 0)return null;let n=null;for(let i of t){if(i.position.start.line>e)return n;n=i.heading}return n}};var oh=require("obsidian");var Rw=require("obsidian");function Ue(){}function jm(r){return r()}function Av(){return Object.create(null)}function Ft(r){r.forEach(jm)}function ul(r){return typeof r=="function"}function Qr(r,e){return r!=r?e==e:r!==e||r&&typeof r=="object"||typeof r=="function"}function Pv(r){return Object.keys(r).length===0}var Nv=typeof window!="undefined"?window:typeof globalThis!="undefined"?globalThis:global,ko=class{constructor(e){this.options=e,this._listeners="WeakMap"in Nv?new WeakMap:void 0}observe(e,t){return this._listeners.set(e,t),this._getObserver().observe(e,this.options),()=>{this._listeners.delete(e),this._observer.unobserve(e)}}_getObserver(){var e;return(e=this._observer)!==null&&e!==void 0?e:this._observer=new ResizeObserver(t=>{var n;for(let i of t)ko.entries.set(i.target,i),(n=this._listeners.get(i.target))===null||n===void 0||n(i)})}};ko.entries="WeakMap"in Nv?new WeakMap:void 0;var Iv=!1;function LN(){Iv=!0}function UN(){Iv=!1}function U(r,e){r.appendChild(e)}function de(r,e,t){r.insertBefore(e,t||null)}function oe(r){r.parentNode&&r.parentNode.removeChild(r)}function Ci(r,e){for(let t=0;t<r.length;t+=1)r[t]&&r[t].d(e)}function Q(r){return document.createElement(r)}function WN(r){return document.createElementNS("http://www.w3.org/2000/svg",r)}function Me(r){return document.createTextNode(r)}function ue(){return Me(" ")}function Fv(){return Me("")}function xe(r,e,t,n){return r.addEventListener(e,t,n),()=>r.removeEventListener(e,t,n)}function Lv(r){return function(e){return e.preventDefault(),r.call(this,e)}}function W(r,e,t){t==null?r.removeAttribute(e):r.getAttribute(e)!==t&&r.setAttribute(e,t)}function Uv(r){let e;return{p(...t){e=t,e.forEach(n=>r.push(n))},r(){e.forEach(t=>r.splice(r.indexOf(t),1))}}}function qN(r){return Array.from(r.childNodes)}function Ar(r,e){e=""+e,r.data!==e&&(r.data=e)}function gr(r,e){r.value=e==null?"":e}function Gm(r,e,t){for(let n=0;n<r.options.length;n+=1){let i=r.options[n];if(i.__value===e){i.selected=!0;return}}(!t||e!==void 0)&&(r.selectedIndex=-1)}function Wv(r){let e=r.querySelector(":checked");return e&&e.__value}var al;function $N(){if(al===void 0){al=!1;try{typeof window!="undefined"&&window.parent&&window.parent.document}catch(r){al=!0}}return al}function qv(r,e){getComputedStyle(r).position==="static"&&(r.style.position="relative");let n=Q("iframe");n.setAttribute("style","display: block; position: absolute; top: 0; left: 0; width: 100%; height: 100%; overflow: hidden; border: 0; opacity: 0; pointer-events: none; z-index: -1;"),n.setAttribute("aria-hidden","true"),n.tabIndex=-1;let i=$N(),s;return i?(n.src="data:text/html,<script>onresize=function(){parent.postMessage(0,'*')}<\/script>",s=xe(window,"message",a=>{a.source===n.contentWindow&&e()})):(n.src="about:blank",n.onload=()=>{s=xe(n.contentWindow,"resize",e),e()}),U(r,n),()=>{(i||s&&n.contentWindow)&&s(),oe(n)}}function Dn(r,e,t){r.classList[t?"add":"remove"](e)}var ks=class{constructor(e=!1){this.is_svg=!1,this.is_svg=e,this.e=this.n=null}c(e){this.h(e)}m(e,t,n=null){this.e||(this.is_svg?this.e=WN(t.nodeName):this.e=Q(t.nodeType===11?"TEMPLATE":t.nodeName),this.t=t.tagName!=="TEMPLATE"?t:t.content,this.c(e)),this.i(n)}h(e){this.e.innerHTML=e,this.n=Array.from(this.e.nodeName==="TEMPLATE"?this.e.content.childNodes:this.e.childNodes)}i(e){for(let t=0;t<this.n.length;t+=1)de(this.t,this.n[t],e)}p(e){this.d(),this.h(e),this.i(this.a)}d(){this.n.forEach(oe)}};var Eo;function wo(r){Eo=r}function jN(){if(!Eo)throw new Error("Function called outside component initialization");return Eo}function Ym(r){jN().$$.on_mount.push(r)}var vs=[];var je=[],ws=[],qm=[],GN=Promise.resolve(),$m=!1;function YN(){$m||($m=!0,GN.then($v))}function Mi(r){ws.push(r)}function Ot(r){qm.push(r)}var Wm=new Set,_s=0;function $v(){if(_s!==0)return;let r=Eo;do{try{for(;_s<vs.length;){let e=vs[_s];_s++,wo(e),BN(e.$$)}}catch(e){throw vs.length=0,_s=0,e}for(wo(null),vs.length=0,_s=0;je.length;)je.pop()();for(let e=0;e<ws.length;e+=1){let t=ws[e];Wm.has(t)||(Wm.add(t),t())}ws.length=0}while(vs.length);for(;qm.length;)qm.pop()();$m=!1,Wm.clear(),wo(r)}function BN(r){if(r.fragment!==null){r.update(),Ft(r.before_update);let e=r.dirty;r.dirty=[-1],r.fragment&&r.fragment.p(r.ctx,e),r.after_update.forEach(Mi)}}function HN(r){let e=[],t=[];ws.forEach(n=>r.indexOf(n)===-1?e.push(n):t.push(n)),t.forEach(n=>n()),ws=e}var ol=new Set,Ri;function jv(){Ri={r:0,c:[],p:Ri}}function Gv(){Ri.r||Ft(Ri.c),Ri=Ri.p}function Lt(r,e){r&&r.i&&(ol.delete(r),r.i(e))}function Qt(r,e,t,n){if(r&&r.o){if(ol.has(r))return;ol.add(r),Ri.c.push(()=>{ol.delete(r),n&&(t&&r.d(1),n())}),r.o(e)}else n&&n()}var VN=["allowfullscreen","allowpaymentrequest","async","autofocus","autoplay","checked","controls","default","defer","disabled","formnovalidate","hidden","inert","ismap","loop","multiple","muted","nomodule","novalidate","open","playsinline","readonly","required","reversed","selected"],iG=new Set([...VN]);function Dt(r,e,t){let n=r.$$.props[e];n!==void 0&&(r.$$.bound[n]=t,t(r.$$.ctx[n]))}function Pr(r){r&&r.c()}function yr(r,e,t,n){let{fragment:i,after_update:s}=r.$$;i&&i.m(e,t),n||Mi(()=>{let a=r.$$.on_mount.map(jm).filter(ul);r.$$.on_destroy?r.$$.on_destroy.push(...a):Ft(a),r.$$.on_mount=[]}),s.forEach(Mi)}function Xt(r,e){let t=r.$$;t.fragment!==null&&(HN(t.after_update),Ft(t.on_destroy),t.fragment&&t.fragment.d(e),t.on_destroy=t.fragment=null,t.ctx=[])}function zN(r,e){r.$$.dirty[0]===-1&&(vs.push(r),YN(),r.$$.dirty.fill(0)),r.$$.dirty[e/31|0]|=1<<e%31}function Xr(r,e,t,n,i,s,a,o=[-1]){let u=Eo;wo(r);let l=r.$$={fragment:null,ctx:[],props:s,update:Ue,not_equal:i,bound:Av(),on_mount:[],on_destroy:[],on_disconnect:[],before_update:[],after_update:[],context:new Map(e.context||(u?u.$$.context:[])),callbacks:Av(),dirty:o,skip_bound:!1,root:e.target||u.$$.root};a&&a(l.root);let c=!1;if(l.ctx=t?t(r,e.props||{},(d,p,...m)=>{let y=m.length?m[0]:p;return l.ctx&&i(l.ctx[d],l.ctx[d]=y)&&(!l.skip_bound&&l.bound[d]&&l.bound[d](y),c&&zN(r,d)),p}):[],l.update(),c=!0,Ft(l.before_update),l.fragment=n?n(l.ctx):!1,e.target){if(e.hydrate){LN();let d=qN(e.target);l.fragment&&l.fragment.l(d),d.forEach(oe)}else l.fragment&&l.fragment.c();e.intro&&Lt(r.$$.fragment),yr(r,e.target,e.anchor,e.customElement),UN(),$v()}wo(u)}var KN;typeof HTMLElement=="function"&&(KN=class extends HTMLElement{constructor(){super(),this.attachShadow({mode:"open"})}connectedCallback(){let{on_mount:r}=this.$$;this.$$.on_disconnect=r.map(jm).filter(ul);for(let e in this.$$.slotted)this.appendChild(this.$$.slotted[e])}attributeChangedCallback(r,e,t){this[r]=t}disconnectedCallback(){Ft(this.$$.on_disconnect)}$destroy(){Xt(this,1),this.$destroy=Ue}$on(r,e){if(!ul(e))return Ue;let t=this.$$.callbacks[r]||(this.$$.callbacks[r]=[]);return t.push(e),()=>{let n=t.indexOf(e);n!==-1&&t.splice(n,1)}}$set(r){this.$$set&&!Pv(r)&&(this.$$.skip_bound=!0,this.$$set(r),this.$$.skip_bound=!1)}});var hr=class{$destroy(){Xt(this,1),this.$destroy=Ue}$on(e,t){if(!ul(t))return Ue;let n=this.$$.callbacks[e]||(this.$$.callbacks[e]=[]);return n.push(t),()=>{let i=n.indexOf(t);i!==-1&&n.splice(i,1)}}$set(e){this.$$set&&!Pv(e)&&(this.$$.skip_bound=!0,this.$$set(e),this.$$.skip_bound=!1)}};function Yv(r,e,t,n){function i(s){return s instanceof t?s:new t(function(a){a(s)})}return new(t||(t=Promise))(function(s,a){function o(c){try{l(n.next(c))}catch(d){a(d)}}function u(c){try{l(n.throw(c))}catch(d){a(d)}}function l(c){c.done?s(c.value):i(c.value).then(o,u)}l((n=n.apply(r,e||[])).next())})}function Es(r){return r.charAt(0).toUpperCase()+r.slice(1)}function pt(r,e){if(e===null)return Es(r);let t=r.toLowerCase().indexOf(e.toLowerCase());if(t===-1)return`${Es(r)} (<span class="accesskey">${e.toLowerCase()}</span>)`;let n=r.substring(0,t);return n+='<span class="accesskey">',t===0?n+=r.substring(t,t+1).toUpperCase():n+=r.substring(t,t+1),n+="</span>",n+=r.substring(t+1),n=Es(n),n}function QN(r){let e,t=pt(r[2],r[4])+"",n,i,s,a,o,u,l,c,d;return{c(){e=Q("label"),n=ue(),i=Q("input"),s=ue(),a=Q("code"),o=Me(r[3]),u=ue(),l=new ks(!1),W(e,"for",r[2]),W(i,"id",r[2]),W(i,"type","text"),W(i,"class","tasks-modal-date-input"),W(i,"placeholder",XN),W(i,"accesskey",r[4]),Dn(i,"tasks-modal-error",!r[1]),l.a=null,W(a,"class","tasks-modal-parsed-date")},m(p,m){de(p,e,m),e.innerHTML=t,de(p,n,m),de(p,i,m),gr(i,r[0]),de(p,s,m),de(p,a,m),U(a,o),U(a,u),l.m(r[5],a),c||(d=xe(i,"input",r[7]),c=!0)},p(p,[m]){m&20&&t!==(t=pt(p[2],p[4])+"")&&(e.innerHTML=t),m&4&&W(e,"for",p[2]),m&4&&W(i,"id",p[2]),m&16&&W(i,"accesskey",p[4]),m&1&&i.value!==p[0]&&gr(i,p[0]),m&2&&Dn(i,"tasks-modal-error",!p[1]),m&8&&Ar(o,p[3]),m&32&&l.p(p[5])},i:Ue,o:Ue,d(p){p&&oe(e),p&&oe(n),p&&oe(i),p&&oe(s),p&&oe(a),c=!1,d()}}}var XN="Try 'Mon' or 'tm' then space";function ZN(r,e,t){let{id:n}=e,{dateSymbol:i}=e,{date:s}=e,{isDateValid:a}=e,{forwardOnly:o}=e,{accesskey:u}=e,l;function c(){s=this.value,t(0,s),t(2,n),t(6,o),t(5,l)}return r.$$set=d=>{"id"in d&&t(2,n=d.id),"dateSymbol"in d&&t(3,i=d.dateSymbol),"date"in d&&t(0,s=d.date),"isDateValid"in d&&t(1,a=d.isDateValid),"forwardOnly"in d&&t(6,o=d.forwardOnly),"accesskey"in d&&t(4,u=d.accesskey)},r.$$.update=()=>{if(r.$$.dirty&101){e:t(0,s=$u(s)),t(5,l=D_(n,s,o)),t(1,a=!l.includes("invalid"))}},[s,a,n,i,u,l,o,c]}var Bm=class extends hr{constructor(e){super(),Xr(this,e,ZN,QN,Qr,{id:2,dateSymbol:3,date:0,isDateValid:1,forwardOnly:6,accesskey:4})}},Ai=Bm;var Pi=Math.min,Ut=Math.max,Oo=Math.round;var xn=r=>({x:r,y:r}),JN={left:"right",right:"left",bottom:"top",top:"bottom"},eI={start:"end",end:"start"};function Hm(r,e,t){return Ut(r,Pi(e,t))}function Ss(r,e){return typeof r=="function"?r(e):r}function Rn(r){return r.split("-")[0]}function Os(r){return r.split("-")[1]}function Vm(r){return r==="x"?"y":"x"}function zm(r){return r==="y"?"height":"width"}function Ds(r){return["top","bottom"].includes(Rn(r))?"y":"x"}function Km(r){return Vm(Ds(r))}function Bv(r,e,t){t===void 0&&(t=!1);let n=Os(r),i=Km(r),s=zm(i),a=i==="x"?n===(t?"end":"start")?"right":"left":n==="start"?"bottom":"top";return e.reference[s]>e.floating[s]&&(a=So(a)),[a,So(a)]}function Hv(r){let e=So(r);return[ll(r),e,ll(e)]}function ll(r){return r.replace(/start|end/g,e=>eI[e])}function tI(r,e,t){let n=["left","right"],i=["right","left"],s=["top","bottom"],a=["bottom","top"];switch(r){case"top":case"bottom":return t?e?i:n:e?n:i;case"left":case"right":return e?s:a;default:return[]}}function Vv(r,e,t,n){let i=Os(r),s=tI(Rn(r),t==="start",n);return i&&(s=s.map(a=>a+"-"+i),e&&(s=s.concat(s.map(ll)))),s}function So(r){return r.replace(/left|right|bottom|top/g,e=>JN[e])}function rI(r){return K({top:0,right:0,bottom:0,left:0},r)}function zv(r){return typeof r!="number"?rI(r):{top:r,right:r,bottom:r,left:r}}function Ni(r){return he(K({},r),{top:r.y,left:r.x,right:r.x+r.width,bottom:r.y+r.height})}function Kv(r,e,t){let{reference:n,floating:i}=r,s=Ds(e),a=Km(e),o=zm(a),u=Rn(e),l=s==="y",c=n.x+n.width/2-i.width/2,d=n.y+n.height/2-i.height/2,p=n[o]/2-i[o]/2,m;switch(u){case"top":m={x:c,y:n.y-i.height};break;case"bottom":m={x:c,y:n.y+n.height};break;case"right":m={x:n.x+n.width,y:d};break;case"left":m={x:n.x-i.width,y:d};break;default:m={x:n.x,y:n.y}}switch(Os(e)){case"start":m[a]-=p*(t&&l?-1:1);break;case"end":m[a]+=p*(t&&l?-1:1);break}return m}var Qv=(r,e,t)=>P(void 0,null,function*(){let{placement:n="bottom",strategy:i="absolute",middleware:s=[],platform:a}=t,o=s.filter(Boolean),u=yield a.isRTL==null?void 0:a.isRTL(e),l=yield a.getElementRects({reference:r,floating:e,strategy:i}),{x:c,y:d}=Kv(l,n,u),p=n,m={},y=0;for(let _=0;_<o.length;_++){let{name:b,fn:E}=o[_],{x:R,y:S,data:F,reset:q}=yield E({x:c,y:d,initialPlacement:n,placement:p,strategy:i,middlewareData:m,rects:l,platform:a,elements:{reference:r,floating:e}});if(c=R!=null?R:c,d=S!=null?S:d,m=he(K({},m),{[b]:K(K({},m[b]),F)}),q&&y<=50){y++,typeof q=="object"&&(q.placement&&(p=q.placement),q.rects&&(l=q.rects===!0?yield a.getElementRects({reference:r,floating:e,strategy:i}):q.rects),{x:c,y:d}=Kv(l,p,u)),_=-1;continue}}return{x:c,y:d,placement:p,strategy:i,middlewareData:m}});function cl(r,e){return P(this,null,function*(){var t;e===void 0&&(e={});let{x:n,y:i,platform:s,rects:a,elements:o,strategy:u}=r,{boundary:l="clippingAncestors",rootBoundary:c="viewport",elementContext:d="floating",altBoundary:p=!1,padding:m=0}=Ss(e,r),y=zv(m),b=o[p?d==="floating"?"reference":"floating":d],E=Ni(yield s.getClippingRect({element:(t=yield s.isElement==null?void 0:s.isElement(b))==null||t?b:b.contextElement||(yield s.getDocumentElement==null?void 0:s.getDocumentElement(o.floating)),boundary:l,rootBoundary:c,strategy:u})),R=d==="floating"?he(K({},a.floating),{x:n,y:i}):a.reference,S=yield s.getOffsetParent==null?void 0:s.getOffsetParent(o.floating),F=(yield s.isElement==null?void 0:s.isElement(S))?(yield s.getScale==null?void 0:s.getScale(S))||{x:1,y:1}:{x:1,y:1},q=Ni(s.convertOffsetParentRelativeRectToViewportRelativeRect?yield s.convertOffsetParentRelativeRectToViewportRelativeRect({rect:R,offsetParent:S,strategy:u}):R);return{top:(E.top-q.top+y.top)/F.y,bottom:(q.bottom-E.bottom+y.bottom)/F.y,left:(E.left-q.left+y.left)/F.x,right:(q.right-E.right+y.right)/F.x}})}var Qm=function(r){return r===void 0&&(r={}),{name:"flip",options:r,fn(t){return P(this,null,function*(){var n,i;let{placement:s,middlewareData:a,rects:o,initialPlacement:u,platform:l,elements:c}=t,D=Ss(r,t),{mainAxis:d=!0,crossAxis:p=!0,fallbackPlacements:m,fallbackStrategy:y="bestFit",fallbackAxisSideDirection:_="none",flipAlignment:b=!0}=D,E=Ho(D,["mainAxis","crossAxis","fallbackPlacements","fallbackStrategy","fallbackAxisSideDirection","flipAlignment"]);if((n=a.arrow)!=null&&n.alignmentOffset)return{};let R=Rn(s),S=Rn(u)===u,F=yield l.isRTL==null?void 0:l.isRTL(c.floating),q=m||(S||!b?[So(u)]:Hv(u));!m&&_!=="none"&&q.push(...Vv(u,b,_,F));let te=[u,...q],G=yield cl(t,E),H=[],be=((i=a.flip)==null?void 0:i.overflows)||[];if(d&&H.push(G[R]),p){let B=Bv(s,o,F);H.push(G[B[0]],G[B[1]])}if(be=[...be,{placement:s,overflows:H}],!H.every(B=>B<=0)){var Pe,j;let B=(((Pe=a.flip)==null?void 0:Pe.index)||0)+1,I=te[B];if(I)return{data:{index:B,overflows:be},reset:{placement:I}};let Z=(j=be.filter(f=>f.overflows[0]<=0).sort((f,h)=>f.overflows[1]-h.overflows[1])[0])==null?void 0:j.placement;if(!Z)switch(y){case"bestFit":{var $;let f=($=be.map(h=>[h.placement,h.overflows.filter(g=>g>0).reduce((g,T)=>g+T,0)]).sort((h,g)=>h[1]-g[1])[0])==null?void 0:$[0];f&&(Z=f);break}case"initialPlacement":Z=u;break}if(s!==Z)return{reset:{placement:Z}}}return{}})}}};function nI(r,e){return P(this,null,function*(){let{placement:t,platform:n,elements:i}=r,s=yield n.isRTL==null?void 0:n.isRTL(i.floating),a=Rn(t),o=Os(t),u=Ds(t)==="y",l=["left","top"].includes(a)?-1:1,c=s&&u?-1:1,d=Ss(e,r),{mainAxis:p,crossAxis:m,alignmentAxis:y}=typeof d=="number"?{mainAxis:d,crossAxis:0,alignmentAxis:null}:K({mainAxis:0,crossAxis:0,alignmentAxis:null},d);return o&&typeof y=="number"&&(m=o==="end"?y*-1:y),u?{x:m*c,y:p*l}:{x:p*l,y:m*c}})}var dl=function(r){return r===void 0&&(r=0),{name:"offset",options:r,fn(t){return P(this,null,function*(){var n,i;let{x:s,y:a,placement:o,middlewareData:u}=t,l=yield nI(t,r);return o===((n=u.offset)==null?void 0:n.placement)&&(i=u.arrow)!=null&&i.alignmentOffset?{}:{x:s+l.x,y:a+l.y,data:he(K({},l),{placement:o})}})}}},fl=function(r){return r===void 0&&(r={}),{name:"shift",options:r,fn(t){return P(this,null,function*(){let{x:n,y:i,placement:s}=t,E=Ss(r,t),{mainAxis:a=!0,crossAxis:o=!1,limiter:u={fn:R=>{let{x:S,y:F}=R;return{x:S,y:F}}}}=E,l=Ho(E,["mainAxis","crossAxis","limiter"]),c={x:n,y:i},d=yield cl(t,l),p=Ds(Rn(s)),m=Vm(p),y=c[m],_=c[p];if(a){let R=m==="y"?"top":"left",S=m==="y"?"bottom":"right",F=y+d[R],q=y-d[S];y=Hm(F,y,q)}if(o){let R=p==="y"?"top":"left",S=p==="y"?"bottom":"right",F=_+d[R],q=_-d[S];_=Hm(F,_,q)}let b=u.fn(he(K({},t),{[m]:y,[p]:_}));return he(K({},b),{data:{x:b.x-n,y:b.y-i}})})}}};var Xm=function(r){return r===void 0&&(r={}),{name:"size",options:r,fn(t){return P(this,null,function*(){let{placement:n,rects:i,platform:s,elements:a}=t,G=Ss(r,t),{apply:o=()=>{}}=G,u=Ho(G,["apply"]),l=yield cl(t,u),c=Rn(n),d=Os(n),p=Ds(n)==="y",{width:m,height:y}=i.floating,_,b;c==="top"||c==="bottom"?(_=c,b=d===((yield s.isRTL==null?void 0:s.isRTL(a.floating))?"start":"end")?"left":"right"):(b=c,_=d==="end"?"top":"bottom");let E=y-l[_],R=m-l[b],S=!t.middlewareData.shift,F=E,q=R;if(p){let H=m-l.left-l.right;q=d||S?Pi(R,H):H}else{let H=y-l.top-l.bottom;F=d||S?Pi(E,H):H}if(S&&!d){let H=Ut(l.left,0),be=Ut(l.right,0),Pe=Ut(l.top,0),j=Ut(l.bottom,0);p?q=m-2*(H!==0||be!==0?H+be:Ut(l.left,l.right)):F=y-2*(Pe!==0||j!==0?Pe+j:Ut(l.top,l.bottom))}yield o(he(K({},t),{availableWidth:q,availableHeight:F}));let te=yield s.getDimensions(a.floating);return m!==te.width||y!==te.height?{reset:{rects:!0}}:{}})}}};function Mn(r){return Zv(r)?(r.nodeName||"").toLowerCase():"#document"}function Wt(r){var e;return(r==null||(e=r.ownerDocument)==null?void 0:e.defaultView)||window}function Cn(r){var e;return(e=(Zv(r)?r.ownerDocument:r.document)||window.document)==null?void 0:e.documentElement}function Zv(r){return r instanceof Node||r instanceof Wt(r).Node}function Zr(r){return r instanceof Element||r instanceof Wt(r).Element}function Nr(r){return r instanceof HTMLElement||r instanceof Wt(r).HTMLElement}function Xv(r){return typeof ShadowRoot=="undefined"?!1:r instanceof ShadowRoot||r instanceof Wt(r).ShadowRoot}function xs(r){let{overflow:e,overflowX:t,overflowY:n,display:i}=Zt(r);return/auto|scroll|overlay|hidden|clip/.test(e+n+t)&&!["inline","contents"].includes(i)}function Jv(r){return["table","td","th"].includes(Mn(r))}function ml(r){let e=hl(),t=Zt(r);return t.transform!=="none"||t.perspective!=="none"||(t.containerType?t.containerType!=="normal":!1)||!e&&(t.backdropFilter?t.backdropFilter!=="none":!1)||!e&&(t.filter?t.filter!=="none":!1)||["transform","perspective","filter"].some(n=>(t.willChange||"").includes(n))||["paint","layout","strict","content"].some(n=>(t.contain||"").includes(n))}function ew(r){let e=Ii(r);for(;Nr(e)&&!Do(e);){if(ml(e))return e;e=Ii(e)}return null}function hl(){return typeof CSS=="undefined"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}function Do(r){return["html","body","#document"].includes(Mn(r))}function Zt(r){return Wt(r).getComputedStyle(r)}function xo(r){return Zr(r)?{scrollLeft:r.scrollLeft,scrollTop:r.scrollTop}:{scrollLeft:r.pageXOffset,scrollTop:r.pageYOffset}}function Ii(r){if(Mn(r)==="html")return r;let e=r.assignedSlot||r.parentNode||Xv(r)&&r.host||Cn(r);return Xv(e)?e.host:e}function tw(r){let e=Ii(r);return Do(e)?r.ownerDocument?r.ownerDocument.body:r.body:Nr(e)&&xs(e)?e:tw(e)}function pl(r,e,t){var n;e===void 0&&(e=[]),t===void 0&&(t=!0);let i=tw(r),s=i===((n=r.ownerDocument)==null?void 0:n.body),a=Wt(i);return s?e.concat(a,a.visualViewport||[],xs(i)?i:[],a.frameElement&&t?pl(a.frameElement):[]):e.concat(i,pl(i,[],t))}function iw(r){let e=Zt(r),t=parseFloat(e.width)||0,n=parseFloat(e.height)||0,i=Nr(r),s=i?r.offsetWidth:t,a=i?r.offsetHeight:n,o=Oo(t)!==s||Oo(n)!==a;return o&&(t=s,n=a),{width:t,height:n,$:o}}function sw(r){return Zr(r)?r:r.contextElement}function Rs(r){let e=sw(r);if(!Nr(e))return xn(1);let t=e.getBoundingClientRect(),{width:n,height:i,$:s}=iw(e),a=(s?Oo(t.width):t.width)/n,o=(s?Oo(t.height):t.height)/i;return(!a||!Number.isFinite(a))&&(a=1),(!o||!Number.isFinite(o))&&(o=1),{x:a,y:o}}var iI=xn(0);function aw(r){let e=Wt(r);return!hl()||!e.visualViewport?iI:{x:e.visualViewport.offsetLeft,y:e.visualViewport.offsetTop}}function sI(r,e,t){return e===void 0&&(e=!1),!t||e&&t!==Wt(r)?!1:e}function Ro(r,e,t,n){e===void 0&&(e=!1),t===void 0&&(t=!1);let i=r.getBoundingClientRect(),s=sw(r),a=xn(1);e&&(n?Zr(n)&&(a=Rs(n)):a=Rs(r));let o=sI(s,t,n)?aw(s):xn(0),u=(i.left+o.x)/a.x,l=(i.top+o.y)/a.y,c=i.width/a.x,d=i.height/a.y;if(s){let p=Wt(s),m=n&&Zr(n)?Wt(n):n,y=p.frameElement;for(;y&&n&&m!==p;){let _=Rs(y),b=y.getBoundingClientRect(),E=Zt(y),R=b.left+(y.clientLeft+parseFloat(E.paddingLeft))*_.x,S=b.top+(y.clientTop+parseFloat(E.paddingTop))*_.y;u*=_.x,l*=_.y,c*=_.x,d*=_.y,u+=R,l+=S,y=Wt(y).frameElement}}return Ni({width:c,height:d,x:u,y:l})}function aI(r){let{rect:e,offsetParent:t,strategy:n}=r,i=Nr(t),s=Cn(t);if(t===s)return e;let a={scrollLeft:0,scrollTop:0},o=xn(1),u=xn(0);if((i||!i&&n!=="fixed")&&((Mn(t)!=="body"||xs(s))&&(a=xo(t)),Nr(t))){let l=Ro(t);o=Rs(t),u.x=l.x+t.clientLeft,u.y=l.y+t.clientTop}return{width:e.width*o.x,height:e.height*o.y,x:e.x*o.x-a.scrollLeft*o.x+u.x,y:e.y*o.y-a.scrollTop*o.y+u.y}}function oI(r){return Array.from(r.getClientRects())}function ow(r){return Ro(Cn(r)).left+xo(r).scrollLeft}function uI(r){let e=Cn(r),t=xo(r),n=r.ownerDocument.body,i=Ut(e.scrollWidth,e.clientWidth,n.scrollWidth,n.clientWidth),s=Ut(e.scrollHeight,e.clientHeight,n.scrollHeight,n.clientHeight),a=-t.scrollLeft+ow(r),o=-t.scrollTop;return Zt(n).direction==="rtl"&&(a+=Ut(e.clientWidth,n.clientWidth)-i),{width:i,height:s,x:a,y:o}}function lI(r,e){let t=Wt(r),n=Cn(r),i=t.visualViewport,s=n.clientWidth,a=n.clientHeight,o=0,u=0;if(i){s=i.width,a=i.height;let l=hl();(!l||l&&e==="fixed")&&(o=i.offsetLeft,u=i.offsetTop)}return{width:s,height:a,x:o,y:u}}function cI(r,e){let t=Ro(r,!0,e==="fixed"),n=t.top+r.clientTop,i=t.left+r.clientLeft,s=Nr(r)?Rs(r):xn(1),a=r.clientWidth*s.x,o=r.clientHeight*s.y,u=i*s.x,l=n*s.y;return{width:a,height:o,x:u,y:l}}function rw(r,e,t){let n;if(e==="viewport")n=lI(r,t);else if(e==="document")n=uI(Cn(r));else if(Zr(e))n=cI(e,t);else{let i=aw(r);n=he(K({},e),{x:e.x-i.x,y:e.y-i.y})}return Ni(n)}function uw(r,e){let t=Ii(r);return t===e||!Zr(t)||Do(t)?!1:Zt(t).position==="fixed"||uw(t,e)}function dI(r,e){let t=e.get(r);if(t)return t;let n=pl(r,[],!1).filter(o=>Zr(o)&&Mn(o)!=="body"),i=null,s=Zt(r).position==="fixed",a=s?Ii(r):r;for(;Zr(a)&&!Do(a);){let o=Zt(a),u=ml(a);!u&&o.position==="fixed"&&(i=null),(s?!u&&!i:!u&&o.position==="static"&&!!i&&["absolute","fixed"].includes(i.position)||xs(a)&&!u&&uw(r,a))?n=n.filter(c=>c!==a):i=o,a=Ii(a)}return e.set(r,n),n}function fI(r){let{element:e,boundary:t,rootBoundary:n,strategy:i}=r,a=[...t==="clippingAncestors"?dI(e,this._c):[].concat(t),n],o=a[0],u=a.reduce((l,c)=>{let d=rw(e,c,i);return l.top=Ut(d.top,l.top),l.right=Pi(d.right,l.right),l.bottom=Pi(d.bottom,l.bottom),l.left=Ut(d.left,l.left),l},rw(e,o,i));return{width:u.right-u.left,height:u.bottom-u.top,x:u.left,y:u.top}}function pI(r){return iw(r)}function mI(r,e,t){let n=Nr(e),i=Cn(e),s=t==="fixed",a=Ro(r,!0,s,e),o={scrollLeft:0,scrollTop:0},u=xn(0);if(n||!n&&!s)if((Mn(e)!=="body"||xs(i))&&(o=xo(e)),n){let l=Ro(e,!0,s,e);u.x=l.x+e.clientLeft,u.y=l.y+e.clientTop}else i&&(u.x=ow(i));return{x:a.left+o.scrollLeft-u.x,y:a.top+o.scrollTop-u.y,width:a.width,height:a.height}}function nw(r,e){return!Nr(r)||Zt(r).position==="fixed"?null:e?e(r):r.offsetParent}function lw(r,e){let t=Wt(r);if(!Nr(r))return t;let n=nw(r,e);for(;n&&Jv(n)&&Zt(n).position==="static";)n=nw(n,e);return n&&(Mn(n)==="html"||Mn(n)==="body"&&Zt(n).position==="static"&&!ml(n))?t:n||ew(r)||t}var hI=function(r){return P(this,null,function*(){let{reference:e,floating:t,strategy:n}=r,i=this.getOffsetParent||lw,s=this.getDimensions;return{reference:mI(e,yield i(t),n),floating:K({x:0,y:0},yield s(t))}})};function gI(r){return Zt(r).direction==="rtl"}var yI={convertOffsetParentRelativeRectToViewportRelativeRect:aI,getDocumentElement:Cn,getClippingRect:fI,getOffsetParent:lw,getElementRects:hI,getClientRects:oI,getDimensions:pI,getScale:Rs,isElement:Zr,isRTL:gI};var Zm=(r,e,t)=>{let n=new Map,i=K({platform:yI},t),s=he(K({},i.platform),{_c:n});return Qv(r,e,he(K({},i),{platform:s}))};function cw(r,e,t){let n=r.slice();return n[5]=e[t],n}function dw(r,e,t){let n=r.slice();n[40]=e[t],n[43]=t;let i=n[17](n[40].taskLocation.path);return n[41]=i,n}function fw(r){let e,t,n,i=r[10],s=[];for(let a=0;a<i.length;a+=1)s[a]=mw(dw(r,i,a));return{c(){e=Q("ul");for(let a=0;a<s.length;a+=1)s[a].c();W(e,"class","task-dependency-dropdown")},m(a,o){de(a,e,o);for(let u=0;u<s.length;u+=1)s[u]&&s[u].m(e,null);r[31](e),t||(n=xe(e,"mouseleave",r[32]),t=!0)},p(a,o){if(o[0]&928832){i=a[10];let u;for(u=0;u<i.length;u+=1){let l=dw(a,i,u);s[u]?s[u].p(l,o):(s[u]=mw(l),s[u].c(),s[u].m(e,null))}for(;u<s.length;u+=1)s[u].d(1);s.length=i.length}},d(a){a&&oe(e),Ci(s,a),r[31](null),t=!1,n()}}}function pw(r){let e,t=r[41]+"",n,i,s;function a(...o){return r[28](r[41],...o)}return{c(){e=Q("div"),n=Me(t),W(e,"class","dependency-path")},m(o,u){de(o,e,u),U(e,n),i||(s=xe(e,"mouseenter",a),i=!0)},p(o,u){r=o,u[0]&1024&&t!==(t=r[41]+"")&&Ar(n,t)},d(o){o&&oe(e),i=!1,s()}}}function mw(r){let e,t,n,i=r[40].status.symbol+"",s,a,o=Oi(r[40])+"",u,l,c,d,p,m;function y(...R){return r[27](r[40],...R)}let _=r[41]&&pw(r);function b(){return r[29](r[40])}function E(){return r[30](r[43])}return{c(){e=Q("li"),t=Q("div"),n=Me("["),s=Me(i),a=Me("] "),u=Me(o),c=ue(),_&&_.c(),d=ue(),W(t,"class",l=r[41]?"dependency-name-shared":"dependency-name"),Dn(e,"selected",r[6]!==null&&r[43]===r[11])},m(R,S){de(R,e,S),U(e,t),U(t,n),U(t,s),U(t,a),U(t,u),U(e,c),_&&_.m(e,null),U(e,d),p||(m=[xe(t,"mouseenter",y),xe(e,"mousedown",b),xe(e,"mouseenter",E)],p=!0)},p(R,S){r=R,S[0]&1024&&i!==(i=r[40].status.symbol+"")&&Ar(s,i),S[0]&1024&&o!==(o=Oi(r[40])+"")&&Ar(u,o),S[0]&1024&&l!==(l=r[41]?"dependency-name-shared":"dependency-name")&&W(t,"class",l),r[41]?_?_.p(r,S):(_=pw(r),_.c(),_.m(e,d)):_&&(_.d(1),_=null),S[0]&2112&&Dn(e,"selected",r[6]!==null&&r[43]===r[11])},d(R){R&&oe(e),_&&_.d(),p=!1,Ft(m)}}}function hw(r){let e,t=r[0][r[1]],n=[];for(let i=0;i<t.length;i+=1)n[i]=gw(cw(r,t,i));return{c(){e=Q("div");for(let i=0;i<n.length;i+=1)n[i].c();W(e,"class","task-dependencies-container results-dependency")},m(i,s){de(i,e,s);for(let a=0;a<n.length;a+=1)n[a]&&n[a].m(e,null)},p(i,s){if(s[0]&802819){t=i[0][i[1]];let a;for(a=0;a<t.length;a+=1){let o=cw(i,t,a);n[a]?n[a].p(o,s):(n[a]=gw(o),n[a].c(),n[a].m(e,null))}for(;a<n.length;a+=1)n[a].d(1);n.length=t.length}},d(i){i&&oe(e),Ci(n,i)}}}function gw(r){let e,t,n,i=r[5].status.symbol+"",s,a,o=Oi(r[5])+"",u,l,c,d,p,m;function y(){return r[33](r[5])}function _(...b){return r[34](r[5],...b)}return{c(){e=Q("div"),t=Q("span"),n=Me("["),s=Me(i),a=Me("] "),u=Me(o),l=ue(),c=Q("button"),c.innerHTML='<svg style="display: block; margin: auto;" xmlns="http://www.w3.org/2000/svg" width="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="4" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-x"><path d="M18 6 6 18"></path><path d="m6 6 12 12"></path></svg>',d=ue(),W(t,"class","task-dependency-name"),W(c,"type","button"),W(c,"class","task-dependency-delete"),W(e,"class","task-dependency")},m(b,E){de(b,e,E),U(e,t),U(t,n),U(t,s),U(t,a),U(t,u),U(e,l),U(e,c),U(e,d),p||(m=[xe(c,"click",y),xe(e,"mouseenter",_)],p=!0)},p(b,E){r=b,E[0]&3&&i!==(i=r[5].status.symbol+"")&&Ar(s,i),E[0]&3&&o!==(o=Oi(r[5])+"")&&Ar(u,o)},d(b){b&&oe(e),p=!1,Ft(m)}}}function bI(r){let e,t=pt(r[2],r[3])+"",n,i,s,a,o,u,l,c,d,p=r[10]&&r[10].length!==0&&fw(r),m=r[0][r[1]].length!==0&&hw(r);return{c(){e=Q("label"),n=ue(),i=Q("span"),s=Q("input"),o=ue(),p&&p.c(),u=ue(),m&&m.c(),l=Fv(),W(e,"for",r[1]),W(s,"accesskey",r[3]),W(s,"id",r[1]),W(s,"class","tasks-modal-dependency-input"),W(s,"type","text"),W(s,"placeholder",r[4]),Mi(()=>r[26].call(i))},m(y,_){de(y,e,_),e.innerHTML=t,de(y,n,_),de(y,i,_),U(i,s),r[22](s),gr(s,r[6]),a=qv(i,r[26].bind(i)),de(y,o,_),p&&p.m(y,_),de(y,u,_),m&&m.m(y,_),de(y,l,_),c||(d=[xe(s,"input",r[23]),xe(s,"keydown",r[24]),xe(s,"focus",r[16]),xe(s,"blur",r[25])],c=!0)},p(y,_){_[0]&12&&t!==(t=pt(y[2],y[3])+"")&&(e.innerHTML=t),_[0]&2&&W(e,"for",y[1]),_[0]&8&&W(s,"accesskey",y[3]),_[0]&2&&W(s,"id",y[1]),_[0]&16&&W(s,"placeholder",y[4]),_[0]&64&&s.value!==y[6]&&gr(s,y[6]),y[10]&&y[10].length!==0?p?p.p(y,_):(p=fw(y),p.c(),p.m(u.parentNode,u)):p&&(p.d(1),p=null),y[0][y[1]].length!==0?m?m.p(y,_):(m=hw(y),m.c(),m.m(l.parentNode,l)):m&&(m.d(1),m=null)},i:Ue,o:Ue,d(y){y&&oe(e),y&&oe(n),y&&oe(i),r[22](null),a(),y&&oe(o),p&&p.d(y),y&&oe(u),m&&m.d(y),y&&oe(l),c=!1,Ft(d)}}}function TI(r,e,t){let{task:n}=e,{editableTask:i}=e,{allTasks:s}=e,{_onDescriptionKeyDown:a}=e,{type:o}=e,{labelText:u}=e,{accesskey:l}=e,{placeholder:c="Type to search..."}=e,d="",p=null,m=0,y,_=!1,b=!1,E,R;function S(v){t(0,i[o]=[...i[o],v],i),t(6,d=""),t(7,_=!1)}function F(v){t(0,i[o]=i[o].filter(x=>x!==v),i)}function q(v){var x;if(p!==null){switch(v.key){case"ArrowUp":v.preventDefault(),!!m&&m>0?t(11,m-=1):t(11,m=p.length-1);break;case"ArrowDown":v.preventDefault(),!!m&&m<p.length-1?t(11,m+=1):t(11,m=0);break;case"Enter":m!==null?(v.preventDefault(),S(p[m]),t(11,m=null),t(7,_=!1)):a(v);break;default:t(11,m=0);break}m&&((x=R==null?void 0:R.getElementsByTagName("li")[m])===null||x===void 0||x.scrollIntoView({block:"nearest"}))}}function te(v){return!v&&!b?[]:(b=!1,Ju(v,s,n,i.blockedBy,i.blocking))}function G(){t(7,_=!0),b=!0}function H(v,x){!v||!x||Zm(v,x,{middleware:[dl(6),fl(),Qm(),Xm({apply(){x&&Object.assign(x.style,{width:`${y}px`})}})]}).then(({x:N,y:re})=>{x.style.left=`${N}px`,x.style.top=`${re}px`})}function be(v){return v===n.taskLocation.path?"":v}function Pe(v){return Oi(v)}function j(v,x){let N=v.createDiv();N.addClasses(["tooltip","pop-up"]),N.innerText=x,Zm(v,N,{placement:"top",middleware:[dl(-18),fl()]}).then(({x:re,y:le})=>{N.style.left=`${re}px`,N.style.top=`${le}px`}),v.addEventListener("mouseleave",()=>N.remove())}function $(v){je[v?"unshift":"push"](()=>{E=v,t(8,E)})}function D(){d=this.value,t(6,d)}let B=v=>q(v),I=()=>t(7,_=!1);function Z(){y=this.clientWidth,t(12,y)}let f=(v,x)=>j(x.currentTarget,Pe(v)),h=(v,x)=>j(x.currentTarget,v),g=v=>S(v),T=v=>t(11,m=v);function w(v){je[v?"unshift":"push"](()=>{R=v,t(9,R)})}let O=()=>t(11,m=null),M=v=>F(v),A=(v,x)=>j(x.currentTarget,Pe(v));return r.$$set=v=>{"task"in v&&t(5,n=v.task),"editableTask"in v&&t(0,i=v.editableTask),"allTasks"in v&&t(20,s=v.allTasks),"_onDescriptionKeyDown"in v&&t(21,a=v._onDescriptionKeyDown),"type"in v&&t(1,o=v.type),"labelText"in v&&t(2,u=v.labelText),"accesskey"in v&&t(3,l=v.accesskey),"placeholder"in v&&t(4,c=v.placeholder)},r.$$.update=()=>{if(r.$$.dirty[0]&768){e:H(E,R)}if(r.$$.dirty[0]&192){e:t(10,p=_?te(d):null)}},[i,o,u,l,c,n,d,_,E,R,p,m,y,S,F,q,G,be,Pe,j,s,a,$,D,B,I,Z,f,h,g,T,w,O,M,A]}var Jm=class extends hr{constructor(e){super(),Xr(this,e,TI,bI,Qr,{task:5,editableTask:0,allTasks:20,_onDescriptionKeyDown:21,type:1,labelText:2,accesskey:3,placeholder:4},null,[-1,-1])}},eh=Jm;var Co=require("obsidian");var Mo,th,rh,_I=["md"];function bl(){return St.getLogger("tasks.File")}var yw=({metadataCache:r,vault:e,workspace:t})=>{Mo=r,th=e,rh=t},br=t=>P(void 0,[t],function*({originalTask:r,newTasks:e}){if(th===void 0||Mo===void 0||rh===void 0){gl("Tasks: cannot use File before initializing it.");return}Array.isArray(e)||(e=[e]);let n=bl(),i="replaceTaskWithTasks()";il(n,i,r),sl(n,i,e),yield Tw({originalTask:r,newTasks:e,vault:th,metadataCache:Mo,workspace:rh,previousTries:0})});function gl(r){console.error(r),new Co.Notice(r,15e3)}function bw(r){console.warn(r),new Co.Notice(r,1e4)}function vI(r){bl().debug(r)}var zn=class extends Error{},yl=class extends Error{},Tw=a=>P(void 0,[a],function*({originalTask:r,newTasks:e,vault:t,metadataCache:n,workspace:i,previousTries:s}){let o=bl();o.debug(`tryRepetitive after ${s} previous tries`);let u=()=>P(void 0,null,function*(){if(s>10){let c=`Tasks: Could not find the correct task line to update.

The task line not updated is:
${r.originalMarkdown}

In this markdown file:
"${r.taskLocation.path}"

Note: further clicks on this checkbox will usually now be ignored until the file is opened (or certain, specific edits are made - it's complicated).

Recommendations:

1. Close all panes that have the above file open, and then re-open the file.

2. Check for exactly identical copies of the task line, in this file, and see if you can make them different.
`;gl(c);return}let l=Math.min(Math.pow(10,s),100);o.debug(`timeout = ${l}`),setTimeout(()=>P(void 0,null,function*(){yield Tw({originalTask:r,newTasks:e,vault:t,metadataCache:n,workspace:i,previousTries:s+1})}),l)});try{let[l,c,d]=yield _w(r,t),p=[...d.slice(0,l),...e.map(m=>m.toFileLineString()),...d.slice(l+1)];yield t.modify(c,p.join(`
`))}catch(l){if(l instanceof zn){l.message&&bw(l.message),yield u();return}else if(l instanceof yl){yield u();return}else l instanceof Error&&gl(l.message)}});function _w(r,e){return P(this,null,function*(){if(Mo===void 0)throw new zn;let t=e.getAbstractFileByPath(r.path);if(!(t instanceof Co.TFile))throw new zn(`Tasks: No file found for task ${r.description}. Retrying ...`);if(!_I.includes(t.extension))throw new Error(`Tasks: Does not support files with the ${t.extension} file extension.`);let n=Mo.getFileCache(t);if(n==null||n===null)throw new zn(`Tasks: No file cache found for file ${t.path}. Retrying ...`);let i=n.listItems;if(i===void 0||i.length===0)throw new zn(`Tasks: No list items found in file cache of ${t.path}. Retrying ...`);let a=(yield e.read(t)).split(`
`),o=wI(r,a,i,vI);if(o===void 0)throw new yl;return[o,t,a]})}function nh(r,e){return P(this,null,function*(){try{let[t,n,i]=yield _w(r,e);return[t,n]}catch(t){t instanceof zn?t.message&&bw(t.message):t instanceof Error&&gl(t.message)}})}function vw(r,e){return r<e.length}function wI(r,e,t,n){let i=kI(r,e);return i!==void 0||(i=EI(r,e),i!==void 0)?i:SI(r,e,t,n)}function kI(r,e){let t=r.taskLocation.lineNumber;if(vw(t,e)&&e[t]===r.originalMarkdown)return bl().debug(`Found original markdown at original line number ${t}`),t}function EI(r,e){let t=[];for(let n=0;n<e.length;n++)e[n]===r.originalMarkdown&&t.push(n);if(t.length===1)return t[0]}function SI(r,e,t,n){let i,s=0;for(let a of t){let o=a.position.start.line;if(!vw(o,e))return;if(o<r.taskLocation.sectionStart||a.task===void 0)continue;let u=e[o];if(_e.getInstance().includedIn(u)){if(s===r.taskLocation.sectionIndex){if(u===r.originalMarkdown)i=o;else{n(`Tasks: Unable to find task in file ${r.taskLocation.path}.
Expected task:
${r.originalMarkdown}
Found task:
${u}`);return}break}s++}}return i}var Ms=class{constructor(e){this.addGlobalFilterOnSave=e.addGlobalFilterOnSave,this.originalBlocking=e.originalBlocking,this.description=e.description,this.status=e.status,this.priority=e.priority,this.onCompletion=e.onCompletion,this.recurrenceRule=e.recurrenceRule,this.createdDate=e.createdDate,this.startDate=e.startDate,this.scheduledDate=e.scheduledDate,this.dueDate=e.dueDate,this.doneDate=e.doneDate,this.cancelledDate=e.cancelledDate,this.forwardOnly=e.forwardOnly,this.blockedBy=e.blockedBy,this.blocking=e.blocking}static fromTask(e,t){let n=_e.getInstance().removeAsWordFrom(e.description),i=n!=e.description||!_e.getInstance().includedIn(e.description),s="none";e.priority==="5"?s="lowest":e.priority==="4"?s="low":e.priority==="2"?s="medium":e.priority==="1"?s="high":e.priority==="0"&&(s="highest");let a=[];for(let u of e.dependsOn){let l=t.find(c=>c.id===u);!l||a.push(l)}let o=t.filter(u=>u.dependsOn.includes(e.id));return new Ms({addGlobalFilterOnSave:i,originalBlocking:o,description:n,status:e.status,priority:s,recurrenceRule:e.recurrence?e.recurrence.toText():"",onCompletion:e.onCompletion,createdDate:e.created.formatAsDate(),startDate:e.start.formatAsDate(),scheduledDate:e.scheduled.formatAsDate(),dueDate:e.due.formatAsDate(),doneDate:e.done.formatAsDate(),cancelledDate:e.cancelled.formatAsDate(),forwardOnly:!0,blockedBy:a,blocking:o})}applyEdits(e,t){return P(this,null,function*(){let n=this.description.trim();this.addGlobalFilterOnSave&&(n=_e.getInstance().prependTo(n));let i=Ti(this.startDate,this.forwardOnly),s=Ti(this.scheduledDate,this.forwardOnly),a=Ti(this.dueDate,this.forwardOnly),o=Ti(this.cancelledDate,this.forwardOnly),u=Ti(this.createdDate,this.forwardOnly),l=Ti(this.doneDate,this.forwardOnly),c=null;this.recurrenceRule&&(c=it.fromText({recurrenceRuleText:this.recurrenceRule,occurrence:new fr({startDate:i,scheduledDate:s,dueDate:a})}));let d;switch(this.priority){case"lowest":d="5";break;case"low":d="4";break;case"medium":d="2";break;case"high":d="1";break;case"highest":d="0";break;default:d="3"}let p=this.onCompletion,m=[];for(let S of this.blockedBy){let F=yield OI(S,t);m.push(F)}let y=e.id,_=[],b=[];(this.blocking.toString()!==this.originalBlocking.toString()||this.blocking.length!==0)&&(e.id===""&&(y=To(t.filter(S=>S.id!=="").map(S=>S.id))),_=this.originalBlocking.filter(S=>!this.blocking.includes(S)),b=this.blocking.filter(S=>!this.originalBlocking.includes(S)));let E=new ae(he(K({},e),{description:n,status:e.status,priority:d,onCompletion:p,recurrence:c,startDate:i,scheduledDate:s,dueDate:a,doneDate:l,createdDate:u,cancelledDate:o,dependsOn:m.map(S=>S.id),id:y}));for(let S of _){let F=yv(S,E);yield br({originalTask:S,newTasks:F})}for(let S of b){let F=gv(S,E);yield br({originalTask:S,newTasks:F})}let R=l||window.moment();return E.handleNewStatusWithRecurrenceInUsersOrder(this.status,R)})}parseAndValidateRecurrence(){var t;if(!this.recurrenceRule)return{parsedRecurrence:"<i>not recurring</>",isRecurrenceValid:!0};let e=(t=it.fromText({recurrenceRuleText:this.recurrenceRule,occurrence:new fr({startDate:null,scheduledDate:null,dueDate:null})}))==null?void 0:t.toText();return e?this.startDate||this.scheduledDate||this.dueDate?{parsedRecurrence:e,isRecurrenceValid:!0}:{parsedRecurrence:"<i>due, scheduled or start date required</i>",isRecurrenceValid:!1}:{parsedRecurrence:"<i>invalid recurrence rule</i>",isRecurrenceValid:!1}}};function OI(r,e){return P(this,null,function*(){if(r.id!=="")return r;let t=e.filter(i=>i.id!==""),n=Zu(r,t.map(i=>i.id));return yield br({originalTask:r,newTasks:n}),n})}function DI(r){let e,t=pt("Recurs",r[2])+"",n,i,s,a,o,u,l,c,d;return{c(){e=Q("label"),n=ue(),i=Q("input"),s=ue(),a=Q("code"),o=Me(r[4]),u=ue(),l=new ks(!1),W(e,"for","recurrence"),W(i,"id","recurrence"),W(i,"type","text"),W(i,"class","tasks-modal-date-input"),W(i,"placeholder","Try 'every day when done'"),W(i,"accesskey",r[2]),Dn(i,"tasks-modal-error",!r[1]),l.a=null,W(a,"class","tasks-modal-parsed-date")},m(p,m){de(p,e,m),e.innerHTML=t,de(p,n,m),de(p,i,m),gr(i,r[0].recurrenceRule),de(p,s,m),de(p,a,m),U(a,o),U(a,u),l.m(r[3],a),c||(d=xe(i,"input",r[5]),c=!0)},p(p,[m]){m&4&&t!==(t=pt("Recurs",p[2])+"")&&(e.innerHTML=t),m&4&&W(i,"accesskey",p[2]),m&1&&i.value!==p[0].recurrenceRule&&gr(i,p[0].recurrenceRule),m&2&&Dn(i,"tasks-modal-error",!p[1]),m&8&&l.p(p[3])},i:Ue,o:Ue,d(p){p&&oe(e),p&&oe(n),p&&oe(i),p&&oe(s),p&&oe(a),c=!1,d()}}}function xI(r,e,t){let{editableTask:n}=e,{isRecurrenceValid:i}=e,{accesskey:s}=e,a,{recurrenceSymbol:o}=Mr.tasksPluginEmoji.taskSerializer.symbols;function u(){n.recurrenceRule=this.value,t(0,n)}return r.$$set=l=>{"editableTask"in l&&t(0,n=l.editableTask),"isRecurrenceValid"in l&&t(1,i=l.isRecurrenceValid),"accesskey"in l&&t(2,s=l.accesskey)},r.$$.update=()=>{if(r.$$.dirty&1){e:t(3,{parsedRecurrence:a,isRecurrenceValid:i}=n.parseAndValidateRecurrence(),a,(t(1,i),t(0,n)))}},[n,i,s,a,o,u]}var ih=class extends hr{constructor(e){super(),Xr(this,e,xI,DI,Qr,{editableTask:0,isRecurrenceValid:1,accesskey:2})}},ww=ih;function kw(r,e,t){let n=r.slice();return n[7]=e[t],n}function Ew(r){let e,t=r[7].name+"",n,i,s=r[7].symbol+"",a,o,u;return{c(){e=Q("option"),n=Me(t),i=Me(" ["),a=Me(s),o=Me("]"),e.__value=u=r[7].symbol,e.value=e.__value},m(l,c){de(l,e,c),U(e,n),U(e,i),U(e,a),U(e,o)},p(l,c){c&1&&t!==(t=l[7].name+"")&&Ar(n,t),c&1&&s!==(s=l[7].symbol+"")&&Ar(a,s),c&1&&u!==(u=l[7].symbol)&&(e.__value=u,e.value=e.__value)},d(l){l&&oe(e)}}}function RI(r){let e,t=pt("Status",r[1])+"",n,i,s,a,o=r[0],u=[];for(let l=0;l<o.length;l+=1)u[l]=Ew(kw(r,o,l));return{c(){e=Q("label"),n=ue(),i=Q("select");for(let l=0;l<u.length;l+=1)u[l].c();W(e,"for","status"),W(i,"id","status-type"),W(i,"class","status-editor-status-selector"),W(i,"accesskey",r[1]),r[2]===void 0&&Mi(()=>r[6].call(i))},m(l,c){de(l,e,c),e.innerHTML=t,de(l,n,c),de(l,i,c);for(let d=0;d<u.length;d+=1)u[d]&&u[d].m(i,null);Gm(i,r[2],!0),s||(a=[xe(i,"change",r[6]),xe(i,"change",r[3])],s=!0)},p(l,[c]){if(c&2&&t!==(t=pt("Status",l[1])+"")&&(e.innerHTML=t),c&1){o=l[0];let d;for(d=0;d<o.length;d+=1){let p=kw(l,o,d);u[d]?u[d].p(p,c):(u[d]=Ew(p),u[d].c(),u[d].m(i,null))}for(;d<u.length;d+=1)u[d].d(1);u.length=o.length}c&2&&W(i,"accesskey",l[1]),c&5&&Gm(i,l[2])},i:Ue,o:Ue,d(l){l&&oe(e),l&&oe(n),l&&oe(i),Ci(u,l),s=!1,Ft(a)}}}function MI(r,e,t){let{task:n}=e,{editableTask:i}=e,{statusOptions:s}=e,{accesskey:a}=e,o=n.status.symbol,u=()=>{let c=s.find(p=>p.symbol===o);if(c)t(4,i.status=c,i);else{console.log(`Error in EditTask: cannot find status with symbol ${o}`);return}let d=n.handleNewStatus(c).pop();d&&(t(4,i.doneDate=d.done.formatAsDate(),i),t(4,i.cancelledDate=d.cancelled.formatAsDate(),i))};function l(){o=Wv(this),t(2,o),t(0,s)}return r.$$set=c=>{"task"in c&&t(5,n=c.task),"editableTask"in c&&t(4,i=c.editableTask),"statusOptions"in c&&t(0,s=c.statusOptions),"accesskey"in c&&t(1,a=c.accesskey)},[s,a,o,u,i,n,l]}var sh=class extends hr{constructor(e){super(),Xr(this,e,MI,RI,Qr,{task:5,editableTask:4,statusOptions:0,accesskey:1})}},Sw=sh;function Ow(r,e,t){let n=r.slice();return n[49]=e[t].value,n[50]=e[t].label,n[51]=e[t].symbol,n[52]=e[t].accessKey,n[53]=e[t].accessKeyIndex,n}function CI(r){let e,t=r[50]+"",n;return{c(){e=Q("span"),n=Me(t)},m(i,s){de(i,e,s),U(e,n)},p:Ue,d(i){i&&oe(e)}}}function AI(r){let e,t=r[50].substring(0,r[53])+"",n,i,s=r[50].substring(r[53],r[53]+1)+"",a,o,u=r[50].substring(r[53]+1)+"",l;return{c(){e=Q("span"),n=Me(t),i=Q("span"),a=Me(s),o=Q("span"),l=Me(u),W(i,"class","accesskey")},m(c,d){de(c,e,d),U(e,n),de(c,i,d),U(i,a),de(c,o,d),U(o,l)},p:Ue,d(c){c&&oe(e),c&&oe(i),c&&oe(o)}}}function PI(r){let e,t=r[51]+"",n;return{c(){e=Q("span"),n=Me(t)},m(i,s){de(i,e,s),U(e,n)},p:Ue,d(i){i&&oe(e)}}}function Dw(r){let e,t,n,i,s,a,o,u,l=r[51]&&r[51].charCodeAt(0)>=256,c,d,p,m,y;function _(S,F){return S[11]?AI:CI}let b=_(r,[-1,-1]),E=b(r),R=l&&PI(r);return p=Uv(r[32][0]),{c(){e=Q("div"),t=Q("input"),a=ue(),o=Q("label"),E.c(),u=ue(),R&&R.c(),d=ue(),W(t,"type","radio"),W(t,"id",n="priority-"+r[49]),t.__value=i=r[49],t.value=t.__value,W(t,"accesskey",s=r[15](r[52])),W(o,"for",c="priority-"+r[49]),W(e,"class","task-modal-priority-option-container"),p.p(t)},m(S,F){de(S,e,F),U(e,t),t.checked=t.__value===r[3].priority,U(e,a),U(e,o),E.m(o,null),U(o,u),R&&R.m(o,null),U(e,d),m||(y=xe(t,"change",r[31]),m=!0)},p(S,F){F[0]&32768&&s!==(s=S[15](S[52]))&&W(t,"accesskey",s),F[0]&8&&(t.checked=t.__value===S[3].priority),b===(b=_(S,F))&&E?E.p(S,F):(E.d(1),E=b(S),E&&(E.c(),E.m(o,u))),l&&R.p(S,F)},d(S){S&&oe(e),E.d(),R&&R.d(),p.r(),m=!1,y()}}}function NI(r){let e;return{c(){e=Q("div"),e.innerHTML="<i>Blocking and blocked by fields are disabled when vault tasks is empty</i>"},m(t,n){de(t,e,n)},p:Ue,i:Ue,o:Ue,d(t){t&&oe(e)}}}function II(r){let e,t,n,i;return e=new eh({props:{type:"blockedBy",labelText:"Before this",task:r[0],editableTask:r[3],allTasks:r[2],_onDescriptionKeyDown:r[24],accesskey:r[15]("b"),placeholder:"Search for tasks that the task being edited depends on..."}}),n=new eh({props:{type:"blocking",labelText:"After this",task:r[0],editableTask:r[3],allTasks:r[2],_onDescriptionKeyDown:r[24],accesskey:r[15]("e"),placeholder:"Search for tasks that depend on this task being done..."}}),{c(){Pr(e.$$.fragment),t=ue(),Pr(n.$$.fragment)},m(s,a){yr(e,s,a),de(s,t,a),yr(n,s,a),i=!0},p(s,a){let o={};a[0]&1&&(o.task=s[0]),a[0]&8&&(o.editableTask=s[3]),a[0]&4&&(o.allTasks=s[2]),a[0]&32768&&(o.accesskey=s[15]("b")),e.$set(o);let u={};a[0]&1&&(u.task=s[0]),a[0]&8&&(u.editableTask=s[3]),a[0]&4&&(u.allTasks=s[2]),a[0]&32768&&(u.accesskey=s[15]("e")),n.$set(u)},i(s){i||(Lt(e.$$.fragment,s),Lt(n.$$.fragment,s),i=!0)},o(s){Qt(e.$$.fragment,s),Qt(n.$$.fragment,s),i=!1},d(s){Xt(e,s),s&&oe(t),Xt(n,s)}}}function FI(r){let e,t,n,i=pt("Description",r[15]("t"))+"",s,a,o,u,l,c,d,p,m,y,_,b,E,R,S,F,q,te,G,H,be,Pe,j,$,D,B,I,Z,f,h,g=pt("Only future dates:",r[15]("f"))+"",T,w,O,M,A,v,x,N,re,le,fe,Ne,Ce,Mt,an,Wr,Y,jt,Qe,Uh,on,pc,mc,Wh,un,hc,gc,qh,Bi,oi,$h,Yo,jh,Zs,qr,yc,Gh,Hi=r[22],vt=[];for(let C=0;C<Hi.length;C+=1)vt[C]=Dw(Ow(r,Hi,C));function Bk(C){r[33](C)}let Yh={editableTask:r[3],accesskey:r[15]("r")};r[10]!==void 0&&(Yh.isRecurrenceValid=r[10]),R=new ww({props:Yh}),je.push(()=>Dt(R,"isRecurrenceValid",Bk));function Hk(C){r[34](C)}function Vk(C){r[35](C)}let bc={id:"due",dateSymbol:r[18],forwardOnly:r[3].forwardOnly,accesskey:r[15]("d")};r[3].dueDate!==void 0&&(bc.date=r[3].dueDate),r[7]!==void 0&&(bc.isDateValid=r[7]),q=new Ai({props:bc}),je.push(()=>Dt(q,"date",Hk)),je.push(()=>Dt(q,"isDateValid",Vk));function zk(C){r[36](C)}function Kk(C){r[37](C)}let Tc={id:"scheduled",dateSymbol:r[17],forwardOnly:r[3].forwardOnly,accesskey:r[15]("s")};r[3].scheduledDate!==void 0&&(Tc.date=r[3].scheduledDate),r[8]!==void 0&&(Tc.isDateValid=r[8]),be=new Ai({props:Tc}),je.push(()=>Dt(be,"date",zk)),je.push(()=>Dt(be,"isDateValid",Kk));function Qk(C){r[38](C)}function Xk(C){r[39](C)}let _c={id:"start",dateSymbol:r[16],forwardOnly:r[3].forwardOnly,accesskey:r[15]("a")};r[3].startDate!==void 0&&(_c.date=r[3].startDate),r[9]!==void 0&&(_c.isDateValid=r[9]),D=new Ai({props:_c}),je.push(()=>Dt(D,"date",Qk)),je.push(()=>Dt(D,"isDateValid",Xk));let Bh=[II,NI],Fn=[];function Hh(C,ee){return C[2].length>0&&C[14]?0:1}N=Hh(r,[-1,-1]),re=Fn[N]=Bh[N](r);function Zk(C){r[41](C)}let Vh={task:r[0],statusOptions:r[1],accesskey:r[15]("u")};r[3]!==void 0&&(Vh.editableTask=r[3]),Mt=new Sw({props:Vh}),je.push(()=>Dt(Mt,"editableTask",Zk));function Jk(C){r[42](C)}function eE(C){r[43](C)}let vc={id:"created",dateSymbol:r[20],forwardOnly:r[3].forwardOnly,accesskey:r[15]("c")};r[3].createdDate!==void 0&&(vc.date=r[3].createdDate),r[5]!==void 0&&(vc.isDateValid=r[5]),Y=new Ai({props:vc}),je.push(()=>Dt(Y,"date",Jk)),je.push(()=>Dt(Y,"isDateValid",eE));function tE(C){r[44](C)}function rE(C){r[45](C)}let wc={id:"done",dateSymbol:r[21],forwardOnly:r[3].forwardOnly,accesskey:r[15]("x")};r[3].doneDate!==void 0&&(wc.date=r[3].doneDate),r[6]!==void 0&&(wc.isDateValid=r[6]),on=new Ai({props:wc}),je.push(()=>Dt(on,"date",tE)),je.push(()=>Dt(on,"isDateValid",rE));function nE(C){r[46](C)}function iE(C){r[47](C)}let kc={id:"cancelled",dateSymbol:r[19],forwardOnly:r[3].forwardOnly,accesskey:r[15]("-")};return r[3].cancelledDate!==void 0&&(kc.date=r[3].cancelledDate),r[4]!==void 0&&(kc.isDateValid=r[4]),un=new Ai({props:kc}),je.push(()=>Dt(un,"date",nE)),je.push(()=>Dt(un,"isDateValid",iE)),{c(){e=Q("form"),t=Q("section"),n=Q("label"),s=ue(),a=Q("textarea"),u=ue(),l=Q("section"),c=Q("label"),d=Me("Priority"),m=ue();for(let C=0;C<vt.length;C+=1)vt[C].c();y=ue(),_=Q("hr"),b=ue(),E=Q("section"),Pr(R.$$.fragment),F=ue(),Pr(q.$$.fragment),H=ue(),Pr(be.$$.fragment),$=ue(),Pr(D.$$.fragment),Z=ue(),f=Q("div"),h=Q("label"),T=ue(),w=Q("input"),M=ue(),A=Q("hr"),v=ue(),x=Q("section"),re.c(),le=ue(),fe=Q("hr"),Ne=ue(),Ce=Q("section"),Pr(Mt.$$.fragment),Wr=ue(),Pr(Y.$$.fragment),Uh=ue(),Pr(on.$$.fragment),Wh=ue(),Pr(un.$$.fragment),qh=ue(),Bi=Q("section"),oi=Q("button"),$h=Me("Apply"),jh=ue(),Zs=Q("button"),Zs.textContent="Cancel",W(n,"for","description"),W(a,"id","description"),W(a,"class","tasks-modal-description"),W(a,"placeholder","Take out the trash"),W(a,"accesskey",o=r[15]("t")),W(t,"class","tasks-modal-description-section"),W(c,"for",p="priority-"+r[3].priority),W(l,"class","tasks-modal-priority-section"),W(h,"for","forwardOnly"),W(w,"id","forwardOnly"),W(w,"type","checkbox"),W(w,"class","task-list-item-checkbox tasks-modal-checkbox"),W(w,"accesskey",O=r[15]("f")),W(f,"class","future-dates-only"),W(E,"class","tasks-modal-dates-section"),W(x,"class","tasks-modal-dependencies-section"),W(Ce,"class","tasks-modal-dates-section"),oi.disabled=Yo=!r[13],W(oi,"type","submit"),W(oi,"class","mod-cta"),W(Zs,"type","button"),W(Bi,"class","tasks-modal-button-section"),W(e,"class","tasks-modal")},m(C,ee){de(C,e,ee),U(e,t),U(t,n),n.innerHTML=i,U(t,s),U(t,a),gr(a,r[3].description),r[30](a),U(e,u),U(e,l),U(l,c),U(c,d),U(l,m);for(let ln=0;ln<vt.length;ln+=1)vt[ln]&&vt[ln].m(l,null);U(e,y),U(e,_),U(e,b),U(e,E),yr(R,E,null),U(E,F),yr(q,E,null),U(E,H),yr(be,E,null),U(E,$),yr(D,E,null),U(E,Z),U(E,f),U(f,h),h.innerHTML=g,U(f,T),U(f,w),w.checked=r[3].forwardOnly,U(e,M),U(e,A),U(e,v),U(e,x),Fn[N].m(x,null),U(e,le),U(e,fe),U(e,Ne),U(e,Ce),yr(Mt,Ce,null),U(Ce,Wr),yr(Y,Ce,null),U(Ce,Uh),yr(on,Ce,null),U(Ce,Wh),yr(un,Ce,null),U(e,qh),U(e,Bi),U(Bi,oi),U(oi,$h),U(Bi,jh),U(Bi,Zs),qr=!0,yc||(Gh=[xe(a,"input",r[29]),xe(a,"keydown",r[24]),xe(a,"paste",r[25]),xe(a,"drop",r[25]),xe(w,"change",r[40]),xe(Zs,"click",r[23]),xe(e,"submit",Lv(r[26]))],yc=!0)},p(C,ee){if((!qr||ee[0]&32768)&&i!==(i=pt("Description",C[15]("t"))+"")&&(n.innerHTML=i),(!qr||ee[0]&32768&&o!==(o=C[15]("t")))&&W(a,"accesskey",o),ee[0]&8&&gr(a,C[3].description),(!qr||ee[0]&8&&p!==(p="priority-"+C[3].priority))&&W(c,"for",p),ee[0]&4229128){Hi=C[22];let nr;for(nr=0;nr<Hi.length;nr+=1){let zh=Ow(C,Hi,nr);vt[nr]?vt[nr].p(zh,ee):(vt[nr]=Dw(zh),vt[nr].c(),vt[nr].m(l,null))}for(;nr<vt.length;nr+=1)vt[nr].d(1);vt.length=Hi.length}let ln={};ee[0]&8&&(ln.editableTask=C[3]),ee[0]&32768&&(ln.accesskey=C[15]("r")),!S&&ee[0]&1024&&(S=!0,ln.isRecurrenceValid=C[10],Ot(()=>S=!1)),R.$set(ln);let Js={};ee[0]&8&&(Js.forwardOnly=C[3].forwardOnly),ee[0]&32768&&(Js.accesskey=C[15]("d")),!te&&ee[0]&8&&(te=!0,Js.date=C[3].dueDate,Ot(()=>te=!1)),!G&&ee[0]&128&&(G=!0,Js.isDateValid=C[7],Ot(()=>G=!1)),q.$set(Js);let ea={};ee[0]&8&&(ea.forwardOnly=C[3].forwardOnly),ee[0]&32768&&(ea.accesskey=C[15]("s")),!Pe&&ee[0]&8&&(Pe=!0,ea.date=C[3].scheduledDate,Ot(()=>Pe=!1)),!j&&ee[0]&256&&(j=!0,ea.isDateValid=C[8],Ot(()=>j=!1)),be.$set(ea);let ta={};ee[0]&8&&(ta.forwardOnly=C[3].forwardOnly),ee[0]&32768&&(ta.accesskey=C[15]("a")),!B&&ee[0]&8&&(B=!0,ta.date=C[3].startDate,Ot(()=>B=!1)),!I&&ee[0]&512&&(I=!0,ta.isDateValid=C[9],Ot(()=>I=!1)),D.$set(ta),(!qr||ee[0]&32768)&&g!==(g=pt("Only future dates:",C[15]("f"))+"")&&(h.innerHTML=g),(!qr||ee[0]&32768&&O!==(O=C[15]("f")))&&W(w,"accesskey",O),ee[0]&8&&(w.checked=C[3].forwardOnly);let Ec=N;N=Hh(C,ee),N===Ec?Fn[N].p(C,ee):(jv(),Qt(Fn[Ec],1,1,()=>{Fn[Ec]=null}),Gv(),re=Fn[N],re?re.p(C,ee):(re=Fn[N]=Bh[N](C),re.c()),Lt(re,1),re.m(x,null));let ra={};ee[0]&1&&(ra.task=C[0]),ee[0]&2&&(ra.statusOptions=C[1]),ee[0]&32768&&(ra.accesskey=C[15]("u")),!an&&ee[0]&8&&(an=!0,ra.editableTask=C[3],Ot(()=>an=!1)),Mt.$set(ra);let na={};ee[0]&8&&(na.forwardOnly=C[3].forwardOnly),ee[0]&32768&&(na.accesskey=C[15]("c")),!jt&&ee[0]&8&&(jt=!0,na.date=C[3].createdDate,Ot(()=>jt=!1)),!Qe&&ee[0]&32&&(Qe=!0,na.isDateValid=C[5],Ot(()=>Qe=!1)),Y.$set(na);let ia={};ee[0]&8&&(ia.forwardOnly=C[3].forwardOnly),ee[0]&32768&&(ia.accesskey=C[15]("x")),!pc&&ee[0]&8&&(pc=!0,ia.date=C[3].doneDate,Ot(()=>pc=!1)),!mc&&ee[0]&64&&(mc=!0,ia.isDateValid=C[6],Ot(()=>mc=!1)),on.$set(ia);let sa={};ee[0]&8&&(sa.forwardOnly=C[3].forwardOnly),ee[0]&32768&&(sa.accesskey=C[15]("-")),!hc&&ee[0]&8&&(hc=!0,sa.date=C[3].cancelledDate,Ot(()=>hc=!1)),!gc&&ee[0]&16&&(gc=!0,sa.isDateValid=C[4],Ot(()=>gc=!1)),un.$set(sa),(!qr||ee[0]&8192&&Yo!==(Yo=!C[13]))&&(oi.disabled=Yo)},i(C){qr||(Lt(R.$$.fragment,C),Lt(q.$$.fragment,C),Lt(be.$$.fragment,C),Lt(D.$$.fragment,C),Lt(re),Lt(Mt.$$.fragment,C),Lt(Y.$$.fragment,C),Lt(on.$$.fragment,C),Lt(un.$$.fragment,C),qr=!0)},o(C){Qt(R.$$.fragment,C),Qt(q.$$.fragment,C),Qt(be.$$.fragment,C),Qt(D.$$.fragment,C),Qt(re),Qt(Mt.$$.fragment,C),Qt(Y.$$.fragment,C),Qt(on.$$.fragment,C),Qt(un.$$.fragment,C),qr=!1},d(C){C&&oe(e),r[30](null),Ci(vt,C),Xt(R),Xt(q),Xt(be),Xt(D),Fn[N].d(),Xt(Mt),Xt(Y),Xt(on),Xt(un),yc=!1,Ft(Gh)}}}function LI(r,e,t){let n,{task:i}=e,{onSubmit:s}=e,{statusOptions:a}=e,{allTasks:o}=e,{prioritySymbols:u,startDateSymbol:l,scheduledDateSymbol:c,dueDateSymbol:d,cancelledDateSymbol:p,createdDateSymbol:m,doneDateSymbol:y}=Mr.tasksPluginEmoji.taskSerializer.symbols,_,b=Ms.fromTask(i,o),E=!0,R=!0,S=!0,F=!0,q=!0,te=!0,G=!0,H=!0,be=!0,Pe=!0,j=!1,$=[{value:"lowest",label:"Lowest",symbol:u.Lowest,accessKey:"o",accessKeyIndex:1},{value:"low",label:"Low",symbol:u.Low,accessKey:"l",accessKeyIndex:0},{value:"none",label:"Normal",symbol:u.None,accessKey:"n",accessKeyIndex:0},{value:"medium",label:"Medium",symbol:u.Medium,accessKey:"m",accessKeyIndex:0},{value:"high",label:"High",symbol:u.High,accessKey:"h",accessKeyIndex:0},{value:"highest",label:"Highest",symbol:u.Highest,accessKey:"i",accessKeyIndex:1}];Ym(()=>{let{provideAccessKeys:Y}=X();t(11,be=Y),t(14,j=!0),setTimeout(()=>{_.focus()},10)});let D=()=>{s([])},B=Y=>{Y.key==="Enter"&&(Y.preventDefault(),Pe&&Z())},I=()=>{setTimeout(()=>{t(3,b.description=b.description.replace(/[\r\n]+/g," "),b)},0)},Z=()=>Yv(void 0,void 0,void 0,function*(){let Y=yield b.applyEdits(i,o);s(Y)}),f=[[]];function h(){b.description=this.value,t(3,b)}function g(Y){je[Y?"unshift":"push"](()=>{_=Y,t(12,_)})}function T(){b.priority=this.__value,t(3,b)}function w(Y){H=Y,t(10,H)}function O(Y){r.$$.not_equal(b.dueDate,Y)&&(b.dueDate=Y,t(3,b))}function M(Y){q=Y,t(7,q)}function A(Y){r.$$.not_equal(b.scheduledDate,Y)&&(b.scheduledDate=Y,t(3,b))}function v(Y){te=Y,t(8,te)}function x(Y){r.$$.not_equal(b.startDate,Y)&&(b.startDate=Y,t(3,b))}function N(Y){G=Y,t(9,G)}function re(){b.forwardOnly=this.checked,t(3,b)}function le(Y){b=Y,t(3,b)}function fe(Y){r.$$.not_equal(b.createdDate,Y)&&(b.createdDate=Y,t(3,b))}function Ne(Y){S=Y,t(5,S)}function Ce(Y){r.$$.not_equal(b.doneDate,Y)&&(b.doneDate=Y,t(3,b))}function Mt(Y){F=Y,t(6,F)}function an(Y){r.$$.not_equal(b.cancelledDate,Y)&&(b.cancelledDate=Y,t(3,b))}function Wr(Y){R=Y,t(4,R)}return r.$$set=Y=>{"task"in Y&&t(0,i=Y.task),"onSubmit"in Y&&t(27,s=Y.onSubmit),"statusOptions"in Y&&t(1,a=Y.statusOptions),"allTasks"in Y&&t(2,o=Y.allTasks)},r.$$.update=()=>{if(r.$$.dirty[0]&2048){e:t(15,n=Y=>be?Y:null)}if(r.$$.dirty[0]&8){e:t(28,E=b.description.trim()!=="")}if(r.$$.dirty[0]&268437488){e:t(13,Pe=q&&H&&te&&G&&E&&R&&S&&F)}},[i,a,o,b,R,S,F,q,te,G,H,be,_,Pe,j,n,l,c,d,p,m,y,$,D,B,I,Z,s,E,h,g,T,f,w,O,M,A,v,x,N,re,le,fe,Ne,Ce,Mt,an,Wr]}var ah=class extends hr{constructor(e){super(),Xr(this,e,LI,FI,Qr,{task:0,onSubmit:27,statusOptions:1,allTasks:2},null,[-1,-1])}},xw=ah;var Kn=class extends Rw.Modal{constructor({app:t,task:n,onSubmit:i,allTasks:s}){super(t);this.task=n,this.allTasks=s,this.onSubmit=a=>{a.length&&i(a),this.close()}}onOpen(){this.titleEl.setText("Create or edit Task"),this.modalEl.style.paddingBottom="0";let{contentEl:t}=this;this.contentEl.style.paddingBottom="0";let n=this.getKnownStatusesAndCurrentTaskStatusIfNotKnown();new xw({target:t,props:{task:this.task,statusOptions:n,onSubmit:this.onSubmit,allTasks:this.allTasks}})}getKnownStatusesAndCurrentTaskStatusIfNotKnown(){let t=De.getInstance().registeredStatuses;return De.getInstance().bySymbol(this.task.status.symbol)===ne.EMPTY&&t.push(this.task.status),t}onClose(){let{contentEl:t}=this;t.empty()}};function UI(){let{setCreatedDate:r}=X();return r?window.moment():null}function WI(r){let{setCreatedDate:e}=X();if(!e||r.createdDate!==null)return!1;let t=r.description==="",n=!_e.getInstance().isEmpty(),i=!_e.getInstance().includedIn(r.description);return t||n&&i}var Tl=({line:r,path:e})=>{var p,m;let t=ae.parseTaskSignifiers(r,ft.fromUnknownPosition(new at(e)),yt.fromPath(e)),n=UI();if(t!==null)return WI(t)?new ae(he(K({},t),{createdDate:n})):t;let i=r.match(J.nonTaskRegex);if(i===null)return console.error("Tasks: Cannot create task on line:",r),new ae({status:ne.TODO,description:"",taskLocation:ft.fromUnknownPosition(new at(e)),indentation:"",listMarker:"-",priority:"3",createdDate:n,startDate:null,scheduledDate:null,dueDate:null,doneDate:null,cancelledDate:null,recurrence:null,onCompletion:"",dependsOn:[],id:"",blockLink:"",tags:[],originalMarkdown:"",scheduledDateIsInferred:!1});let s=i[1],a=(p=i[2])!=null?p:"-",o=(m=i[4])!=null?m:" ",u=De.getInstance().bySymbolOrCreate(o),l=i[5],c=r.match(J.blockLinkRegex),d=c!==null?c[0]:"";return d!==""&&(l=l.replace(J.blockLinkRegex,"")),new ae({status:u,description:l,taskLocation:ft.fromUnknownPosition(new at(e)),indentation:s,listMarker:a,blockLink:d,priority:"3",createdDate:n,startDate:null,scheduledDate:null,dueDate:null,doneDate:null,cancelledDate:null,recurrence:null,onCompletion:"",tags:[],originalMarkdown:"",scheduledDateIsInferred:!1,id:"",dependsOn:[]})};var Mw=(r,e,t,n,i)=>{var p;if(r)return t instanceof oh.MarkdownView;if(!(t instanceof oh.MarkdownView))return;let s=(p=t.file)==null?void 0:p.path;if(s===void 0)return;let o=e.getCursor().line,u=e.getLine(o),l=Tl({line:u,path:s}),c=m=>{let y=yt.removeInferredStatusIfNeeded(l,m).map(_=>_.toFileLineString()).join(`
`);e.setLine(o,y)};new Kn({app:n,task:l,onSubmit:c,allTasks:i}).open()};var uh=require("obsidian");var Cw=(r,e,t)=>{var u;if(r)return t instanceof uh.MarkdownView;if(!(t instanceof uh.MarkdownView))return;let n=(u=t.file)==null?void 0:u.path;if(n===void 0)return;let i=e.getCursor(),s=i.line,a=e.getLine(s),o=lh(a,n);e.setLine(s,o.text),e.setCursor(qI(i,o))},lh=(r,e)=>{let t=ae.fromLine({line:r,taskLocation:ft.fromUnknownPosition(new at(e)),fallbackDate:null});if(t!==null){let n=t.toggleWithRecurrenceInUsersOrder().map(i=>i.toFileLineString());return{text:n.join(`
`),moveTo:{line:n.length-1}}}else{let n=r.match(J.taskRegex);if(n!==null){let i=n[3],a=De.getInstance().bySymbol(i).nextStatusSymbol;return{text:r.replace(J.taskRegex,`$1- [${a}] $4`)}}else if(J.listItemRegex.test(r)){let i=r.replace(J.listItemRegex,"$1$2 [ ]");return{text:i,moveTo:{ch:i.length}}}else{let i=r.replace(J.indentationRegex,"$1- ");return{text:i,moveTo:{ch:i.length}}}}},qI=(r,e)=>{var s;let t={line:0,ch:r.ch},n=K(K({},t),(s=e.moveTo)!=null?s:{}),i=e.text.split(`
`)[n.line].length;return{line:r.line+n.line,ch:Math.min(n.ch,i)}};var _l=class{get app(){return this.plugin.app}constructor({plugin:e}){this.plugin=e,e.addCommand({id:"edit-task",name:"Create or edit task",icon:"pencil",editorCheckCallback:(t,n,i)=>Mw(t,n,i,this.app,this.plugin.getTasks())}),e.addCommand({id:"toggle-done",name:"Toggle task done",icon:"check-in-circle",editorCheckCallback:Cw})}};var Qn=class{constructor(){this.hidePostponeButton=!1;this.hideTaskCount=!1;this.hideBacklinks=!1;this.hideEditButton=!1;this.hideUrgency=!0;this.shortMode=!1;this.explainQuery=!1}};function Fi(r,e){let t=`Error: ${r}.
The error message was:
    `,n="";return e instanceof Error?n+=e:n+="Unknown error",`${t}"${n}"`}var $I=Object.prototype.toString,As=Array.isArray||function(e){return $I.call(e)==="[object Array]"};function dh(r){return typeof r=="function"}function jI(r){return As(r)?"array":typeof r}function ch(r){return r.replace(/[\-\[\]{}()*+?.,\\\^$|#\s]/g,"\\$&")}function Aw(r,e){return r!=null&&typeof r=="object"&&e in r}function GI(r,e){return r!=null&&typeof r!="object"&&r.hasOwnProperty&&r.hasOwnProperty(e)}var YI=RegExp.prototype.test;function BI(r,e){return YI.call(r,e)}var HI=/\S/;function VI(r){return!BI(HI,r)}var zI={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;","/":"&#x2F;","`":"&#x60;","=":"&#x3D;"};function KI(r){return String(r).replace(/[&<>"'`=\/]/g,function(t){return zI[t]})}var QI=/\s*/,XI=/\s+/,Pw=/\s*=/,ZI=/\s*\}/,JI=/#|\^|\/|>|\{|&|=|!/;function eF(r,e){if(!r)return[];var t=!1,n=[],i=[],s=[],a=!1,o=!1,u="",l=0;function c(){if(a&&!o)for(;s.length;)delete i[s.pop()];else s=[];a=!1,o=!1}var d,p,m;function y(H){if(typeof H=="string"&&(H=H.split(XI,2)),!As(H)||H.length!==2)throw new Error("Invalid tags: "+H);d=new RegExp(ch(H[0])+"\\s*"),p=new RegExp("\\s*"+ch(H[1])),m=new RegExp("\\s*"+ch("}"+H[1]))}y(e||Tr.tags);for(var _=new Po(r),b,E,R,S,F,q;!_.eos();){if(b=_.pos,R=_.scanUntil(d),R)for(var te=0,G=R.length;te<G;++te)S=R.charAt(te),VI(S)?(s.push(i.length),u+=S):(o=!0,t=!0,u+=" "),i.push(["text",S,b,b+1]),b+=1,S===`
`&&(c(),u="",l=0,t=!1);if(!_.scan(d))break;if(a=!0,E=_.scan(JI)||"name",_.scan(QI),E==="="?(R=_.scanUntil(Pw),_.scan(Pw),_.scanUntil(p)):E==="{"?(R=_.scanUntil(m),_.scan(ZI),_.scanUntil(p),E="&"):R=_.scanUntil(p),!_.scan(p))throw new Error("Unclosed tag at "+_.pos);if(E==">"?F=[E,R,b,_.pos,u,l,t]:F=[E,R,b,_.pos],l++,i.push(F),E==="#"||E==="^")n.push(F);else if(E==="/"){if(q=n.pop(),!q)throw new Error('Unopened section "'+R+'" at '+b);if(q[1]!==R)throw new Error('Unclosed section "'+q[1]+'" at '+b)}else E==="name"||E==="{"||E==="&"?o=!0:E==="="&&y(R)}if(c(),q=n.pop(),q)throw new Error('Unclosed section "'+q[1]+'" at '+_.pos);return rF(tF(i))}function tF(r){for(var e=[],t,n,i=0,s=r.length;i<s;++i)t=r[i],t&&(t[0]==="text"&&n&&n[0]==="text"?(n[1]+=t[1],n[3]=t[3]):(e.push(t),n=t));return e}function rF(r){for(var e=[],t=e,n=[],i,s,a=0,o=r.length;a<o;++a)switch(i=r[a],i[0]){case"#":case"^":t.push(i),n.push(i),t=i[4]=[];break;case"/":s=n.pop(),s[5]=i[2],t=n.length>0?n[n.length-1][4]:e;break;default:t.push(i)}return e}function Po(r){this.string=r,this.tail=r,this.pos=0}Po.prototype.eos=function(){return this.tail===""};Po.prototype.scan=function(e){var t=this.tail.match(e);if(!t||t.index!==0)return"";var n=t[0];return this.tail=this.tail.substring(n.length),this.pos+=n.length,n};Po.prototype.scanUntil=function(e){var t=this.tail.search(e),n;switch(t){case-1:n=this.tail,this.tail="";break;case 0:n="";break;default:n=this.tail.substring(0,t),this.tail=this.tail.substring(t)}return this.pos+=n.length,n};function Cs(r,e){this.view=r,this.cache={".":this.view},this.parent=e}Cs.prototype.push=function(e){return new Cs(e,this)};Cs.prototype.lookup=function(e){var t=this.cache,n;if(t.hasOwnProperty(e))n=t[e];else{for(var i=this,s,a,o,u=!1;i;){if(e.indexOf(".")>0)for(s=i.view,a=e.split("."),o=0;s!=null&&o<a.length;)o===a.length-1&&(u=Aw(s,a[o])||GI(s,a[o])),s=s[a[o++]];else s=i.view[e],u=Aw(i.view,e);if(u){n=s;break}i=i.parent}t[e]=n}return dh(n)&&(n=n.call(this.view)),n};function xt(){this.templateCache={_cache:{},set:function(e,t){this._cache[e]=t},get:function(e){return this._cache[e]},clear:function(){this._cache={}}}}xt.prototype.clearCache=function(){typeof this.templateCache!="undefined"&&this.templateCache.clear()};xt.prototype.parse=function(e,t){var n=this.templateCache,i=e+":"+(t||Tr.tags).join(":"),s=typeof n!="undefined",a=s?n.get(i):void 0;return a==null&&(a=eF(e,t),s&&n.set(i,a)),a};xt.prototype.render=function(e,t,n,i){var s=this.getConfigTags(i),a=this.parse(e,s),o=t instanceof Cs?t:new Cs(t,void 0);return this.renderTokens(a,o,n,e,i)};xt.prototype.renderTokens=function(e,t,n,i,s){for(var a="",o,u,l,c=0,d=e.length;c<d;++c)l=void 0,o=e[c],u=o[0],u==="#"?l=this.renderSection(o,t,n,i,s):u==="^"?l=this.renderInverted(o,t,n,i,s):u===">"?l=this.renderPartial(o,t,n,s):u==="&"?l=this.unescapedValue(o,t):u==="name"?l=this.escapedValue(o,t,s):u==="text"&&(l=this.rawValue(o)),l!==void 0&&(a+=l);return a};xt.prototype.renderSection=function(e,t,n,i,s){var a=this,o="",u=t.lookup(e[1]);function l(p){return a.render(p,t,n,s)}if(!!u){if(As(u))for(var c=0,d=u.length;c<d;++c)o+=this.renderTokens(e[4],t.push(u[c]),n,i,s);else if(typeof u=="object"||typeof u=="string"||typeof u=="number")o+=this.renderTokens(e[4],t.push(u),n,i,s);else if(dh(u)){if(typeof i!="string")throw new Error("Cannot use higher-order sections without the original template");u=u.call(t.view,i.slice(e[3],e[5]),l),u!=null&&(o+=u)}else o+=this.renderTokens(e[4],t,n,i,s);return o}};xt.prototype.renderInverted=function(e,t,n,i,s){var a=t.lookup(e[1]);if(!a||As(a)&&a.length===0)return this.renderTokens(e[4],t,n,i,s)};xt.prototype.indentPartial=function(e,t,n){for(var i=t.replace(/[^ \t]/g,""),s=e.split(`
`),a=0;a<s.length;a++)s[a].length&&(a>0||!n)&&(s[a]=i+s[a]);return s.join(`
`)};xt.prototype.renderPartial=function(e,t,n,i){if(!!n){var s=this.getConfigTags(i),a=dh(n)?n(e[1]):n[e[1]];if(a!=null){var o=e[6],u=e[5],l=e[4],c=a;u==0&&l&&(c=this.indentPartial(a,l,o));var d=this.parse(c,s);return this.renderTokens(d,t,n,c,i)}}};xt.prototype.unescapedValue=function(e,t){var n=t.lookup(e[1]);if(n!=null)return n};xt.prototype.escapedValue=function(e,t,n){var i=this.getConfigEscape(n)||Tr.escape,s=t.lookup(e[1]);if(s!=null)return typeof s=="number"&&i===Tr.escape?String(s):i(s)};xt.prototype.rawValue=function(e){return e[1]};xt.prototype.getConfigTags=function(e){return As(e)?e:e&&typeof e=="object"?e.tags:void 0};xt.prototype.getConfigEscape=function(e){if(e&&typeof e=="object"&&!As(e))return e.escape};var Tr={name:"mustache.js",version:"4.2.0",tags:["{{","}}"],clearCache:void 0,escape:void 0,parse:void 0,render:void 0,Scanner:void 0,Context:void 0,Writer:void 0,set templateCache(r){Ao.templateCache=r},get templateCache(){return Ao.templateCache}},Ao=new xt;Tr.clearCache=function(){return Ao.clearCache()};Tr.parse=function(e,t){return Ao.parse(e,t)};Tr.render=function(e,t,n,i){if(typeof e!="string")throw new TypeError('Invalid template! Template should be a "string" but "'+jI(e)+'" was given as the first argument for mustache#render(template, view, partials)');return Ao.render(e,t,n,i)};Tr.escape=KI;Tr.Scanner=Po;Tr.Context=Cs;Tr.Writer=xt;var fh=Tr;var Uw=oa(Lw());function Ww(r,e){fh.escape=function(t){return t};try{return fh.render(r,(0,Uw.default)(e))}catch(t){let n="";throw t instanceof Error?n=`There was an error expanding one or more placeholders.

The error message was:
    ${t.message.replace(/ > /g,".").replace("Missing Mustache data property","Unknown property")}`:n="Unknown error expanding placeholders.",n+=`

The problem is in:
    ${r}`,Error(n)}}function qw(r){return mh(r,[])}function mh(r,e){return{query:{file:r,allTasks:e}}}var Ps=class{constructor(e=""){this.indentation=e}explainQuery(e){if(e.error!==void 0)return this.explainError(e);let t=[];return t.push(this.explainFilters(e)),t.push(this.explainGroups(e)),t.push(this.explainSorters(e)),t.push(this.explainQueryLimits(e)),t.push(this.explainDebugSettings()),t.filter(n=>n!=="").join(`
`)}explainError(e){let t="";return t+=`Query has an error:
`,t+=e.error+`
`,t}explainFilters(e){return e.filters.length===0?this.indent(`No filters supplied. All tasks will match the query.
`):e.filters.map(n=>n.explainFilterIndented(this.indentation)).join(`
`)}explainGroups(e){let t=e.grouping.length;if(t===0)return this.indent(`No grouping instructions supplied.
`);let n="";for(let i=0;i<t;i++)n+=this.indentation+e.grouping[i].instruction+`
`;return n}explainSorters(e){let t=e.sorting.length;if(t===0)return this.indent(`No sorting instructions supplied.
`);let n="";for(let i=0;i<t;i++)n+=this.indentation+e.sorting[i].instruction+`
`;return n}explainQueryLimits(e){function t(i){let s=`At most ${i} task`;return i!==1&&(s+="s"),s}let n=[];if(e.limit!==void 0){let i=t(e.limit)+`.
`;n.push(this.indent(i))}if(e.taskGroupLimit!==void 0){let i=t(e.taskGroupLimit)+` per group (if any "group by" options are supplied).
`;n.push(this.indent(i))}return n.join(`
`)}explainDebugSettings(){let e="",{debugSettings:t}=X();return t.ignoreSortInstructions&&(e+=this.indent(`NOTE: All sort instructions, including default sort order, are disabled, due to 'ignoreSortInstructions' setting.
`)),e}indent(e){return this.indentation+e}};var Se=class{constructor(e,t=[],n=""){this.description=e,this.symbol=n,this.children=t}static booleanAnd(e){return this.combineOrCreateExplanation("All of",e,"AND")}static booleanOr(e){return this.combineOrCreateExplanation("At least one of",e,"OR")}static booleanNot(e){return new Se("None of",e,"NOT")}static booleanXor(e){return new Se("Exactly one of",e,"XOR")}asString(e=""){if(this.children.length==0)return e+this.description;let t=e;this.symbol===""?t+=this.description:(t+=this.symbol,this.children.length>1&&(t+=` (${this.description})`),t+=":");let n=e+"  ";for(let i=0;i<this.children.length;i++)t+=`
${this.children[i].asString(n)}`;return t}static combineOrCreateExplanation(e,t,n){if(t.length===2){let i=t[0],s=t[1];if(i.symbol===n&&s.symbol==="")return i.children.push(s),i}return new Se(e,t,n)}};var Ns=class{matchesAnyOf(e){return e.some(t=>this.matches(t))}};var Is=class extends Ns{constructor(t){super();this.stringToFind=t}matches(t){return Is.stringIncludesCaseInsensitive(t,this.stringToFind)}static stringIncludesCaseInsensitive(t,n){return t.toLocaleLowerCase().includes(n.toLocaleLowerCase())}explanation(t){return new Se(t)}};var Xn=class extends Ns{constructor(t){super();this.regex=t}static validateAndConstruct(t){let n=/^\/(.+)\/([^/]*)$/,i=t.match(n);if(i!==null){let s=new RegExp(i[1],i[2]);return new Xn(s)}else return null}matches(t){return t.match(this.regex)!==null}static helpMessage(){return String.raw`See https://publish.obsidian.md/tasks/Queries/Regular+Expressions

Regular expressions must look like this:
    /pattern/
or this:
    /pattern/flags

Where:
- pattern: The 'regular expression' pattern to search for.
- flags:   Optional characters that modify the search.
           i => make the search case-insensitive
           u => add Unicode support

Examples:  /^Log/
           /^Log/i
           /File Name\.md/
           /waiting|waits|waited/i
           /\d\d:\d\d/

The following characters have special meaning in the pattern:
to find them literally, you must add a \ before them:
    [\^$.|?*+()

CAUTION! Regular expression (or 'regex') searching is a powerful
but advanced feature that requires thorough knowledge in order to
use successfully, and not miss intended search results.
`}explanation(t){let i=nF(t,"using regex: ",this.regexAsString());return new Se(i)}regexAsString(){let t=`'${this.regex.source}' with `;switch(this.regex.flags.length){case 0:t+="no flags";break;case 1:t+=`flag '${this.regex.flags}'`;break;default:t+=`flags '${this.regex.flags}'`;break}return t}};function nF(r,e,t){var o;let n=r.match(/\//);if(!n)return"Error explaining instruction. Could not find a slash character";let i=2,s=((o=n.index)!=null?o:i)-i;return`${e.padEnd(s)}${t}`}var Ir=class{constructor(e,t,n,i){this.instruction=e,this.property=t,this.comparator=Ir.maybeReverse(i,n)}static maybeReverse(e,t){return e?Ir.makeReversedComparator(t):t}static makeReversedComparator(e){return(t,n,i)=>e(t,n,i)*-1}};var Zn=class{constructor(e,t,n,i){this.instruction=e,this.property=t,this.grouper=n,this.reverse=i}};var Ee=class{canCreateFilterForLine(e){return Ee.lineMatchesFilter(this.filterRegExp(),e)}static lineMatchesFilter(e,t){return e?e.test(t):!1}static getMatch(e,t){return e?t.match(e):null}fieldNameSingular(){return this.fieldName()}fieldNameSingularEscaped(){return Hn(this.fieldNameSingular())}supportsSorting(){return!1}createSorterFromLine(e){if(!this.supportsSorting())return null;let t=Ee.getMatch(this.sorterRegExp(),e);if(t===null)return null;let n=!!t[1];return this.createSorter(n)}sorterRegExp(){if(!this.supportsSorting())throw Error(`sorterRegExp() unimplemented for ${this.fieldNameSingular()}`);return new RegExp(`^sort by ${this.fieldNameSingularEscaped()}( reverse)?`,"i")}sorterInstruction(e){let t=`sort by ${this.fieldNameSingular()}`;return e&&(t+=" reverse"),t}comparator(){throw Error(`comparator() unimplemented for ${this.fieldNameSingular()}`)}createSorter(e){return new Ir(this.sorterInstruction(e),this.fieldNameSingular(),this.comparator(),e)}createNormalSorter(){return this.createSorter(!1)}createReverseSorter(){return this.createSorter(!0)}supportsGrouping(){return!1}createGrouperFromLine(e){if(!this.supportsGrouping())return null;let t=Ee.getMatch(this.grouperRegExp(),e);if(t===null)return null;let n=!!t[1];return this.createGrouper(n)}grouperRegExp(){if(!this.supportsGrouping())throw Error(`grouperRegExp() unimplemented for ${this.fieldNameSingular()}`);return new RegExp(`^group by ${this.fieldNameSingularEscaped()}( reverse)?$`,"i")}grouperInstruction(e){let t=`group by ${this.fieldNameSingular()}`;return e&&(t+=" reverse"),t}grouper(){throw Error(`grouper() unimplemented for ${this.fieldNameSingular()}`)}createGrouper(e){return new Zn(this.grouperInstruction(e),this.fieldNameSingular(),this.grouper(),e)}createNormalGrouper(){return this.createGrouper(!1)}createReverseGrouper(){return this.createGrouper(!0)}};var An=class{constructor(e,t){this._rawInstruction=e,this._anyContinuationLinesRemoved=t.trim(),this._anyPlaceholdersExpanded=this._anyContinuationLinesRemoved}recordExpandedPlaceholders(e){this._anyPlaceholdersExpanded=e}get rawInstruction(){return this._rawInstruction}get anyContinuationLinesRemoved(){return this._anyContinuationLinesRemoved}get anyPlaceholdersExpanded(){return this._anyPlaceholdersExpanded}explainStatement(e){function t(a,o){o!==a&&(s+=` =>
${e}${o}`)}let n=this._rawInstruction.trim(),i=n.split(`
`).join(`
`+e),s=`${e}${i}`;return this._rawInstruction.includes(`
`)&&(s+=`
`+e),t(n,this._anyContinuationLinesRemoved),t(this._anyContinuationLinesRemoved,this._anyPlaceholdersExpanded),s}allLinesIdentical(){return this._rawInstruction===this._anyContinuationLinesRemoved&&this._rawInstruction===this._anyPlaceholdersExpanded}};var bt=class{constructor(e,t,n){this._statement=new An(e,e),this.explanation=n,this.filterFunction=t}get statement(){return this._statement}setStatement(e){this._statement=e}get instruction(){return this._statement.anyPlaceholdersExpanded}explainFilterIndented(e){let t=this._statement.explainStatement(e);return this.onlyNeedsOneLineExplanation()?`${t}
`:`${t} =>
${this.explanation.asString(e+"  ")}
`}simulateExplainFilter(){return this.onlyNeedsOneLineExplanation()?this.explanation:new Se(this.instruction+" =>",[this.explanation])}onlyNeedsOneLineExplanation(){return this.explanation.asString("")===this.instruction}};var Jr=class{constructor(e){this.instruction=e}get queryComponent(){return this._queryComponent}set queryComponent(e){this._queryComponent=e}get error(){return this._error}set error(e){this._error=e}isValid(){return this._queryComponent!==void 0}static fromObject(e,t){let n=new Jr(e);return n._queryComponent=t,n}static fromError(e,t){let n=new Jr(e);return n._error=t,n}};var ie=class{constructor(e){this.object=e}get instruction(){return this.object.instruction}get filter(){return this.object.queryComponent}isValid(){return this.object.isValid()}get error(){return this.object.error}get filterFunction(){if(this.filter)return this.filter.filterFunction}static fromFilter(e){return new ie(Jr.fromObject(e.instruction,e))}static fromError(e,t){return new ie(Jr.fromError(e,t))}};var Ie=class extends Ee{createFilterOrErrorMessage(e){let t=Ee.getMatch(this.filterRegExp(),e);if(t===null)return ie.fromError(e,`do not understand query filter (${this.fieldName()})`);let n=t[1].toLowerCase(),i=t[2],s=null;if(n.includes("include"))s=new Is(i);else if(n.includes("regex")){try{s=Xn.validateAndConstruct(i)}catch(u){let l=Fi("Parsing regular expression",u)+`

${Xn.helpMessage()}`;return ie.fromError(e,l)}if(s===null)return ie.fromError(e,`Invalid instruction: '${e}'

${Xn.helpMessage()}`)}if(s===null)return ie.fromError(e,`do not understand query filter (${this.fieldName()})`);let a=n.match(/not/)!==null,o=new bt(e,this.getFilter(s,a),s.explanation(e));return ie.fromFilter(o)}fieldPattern(){return this.fieldNameSingularEscaped()}filterOperatorPattern(){return"includes|does not include|regex matches|regex does not match"}filterRegExp(){return new RegExp(`^(?:${this.fieldPattern()}) (${this.filterOperatorPattern()}) (.*)`,"i")}getFilter(e,t){return n=>{let i=e.matches(this.value(n));return t?!i:i}}comparator(){return(e,t)=>this.value(e).localeCompare(this.value(t),void 0,{numeric:!0})}grouper(){return e=>[this.value(e)]}static escapeMarkdownCharacters(e){return e.replace(/\\/g,"\\\\").replace(/_/g,"\\_")}};var Li=class extends Ie{fieldName(){return"description"}value(e){return _e.getInstance().removeAsSubstringFrom(e.description)}supportsSorting(){return!0}comparator(){return(e,t)=>{let n=Li.cleanDescription(e.description),i=Li.cleanDescription(t.description);return n.localeCompare(i,void 0,{numeric:!0})}}static cleanDescription(e){e=_e.getInstance().removeAsSubstringFrom(e);let t=/^\[\[?([^\]]*)]]?/,n=e.match(t);if(n!==null){let i=n[1];e=i.substring(i.indexOf("|")+1)+e.replace(t,"")}return e=this.replaceFormatting(e,/^\*\*([^*]+)\*\*/),e=this.replaceFormatting(e,/^\*([^*]+)\*/),e=this.replaceFormatting(e,/^==([^=]+)==/),e=this.replaceFormatting(e,/^__([^_]+)__/),e=this.replaceFormatting(e,/^_([^_]+)_/),e}static replaceFormatting(e,t){let n=e.match(t);return n!==null&&(e=n[1]+e.replace(t,"")),e}};var vl=class{findUnexpandedDateText(e){let t=["<%","YYYY-MM-DD"];for(let n of t)if(e.includes(n))return this.unexpandedDateTextMessage(n);return null}unexpandedDateTextMessage(e){return`Instruction contains unexpanded template text: "${e}" - and cannot be interpreted.

Possible causes:
- The query is an a template file, and is not intended to be searched.
- A command such as "Replace templates in the active file" needs to be run.
- The core "Daily notes" plugin is in use, and the template contained
  date calculations that it does not support.
- Some sample template text was accidentally pasted in to a tasks query,
  instead of in to a template file.

See: https://publish.obsidian.md/tasks/Advanced/Instruction+contains+unexpanded+template+text
`}};var wl=class{constructor(e,t){this._instruction=e,this._filter=t}canCreateFilterForLine(e){return e.toLocaleLowerCase()===this._instruction.toLocaleLowerCase()}createFilterOrErrorMessage(e){return this.canCreateFilterForLine(e)?ie.fromFilter(new bt(e,this._filter,new Se(e))):ie.fromError(e,`do not understand filter: ${e}`)}};var qt=class{constructor(){this._filters=[]}add(e,t){this._filters.push(new wl(e,t))}canCreateFilterForLine(e){return this._filters.some(t=>t.canCreateFilterForLine(e))}createFilterOrErrorMessage(e){for(let t of this._filters){let n=t.createFilterOrErrorMessage(e);if(n.isValid())return n}return ie.fromError(e,`do not understand filter: ${e}`)}};var st=class extends Ee{constructor(t=null){super();t!==null?this.filterInstructions=t:(this.filterInstructions=new qt,this.filterInstructions.add(`has ${this.fieldName()} date`,n=>this.date(n)!==null),this.filterInstructions.add(`no ${this.fieldName()} date`,n=>this.date(n)===null),this.filterInstructions.add(`${this.fieldName()} date is invalid`,n=>{let i=this.date(n);return i!==null&&!i.isValid()}))}canCreateFilterForLine(t){return this.filterInstructions.canCreateFilterForLine(t)?!0:super.canCreateFilterForLine(t)}createFilterOrErrorMessage(t){var p;let n=this.checkForUnexpandedTemplateText(t);if(n)return ie.fromError(t,n);let i=this.filterInstructions.createFilterOrErrorMessage(t);if(i.isValid())return i;let s=Ee.getMatch(this.filterRegExp(),t);if(s===null)return ie.fromError(t,"do not understand query filter ("+this.fieldName()+" date)");let a=s[1],o=(p=s[2])==null?void 0:p.toLowerCase(),u=s[3],l=Ht.parseDateRange(u);if(!l.isValid()){let m=Ht.parseDate(a);m.isValid()&&(l=new kt(m,m))}if(!l.isValid())return ie.fromError(t,"do not understand "+this.fieldName()+" date");let c=this.buildFilterFunction(o,l),d=st.buildExplanation(this.fieldNameForExplanation(),o,this.filterResultIfFieldMissing(),l);return ie.fromFilter(new bt(t,c,d))}buildFilterFunction(t,n){let i;switch(t){case"before":i=s=>s?s.isBefore(n.start):this.filterResultIfFieldMissing();break;case"after":i=s=>s?s.isAfter(n.end):this.filterResultIfFieldMissing();break;case"on or before":case"in or before":i=s=>s?s.isSameOrBefore(n.end):this.filterResultIfFieldMissing();break;case"on or after":case"in or after":i=s=>s?s.isSameOrAfter(n.start):this.filterResultIfFieldMissing();break;default:i=s=>s?s.isSameOrAfter(n.start)&&s.isSameOrBefore(n.end):this.filterResultIfFieldMissing()}return this.getFilter(i)}getFilter(t){return n=>t(this.date(n))}filterRegExp(){return new RegExp(`^${this.fieldNameForFilterInstruction()} (((?:on|in) or before|before|(?:on|in) or after|after|on|in)? ?(.*))`,"i")}fieldNameForFilterInstruction(){return this.fieldName()}static buildExplanation(t,n,i,s){let a=n,o="YYYY-MM-DD (dddd Do MMMM YYYY)",u;switch(n){case"before":case"on or after":u=s.start.format(o);break;case"after":case"on or before":u=s.end.format(o);break;case"in or before":a="on or before",u=s.end.format(o);break;case"in or after":a="on or after",u=s.start.format(o);break;default:if(!s.start.isSame(s.end)){let c=`${t} date is between:`,d=[new Se(`${s.start.format(o)} and`),new Se(`${s.end.format(o)} inclusive`)];return i&&d.push(new Se(`OR no ${t} date`)),new Se(c,d)}a="on",u=s.start.format(o);break}let l=`${t} date is ${a} ${u}`;return i&&(l+=` OR no ${t} date`),new Se(l)}fieldNameForExplanation(){return this.fieldName()}supportsSorting(){return!0}comparator(){return(t,n)=>dr(this.date(t),this.date(n))}supportsGrouping(){return!0}grouper(){return t=>{let n=this.date(t);return n===null?["No "+this.fieldName()+" date"]:n.isValid()?[n.format("YYYY-MM-DD dddd")]:["%%0%% Invalid "+this.fieldName()+" date"]}}checkForUnexpandedTemplateText(t){return new vl().findUnexpandedDateText(t)}};var kl=class extends st{fieldName(){return"created"}date(e){return e.createdDate}filterResultIfFieldMissing(){return!1}};var El=class extends st{fieldName(){return"done"}date(e){return e.doneDate}filterResultIfFieldMissing(){return!1}};var Fs=class extends st{fieldName(){return"due"}date(e){return e.dueDate}filterResultIfFieldMissing(){return!1}};var en=class extends Ee{constructor(){super(...arguments);this._filters=new qt}canCreateFilterForLine(t){return this._filters.canCreateFilterForLine(t)}createFilterOrErrorMessage(t){return this._filters.createFilterOrErrorMessage(t)}filterRegExp(){return null}};var Sl=class extends en{constructor(){super(),this._filters.add("exclude sub-items",e=>{if(e.indentation==="")return!0;let t=e.indentation.lastIndexOf(">");return t===-1?!1:/^ ?$/.test(e.indentation.slice(t+1))})}fieldName(){return"exclude"}};var No=class extends Jr{};function hh(r,e){let t=r.map(([n])=>n);try{let n=e.includes("return")?e:`return ${e}`,i=e&&new Function(...t,n);return i instanceof Function?No.fromObject(e,i):No.fromError(e,"Error parsing group function")}catch(n){return No.fromError(e,Fi(`Failed parsing expression "${e}"`,n))}}function gh(r,e){let t=e.map(([n,i])=>i);return r(...t)}function yh(r,e,t){try{return gh(r,e)}catch(n){return Fi(`Failed calculating expression "${t}"`,n)}}function Ol(r,e){return[["task",r],["query",e?e.query:null]]}function $w(r,e,t){let n=Ol(r,t||null),i=hh(n,e);return i.error?i.error:yh(i.queryComponent,n,e)}var Io=class{constructor(e){this.line=e,this.functionOrError=hh(Ol(null,null),e)}isValid(){return this.functionOrError.isValid()}get parseError(){return this.functionOrError.error}evaluate(e,t){if(!this.isValid())throw Error(`Error: Cannot evaluate an expression which is not valid: "${this.line}" gave error: "${this.parseError}"`);return gh(this.functionOrError.queryComponent,Ol(e,t||null))}evaluateOrCatch(e,t){return this.isValid()?yh(this.functionOrError.queryComponent,Ol(e,t),this.line):`Error: Cannot evaluate an expression which is not valid: "${this.line}" gave error: "${this.parseError}"`}};function bh(r){if(r===null)return"null";let e=typeof r;return e==="object"?r.constructor.name:e}var Dl=class extends Ee{createFilterOrErrorMessage(e){let t=Ee.getMatch(this.filterRegExp(),e);if(t===null)return ie.fromError(e,"Unable to parse line");let n=t[1],i=new Io(n);return i.isValid()?ie.fromFilter(new bt(e,iF(i),new Se(e))):ie.fromError(e,i.parseError)}fieldName(){return"function"}filterRegExp(){return new RegExp(`^filter by ${this.fieldNameSingularEscaped()} (.*)`,"i")}supportsSorting(){return!0}sorterRegExp(){return new RegExp(`^sort by ${this.fieldNameSingularEscaped()}( reverse)? (.*)`,"i")}createSorterFromLine(e){let t=Ee.getMatch(this.sorterRegExp(),e);if(t===null)return null;let n=!!t[1],i=t[2],s=new Io(i);if(!s.isValid())throw new Error(s.parseError);let a=(o,u,l)=>{try{let c=l.queryContext(),d=this.validateTaskSortKey(s.evaluate(o,c)),p=this.validateTaskSortKey(s.evaluate(u,c));return this.compareTaskSortKeys(d,p)}catch(c){throw c instanceof Error&&(c.message+=`: while evaluating instruction '${e}'`),c}};return new Ir(e,this.fieldNameSingular(),a,n)}validateTaskSortKey(e){function t(n){throw new Error(`"${n}" is not a valid sort key`)}return e===void 0&&t("undefined"),Number.isNaN(e)&&t("NaN (Not a Number)"),Array.isArray(e)&&t("array"),e}compareTaskSortKeys(e,t){let n=bh(e),i=bh(t),s=this.compareTaskSortKeysIfOptionalMoment(e,t,n,i);if(s!==void 0)return s;let a=this.compareTaskSortKeysIfEitherIsNull(e,t);if(a!==void 0)return a;if(n!==i)throw new Error(`Unable to compare two different sort key types '${n}' and '${i}' order`);if(n==="string")return e.localeCompare(t,void 0,{numeric:!0});if(n==="TasksDate")return dr(e.moment,t.moment);if(n==="boolean")return Number(t)-Number(e);let o=Number(e)-Number(t);if(isNaN(o))throw new Error(`Unable to determine sort order for sort key types '${n}' and '${i}'`);return o}compareTaskSortKeysIfOptionalMoment(e,t,n,i){let s=n==="Moment",a=i==="Moment";if(s&&a||s&&t===null||a&&e===null)return dr(e,t)}compareTaskSortKeysIfEitherIsNull(e,t){if(e===null&&t===null)return 0;if(e===null&&t!==null)return-1;if(e!==null&&t===null)return 1}supportsGrouping(){return!0}createGrouperFromLine(e){let t=Ee.getMatch(this.grouperRegExp(),e);if(t===null)return null;let n=!!t[1],i=t[2];return new Zn(e,"function",aF(i),n)}grouperRegExp(){return new RegExp(`^group by ${this.fieldNameSingularEscaped()}( reverse)? (.*)`,"i")}grouper(){throw Error("grouper() function not valid for FunctionField. Use createGrouperFromLine() instead.")}};function iF(r){return(e,t)=>{let n=t.queryContext();return sF(r,e,n)}}function sF(r,e,t){let n=r.evaluate(e,t);if(typeof n=="boolean")return n;throw Error(`filtering function must return true or false. This returned "${n}".`)}function aF(r){return(e,t)=>{let n=t.queryContext();return oF(e,r,n)}}function oF(r,e,t){try{let n=$w(r,e,t);return Array.isArray(n)?n.map(s=>s.toString()):n===null?[]:[n.toString()]}catch(n){let i=`Error: Failed calculating expression "${e}". The error message was: `;return n instanceof Error?[i+n.message]:[i+"Unknown error"]}}var xl=class extends Ie{fieldName(){return"heading"}value(e){return e.precedingHeader?e.precedingHeader:""}supportsSorting(){return!0}supportsGrouping(){return!0}grouper(){return e=>e.precedingHeader===null||e.precedingHeader.length===0?["(No heading)"]:[e.precedingHeader]}};var Ls=class extends Ie{fieldName(){return"path"}value(e){return e.path}supportsSorting(){return!0}supportsGrouping(){return!0}grouper(){return e=>[Ie.escapeMarkdownCharacters(e.path.replace(".md",""))]}};var Th=class extends Ee{createFilterOrErrorMessage(e){var n;let t=Ee.getMatch(this.filterRegExp(),e);if(t!==null){let i=t[5],s=null;switch(i.toLowerCase()){case"lowest":s="5";break;case"low":s="4";break;case"none":s="3";break;case"medium":s="2";break;case"high":s="1";break;case"highest":s="0";break}if(s===null)return ie.fromError(e,"do not understand priority");let a=e,o;switch((n=t[3])==null?void 0:n.toLowerCase()){case"above":o=u=>u.priority.localeCompare(s)<0;break;case"below":o=u=>u.priority.localeCompare(s)>0;break;case"not":o=u=>u.priority!==s;break;default:o=u=>u.priority===s,a=`${this.fieldName()} is ${i}`}return ie.fromFilter(new bt(e,o,new Se(a)))}else return ie.fromError(e,"do not understand query filter (priority)")}fieldName(){return"priority"}filterRegExp(){return Th.priorityRegexp}supportsSorting(){return!0}comparator(){return(e,t)=>e.priority.localeCompare(t.priority)}supportsGrouping(){return!0}grouper(){return e=>[e.priorityNameGroupText]}},Ui=Th;Ui.priorityRegexp=/^priority(\s+is)?(\s+(above|below|not))?(\s+(lowest|low|none|medium|high|highest))$/i;var Rl=class extends st{fieldName(){return"scheduled"}date(e){return e.scheduledDate}filterResultIfFieldMissing(){return!1}};var Ml=class extends st{fieldName(){return"start"}fieldNameForFilterInstruction(){return"starts"}date(e){return e.startDate}filterResultIfFieldMissing(){return!0}};var Cl=class extends st{constructor(){let e=new qt;e.add("has happens date",t=>this.dates(t).some(n=>n!==null)),e.add("no happens date",t=>!this.dates(t).some(n=>n!==null)),super(e)}fieldName(){return"happens"}fieldNameForExplanation(){return"due, start or scheduled"}date(e){return this.earliestDate(e)}dates(e){return e.happensDates}earliestDate(e){return e.happens.moment}filterResultIfFieldMissing(){return!1}getFilter(e){return t=>this.dates(t).some(n=>e(n))}};var Al=class extends en{constructor(){super(),this._filters.add("is recurring",e=>e.recurrence!==null),this._filters.add("is not recurring",e=>e.recurrence===null)}fieldName(){return"recurring"}supportsSorting(){return!0}comparator(){return(e,t)=>e.recurrence!==null&&t.recurrence===null?-1:e.recurrence===null&&t.recurrence!==null?1:0}supportsGrouping(){return!0}grouper(){return e=>e.recurrence!==null?["Recurring"]:["Not Recurring"]}};var Jn=class extends en{constructor(){super(),this._filters.add("done",e=>e.isDone),this._filters.add("not done",e=>!e.isDone)}fieldName(){return"status"}supportsSorting(){return!0}comparator(){return(e,t)=>{let n=Jn.oldStatusName(e),i=Jn.oldStatusName(t);return n<i?1:n>i?-1:0}}static oldStatusName(e){return e.status.symbol===" "?"Todo":"Done"}supportsGrouping(){return!0}grouper(){return e=>[Jn.oldStatusName(e)]}};var Pl=class extends Ie{fieldNamePlural(){return this.fieldNameSingular()+"s"}fieldName(){return`${this.fieldNameSingular()}/${this.fieldNamePlural()}`}fieldPattern(){return`${this.fieldNameSingular()}|${this.fieldNamePlural()}`}filterOperatorPattern(){return`${super.filterOperatorPattern()}|include|do not include`}value(e){return this.values(e).join(", ")}getFilter(e,t){return n=>{let i=e.matchesAnyOf(this.values(n));return t?!i:i}}createGrouper(e){return new Zn(this.grouperInstruction(e),this.fieldNamePlural(),this.grouper(),e)}grouperRegExp(){if(!this.supportsGrouping())throw Error(`grouperRegExp() unimplemented for ${this.fieldNameSingular()}`);return new RegExp(`^group by ${this.fieldNamePlural()}( reverse)?$`,"i")}grouperInstruction(e){let t=`group by ${this.fieldNamePlural()}`;return e&&(t+=" reverse"),t}};var Wi=class extends Pl{constructor(){super();this.filterInstructions=new qt,this.filterInstructions.add(`has ${this.fieldNameSingular()}`,t=>this.values(t).length>0),this.filterInstructions.add(`has ${this.fieldNamePlural()}`,t=>this.values(t).length>0),this.filterInstructions.add(`no ${this.fieldNameSingular()}`,t=>this.values(t).length===0),this.filterInstructions.add(`no ${this.fieldNamePlural()}`,t=>this.values(t).length===0)}createFilterOrErrorMessage(t){let n=this.filterInstructions.createFilterOrErrorMessage(t);return n.isValid()?n:super.createFilterOrErrorMessage(t)}canCreateFilterForLine(t){return this.filterInstructions.canCreateFilterForLine(t)?!0:super.canCreateFilterForLine(t)}fieldNameSingular(){return"tag"}values(t){return t.tags}supportsSorting(){return!0}createSorterFromLine(t){let n=t.match(this.sorterRegExp());if(n===null)return null;let i=!!n[1],s=isNaN(+n[2])?1:+n[2],a=Wi.makeCompareByTagComparator(s);return new Ir(t,this.fieldNameSingular(),a,i)}sorterRegExp(){return/^sort by tag( reverse)?[\s]*(\d+)?/i}comparator(){return Wi.makeCompareByTagComparator(1)}static makeCompareByTagComparator(t){return(n,i)=>{if(n.tags.length===0&&i.tags.length===0)return 0;if(n.tags.length===0)return 1;if(i.tags.length===0)return-1;let s=t-1;if(n.tags.length<t&&i.tags.length>=t)return 1;if(i.tags.length<t&&n.tags.length>=t)return-1;if(n.tags.length<t&&i.tags.length<t)return 0;let a=n.tags[s],o=i.tags[s];return a.localeCompare(o,void 0,{numeric:!0})}}supportsGrouping(){return!0}grouper(){return t=>t.tags.length==0?["(No tags)"]:t.tags}};var tk=oa(ek());function ei(r){return new RegExp("["+Hn(r)+"]").source}var Rh=[["(",")"],["[","]"],["{","}"],['"','"']],Pn=class{constructor(e,t,n){this.openFilterChars=e,this.closeFilterChars=t,this.openAndCloseFilterChars=n,this.openFilter=ei(this.openFilterChars),this.closeFilter=ei(this.closeFilterChars)}static allSupportedDelimiters(){let e="",t="",n="";for(let[i,s]of Rh)e+=i,t+=s,n+=Pn.openAndClosing(i,s);return new Pn(e,t,n)}static fromInstructionLine(e){let t=e.trim(),i=/^[A-Z ]*\s*(.*)/.exec(t);if(i){let a=i[1],o=a[0],u=a.slice(-1);for(let[l,c]of Rh)if(o===l&&u===c){let d=this.openAndClosing(l,c);return new Pn(l,c,d)}}let s="All filters in a Boolean instruction must be inside one of these pairs of delimiter characters: "+Rh.map(([a,o])=>a+"..."+o).join(" or ")+". Combinations of those delimiters are no longer supported.";throw new Error(s)}static openAndClosing(e,t){let n=e;return t!=e&&(n+=t),n}};var ti=class{static preprocessExpression(e,t){let n=ti.splitLine(e,t);return ti.getFiltersAndSimplifiedLine(n,t)}static splitLine(e,t){let n=new RegExp("("+t.closeFilter+"\\s*(?:AND|OR|AND +NOT|OR +NOT|XOR)\\s*"+t.openFilter+")"),i=e.split(n),s=new RegExp("(NOT\\s*"+t.openFilter+")"),a=i.flatMap(l=>l.split(s)).filter(l=>l!==""),o=new RegExp("(^"+ei(t.openFilterChars+" ")+"*)"),u=new RegExp("("+ei(t.closeFilterChars+" ")+"*$)");return a.flatMap(l=>l.split(o)).flatMap(l=>l.split(u)).filter(l=>l!=="")}static getFiltersAndSimplifiedLine(e,t){let n="",i=1,s={};e.forEach(l=>{if(!ti.isAFilter(l,t))n+=`${l}`;else{let c=`f${i}`;s[c]=l,n+=c,i++}});let a=new RegExp(`(${t.closeFilter})([A-Z])`,"g");n=n.replace(a,"$1 $2");let o=new RegExp(`([A-Z])(${t.openFilter})`,"g");n=n.replace(o,"$1 $2");let u=t.openFilterChars;if(u!='"'&&u!="("){let l=new RegExp(ei(u),"g");n=n.replace(l,"(");let c=t.closeFilterChars,d=new RegExp(ei(c),"g");n=n.replace(d,")")}return{simplifiedLine:n,filters:s}}static isAFilter(e,t){let n=new RegExp("^"+ei(" "+t.openAndCloseFilterChars)+"+$"),i=new RegExp("^ *"+t.closeFilter+" *(AND|OR|XOR) *"+t.openFilter+" *$"),s=new RegExp("^(AND|OR|XOR|NOT) *"+t.openFilter+"$"),a=new RegExp("^"+t.closeFilter+" *(AND|OR|XOR)$");return![n,i,s,a,/^(AND|OR|XOR|NOT)$/].some(u=>RegExp(u).exec(e))}};var Ul=class extends Ee{constructor(){super();this.supportedOperators=["AND","OR","XOR","NOT"];this.subFields={};let t=Pn.allSupportedDelimiters();this.basicBooleanRegexp=new RegExp("(.*(AND|OR|XOR|NOT)\\s*"+t.openFilter+".*|"+t.openFilter+".+"+t.closeFilter+")","g")}filterRegExp(){return this.basicBooleanRegexp}createFilterOrErrorMessage(t){return this.parseLine(t)}fieldName(){return"boolean query"}parseLine(t){if(t.length===0)return ie.fromError(t,"empty line");let n;try{n=Pn.fromInstructionLine(t)}catch(o){let u=o instanceof Error?o.message:"unknown error type";return ie.fromError(t,this.helpMessageFromSimpleError(t,u))}let i=ti.preprocessExpression(t,n),s=i.simplifiedLine,a=i.filters;try{let o=(0,tk.parse)(s);for(let c of o)if(c.name==="IDENTIFIER"&&c.value){let d=c.value.trim(),p=a[d];if(c.value=p,!(p in this.subFields)){let m=Lo(p);if(m===null)return this.helpMessage(t,`couldn't parse sub-expression '${p}'`,i);if(m.error)return this.helpMessage(t,`couldn't parse sub-expression '${p}': ${m.error}`,i);m.filter&&(this.subFields[p]=m.filter)}}else if(c.name==="OPERATOR"){if(c.value==null)return this.helpMessage(t,"empty operator in boolean query",i);if(!this.supportedOperators.includes(c.value))return this.helpMessage(t,`unknown boolean operator '${c.value}'`,i)}let u=(c,d)=>this.filterTaskWithParsedQuery(c,o,d),l=this.constructExplanation(o);return ie.fromFilter(new bt(t,u,l))}catch(o){let u=o instanceof Error?o.message:"unknown error type";return this.helpMessage(t,`malformed boolean query -- ${u} (check the documentation for guidelines)`,i)}}filterTaskWithParsedQuery(t,n,i){let s=u=>u==="true",a=u=>u?"true":"false",o=[];for(let u of n)if(u.name==="IDENTIFIER"){if(u.value==null)throw Error("null token value");let c=this.subFields[u.value.trim()].filterFunction(t,i);o.push(a(c))}else if(u.name==="OPERATOR")if(u.value==="NOT"){let l=s(o.pop());o.push(a(!l))}else if(u.value==="OR"){let l=s(o.pop()),c=s(o.pop());o.push(a(l||c))}else if(u.value==="AND"){let l=s(o.pop()),c=s(o.pop());o.push(a(l&&c))}else if(u.value==="XOR"){let l=s(o.pop()),c=s(o.pop());o.push(a(l&&!c||!l&&c))}else throw Error("Unsupported operator: "+u.value);else throw Error("Unsupported token type: "+u);return s(o[0])}constructExplanation(t){let n=[];for(let i of t)if(i.name==="IDENTIFIER")this.explainExpression(i,n);else if(i.name==="OPERATOR")this.explainOperator(i,n);else throw Error("Unsupported token type: "+i.name);return n[0]}explainExpression(t,n){if(t.value==null)throw Error("null token value");let i=this.subFields[t.value.trim()],s=this.simulateExplainFilter(i);n.push(s)}simulateExplainFilter(t){return t.simulateExplainFilter()}explainOperator(t,n){if(t.value==="NOT"){let i=n.pop();n.push(Se.booleanNot([i]))}else if(t.value==="OR"){let i=n.pop(),s=n.pop();n.push(Se.booleanOr([s,i]))}else if(t.value==="AND"){let i=n.pop(),s=n.pop();n.push(Se.booleanAnd([s,i]))}else if(t.value==="XOR"){let i=n.pop(),s=n.pop();n.push(Se.booleanXor([s,i]))}else throw Error("Unsupported operator: "+t.value)}helpMessage(t,n,i){let s=i.filters,a=this.stringifySubExpressionsForErrorMessage(s),u=`${this.helpMessageFromSimpleError(t,n)}

The instruction was converted to the following simplified line:
    ${i.simplifiedLine}

Where the sub-expressions in the simplified line are:
${a}

For help, see:
    https://publish.obsidian.md/tasks/Queries/Combining+Filters
`;return ie.fromError(t,u)}stringifySubExpressionsForErrorMessage(t){return Object.entries(t).map(([n,i])=>`    '${n}': '${i}'
        => ${this.stringifySubExpressionStatus(i)}`).join(`
`)}stringifySubExpressionStatus(t){let n=Lo(t);return n?n.error?`ERROR:
           ${n.error.split(`
`).map(s=>s.trim()).join(`
           `)}`:"OK":`ERROR:
           do not understand query`}helpMessageFromSimpleError(t,n){return`Could not interpret the following instruction as a Boolean combination:
    ${t}

The error message is:
    ${n}`}};var Wl=class extends Ie{fieldName(){return"filename"}value(e){let t=e.filename;return t===null?"":t+".md"}supportsSorting(){return!0}supportsGrouping(){return!0}grouper(){return e=>{let t=e.filename;return t===null?["Unknown Location"]:["[["+t+"]]"]}}};var Bs=class extends Ee{canCreateFilterForLine(e){return!1}createFilterOrErrorMessage(e){return ie.fromError(e,"Filtering by urgency is not yet supported")}fieldName(){return"urgency"}filterRegExp(){throw Error(`filterRegExp() unimplemented for ${this.fieldName()}`)}supportsSorting(){return!0}comparator(){return(e,t)=>t.urgency-e.urgency}supportsGrouping(){return!0}grouper(){return e=>[`${e.urgency.toFixed(2)}`]}createGrouper(e){return super.createGrouper(!e)}grouperInstruction(e){return super.grouperInstruction(!e)}};var ql=class extends Ie{constructor(){super()}fieldName(){return"status.name"}value(e){return e.status.name}supportsSorting(){return!0}supportsGrouping(){return!0}};var sn=class extends Ee{canCreateFilterForLine(e){let t=new RegExp(`^(?:${this.fieldNameSingularEscaped()})`,"i");return Ee.lineMatchesFilter(t,e)}createFilterOrErrorMessage(e){let t=Ee.getMatch(this.filterRegExp(),e);if(t===null)return this.helpMessage(e);let n=t[1].toLowerCase(),i=t[2],s=Nt[i.toUpperCase()];if(!s)return this.helpMessage(e);let a;switch(n){case"is":a=o=>o.status.type===s;break;case"is not":a=o=>o.status.type!==s;break;default:return this.helpMessage(e)}return ie.fromFilter(new bt(e,a,new Se(e)))}filterRegExp(){return new RegExp(`^(?:${this.fieldNameSingularEscaped()}) (is|is not) ([^ ]+)$`,"i")}helpMessage(e){let t=Object.values(Nt).filter(i=>i!=="EMPTY").join(" "),n=`Invalid ${this.fieldNameSingular()} instruction: '${e}'.
    Allowed options: 'is' and 'is not' (without quotes).
    Allowed values:  ${t}
                     Note: values are case-insensitive,
                           so 'in_progress' works too, for example.
    Example:         ${this.fieldNameSingular()} is not NON_TASK`;return ie.fromError(e,n)}fieldName(){return"status.type"}value(e){return e.status.type}supportsSorting(){return!0}comparator(){return(e,t)=>{let n=sn.groupName(e),i=sn.groupName(t);return n.localeCompare(i,void 0,{numeric:!0})}}supportsGrouping(){return!0}grouper(){return e=>[sn.groupName(e)]}static groupName(e){return e.status.typeGroupText}};var $l=class extends Ie{fieldName(){return"recurrence"}value(e){return e.recurrence!==null?e.recurrence.toText():""}supportsGrouping(){return!0}grouper(){return e=>e.recurrence!==null?[e.recurrence.toText()]:["None"]}};var jl=class extends Ie{fieldName(){return"folder"}value(e){return e.file.folder}supportsGrouping(){return!0}grouper(){return e=>[Ie.escapeMarkdownCharacters(this.value(e))]}};var Gl=class extends Ie{fieldName(){return"root"}value(e){return e.file.root}supportsGrouping(){return!0}grouper(){return e=>[Ie.escapeMarkdownCharacters(this.value(e))]}};var Yl=class extends Ie{fieldName(){return"backlink"}value(e){let t=e.getLinkText({isFilenameUnique:!0});return t===null?"Unknown Location":t}createFilterOrErrorMessage(e){return ie.fromError(e,"backlink field does not support filtering")}canCreateFilterForLine(e){return!1}supportsGrouping(){return!0}grouper(){return e=>{let t=e.filename;if(t===null)return["Unknown Location"];let n=e.precedingHeader;return n===null?["[["+t+"]]"]:[`[[${t}#${n}|${t} > ${n}]]`]}}};var Bl=class extends st{fieldName(){return"cancelled"}date(e){return e.cancelledDate}filterResultIfFieldMissing(){return!1}};var Hl=class extends en{constructor(){super(),this._filters.add("is blocking",(e,t)=>e.isBlocking(t.allTasks)),this._filters.add("is not blocking",(e,t)=>!e.isBlocking(t.allTasks)),this._filters.add("is blocked",(e,t)=>e.isBlocked(t.allTasks)),this._filters.add("is not blocked",(e,t)=>!e.isBlocked(t.allTasks))}fieldName(){return"blocking"}};var Vl=class extends Ie{constructor(){super();this.filterInstructions=new qt;this.filterInstructions.add("has id",t=>t.id.length>0),this.filterInstructions.add("no id",t=>t.id.length===0)}canCreateFilterForLine(t){return this.filterInstructions.canCreateFilterForLine(t)?!0:super.canCreateFilterForLine(t)}createFilterOrErrorMessage(t){let n=this.filterInstructions.createFilterOrErrorMessage(t);return n.isValid()?n:super.createFilterOrErrorMessage(t)}fieldName(){return"id"}value(t){return t.id}supportsSorting(){return!0}supportsGrouping(){return!0}};var zl=class extends Ee{constructor(){super();this.filterInstructions=new qt;this.filterInstructions.add("has depends on",t=>t.dependsOn.length>0),this.filterInstructions.add("no depends on",t=>t.dependsOn.length===0)}canCreateFilterForLine(t){return this.filterInstructions.canCreateFilterForLine(t)?!0:super.canCreateFilterForLine(t)}createFilterOrErrorMessage(t){let n=this.filterInstructions.createFilterOrErrorMessage(t);return n.isValid()?n:ie.fromError(t,"Unknown instruction")}fieldName(){return"blocked by"}filterRegExp(){return null}};var Mh=[()=>new ql,()=>new sn,()=>new Jn,()=>new Al,()=>new Ui,()=>new Cl,()=>new Bl,()=>new kl,()=>new Ml,()=>new Rl,()=>new Fs,()=>new El,()=>new Ls,()=>new jl,()=>new Gl,()=>new Yl,()=>new Li,()=>new Wi,()=>new xl,()=>new Sl,()=>new Wl,()=>new Bs,()=>new $l,()=>new Dl,()=>new Vl,()=>new zl,()=>new Hl,()=>new Ul];function Lo(r){for(let e of Mh){let t=e();if(t.canCreateFilterForLine(r))return t.createFilterOrErrorMessage(r)}return null}function rk(r){let e=/^sort by /i;if(r.match(e)===null)return null;for(let t of Mh){let i=t().createSorterFromLine(r);if(i)return i}return null}function nk(r){let e=/^group by /i;if(r.match(e)===null)return null;for(let t of Mh){let i=t().createGrouperFromLine(r);if(i)return i}return null}var Kl=class{constructor(e,t,n){this.nestingLevel=e,this.displayName=t,this.property=n}};var Ql=class{constructor(e,t){this.lastHeadingAtLevel=new Array;this.groupers=t;let i=e.keys().next().value.length;for(let s=0;s<i;s++)this.lastHeadingAtLevel.push("")}getHeadingsForTaskGroup(e){let t=new Array;for(let n=0;n<e.length;n++){let i=e[n];if(i!=this.lastHeadingAtLevel[n]){t.push(new Kl(n,i,this.groupers[n].property));for(let s=n;s<e.length;s++)this.lastHeadingAtLevel[s]="";this.lastHeadingAtLevel[n]=i}}return t}};var Xl=class{constructor(e){this.children=new Map;this.values=[];this.values=e}generateAllPaths(e=[]){let t=new Map;if(this.children.size==0)return t.set([...e],this.values),t;for(let[n,i]of this.children)e.push(n),i.generateAllPaths(e).forEach((a,o)=>t.set(o,a)),e.pop();return t}};var Zl=class extends Xl{},Jl=class{constructor(e,t,n){this.root=new Zl(t),this.buildGroupingTree(e,n)}buildGroupingTree(e,t){let n=[this.root];for(let i of e){let s=[];for(let a of n)for(let o of a.values){let u=i.grouper(o,t);u.length===0&&u.push("");for(let l of u){let c=a.children.get(l);c===void 0&&(c=new Zl([]),a.children.set(l,c),s.push(c)),c.values.push(o)}}n=s}}generateTaskTreeStorage(){return this.root.generateAllPaths()}};var ec=class{constructor(e,t){this.groups=e,this.groupHeadings=[],this.tasks=t}setGroupHeadings(e){for(let t of e)this.groupHeadings.push(t)}applyTaskLimit(e){this.tasks=this.tasks.slice(0,e)}tasksAsStringOfLines(){let e="";for(let t of this.tasks)e+=t.toFileLineString()+`
`;return e}toString(){let e=`
`;e+=`Group names: [${this.groups}]
`;for(let t of this.groupHeadings)e+=`${"#".repeat(4+t.nestingLevel)} [${t.property}] ${t.displayName}
`;return e+=this.tasksAsStringOfLines(),e}};var Hs=class{constructor(e,t,n){this._groups=new Array;this._totalTaskCount=0;this._totalTaskCount=t.length,this._groupers=e;let s=new Jl(e,t,n).generateTaskTreeStorage();this.addTaskGroups(s),this.sortTaskGroups(),this.setGroupsHeadings(s)}get groupers(){return this._groupers}get groups(){return this._groups}totalTasksCount(){return this._totalTaskCount}toString(){let e="";e+=`Groupers (if any):
`;for(let n of this._groupers){let i=n.reverse?" reverse":"";e+=`- ${n.property}${i}
`}for(let n of this.groups)e+=n.toString(),e+=`
---
`;return e+=`
${this.totalTasksCount()} tasks
`,e}addTaskGroups(e){for(let[t,n]of e){let i=new ec(t,n);this.addTaskGroup(i)}}addTaskGroup(e){this._groups.push(e)}sortTaskGroups(){let e=(t,n)=>{let i=t.groups,s=n.groups;for(let a=0;a<i.length;a++){let o=this._groupers[a],u=i[a].localeCompare(s[a],void 0,{numeric:!0});if(u!==0)return o.reverse?-u:u}return 0};this._groups.sort(e)}setGroupsHeadings(e){let t=new Ql(e,this._groupers);for(let n of this._groups)n.setGroupHeadings(t.getHeadingsForTaskGroup(n.groups))}applyTaskLimit(e){this._groupers.length!==0&&(this._groups.forEach(t=>{t.applyTaskLimit(e)}),this.recalculateTotalTaskCount())}recalculateTotalTaskCount(){let e=[];this._groups.forEach(n=>{e=[...e,...n.tasks]});let t=[...new Set(e)];this._totalTaskCount=t.length}};var ri=class{constructor(e,t){this.tasksFile=e,this.allTasks=[...t]}static fromAllTasks(e){return new ri(void 0,e)}get queryPath(){var e,t;return(t=(e=this.tasksFile)==null?void 0:e.path)!=null?t:void 0}queryContext(){return this.tasksFile?mh(this.tasksFile,this.allTasks):void 0}};function ik(r){return`task${r!==1?"s":""}`}var ji=class{constructor(e,t){this.totalTasksCountBeforeLimit=0;this._searchErrorMessage=void 0;this.taskGroups=e,this.totalTasksCountBeforeLimit=t}get searchErrorMessage(){return this._searchErrorMessage}set searchErrorMessage(e){this._searchErrorMessage=e}get totalTasksCount(){return this.taskGroups.totalTasksCount()}totalTasksCountDisplayText(){let e=this.totalTasksCount,t=this.totalTasksCountBeforeLimit;return e===t?`${e} ${ik(e)}`:`${e} of ${t} ${ik(t)}`}get groups(){return this.taskGroups.groups}static fromError(e){let t=new ji(new Hs([],[],ri.fromAllTasks([])),0);return t._searchErrorMessage=e,t}};function sk(r){return r.endsWith("\\")}function ak(r){return r.endsWith("\\\\")}function IF(r){return r.replace(/^[ \t]*/,"")}function FF(r){return r.replace(/[ \t]*\\$/,"")}function LF(r,e){let t=r;return e&&(t=IF(r)),ak(t)?t=t.slice(0,-1):sk(r)&&(t=FF(t)),t}function ok(r){let e=[],t=!1,n="",i="";for(let s of r.split(`
`)){let a=LF(s,t);t?(n+=`
`+s,i+=" "+a):(n=s,i=a),ak(s)?t=!1:t=sk(s),t||(i.trim()!==""&&e.push(new An(n,i)),n="",i="")}return e}var Vs=class{static by(e,t,n){let i=this.defaultSorters().map(a=>a.comparator),s=[];for(let a of e)s.push(a.comparator);return t.sort(Vs.makeCompositeComparator([...s,...i],n))}static defaultSorters(){return[new sn().createNormalSorter(),new Bs().createNormalSorter(),new Fs().createNormalSorter(),new Ui().createNormalSorter(),new Ls().createNormalSorter()]}static makeCompositeComparator(e,t){return(n,i)=>{for(let s of e){let a=s(n,i,t);if(a!==0)return a}return 0}}};var Nn=class{constructor(e,t=void 0){this._limit=void 0;this._taskGroupLimit=void 0;this._taskLayoutOptions=new zr;this._queryLayoutOptions=new Qn;this._filters=[];this._error=void 0;this._sorting=[];this._grouping=[];this._ignoreGlobalQuery=!1;this.hideOptionsRegexp=/^(hide|show) (task count|backlink|priority|cancelled date|created date|start date|scheduled date|done date|due date|recurrence rule|edit button|postpone button|urgency|tags|depends on|id|on completion)/i;this.shortModeRegexp=/^short/i;this.fullModeRegexp=/^full/i;this.explainQueryRegexp=/^explain/i;this.ignoreGlobalQueryRegexp=/^ignore global query/i;this.logger=St.getLogger("tasks.Query");this._queryId="";this.limitRegexp=/^limit (groups )?(to )?(\d+)( tasks?)?/i;this.commentRegexp=/^#.*/;this._queryId=this.generateQueryId(10),this.source=e,this.tasksFile=t,this.debug(`Creating query: ${this.formatQueryForLogging()}`),ok(e).forEach(n=>{let i=this.expandPlaceholders(n,t);if(this.error===void 0)try{this.parseLine(i,n)}catch(s){let a;s instanceof Error?a=s.message:a="Unknown error",this.setError(a,n);return}})}get filePath(){var e,t;return(t=(e=this.tasksFile)==null?void 0:e.path)!=null?t:void 0}get queryId(){return this._queryId}parseLine(e,t){switch(!0){case this.shortModeRegexp.test(e):this._queryLayoutOptions.shortMode=!0;break;case this.fullModeRegexp.test(e):this._queryLayoutOptions.shortMode=!1;break;case this.explainQueryRegexp.test(e):this._queryLayoutOptions.explainQuery=!0;break;case this.ignoreGlobalQueryRegexp.test(e):this._ignoreGlobalQuery=!0;break;case this.limitRegexp.test(e):this.parseLimit(e);break;case this.parseSortBy(e):break;case this.parseGroupBy(e):break;case this.hideOptionsRegexp.test(e):this.parseHideOptions(e);break;case this.commentRegexp.test(e):break;case this.parseFilter(e,t):break;default:this.setError("do not understand query",t)}}formatQueryForLogging(){return`[${this.source.split(`
`).join(" ; ")}]`}expandPlaceholders(e,t){let n=e.anyContinuationLinesRemoved;if(n.includes("{{")&&n.includes("}}")&&this.tasksFile===void 0)return this._error=`The query looks like it contains a placeholder, with "{{" and "}}"
but no file path has been supplied, so cannot expand placeholder values.
The query is:
${n}`,n;let i=n;if(t){let s=qw(t);try{i=Ww(n,s)}catch(a){return a instanceof Error?this._error=a.message:this._error="Internal error. expandPlaceholders() threw something other than Error.",n}}return e.recordExpandedPlaceholders(i),i}append(e){return this.source===""?e:e.source===""?this:new Nn(`${this.source}
${e.source}`,this.tasksFile)}explainQuery(){return new Ps().explainQuery(this)}get limit(){return this._limit}get taskGroupLimit(){return this._taskGroupLimit}get taskLayoutOptions(){return this._taskLayoutOptions}get queryLayoutOptions(){return this._queryLayoutOptions}get filters(){return this._filters}addFilter(e){this._filters.push(e)}get sorting(){return this._sorting}get grouping(){return this._grouping}get error(){return this._error}setError(e,t){t.allLinesIdentical()?this._error=`${e}
Problem line: "${t.rawInstruction}"`:this._error=`${e}
Problem statement:
${t.explainStatement("    ")}
`}get ignoreGlobalQuery(){return this._ignoreGlobalQuery}applyQueryToTasks(e){this.debug(`Executing query: ${this.formatQueryForLogging()}`);let t=new ri(this.tasksFile,e);try{this.filters.forEach(o=>{e=e.filter(u=>o.filterFunction(u,t))});let{debugSettings:n}=X(),i=n.ignoreSortInstructions?e:Vs.by(this.sorting,e,t),s=i.slice(0,this.limit),a=new Hs(this.grouping,s,t);return this._taskGroupLimit!==void 0&&a.applyTaskLimit(this._taskGroupLimit),new ji(a,i.length)}catch(n){let i="Search failed";return ji.fromError(Fi(i,n))}}parseHideOptions(e){let t=e.match(this.hideOptionsRegexp);if(t!==null){let n=t[1].toLowerCase()==="hide";switch(t[2].toLowerCase()){case"task count":this._queryLayoutOptions.hideTaskCount=n;break;case"backlink":this._queryLayoutOptions.hideBacklinks=n;break;case"postpone button":this._queryLayoutOptions.hidePostponeButton=n;break;case"priority":this._taskLayoutOptions.setVisibility("priority",!n);break;case"cancelled date":this._taskLayoutOptions.setVisibility("cancelledDate",!n);break;case"created date":this._taskLayoutOptions.setVisibility("createdDate",!n);break;case"start date":this._taskLayoutOptions.setVisibility("startDate",!n);break;case"scheduled date":this._taskLayoutOptions.setVisibility("scheduledDate",!n);break;case"due date":this._taskLayoutOptions.setVisibility("dueDate",!n);break;case"done date":this._taskLayoutOptions.setVisibility("doneDate",!n);break;case"recurrence rule":this._taskLayoutOptions.setVisibility("recurrenceRule",!n);break;case"edit button":this._queryLayoutOptions.hideEditButton=n;break;case"urgency":this._queryLayoutOptions.hideUrgency=n;break;case"tags":this._taskLayoutOptions.setTagsVisibility(!n);break;case"id":this._taskLayoutOptions.setVisibility("id",!n);break;case"depends on":this._taskLayoutOptions.setVisibility("dependsOn",!n);break;case"on completion":this._taskLayoutOptions.setVisibility("onCompletion",!n);break;default:this.setError("do not understand hide/show option",new An(e,e))}}}parseFilter(e,t){var i;let n=Lo(e);return n!=null?(n.filter?(n.filter.setStatement(t),this._filters.push(n.filter)):this.setError((i=n.error)!=null?i:"Unknown error",t),!0):!1}parseLimit(e){let t=e.match(this.limitRegexp);if(t===null){this.setError("do not understand query limit",new An(e,e));return}let n=Number.parseInt(t[3],10);t[1]!==void 0?this._taskGroupLimit=n:this._limit=n}parseSortBy(e){let t=rk(e);return t?(this._sorting.push(t),!0):!1}parseGroupBy(e){let t=nk(e);return t?(this._grouping.push(t),!0):!1}generateQueryId(e){let t="AaBbCcDdEeFfGgHhIiJjKkLlMmNnOoPpQqRrSsTtUuVvWwXxYyZz1234567890";return Array.from({length:e},()=>t[Math.floor(Math.random()*t.length)]).join("")}debug(e,t){this.logger.debugWithId(this._queryId,`"${this.filePath}": ${e}`,t)}};var ni=class{constructor(e=ni.empty){this._source=e}static getInstance(){return ni.instance||(ni.instance=new ni),ni.instance}set(e){this._source=e}query(e=void 0){return new Nn(this._source,e)}hasInstructions(){return this._source.trim()!==ni.empty}},tr=ni;tr.empty="";var tc=class{constructor({obsidianEvents:e}){this.logger=St.getLogger("tasks.Events");this.obsidianEvents=e}onCacheUpdate(e){return this.logger.debug("TasksEvents.onCacheUpdate()"),this.obsidianEvents.on("obsidian-tasks-plugin:cache-update",e)}triggerCacheUpdate(e){this.logger.debug("TasksEvents.triggerCacheUpdate()"),this.obsidianEvents.trigger("obsidian-tasks-plugin:cache-update",e)}onRequestCacheUpdate(e){return this.logger.debug("TasksEvents.onRequestCacheUpdate()"),this.obsidianEvents.on("obsidian-tasks-plugin:request-cache-update",e)}triggerRequestCacheUpdate(e){this.logger.debug("TasksEvents.triggerRequestCacheUpdate()"),this.obsidianEvents.trigger("obsidian-tasks-plugin:request-cache-update",e)}off(e){this.logger.debug("TasksEvents.off()"),this.obsidianEvents.offref(e)}};var dk=require("obsidian");var ck=require("obsidian");var Ch=class{constructor(e){this.newStatus=e}apply(e){return this.isCheckedForTask(e)?[e]:e.handleNewStatusWithRecurrenceInUsersOrder(this.newStatus)}instructionDisplayName(){return`Change status to: [${this.newStatus.symbol}] ${this.newStatus.name}`}isCheckedForTask(e){return this.newStatus.symbol===e.status.symbol}};function uk(r){let e=[],t=new Le().coreStatuses.map(n=>n.symbol);for(let n of[!0,!1])for(let i of r.registeredStatuses)t.includes(i.symbol)===n&&e.push(new Ch(i));return e}var lk=require("obsidian");function Uo(r,e){return P(this,null,function*(){yield br({originalTask:r,newTasks:e})})}var zs=class extends lk.Menu{constructor(t){super();this.taskSaver=t}addItemsForInstructions(t,n){for(let i of t)this.addItemForInstruction(n,i)}addItemForInstruction(t,n){this.addItem(i=>this.getMenuItemCallback(t,i,n))}getMenuItemCallback(t,n,i){n.setTitle(i.instructionDisplayName()).setChecked(i.isCheckedForTask(t)).onClick(()=>P(this,null,function*(){let s=i.apply(t);(s.length!==1||!Object.is(s[0],t))&&(yield this.taskSaver(t,s))}))}};var rc=class extends zs{constructor(e,t,n=Uo){super(n);let i=uk(e);this.addItemsForInstructions(i,t)}};var nc=class{constructor(){this.data=UF}addDataAttribute(e,t,n){this.data[n].addDataAttribute(e,t,n)}addClassName(e,t){let n=this.data[t].className;e.classList.add(n)}},Ah=class{constructor(e,t,n){if(e==="")throw Error("Developer note: CSS class cannot be an empty string, please specify one.");this.className=e,this.attributeName=t,this.attributeValueCalculator=n}addDataAttribute(e,t,n){if(this.attributeName===Ah.noAttributeName)return;let i=this.attributeValueCalculator(n,t);i!==""&&(e.dataset[this.attributeName]=i)}},Lr=Ah;Lr.noAttributeName="",Lr.noAttributeValueCalculator=()=>"",Lr.dateAttributeCalculator=(e,t)=>{let i="far";function s(o){let l=window.moment().startOf("day").diff(o,"days");if(isNaN(l))return null;if(l===0)return"today";let c="";return l>0?c+="past-":l<0&&(c+="future-"),Math.abs(l)<=7?c+=Math.abs(l).toString()+"d":c+=i,c}let a=t[e];if(!Array.isArray(a)&&a instanceof window.moment){let o=s(a);if(o)return o}return""};function Ks(r){return new Lr(r,Lr.noAttributeName,Lr.noAttributeValueCalculator)}function Qs(r,e){return new Lr(r,e,Lr.dateAttributeCalculator)}var UF={createdDate:Qs("task-created","taskCreated"),dueDate:Qs("task-due","taskDue"),startDate:Qs("task-start","taskStart"),scheduledDate:Qs("task-scheduled","taskScheduled"),doneDate:Qs("task-done","taskDone"),cancelledDate:Qs("task-cancelled","taskCancelled"),priority:new Lr("task-priority","taskPriority",(r,e)=>Sn.priorityNameUsingNormal(e.priority).toLocaleLowerCase()),description:Ks("task-description"),recurrenceRule:Ks("task-recurring"),onCompletion:Ks("task-onCompletion"),dependsOn:Ks("task-dependsOn"),id:Ks("task-id"),blockLink:Ks("task-block-link")};function $t(r,e){let t=document.createElement(r);return e.appendChild(t),t}var ii=class{static obsidianMarkdownRenderer(e,t,n,i){return P(this,null,function*(){if(!i)throw new Error("Must call the Obsidian renderer with an Obsidian Component object");yield ck.MarkdownRenderer.renderMarkdown(e,t,n,i)})}constructor({textRenderer:e=ii.obsidianMarkdownRenderer,obsidianComponent:t,parentUlElement:n,taskLayoutOptions:i,queryLayoutOptions:s}){this.textRenderer=e,this.obsidianComponent=t,this.parentUlElement=n,this.taskLayoutOptions=i,this.queryLayoutOptions=s}renderTaskLine(e,t,n){return P(this,null,function*(){let i=$t("li",this.parentUlElement);i.classList.add("task-list-item","plugin-tasks-list-item");let s=$t("span",i);s.classList.add("tasks-list-text"),yield this.taskToHtml(e,s,i);let a=$t("input",i);return a.classList.add("task-list-item-checkbox"),a.type="checkbox",e.status.symbol!==" "&&(a.checked=!0,i.classList.add("is-checked")),e.taskLocation.hasKnownPath&&(a.addEventListener("click",u=>{u.preventDefault(),u.stopPropagation(),a.disabled=!0;let l=e.toggleWithRecurrenceInUsersOrder();br({originalTask:e,newTasks:l})}),a.addEventListener("contextmenu",u=>{new rc(De.getInstance(),e).showAtPosition({x:u.clientX,y:u.clientY})}),a.setAttribute("title","Right-click for options")),i.prepend(a),i.setAttribute("data-task",e.status.symbol.trim()),i.setAttribute("data-line",t.toString()),i.setAttribute("data-task-status-name",e.status.name),i.setAttribute("data-task-status-type",e.status.type),a.setAttribute("data-line",t.toString()),this.queryLayoutOptions.shortMode&&this.addTooltip(e,s,n),i})}taskToHtml(e,t,n){return P(this,null,function*(){let i=new nc,s=Mr.tasksPluginEmoji.taskSerializer;for(let a of this.taskLayoutOptions.shownComponents){let o=s.componentToString(e,this.queryLayoutOptions.shortMode,a);if(o){let u=$t("span",t),l=$t("span",u);yield this.renderComponentText(l,o,a,e),this.addInternalClasses(a,l),i.addClassName(u,a),i.addDataAttribute(u,e,a),i.addDataAttribute(n,e,a)}}for(let a of this.taskLayoutOptions.hiddenComponents)i.addDataAttribute(n,e,a);n.dataset.taskPriority===void 0&&i.addDataAttribute(n,e,"priority")})}renderComponentText(e,t,n,i){return P(this,null,function*(){if(n==="description"){t=_e.getInstance().removeAsWordFromDependingOnSettings(t);let{debugSettings:s}=X();s.showTaskHiddenData&&(t+=`<br>\u{1F41B} <b>${i.lineNumber}</b> . ${i.sectionStart} . ${i.sectionIndex} . '<code>${i.originalMarkdown}</code>'<br>'<code>${i.path}</code>' > '<code>${i.precedingHeader}</code>'<br>`),yield this.textRenderer(t,e,i.path,this.obsidianComponent);let a=e.querySelector("blockquote"),o=a!=null?a:e,u=o.querySelector("p");if(u!==null){for(;u.firstChild;)o.insertBefore(u.firstChild,u);u.remove()}e.querySelectorAll("p").forEach(l=>{l.hasChildNodes()||l.remove()}),e.querySelectorAll(".footnotes").forEach(l=>{l.remove()})}else e.innerHTML=t})}addInternalClasses(e,t){function n(i){let s=/["&\x00\r\n]/g,a=i.replace(s,"-");return a=a.replace(/^[-_]+/,""),a.length>0?a:null}if(e==="description"){let i=t.getElementsByClassName("tag");for(let s=0;s<i.length;s++){let a=i[s].textContent;if(a){let o=n(a),u=i[s];o&&(u.dataset.tagName=o)}}}}addTooltip(e,t,n){let{recurrenceSymbol:i,startDateSymbol:s,createdDateSymbol:a,scheduledDateSymbol:o,dueDateSymbol:u,cancelledDateSymbol:l,doneDateSymbol:c}=Mr.tasksPluginEmoji.taskSerializer.symbols;t.addEventListener("mouseenter",()=>{function d(_,b,E){b&&_.createDiv().setText(p({signifier:E,date:b}))}function p({signifier:_,date:b}){return`${_} ${b.format(J.dateFormat)} (${b.from(window.moment().startOf("day"))})`}let m=t.createDiv();m.addClasses(["tooltip","pop-up"]),e.recurrence&&m.createDiv().setText(`${i} ${e.recurrence.toText()}`),d(m,e.createdDate,a),d(m,e.startDate,s),d(m,e.scheduledDate,o),d(m,e.dueDate,u),d(m,e.cancelledDate,l),d(m,e.doneDate,c);let y=e.getLinkText({isFilenameUnique:n});y&&m.createDiv().setText(`\u{1F517} ${y}`),t.addEventListener("mouseleave",()=>{m.remove()})})}};var ic=class{constructor({plugin:e}){this.markdownPostProcessor=this._markdownPostProcessor.bind(this);e.registerMarkdownPostProcessor(this._markdownPostProcessor.bind(this))}_markdownPostProcessor(e,t){return P(this,null,function*(){var d;let n=new dk.MarkdownRenderChild(e);t.addChild(n);let i=e.findAll(".task-list-item").filter(p=>{var _;let m=(_=p.textContent)==null?void 0:_.split(`
`);if(m===void 0)return!1;let y=null;for(let b=0;b<m.length;b=b+1)if(m[b]!==""){y=m[b];break}return y===null?!1:_e.getInstance().includedIn(y)});if(i.length===0)return;let s=t.sourcePath,a=t.getSectionInfo(e);if(a===null)return;let o=a.text.split(`
`),u=0,l=[];for(let p=a.lineStart;p<=a.lineEnd;p++){let m=o[p];if(m===void 0)continue;let y=null,_=ae.fromLine({line:m,taskLocation:new ft(new at(s),p,a.lineStart,u,y),fallbackDate:null});_!==null&&(l.push(_),u++)}let c=new ii({obsidianComponent:n,parentUlElement:e,taskLayoutOptions:new zr,queryLayoutOptions:new Qn});for(let p=0;p<i.length;p++){let m=l[p],y=i[p];if(m===void 0||y===void 0)continue;let _=(d=y.getAttr("data-line"))!=null?d:"0",b=Number.parseInt(_,10),E=yield c.renderTaskLine(m,b),R=y.childNodes;for(let q=0;q<R.length;q=q+1){let te=R[q],G=te.nodeName.toLowerCase();G==="div"?E.prepend(te):(G==="ul"||G==="ol")&&E.append(te)}let S=y.querySelectorAll("[data-footnote-id]"),F=E.querySelectorAll("[data-footnote-id]");if(S.length===F.length)for(let q=0;q<S.length;q++)F[q].replaceWith(S[q]);y.replaceWith(E)}})}};var fk=require("@codemirror/view"),pk=require("obsidian");var mk=()=>fk.ViewPlugin.fromClass(Ph),Ph=class{constructor(e){this.view=e,this.handleClickEvent=this.handleClickEvent.bind(this),this.view.dom.addEventListener("click",this.handleClickEvent)}destroy(){this.view.dom.removeEventListener("click",this.handleClickEvent)}handleClickEvent(e){let{target:t}=e;if(!t||!(t instanceof HTMLInputElement)||t.type!=="checkbox")return!1;let n=t.closest("ul.plugin-tasks-query-result, div.callout-content");if(n){if(n.matches("div.callout-content")){let p=`obsidian-tasks-plugin warning: Tasks cannot add or remove completion dates or make the next copy of a recurring task for tasks written inside a callout when you click their checkboxes in Live Preview. 
If you wanted Tasks to do these things, please undo your change, then either click the line of the task and use the "Toggle Task Done" command, or switch to Reading View to click the checkbox.`;console.warn(p),new pk.Notice(p,45e3)}return!1}let{state:i}=this.view,s=this.view.posAtDOM(t),a=i.doc.lineAt(s),o=ae.fromLine({line:a.text,taskLocation:ft.fromUnknownPosition(new at("")),fallbackDate:null});if(o===null)return!1;e.preventDefault();let l=o.toggleWithRecurrenceInUsersOrder().map(p=>p.toFileLineString()).join(i.lineBreak),c=i.update({changes:{from:a.from,to:a.to,insert:l}});this.view.dispatch(c);let d=t.checked;return setTimeout(()=>{t.checked=d},1),!0}};var lc=require("obsidian"),Dk=require("obsidian");function hk(r,e,t,n=void 0){let i="";e.isEmpty()||(i+=`Only tasks containing the global filter '${e.get()}'.

`);let s=new Ps("  "),a=new Nn(r,n);if(!a.ignoreGlobalQuery&&t.hasInstructions()){let o=t.query(n);i+=`Explanation of the global query:

${s.explainQuery(o)}
`}return i+=`Explanation of this Tasks code block query:

${s.explainQuery(a)}`,i}function Wo(r,e,t){let n=new Nn(r,t);return n.ignoreGlobalQuery?n:e.query(t).append(n)}function qo(r,e,t){e&&r.push(WF(t))}function WF(r){return`tasks-layout-hide-${r}`}var sc=class{constructor(e){e?this.queryLayoutOptions=e:this.queryLayoutOptions=new Qn}getHiddenClasses(){let e=[],t=[[this.queryLayoutOptions.hideUrgency,"urgency"],[this.queryLayoutOptions.hideBacklinks,"backlinks"],[this.queryLayoutOptions.hideEditButton,"edit-button"],[this.queryLayoutOptions.hidePostponeButton,"postpone-button"]];for(let[n,i]of t)qo(e,n,i);return this.queryLayoutOptions.shortMode&&e.push("tasks-layout-short-mode"),e}};var ac=class{constructor(e){e?this.taskLayoutOptions=e:this.taskLayoutOptions=new zr}generateHiddenClasses(){let e=[];return this.taskLayoutOptions.toggleableComponents.forEach(t=>{qo(e,!this.taskLayoutOptions.isShown(t),t)}),qo(e,!this.taskLayoutOptions.areTagsShown(),"tags"),e}};var $o=class{constructor(e){this.label=e,this.start()}start(){!this.recordTimings()||performance.mark(this.labelForStart())}finish(){!this.recordTimings()||(performance.mark(this.labelForEnd()),performance.measure(this.label,this.labelForStart(),this.labelForEnd()),this.printDuration())}printDuration(){let e=performance.getEntriesByName(this.label),t=e[e.length-1];t?console.log(this.label+":",t.duration.toFixed(2),"milliseconds"):console.log(`Measurement for ${this.label} not found`)}labelForStart(){return`${this.label} - start`}labelForEnd(){return`${this.label} - end`}recordTimings(){let{debugSettings:e}=X();return e.recordTimings}};function gk(r){for(let t of ae.allDateFields()){let n=r[t];if(n&&!n.isValid())return!1}let e=r.happensDates.some(t=>!!(t!=null&&t.isValid()));return!r.isDone&&e}function jo(r){return r.dueDate?"dueDate":r.scheduledDate?"scheduledDate":r.startDate?"startDate":null}function Nh(r,e,t,n){let i=r[e];return Tk(i,r,e,t,n)}function yk(r,e,t,n){let i=window.moment();return Tk(i,r,e,t,n)}function bk(r,e,t,n){return _k(r,e,null)}function Tk(r,e,t,n,i){let s=new It(r).postpone(n,i);return _k(e,t,s)}function _k(r,e,t){let n=yt.removeInferredStatusIfNeeded(r,[new ae(he(K({},r),{[e]:t}))])[0];return{postponedDate:t,postponedTask:n}}function vk(r,e){if(r){let t=r==null?void 0:r.format("DD MMM YYYY");return`Task's ${e} changed to ${t}`}else return`Task's ${e} removed`}function wk(r,e,t){return`\u2139\uFE0F ${Ih(r,e,t)} (right-click for more options)`}function Ih(r,e,t){let n=jo(r),i=r[n];return Ok(n,i,e,t)}function kk(r,e,t){let n=jo(r),i=window.moment().startOf("day");return Ok(n,i,e,t)}function Ek(r,e,t){let n=jo(r);return n==="scheduledDate"&&r.scheduledDateIsInferred?"Cannot remove inferred scheduled date":`Remove ${Sk(n)}`}function qF(r){return Es(r.replace("Date",""))}function Sk(r){return r.replace("Date"," date")}function Ok(r,e,t,n){let s=new It(e).postpone(n,t).format("ddd Do MMM"),a=t!=1?t:"a";return e.isSameOrBefore(window.moment(),"day")?`${qF(r)} in ${a} ${n}, on ${s}`.replace(" in 0 days"," today").replace("in a day","tomorrow"):`Postpone ${Sk(r)} by ${a} ${n}, to ${s}`}var Fh=require("obsidian");var si=class extends zs{constructor(e,t,n=Uo){super(n);let i=(l,c,d,p,m,y)=>{let _=m(t,p,d);c.setTitle(_).onClick(()=>si.postponeOnClickCallback(l,t,p,d,y,n))},s=kk,a=yk;this.addItem(l=>i(e,l,"days",0,s,a)),this.addItem(l=>i(e,l,"day",1,s,a)),this.addSeparator();let o=Ih,u=Nh;this.addItem(l=>i(e,l,"days",2,o,u)),this.addItem(l=>i(e,l,"days",3,o,u)),this.addItem(l=>i(e,l,"days",4,o,u)),this.addItem(l=>i(e,l,"days",5,o,u)),this.addItem(l=>i(e,l,"days",6,o,u)),this.addSeparator(),this.addItem(l=>i(e,l,"week",1,o,u)),this.addItem(l=>i(e,l,"weeks",2,o,u)),this.addItem(l=>i(e,l,"weeks",3,o,u)),this.addItem(l=>i(e,l,"month",1,o,u)),this.addSeparator(),this.addItem(l=>i(e,l,"days",2,Ek,bk))}static postponeOnClickCallback(o,u,l,c){return P(this,arguments,function*(e,t,n,i,s=Nh,a=Uo){let d=jo(t);if(d===null){let y="\u26A0\uFE0F Postponement requires a date: due, scheduled or start.";return new Fh.Notice(y,1e4)}let{postponedDate:p,postponedTask:m}=s(t,d,i,n);yield a(t,m),si.postponeSuccessCallback(e,d,p)})}static postponeSuccessCallback(e,t,n){e.style.pointerEvents="none";let i=vk(n,t);new Fh.Notice(i,2e3)}};var oc=class{constructor(e,t,n,i,s){switch(this.source=t,this.tasksFile=n,this.renderMarkdown=i,this.obsidianComponent=s,e){case"block-language-tasks":this.query=Wo(this.source,tr.getInstance(),this.tasksFile),this.queryType="tasks";break;default:this.query=Wo(this.source,tr.getInstance(),this.tasksFile),this.queryType="tasks";break}}get filePath(){var e,t;return(t=(e=this.tasksFile)==null?void 0:e.path)!=null?t:void 0}render2(e,t,n,i){return P(this,null,function*(){e==="Warm"&&this.query.error===void 0?yield this.renderQuerySearchResults(t,e,n,i):this.query.error!==void 0?this.renderErrorMessage(n,this.query.error):this.renderLoadingMessage(n)})}renderQuerySearchResults(e,t,n,i){return P(this,null,function*(){let s=this.explainAndPerformSearch(t,e,n);if(s.searchErrorMessage!==void 0){this.renderErrorMessage(n,s.searchErrorMessage);return}yield this.renderSearchResults(s,n,i)})}explainAndPerformSearch(e,t,n){let i=new $o(`Search: ${this.query.queryId} - ${this.filePath}`);i.start(),this.query.debug(`[render] Render called: plugin state: ${e}; searching ${t.length} tasks`),this.query.queryLayoutOptions.explainQuery&&this.createExplanation(n);let s=this.query.applyQueryToTasks(t);return i.finish(),s}renderSearchResults(e,t,n){return P(this,null,function*(){let i=new $o(`Render: ${this.query.queryId} - ${this.filePath}`);i.start(),yield this.addAllTaskGroups(e.taskGroups,t,n);let s=e.totalTasksCount;this.addTaskCount(t,e),this.query.debug(`[render] ${s} tasks displayed`),i.finish()})}renderErrorMessage(e,t){e.createDiv().innerHTML=`<pre>Tasks query: ${t.replace(/\n/g,"<br>")}</pre>`}renderLoadingMessage(e){e.setText("Loading Tasks ...")}createExplanation(e){let t=hk(this.source,_e.getInstance(),tr.getInstance(),this.tasksFile),n=$t("pre",e);n.addClasses(["plugin-tasks-query-explanation"]),n.setText(t),e.appendChild(n)}addAllTaskGroups(e,t,n){return P(this,null,function*(){for(let i of e.groups)yield this.addGroupHeadings(t,i.groupHeadings),yield this.createTaskList(i.tasks,t,n)})}createTaskList(e,t,n){return P(this,null,function*(){let i=$t("ul",t);i.addClasses(["contains-task-list","plugin-tasks-query-result"]);let s=new ac(this.query.taskLayoutOptions);i.addClasses(s.generateHiddenClasses());let a=new sc(this.query.queryLayoutOptions);i.addClasses(a.getHiddenClasses());let o=this.getGroupingAttribute();o&&o.length>0&&(i.dataset.taskGroupBy=o);let u=new ii({obsidianComponent:this.obsidianComponent,parentUlElement:i,taskLayoutOptions:this.query.taskLayoutOptions,queryLayoutOptions:this.query.queryLayoutOptions});for(let[l,c]of e.entries())yield this.addTask(i,u,c,l,n);t.appendChild(i)})}addTask(e,t,n,i,s){return P(this,null,function*(){let a=this.isFilenameUnique({task:n},s.allMarkdownFiles),o=yield t.renderTaskLine(n,i,a);o.querySelectorAll("[data-footnote-id]").forEach(d=>d.remove());let l=o.createSpan("task-extras");this.query.queryLayoutOptions.hideUrgency||this.addUrgency(l,n);let c=this.query.queryLayoutOptions.shortMode;this.query.queryLayoutOptions.hideBacklinks||this.addBacklinks(l,n,c,a,s),this.query.queryLayoutOptions.hideEditButton||this.addEditButton(l,n,s),!this.query.queryLayoutOptions.hidePostponeButton&&gk(n)&&this.addPostponeButton(l,n,c),e.appendChild(o)})}addEditButton(e,t,n){let i=$t("a",e);i.addClass("tasks-edit"),i.title="Edit task",i.href="#",i.onClickEvent(s=>{n.editTaskPencilClickHandler(s,t,n.allTasks)})}addUrgency(e,t){let n=new Intl.NumberFormat().format(t.urgency);e.createSpan({text:n,cls:"tasks-urgency"})}addGroupHeadings(e,t){return P(this,null,function*(){for(let n of t)yield this.addGroupHeading(e,n)})}addGroupHeading(e,t){return P(this,null,function*(){let n="h6";t.nestingLevel===0?n="h4":t.nestingLevel===1&&(n="h5");let i=$t(n,e);i.addClass("tasks-group-heading"),yield this.renderMarkdown(t.displayName,i,this.tasksFile.path,this.obsidianComponent)})}addBacklinks(e,t,n,i,s){var l;let a=e.createSpan({cls:"tasks-backlink"});n||a.append(" (");let o=$t("a",a);o.rel="noopener",o.target="_blank",o.addClass("internal-link"),n&&o.addClass("internal-link-short-mode");let u;n?u=" \u{1F517}":u=(l=t.getLinkText({isFilenameUnique:i}))!=null?l:"",o.setText(u),o.addEventListener("click",c=>P(this,null,function*(){yield s.backlinksClickHandler(c,t)})),o.addEventListener("mousedown",c=>P(this,null,function*(){yield s.backlinksMousedownHandler(c,t)})),n||a.append(")")}addPostponeButton(e,t,n){let s="day",a=wk(t,1,s),o=$t("a",e);o.addClass("tasks-postpone"),n&&o.addClass("tasks-postpone-short-mode"),o.title=a,o.addEventListener("click",u=>{u.preventDefault(),u.stopPropagation(),si.postponeOnClickCallback(o,t,1,s)}),o.addEventListener("contextmenu",u=>P(this,null,function*(){u.preventDefault(),u.stopPropagation(),new si(o,t).showAtPosition({x:u.clientX,y:u.clientY})}))}addTaskCount(e,t){this.query.queryLayoutOptions.hideTaskCount||e.createDiv({text:t.totalTasksCountDisplayText(),cls:"tasks-count"})}isFilenameUnique({task:e},t){let n=e.path.match(/([^/]*)\..+$/i);if(n===null)return;let i=n[1];return t.filter(a=>{if(a.basename===i)return!0}).length<2}getGroupingAttribute(){let e=[];for(let t of this.query.grouping)e.push(t.property);return e.join(",")}};var uc=class{constructor({plugin:e,events:t}){this.addQueryRenderChild=this._addQueryRenderChild.bind(this);this.app=e.app,this.plugin=e,this.events=t,e.registerMarkdownCodeBlockProcessor("tasks",this._addQueryRenderChild.bind(this))}_addQueryRenderChild(e,t,n){return P(this,null,function*(){let i=new Lh({app:this.app,plugin:this.plugin,events:this.events,container:t,source:e,tasksFile:new at(n.sourcePath)});n.addChild(i),i.load()})}},Lh=class extends lc.MarkdownRenderChild{constructor({app:t,plugin:n,events:i,container:s,source:a,tasksFile:o}){super(s);this.queryResultsRenderer=new oc(this.containerEl.className,a,o,lc.MarkdownRenderer.renderMarkdown,this),this.app=t,this.plugin=n,this.events=i}onload(){this.events.triggerRequestCacheUpdate(this.render.bind(this)),this.renderEventRef=this.events.onCacheUpdate(this.render.bind(this)),this.reloadQueryAtMidnight()}onunload(){this.renderEventRef!==void 0&&this.events.off(this.renderEventRef),this.queryReloadTimeout!==void 0&&clearTimeout(this.queryReloadTimeout)}reloadQueryAtMidnight(){let t=new Date;t.setHours(24,0,0,0);let n=new Date,i=t.getTime()-n.getTime();this.queryReloadTimeout=setTimeout(()=>{this.queryResultsRenderer.query=Wo(this.queryResultsRenderer.source,tr.getInstance(),this.queryResultsRenderer.tasksFile),this.events.triggerRequestCacheUpdate(this.render.bind(this)),this.reloadQueryAtMidnight()},i+1e3)}render(i){return P(this,arguments,function*({tasks:t,state:n}){var a;let s=$t("div",this.containerEl);yield this.queryResultsRenderer.render2(n,t,s,{allTasks:this.plugin.getTasks(),allMarkdownFiles:this.app.vault.getMarkdownFiles(),backlinksClickHandler:jF,backlinksMousedownHandler:GF,editTaskPencilClickHandler:$F}),(a=this.containerEl.firstChild)==null||a.replaceWith(s)})}};function $F(r,e,t){r.preventDefault();let n=s=>P(this,null,function*(){yield br({originalTask:e,newTasks:yt.removeInferredStatusIfNeeded(e,s)})});new Kn({app,task:e,onSubmit:n,allTasks:t}).open()}function jF(r,e){return P(this,null,function*(){let t=yield nh(e,app.vault);if(t){let[n,i]=t,s=app.workspace.getLeaf(Dk.Keymap.isModEvent(r));r.preventDefault(),yield s.openFile(i,{eState:{line:n}})}})}function GF(r,e){return P(this,null,function*(){if(r.button===1){let t=yield nh(e,app.vault);if(t){let[n,i]=t,s=app.workspace.getLeaf("tab");r.preventDefault(),yield s.openFile(i,{eState:{line:n}})}}})}var ve=require("obsidian");var cc=class{constructor(e){this._markdown="";this.columnNames=e,this.addTitleRow()}get markdown(){return this._markdown}addTitleRow(){let e="|",t="|";this.columnNames.forEach(n=>{e+=` ${n} |`,t+=" ----- |"}),this._markdown+=`${e}
`,this._markdown+=`${t}
`}addRow(e){let t=this.makeRowText(e);this._markdown+=`${t}
`}addRowIfNew(e){let t=this.makeRowText(e);this._markdown.includes(t)||(this._markdown+=`${t}
`)}makeRowText(e){let t="|";return e.forEach(n=>{t+=` ${n} |`}),t}};function xk(r,e){return r.findIndex(t=>t.symbol===e)}function In(r){return r===""?r:"`"+(r!==" "?r:"space")+"`"}function YF(r,e){let t=ne.getTypeForUnknownSymbol(r.symbol);r.type!==t&&(t==="TODO"&&r.symbol!==" "||e.push(`For information, the conventional type for status symbol ${In(r.symbol)} is ${In(t)}: you may wish to review this type.`))}function BF(r,e,t){let n=xk(r,e.nextStatusSymbol);if(n===-1){t.push(`Next symbol ${In(e.nextStatusSymbol)} is unknown: create a status with symbol ${In(e.nextStatusSymbol)}.`);return}if(e.type!=="DONE")return;let i=r[n];if(i){if(i.type!=="TODO"&&i.type!=="IN_PROGRESS"){let s="https://publish.obsidian.md/tasks/Getting+Started/Statuses/Recurring+Tasks+and+Custom+Statuses",a=[`This \`DONE\` status is followed by ${In(i.type)}, not \`TODO\` or \`IN_PROGRESS\`.`,"If used to complete a recurring task, it will instead be followed by `TODO` or `IN_PROGRESS`, to ensure the next task matches the `not done` filter.",`See [Recurring Tasks and Custom Statuses](${s}).`].join("<br>");t.push(a)}}else t.push("Unexpected failure to find the next status.")}function HF(r,e,t){let n=[];return e.symbol===ne.EMPTY.symbol?(n.push("Empty symbol: this status will be ignored."),n):xk(r,e.symbol)!=t?(n.push(`Duplicate symbol '${In(e.symbol)}': this status will be ignored.`),n):(YF(e,n),BF(r,e,n),n)}function Rk(r){let e=new cc(["Status Symbol","Next Status Symbol","Status Name","Status Type","Problems (if any)"]),t=Le.allStatuses(r);return t.forEach((n,i)=>{e.addRow([In(n.symbol),In(n.nextStatusSymbol),n.name,In(n.type),HF(t,n,i).join("<br>")])}),e.markdown}function Mk(r,e,t,n){let s=Rk(r),a=e.mermaidDiagram(!0);return`# ${t}

## About this file

This file was created by the Obsidian Tasks plugin (version ${n}) to help visualise the task statuses in this vault.

If you change the Tasks status settings, you can get an updated report by:

- Going to \`Settings\` -> \`Tasks\`.
- Clicking on \`Review and check your Statuses\`.

You can delete this file any time.

## Status Settings

<!--
Switch to Live Preview or Reading Mode to see the table.
If there are any Markdown formatting characters in status names, such as '*' or '_',
Obsidian may only render the table correctly in Reading Mode.
-->

These are the status values in the Core and Custom statuses sections.

${s}
## Loaded Settings

<!-- Switch to Live Preview or Reading Mode to see the diagram. -->

These are the settings actually used by Tasks.
${a}`}function Ck(){return[[" ","Unchecked","x","TODO"],["x","Checked"," ","DONE"],[">","Rescheduled","x","TODO"],["<","Scheduled","x","TODO"],["!","Important","x","TODO"],["-","Cancelled"," ","CANCELLED"],["/","In Progress","x","IN_PROGRESS"],["?","Question","x","TODO"],["*","Star","x","TODO"],["n","Note","x","TODO"],["l","Location","x","TODO"],["i","Information","x","TODO"],["I","Idea","x","TODO"],["S","Amount","x","TODO"],["p","Pro","x","TODO"],["c","Con","x","TODO"],["b","Bookmark","x","TODO"],['"',"Quote","x","TODO"],["0","Speech bubble 0","0","NON_TASK"],["1","Speech bubble 1","1","NON_TASK"],["2","Speech bubble 2","2","NON_TASK"],["3","Speech bubble 3","3","NON_TASK"],["4","Speech bubble 4","4","NON_TASK"],["5","Speech bubble 5","5","NON_TASK"],["6","Speech bubble 6","6","NON_TASK"],["7","Speech bubble 7","7","NON_TASK"],["8","Speech bubble 8","8","NON_TASK"],["9","Speech bubble 9","9","NON_TASK"]]}function Ak(){return[[" ","incomplete","x","TODO"],["x","complete / done"," ","DONE"],["-","cancelled"," ","CANCELLED"],[">","deferred","x","TODO"],["/","in progress, or half-done","x","IN_PROGRESS"],["!","Important","x","TODO"],["?","question","x","TODO"],["R","review","x","TODO"],["+","Inbox / task that should be processed later","x","TODO"],["b","bookmark","x","TODO"],["B","brainstorm","x","TODO"],["D","deferred or scheduled","x","TODO"],["I","Info","x","TODO"],["i","idea","x","TODO"],["N","note","x","TODO"],["Q","quote","x","TODO"],["W","win / success / reward","x","TODO"],["P","pro","x","TODO"],["C","con","x","TODO"]]}function Pk(){return[[" ","To Do","x","TODO"],["/","In Progress","x","IN_PROGRESS"],["x","Done"," ","DONE"],["-","Cancelled"," ","CANCELLED"],[">","Rescheduled","x","TODO"],["<","Scheduled","x","TODO"],["!","Important","x","TODO"],["?","Question","x","TODO"],["i","Infomation","x","TODO"],["S","Amount","x","TODO"],["*","Star","x","TODO"],["b","Bookmark","x","TODO"],["\u201C","Quote","x","TODO"],["n","Note","x","TODO"],["l","Location","x","TODO"],["I","Idea","x","TODO"],["p","Pro","x","TODO"],["c","Con","x","TODO"],["u","Up","x","TODO"],["d","Down","x","TODO"]]}function Nk(){return[[" ","Unchecked","x","TODO"],["x","Checked"," ","DONE"],["-","Cancelled"," ","CANCELLED"],["/","In Progress","x","IN_PROGRESS"],[">","Deferred","x","TODO"],["!","Important","x","TODO"],["?","Question","x","TODO"],["r","Review","x","TODO"]]}function Ik(){return[[" ","Unchecked","x","TODO"],["x","Regular"," ","DONE"],["X","Checked"," ","DONE"],["-","Dropped"," ","CANCELLED"],[">","Forward","x","TODO"],["D","Date","x","TODO"],["?","Question","x","TODO"],["/","Half Done","x","IN_PROGRESS"],["+","Add","x","TODO"],["R","Research","x","TODO"],["!","Important","x","TODO"],["i","Idea","x","TODO"],["B","Brainstorm","x","TODO"],["P","Pro","x","TODO"],["C","Con","x","TODO"],["Q","Quote","x","TODO"],["N","Note","x","TODO"],["b","Bookmark","x","TODO"],["I","Information","x","TODO"],["p","Paraphrase","x","TODO"],["L","Location","x","TODO"],["E","Example","x","TODO"],["A","Answer","x","TODO"],["r","Reward","x","TODO"],["c","Choice","x","TODO"],["d","Doing","x","IN_PROGRESS"],["T","Time","x","TODO"],["@","Character / Person","x","TODO"],["t","Talk","x","TODO"],["O","Outline / Plot","x","TODO"],["~","Conflict","x","TODO"],["W","World","x","TODO"],["f","Clue / Find","x","TODO"],["F","Foreshadow","x","TODO"],["H","Favorite / Health","x","TODO"],["&","Symbolism","x","TODO"],["s","Secret","x","TODO"]]}function Fk(){return[[" ","Unchecked","x","TODO"],["x","Checked"," ","DONE"],[">","Rescheduled","x","TODO"],["<","Scheduled","x","TODO"],["!","Important","x","TODO"],["-","Cancelled"," ","CANCELLED"],["/","In Progress","x","IN_PROGRESS"],["?","Question","x","TODO"],["*","Star","x","TODO"],["n","Note","x","TODO"],["l","Location","x","TODO"],["i","Information","x","TODO"],["I","Idea","x","TODO"],["S","Amount","x","TODO"],["p","Pro","x","TODO"],["c","Con","x","TODO"],["b","Bookmark","x","TODO"],["f","Fire","x","TODO"],["k","Key","x","TODO"],["w","Win","x","TODO"],["u","Up","x","TODO"],["d","Down","x","TODO"]]}function Lk(){return[[" ","to-do","x","TODO"],["/","incomplete","x","IN_PROGRESS"],["x","done"," ","DONE"],["-","canceled"," ","CANCELLED"],[">","forwarded","x","TODO"],["<","scheduling","x","TODO"],["?","question","x","TODO"],["!","important","x","TODO"],["*","star","x","TODO"],['"',"quote","x","TODO"],["l","location","x","TODO"],["b","bookmark","x","TODO"],["i","information","x","TODO"],["S","savings","x","TODO"],["I","idea","x","TODO"],["p","pros","x","TODO"],["c","cons","x","TODO"],["f","fire","x","TODO"],["k","key","x","TODO"],["w","win","x","TODO"],["u","up","x","TODO"],["d","down","x","TODO"]]}function Uk(){return[[" ","to-do","x","TODO"],["/","incomplete","x","IN_PROGRESS"],["x","done"," ","DONE"],["-","canceled"," ","CANCELLED"],[">","forwarded","x","TODO"],["<","scheduling","x","TODO"],["?","question","x","TODO"],["!","important","x","TODO"],["*","star","x","TODO"],['"',"quote","x","TODO"],["l","location","x","TODO"],["b","bookmark","x","TODO"],["i","information","x","TODO"],["S","savings","x","TODO"],["I","idea","x","TODO"],["p","pros","x","TODO"],["c","cons","x","TODO"],["f","fire","x","TODO"],["k","key","x","TODO"],["w","win","x","TODO"],["u","up","x","TODO"],["d","down","x","TODO"]]}var Wk=[{text:"Core Statuses",level:"h3",class:"",open:!0,notice:{class:"setting-item-description",text:null,html:"<p>These are the core statuses that Tasks supports natively, with no need for custom CSS styling or theming.</p><p>You can add edit and add your own custom statuses in the section below.</p>"},settings:[{name:"",description:"",type:"function",initialValue:"",placeholder:"",settingName:"insertTaskCoreStatusSettings",featureFlag:"",notice:null}]},{text:"Custom Statuses",level:"h3",class:"",open:!0,notice:{class:"setting-item-description",text:null,html:`<p>You should first <b>select and install a CSS Snippet or Theme</b> to style custom checkboxes.</p><p>Then, use the buttons below to set up your custom statuses, to match your chosen CSS checkboxes.</p><p><b>Note</b> Any statuses with the same symbol as any earlier statuses will be ignored. You can confirm the actually loaded statuses by running the 'Create or edit task' command and looking at the Status drop-down.</p><p></p><p>See the <a href="https://publish.obsidian.md/tasks/Getting+Started/Statuses">documentation</a> to get started!</p>`},settings:[{name:"",description:"",type:"function",initialValue:"",placeholder:"",settingName:"insertCustomTaskStatusSettings",featureFlag:"",notice:null}]}];var Ur=require("obsidian");var Gi=class{validate(e){let t=[];return t.push(...this.validateSymbol(e)),t.push(...this.validateName(e)),t.push(...this.validateNextSymbol(e)),t}validateStatusCollectionEntry(e){let[t,n,i,s]=e,a=[];if(a.push(...this.validateType(s)),t===i&&s!=="NON_TASK"&&a.push(`Status symbol '${t}' toggles to itself`),a.length>0)return a;let o=ne.createFromImportedValue(e).configuration;return a.push(...this.validateSymbolTypeConventions(o)),a.push(...this.validate(o)),a}validateSymbol(e){return Gi.validateOneSymbol(e.symbol,"Task Status Symbol")}validateNextSymbol(e){return Gi.validateOneSymbol(e.nextStatusSymbol,"Task Next Status Symbol")}validateName(e){let t=[];return e.name.length===0&&t.push("Task Status Name cannot be empty."),t}validateType(e){let t=Nt[e],n=[];return t||n.push(`Status Type "${e}" is not a valid type`),t=="EMPTY"&&n.push('Status Type "EMPTY" is not permitted in user data'),n}validateSymbolTypeConventions(e){let t=[],n=e.symbol,i=new De,s=n==="X"?"x":n,a=i.bySymbol(s);return a.type!=="EMPTY"&&(e.nextStatusSymbol!==a.nextStatusSymbol&&t.push(`Next Status Symbol for symbol '${n}': '${e.nextStatusSymbol}' is inconsistent with convention '${a.nextStatusSymbol}'`),e.type!==a.type&&t.push(`Status Type for symbol '${n}': '${e.type}' is inconsistent with convention '${a.type}'`)),t}static validateOneSymbol(e,t){let n=[];return e.length===0&&n.push(`${t} cannot be empty.`),e.length>1&&n.push(`${t} ("${e}") must be a single character.`),n}};var Yi=new Gi,rr=class extends Ur.Modal{constructor(t,n,i){super(t.app);this.plugin=t;this.saved=!1;this.error=!1;this.statusSymbol=n.symbol,this.statusName=n.name,this.statusNextSymbol=n.nextStatusSymbol,this.statusAvailableAsCommand=n.availableAsCommand,this.type=n.type,this.isCoreStatus=i}statusConfiguration(){return new Ke(this.statusSymbol,this.statusName,this.statusNextSymbol,this.statusAvailableAsCommand,this.type)}display(){return P(this,null,function*(){let{contentEl:t}=this;t.empty();let n=t.createDiv(),i;new Ur.Setting(n).setName("Task Status Symbol").setDesc("This is the character between the square braces. (It can only be edited for Custom statuses, and not Core statuses.)").addText(l=>{i=l,l.setValue(this.statusSymbol).onChange(c=>{this.statusSymbol=c,rr.setValid(l,Yi.validateSymbol(this.statusConfiguration()))})}).setDisabled(this.isCoreStatus).then(l=>{rr.setValid(i,Yi.validateSymbol(this.statusConfiguration()))});let s;new Ur.Setting(n).setName("Task Status Name").setDesc("This is the friendly name of the task status.").addText(l=>{s=l,l.setValue(this.statusName).onChange(c=>{this.statusName=c,rr.setValid(l,Yi.validateName(this.statusConfiguration()))})}).then(l=>{rr.setValid(s,Yi.validateName(this.statusConfiguration()))});let a;new Ur.Setting(n).setName("Task Next Status Symbol").setDesc("When clicked on this is the symbol that should be used next.").addText(l=>{a=l,l.setValue(this.statusNextSymbol).onChange(c=>{this.statusNextSymbol=c,rr.setValid(l,Yi.validateNextSymbol(this.statusConfiguration()))})}).then(l=>{rr.setValid(a,Yi.validateNextSymbol(this.statusConfiguration()))}),new Ur.Setting(n).setName("Task Status Type").setDesc("Control how the status behaves for searching and toggling.").addDropdown(l=>{["TODO","IN_PROGRESS","DONE","CANCELLED","NON_TASK"].forEach(d=>{l.addOption(d,d)}),l.setValue(this.type).onChange(d=>{this.type=ne.getTypeFromStatusTypeString(d)})}),ne.tasksPluginCanCreateCommandsForStatuses()&&new Ur.Setting(n).setName("Available as command").setDesc("If enabled this status will be available as a command so you can assign a hotkey and toggle the status using it.").addToggle(l=>{l.setValue(this.statusAvailableAsCommand).onChange(c=>P(this,null,function*(){this.statusAvailableAsCommand=c}))});let o=t.createDiv(),u=new Ur.Setting(o);u.addButton(l=>(l.setTooltip("Save").setIcon("checkmark").onClick(()=>P(this,null,function*(){let c=Yi.validate(this.statusConfiguration());if(c.length>0){let d=c.join(`
`)+`

Fix errors before saving.`;new Ur.Notice(d);return}this.saved=!0,this.close()})),l)),u.addExtraButton(l=>(l.setIcon("cross").setTooltip("Cancel").onClick(()=>{this.saved=!1,this.close()}),l))})}onOpen(){this.display()}static setValidationError(t){t.inputEl.addClass("tasks-settings-is-invalid")}static removeValidationError(t){t.inputEl.removeClass("tasks-settings-is-invalid")}static setValid(t,n){n.length===0?rr.removeValidationError(t):rr.setValidationError(t)}};var Rt=class extends ve.PluginSettingTab{constructor({plugin:t}){super(t.app,t);this.customFunctions={insertTaskCoreStatusSettings:this.insertTaskCoreStatusSettings.bind(this),insertCustomTaskStatusSettings:this.insertCustomTaskStatusSettings.bind(this)};this.plugin=t}saveSettings(t){return P(this,null,function*(){yield this.plugin.saveSettings(),t&&this.display()})}display(){let{containerEl:t}=this;t.empty(),this.containerEl.addClass("tasks-settings"),t.createEl("h3",{text:"Tasks Settings"}),t.createEl("p",{cls:"tasks-setting-important",text:"Changing any settings requires a restart of obsidian."}),t.createEl("h4",{text:"Task Format Settings"}),new ve.Setting(t).setName("Task Format").setDesc(Rt.createFragmentWithHTML('<p>The format that Tasks uses to read and write tasks.</p><p><b>Important:</b> Tasks currently only supports one format at a time. Selecting Dataview will currently <b>stop Tasks reading its own emoji signifiers</b>.</p><p>See the <a href="https://publish.obsidian.md/tasks/Reference/Task+Formats/About+Task+Formats">documentation</a>.</p>')).addDropdown(i=>{for(let s of Object.keys(Mr))i.addOption(s,Mr[s].displayName);i.setValue(X().taskFormat).onChange(s=>P(this,null,function*(){Ve({taskFormat:s}),yield this.plugin.saveSettings()}))}),t.createEl("h4",{text:"Global filter Settings"}),new ve.Setting(t).setName("Global task filter").setDesc(Rt.createFragmentWithHTML('<p><b>Recommended: Leave empty if you want all checklist items in your vault to be tasks managed by this plugin.</b></p><p>Use a global filter if you want Tasks to only act on a subset of your "<code>- [ ]</code>" checklist items, so that a checklist item must include the specified string in its description in order to be considered a task.<p><p>For example, if you set the global filter to <code>#task</code>, the Tasks plugin will only handle checklist items tagged with <code>#task</code>.</br>Other checklist items will remain normal checklist items and not appear in queries or get a done date set.</p><p>See the <a href="https://publish.obsidian.md/tasks/Getting+Started/Global+Filter">documentation</a>.</p>')).addText(i=>{i.setPlaceholder("e.g. #task or TODO").setValue(_e.getInstance().get()).onChange(s=>P(this,null,function*(){Ve({globalFilter:s}),_e.getInstance().set(s),yield this.plugin.saveSettings()}))}),new ve.Setting(t).setName("Remove global filter from description").setDesc("Enabling this removes the string that you set as global filter from the task description when displaying a task.").addToggle(i=>{let s=X();i.setValue(s.removeGlobalFilter).onChange(a=>P(this,null,function*(){Ve({removeGlobalFilter:a}),_e.getInstance().setRemoveGlobalFilter(a),yield this.plugin.saveSettings()}))}),t.createEl("h4",{text:"Global Query"}),QF(new ve.Setting(t).setDesc(Rt.createFragmentWithHTML('<p>A query that is automatically included at the start of every Tasks block in the vault. Useful for adding default filters, or layout options.</p><p>See the <a href="https://publish.obsidian.md/tasks/Queries/Global+Query">documentation</a>.</p>')).addTextArea(i=>{let s=X();i.inputEl.rows=4,i.setPlaceholder(`# For example...
path does not include _templates/
limit 300
show urgency`).setValue(s.globalQuery).onChange(a=>P(this,null,function*(){Ve({globalQuery:a}),tr.getInstance().set(a),yield this.plugin.saveSettings()}))})),t.createEl("h4",{text:"Task Statuses"});let{headingOpened:n}=X();Wk.forEach(i=>{this.addOneSettingsBlock(t,i,n)}),t.createEl("h4",{text:"Date Settings"}),new ve.Setting(t).setName("Set created date on every added task").setDesc(Rt.createFragmentWithHTML(`Enabling this will add a timestamp \u2795 YYYY-MM-DD before other date values, when a task is created with 'Create or edit task', or by completing a recurring task.</br><p>See the <a href="https://publish.obsidian.md/tasks/Getting+Started/Dates#Created+date">documentation</a>.</p>`)).addToggle(i=>{let s=X();i.setValue(s.setCreatedDate).onChange(a=>P(this,null,function*(){Ve({setCreatedDate:a}),yield this.plugin.saveSettings()}))}),new ve.Setting(t).setName("Set done date on every completed task").setDesc(Rt.createFragmentWithHTML('Enabling this will add a timestamp \u2705 YYYY-MM-DD at the end when a task is toggled to done.</br><p>See the <a href="https://publish.obsidian.md/tasks/Getting+Started/Dates#Done+date">documentation</a>.</p>')).addToggle(i=>{let s=X();i.setValue(s.setDoneDate).onChange(a=>P(this,null,function*(){Ve({setDoneDate:a}),yield this.plugin.saveSettings()}))}),new ve.Setting(t).setName("Set cancelled date on every cancelled task").setDesc(Rt.createFragmentWithHTML('Enabling this will add a timestamp \u274C YYYY-MM-DD at the end when a task is toggled to cancelled.</br><p>See the <a href="https://publish.obsidian.md/tasks/Getting+Started/Dates#Cancelled+date">documentation</a>.</p>')).addToggle(i=>{let s=X();i.setValue(s.setCancelledDate).onChange(a=>P(this,null,function*(){Ve({setCancelledDate:a}),yield this.plugin.saveSettings()}))}),new ve.Setting(t).setName("Use filename as Scheduled date for undated tasks").setDesc(Rt.createFragmentWithHTML('Save time entering Scheduled (\u23F3) dates.</br>If this option is enabled, any undated tasks will be given a default Scheduled date extracted from their file name.</br>By default, Tasks plugin will match both <code>YYYY-MM-DD</code> and <code>YYYYMMDD</code> date formats.</br>Undated tasks have none of Due (\u{1F4C5} ), Scheduled (\u23F3) and Start (\u{1F6EB}) dates.</br><p>See the <a href="https://publish.obsidian.md/tasks/Getting+Started/Use+Filename+as+Default+Date">documentation</a>.</p>')).addToggle(i=>{let s=X();i.setValue(s.useFilenameAsScheduledDate).onChange(a=>P(this,null,function*(){Ve({useFilenameAsScheduledDate:a}),yield this.plugin.saveSettings()}))}),new ve.Setting(t).setName("Additional filename date format as Scheduled date for undated tasks").setDesc(Rt.createFragmentWithHTML('An additional date format that Tasks plugin will recogize when using the file name as the Scheduled date for undated tasks.</br><p><a href="https://momentjs.com/docs/#/displaying/format/">Syntax Reference</a></p>')).addText(i=>{let s=X();i.setPlaceholder("example: MMM DD YYYY").setValue(s.filenameAsScheduledDateFormat).onChange(a=>P(this,null,function*(){Ve({filenameAsScheduledDateFormat:a}),yield this.plugin.saveSettings()}))}),new ve.Setting(t).setName("Folders with default Scheduled dates").setDesc("Leave empty if you want to use default Scheduled dates everywhere, or enter a comma-separated list of folders.").addText(i=>P(this,null,function*(){let s=X();yield this.plugin.saveSettings(),i.setValue(Rt.renderFolderArray(s.filenameAsDateFolders)).onChange(a=>P(this,null,function*(){let o=Rt.parseCommaSeparatedFolders(a);Ve({filenameAsDateFolders:o}),yield this.plugin.saveSettings()}))})),t.createEl("h4",{text:"Recurring task Settings"}),new ve.Setting(t).setName("Next recurrence appears on the line below").setDesc(Rt.createFragmentWithHTML('Enabling this will make the next recurrence of a task appear on the line below the completed task. Otherwise the next recurrence will appear before the completed one.</br><p>See the <a href="https://publish.obsidian.md/tasks/Getting+Started/Recurring+Tasks">documentation</a>.</p>')).addToggle(i=>{let{recurrenceOnNextLine:s}=X();i.setValue(s).onChange(a=>P(this,null,function*(){Ve({recurrenceOnNextLine:a}),yield this.plugin.saveSettings()}))}),t.createEl("h4",{text:"Auto-suggest Settings"}),new ve.Setting(t).setName("Auto-suggest task content").setDesc(Rt.createFragmentWithHTML('Enabling this will open an intelligent suggest menu while typing inside a recognized task line.</br><p>See the <a href="https://publish.obsidian.md/tasks/Getting+Started/Auto-Suggest">documentation</a>.</p>')).addToggle(i=>{let s=X();i.setValue(s.autoSuggestInEditor).onChange(a=>P(this,null,function*(){Ve({autoSuggestInEditor:a}),yield this.plugin.saveSettings()}))}),new ve.Setting(t).setName("Minimum match length for auto-suggest").setDesc("If higher than 0, auto-suggest will be triggered only when the beginning of any supported keywords is recognized.").addSlider(i=>{let s=X();i.setLimits(0,3,1).setValue(s.autoSuggestMinMatch).setDynamicTooltip().onChange(a=>P(this,null,function*(){Ve({autoSuggestMinMatch:a}),yield this.plugin.saveSettings()}))}),new ve.Setting(t).setName("Maximum number of auto-suggestions to show").setDesc('How many suggestions should be shown when an auto-suggest menu pops up (including the "\u23CE" option).').addSlider(i=>{let s=X();i.setLimits(3,20,1).setValue(s.autoSuggestMaxItems).setDynamicTooltip().onChange(a=>P(this,null,function*(){Ve({autoSuggestMaxItems:a}),yield this.plugin.saveSettings()}))}),t.createEl("h4",{text:"Dialog Settings"}),new ve.Setting(t).setName("Provide access keys in dialogs").setDesc(Rt.createFragmentWithHTML('If the access keys (keyboard shortcuts) for various controls in dialog boxes conflict with system keyboard shortcuts or assistive technology functionality that is important for you, you may want to deactivate them here.</br><p>See the <a href="https://publish.obsidian.md/tasks/Getting+Started/Create+or+edit+Task#Keyboard+shortcuts">documentation</a>.</p>')).addToggle(i=>{let s=X();i.setValue(s.provideAccessKeys).onChange(a=>P(this,null,function*(){Ve({provideAccessKeys:a}),yield this.plugin.saveSettings()}))})}addOneSettingsBlock(t,n,i){let s=t.createEl("details",{cls:"tasks-nested-settings",attr:K({},n.open||i[n.text]?{open:!0}:{})});s.empty(),s.ontoggle=()=>{i[n.text]=s.open,Ve({headingOpened:i}),this.plugin.saveSettings()};let a=s.createEl("summary");if(new ve.Setting(a).setHeading().setName(n.text),a.createDiv("collapser").createDiv("handle"),n.notice!==null){let o=s.createEl("div",{cls:n.notice.class,text:n.notice.text});n.notice.html!==null&&o.insertAdjacentHTML("beforeend",n.notice.html)}n.settings.forEach(o=>{if(!(o.featureFlag!==""&&!Sv(o.featureFlag))&&(o.type==="checkbox"?new ve.Setting(s).setName(o.name).setDesc(o.description).addToggle(u=>{let l=X();l.generalSettings[o.settingName]||xi(o.settingName,o.initialValue),u.setValue(l.generalSettings[o.settingName]).onChange(c=>P(this,null,function*(){xi(o.settingName,c),yield this.plugin.saveSettings()}))}):o.type==="text"?new ve.Setting(s).setName(o.name).setDesc(o.description).addText(u=>{let l=X();l.generalSettings[o.settingName]||xi(o.settingName,o.initialValue);let c=d=>P(this,null,function*(){xi(o.settingName,d),yield this.plugin.saveSettings()});u.setPlaceholder(o.placeholder.toString()).setValue(l.generalSettings[o.settingName].toString()).onChange((0,ve.debounce)(c,500,!0))}):o.type==="textarea"?new ve.Setting(s).setName(o.name).setDesc(o.description).addTextArea(u=>{let l=X();l.generalSettings[o.settingName]||xi(o.settingName,o.initialValue);let c=d=>P(this,null,function*(){xi(o.settingName,d),yield this.plugin.saveSettings()});u.setPlaceholder(o.placeholder.toString()).setValue(l.generalSettings[o.settingName].toString()).onChange((0,ve.debounce)(c,500,!0)),u.inputEl.rows=8,u.inputEl.cols=40}):o.type==="function"&&this.customFunctions[o.settingName](s,this),o.notice!==null)){let u=s.createEl("p",{cls:o.notice.class,text:o.notice.text});o.notice.html!==null&&u.insertAdjacentHTML("beforeend",o.notice.html)}})}static parseCommaSeparatedFolders(t){return t.split(",").map(n=>n.trim()).map(n=>n.replace(/^\/|\/$/g,"")).filter(n=>n!=="")}static renderFolderArray(t){return t.join(",")}insertTaskCoreStatusSettings(t,n){let{statusSettings:i}=X();i.coreStatuses.forEach(a=>{qk(t,a,i.coreStatuses,i,n,n.plugin,!0)}),new ve.Setting(t).addButton(a=>{let o="Review and check your Statuses";a.setButtonText(o).setCta().onClick(()=>P(this,null,function*(){let l=window.moment().format("YYYY-MM-DD HH-mm-ss"),c=`Tasks Plugin - ${o} ${l}.md`,d=this.plugin.manifest.version,p=De.getInstance(),m=Mk(i,p,o,d),y=yield app.vault.create(c,m);yield this.app.workspace.getLeaf(!0).openFile(y)})),a.setTooltip("Create a new file in the root of the vault, containing a Mermaid diagram of the current status settings.")}).infoEl.remove()}insertCustomTaskStatusSettings(t,n){let{statusSettings:i}=X();i.customStatuses.forEach(l=>{qk(t,l,i.customStatuses,i,n,n.plugin,!1)}),t.createEl("div"),new ve.Setting(t).addButton(l=>{l.setButtonText("Add New Task Status").setCta().onClick(()=>P(this,null,function*(){Le.addStatus(i.customStatuses,new Ke("","","",!1,"TODO")),yield Xs(i,n)}))}).infoEl.remove();let a=[["AnuPpuccin Theme",Ck()],["Aura Theme",Ak()],["Border Theme",Pk()],["Ebullientworks Theme",Nk()],["ITS Theme & SlRvb Checkboxes",Ik()],["Minimal Theme",Lk()],["Things Theme",Uk()],["LYT Mode Theme (Dark mode only)",Fk()]];for(let[l,c]of a)new ve.Setting(t).addButton(p=>{let m=`${l}: Add ${c.length} supported Statuses`;p.setButtonText(m).onClick(()=>P(this,null,function*(){yield KF(c,i,n)}))}).infoEl.remove();new ve.Setting(t).addButton(l=>{l.setButtonText("Add All Unknown Status Types").setCta().onClick(()=>P(this,null,function*(){let d=this.plugin.getTasks().map(m=>m.status),p=De.getInstance().findUnknownStatuses(d);p.length!==0&&(p.forEach(m=>{Le.addStatus(i.customStatuses,m)}),yield Xs(i,n))}))}).infoEl.remove(),new ve.Setting(t).addButton(l=>{l.setButtonText("Reset Custom Status Types to Defaults").setWarning().onClick(()=>P(this,null,function*(){Le.resetAllCustomStatuses(i),yield Xs(i,n)}))}).infoEl.remove()}},Go=Rt;Go.createFragmentWithHTML=t=>createFragment(n=>n.createDiv().innerHTML=t);function qk(r,e,t,n,i,s,a){let o=r.createEl("pre");o.addClass("row-for-status"),o.textContent=new ne(e).previewText();let u=new ve.Setting(r);u.infoEl.replaceWith(o),a||u.addExtraButton(l=>{l.setIcon("cross").setTooltip("Delete").onClick(()=>P(this,null,function*(){Le.deleteStatus(t,e)&&(yield Xs(n,i))}))}),u.addExtraButton(l=>{l.setIcon("pencil").setTooltip("Edit").onClick(()=>P(this,null,function*(){let c=new rr(s,e,a);c.onClose=()=>P(this,null,function*(){c.saved&&Le.replaceStatus(t,e,c.statusConfiguration())&&(yield Xs(n,i))}),c.open()}))}),u.infoEl.remove()}function KF(r,e,t){return P(this,null,function*(){Le.bulkAddStatusCollection(e,r).forEach(i=>{new ve.Notice(i)}),yield Xs(e,t)})}function Xs(r,e){return P(this,null,function*(){Ve({statusSettings:r}),Le.applyToStatusRegistry(r,De.getInstance()),yield e.saveSettings(!0)})}function QF(r){let{settingEl:e,infoEl:t,controlEl:n}=r,i=n.querySelector("textarea");i!==null&&(e.style.display="block",t.style.marginRight="0px",i.style.minWidth="-webkit-fill-available")}var ai=require("obsidian");function XF(r){console.error(r),new ai.Notice(r+`

This message has been written to the console.
`,1e4)}var dc=class extends ai.EditorSuggest{constructor(t,n,i){super(t);this.settings=n,this.plugin=i,t.scope.register([],"Tab",()=>{var a;let s=(a=this.context)==null?void 0:a.editor;return s?(s.exec("indentMore"),!1):!0})}onTrigger(t,n,i){if(!this.settings.autoSuggestInEditor)return null;let s=n.getLine(t.line);return kv(s,t,n)?{start:{line:t.line,ch:0},end:{line:t.line,ch:s.length},query:s}:null}getSuggestions(t){var c,d,p;let n=t.query,i=t.editor.getCursor(),s=this.plugin.getTasks(),a=s.find(m=>m.taskLocation.path==t.file.path&&m.taskLocation.lineNumber==i.line),o=this.getMarkdownFileInfo(t),u=this.canSaveEdits(o);return((p=(d=(c=_o()).buildSuggestions)==null?void 0:d.call(c,n,i.ch,this.settings,s,u,a))!=null?p:[]).map(m=>he(K({},m),{context:t}))}getMarkdownFileInfo(t){return t.editor.cm.state.field(ai.editorInfoField)}canSaveEdits(t){return t instanceof ai.MarkdownView}renderSuggestion(t,n){n.setText(t.displayText)}selectSuggestion(t,n){return P(this,null,function*(){var l,c,d;let i=t.context.editor;if(t.suggestionType==="empty"){this.close();let p=new KeyboardEvent("keydown",{code:"Enter",key:"Enter"});(c=(l=i==null?void 0:i.cm)==null?void 0:l.contentDOM)==null||c.dispatchEvent(p);return}if(t.taskItDependsOn!=null){let p=Zu(t.taskItDependsOn,this.plugin.getTasks().map(m=>m.id));if(t.appendText+=` ${p.id}`,t.taskItDependsOn!==p)if(t.context.file.path==p.path){let m=t.taskItDependsOn.originalMarkdown,y={line:t.taskItDependsOn.lineNumber,ch:0},_={line:t.taskItDependsOn.lineNumber,ch:m.length},b=t.context.editor.getRange(y,_);if(b!==m){let E=`Error adding new ID, due to mismatched data in Tasks memory and the editor:
task line in memory: '${t.taskItDependsOn.originalMarkdown}'

task line in editor: '${b}'

file: '${p.path}'
`;XF(E);return}t.context.editor.replaceRange(p.toFileLineString(),y,_)}else br({originalTask:t.taskItDependsOn,newTasks:p})}let s=t.context.editor.getCursor(),a={line:s.line,ch:(d=t.insertAt)!=null?d:s.ch},o=t.insertSkip?{line:s.line,ch:a.ch+t.insertSkip}:void 0;t.context.editor.replaceRange(t.appendText,a,o),t.context.editor.setCursor({line:s.line,ch:a.ch+t.appendText.length});let u=this.getMarkdownFileInfo(t.context);this.canSaveEdits(u)&&(yield u.save())})}};var $k=(r,e)=>{let t,n=new Promise((a,o)=>{t=a});return e(r,a=>{let o=a.map(u=>u.toFileLineString()).join(`
`);t(o)}).open(),n};var jk=(r,e)=>{let t=Tl({line:"",path:""});return new Kn({app:r,task:t,onSubmit:e,allTasks:[]})};var Gk=r=>({createTaskLineModal:()=>$k(r,jk),executeToggleTaskDoneCommand:(e,t)=>lh(e,t).text});var fc=class extends Yk.Plugin{get apiV1(){return Gk(app)}onload(){return P(this,null,function*(){St.registerConsoleLogger(),Um("info",`loading plugin "${this.manifest.name}" v${this.manifest.version}`),yield this.loadSettings();let{loggingOptions:t}=X();St.configure(t),this.addSettingTab(new Go({plugin:this})),yw({metadataCache:this.app.metadataCache,vault:this.app.vault,workspace:this.app.workspace}),yield this.loadTaskStatuses();let n=new tc({obsidianEvents:this.app.workspace});this.cache=new Ts({metadataCache:this.app.metadataCache,vault:this.app.vault,events:n}),this.inlineRenderer=new ic({plugin:this}),this.queryRenderer=new uc({plugin:this,events:n}),this.registerEditorExtension(mk()),this.registerEditorSuggest(new dc(this.app,X(),this)),new _l({plugin:this})})}loadTaskStatuses(){return P(this,null,function*(){let{statusSettings:t}=X();Le.applyToStatusRegistry(t,De.getInstance())})}onunload(){var t;Um("info",`unloading plugin "${this.manifest.name}" v${this.manifest.version}`),(t=this.cache)==null||t.unload()}loadSettings(){return P(this,null,function*(){let t=yield this.loadData();Ve(t),t=X(),_e.getInstance().set(t.globalFilter),_e.getInstance().setRemoveGlobalFilter(t.removeGlobalFilter),tr.getInstance().set(t.globalQuery),yield this.loadTaskStatuses()})}saveSettings(){return P(this,null,function*(){yield this.saveData(X())})}getTasks(){return this.cache===void 0?[]:this.cache.getTasks()}};
/*!
 * EventEmitter2
 * https://github.com/hij1nx/EventEmitter2
 *
 * Copyright (c) 2013 hij1nx
 * Licensed under the MIT license.
 */
/*!
 * mustache.js - Logic-less {{mustache}} templates with JavaScript
 * http://github.com/janl/mustache.js
 */
