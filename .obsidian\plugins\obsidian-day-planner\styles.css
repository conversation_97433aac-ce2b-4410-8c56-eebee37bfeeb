/* src/styles.scss */
.progress-pie {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: #eee;
  background-image: linear-gradient(to right, transparent 50%, #4CC9D8 0);
  position: relative;
  display: inline-block;
}
.progress-pie::before {
  content: "";
  display: block;
  margin-left: 50%;
  height: 100%;
  border-radius: 0 100% 100% 0/50%;
  background-color: inherit;
  transform-origin: left;
}
.progress-pie::after {
  content: attr(data-value);
  position: absolute;
  width: 70%;
  height: 70%;
  margin: auto;
  border-radius: 50%;
  background-color: #fff;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  text-align: center;
  font: 900 20px/41px Tahoma;
}
.progress-pie[data-value="0"]:before {
  transform: rotate(0turn);
}
.progress-pie[data-value="1"]:before {
  transform: rotate(0.01turn);
}
.progress-pie[data-value="2"]:before {
  transform: rotate(0.02turn);
}
.progress-pie[data-value="3"]:before {
  transform: rotate(0.03turn);
}
.progress-pie[data-value="4"]:before {
  transform: rotate(0.04turn);
}
.progress-pie[data-value="5"]:before {
  transform: rotate(0.05turn);
}
.progress-pie[data-value="6"]:before {
  transform: rotate(0.06turn);
}
.progress-pie[data-value="7"]:before {
  transform: rotate(0.07turn);
}
.progress-pie[data-value="8"]:before {
  transform: rotate(0.08turn);
}
.progress-pie[data-value="9"]:before {
  transform: rotate(0.09turn);
}
.progress-pie[data-value="10"]:before {
  transform: rotate(0.1turn);
}
.progress-pie[data-value="11"]:before {
  transform: rotate(0.11turn);
}
.progress-pie[data-value="12"]:before {
  transform: rotate(0.12turn);
}
.progress-pie[data-value="13"]:before {
  transform: rotate(0.13turn);
}
.progress-pie[data-value="14"]:before {
  transform: rotate(0.14turn);
}
.progress-pie[data-value="15"]:before {
  transform: rotate(0.15turn);
}
.progress-pie[data-value="16"]:before {
  transform: rotate(0.16turn);
}
.progress-pie[data-value="17"]:before {
  transform: rotate(0.17turn);
}
.progress-pie[data-value="18"]:before {
  transform: rotate(0.18turn);
}
.progress-pie[data-value="19"]:before {
  transform: rotate(0.19turn);
}
.progress-pie[data-value="20"]:before {
  transform: rotate(0.2turn);
}
.progress-pie[data-value="21"]:before {
  transform: rotate(0.21turn);
}
.progress-pie[data-value="22"]:before {
  transform: rotate(0.22turn);
}
.progress-pie[data-value="23"]:before {
  transform: rotate(0.23turn);
}
.progress-pie[data-value="24"]:before {
  transform: rotate(0.24turn);
}
.progress-pie[data-value="25"]:before {
  transform: rotate(0.25turn);
}
.progress-pie[data-value="26"]:before {
  transform: rotate(0.26turn);
}
.progress-pie[data-value="27"]:before {
  transform: rotate(0.27turn);
}
.progress-pie[data-value="28"]:before {
  transform: rotate(0.28turn);
}
.progress-pie[data-value="29"]:before {
  transform: rotate(0.29turn);
}
.progress-pie[data-value="30"]:before {
  transform: rotate(0.3turn);
}
.progress-pie[data-value="31"]:before {
  transform: rotate(0.31turn);
}
.progress-pie[data-value="32"]:before {
  transform: rotate(0.32turn);
}
.progress-pie[data-value="33"]:before {
  transform: rotate(0.33turn);
}
.progress-pie[data-value="34"]:before {
  transform: rotate(0.34turn);
}
.progress-pie[data-value="35"]:before {
  transform: rotate(0.35turn);
}
.progress-pie[data-value="36"]:before {
  transform: rotate(0.36turn);
}
.progress-pie[data-value="37"]:before {
  transform: rotate(0.37turn);
}
.progress-pie[data-value="38"]:before {
  transform: rotate(0.38turn);
}
.progress-pie[data-value="39"]:before {
  transform: rotate(0.39turn);
}
.progress-pie[data-value="40"]:before {
  transform: rotate(0.4turn);
}
.progress-pie[data-value="41"]:before {
  transform: rotate(0.41turn);
}
.progress-pie[data-value="42"]:before {
  transform: rotate(0.42turn);
}
.progress-pie[data-value="43"]:before {
  transform: rotate(0.43turn);
}
.progress-pie[data-value="44"]:before {
  transform: rotate(0.44turn);
}
.progress-pie[data-value="45"]:before {
  transform: rotate(0.45turn);
}
.progress-pie[data-value="46"]:before {
  transform: rotate(0.46turn);
}
.progress-pie[data-value="47"]:before {
  transform: rotate(0.47turn);
}
.progress-pie[data-value="48"]:before {
  transform: rotate(0.48turn);
}
.progress-pie[data-value="49"]:before {
  transform: rotate(0.49turn);
}
.progress-pie[data-value="50"]:before {
  transform: rotate(0.5turn);
}
.progress-pie[data-value="51"]:before {
  background-color: #4CC9D8;
  transform: rotate(0.01turn);
}
.progress-pie[data-value="52"]:before {
  background-color: #4CC9D8;
  transform: rotate(0.02turn);
}
.progress-pie[data-value="53"]:before {
  background-color: #4CC9D8;
  transform: rotate(0.03turn);
}
.progress-pie[data-value="54"]:before {
  background-color: #4CC9D8;
  transform: rotate(0.04turn);
}
.progress-pie[data-value="55"]:before {
  background-color: #4CC9D8;
  transform: rotate(0.05turn);
}
.progress-pie[data-value="56"]:before {
  background-color: #4CC9D8;
  transform: rotate(0.06turn);
}
.progress-pie[data-value="57"]:before {
  background-color: #4CC9D8;
  transform: rotate(0.07turn);
}
.progress-pie[data-value="58"]:before {
  background-color: #4CC9D8;
  transform: rotate(0.08turn);
}
.progress-pie[data-value="59"]:before {
  background-color: #4CC9D8;
  transform: rotate(0.09turn);
}
.progress-pie[data-value="60"]:before {
  background-color: #4CC9D8;
  transform: rotate(0.1turn);
}
.progress-pie[data-value="61"]:before {
  background-color: #4CC9D8;
  transform: rotate(0.11turn);
}
.progress-pie[data-value="62"]:before {
  background-color: #4CC9D8;
  transform: rotate(0.12turn);
}
.progress-pie[data-value="63"]:before {
  background-color: #4CC9D8;
  transform: rotate(0.13turn);
}
.progress-pie[data-value="64"]:before {
  background-color: #4CC9D8;
  transform: rotate(0.14turn);
}
.progress-pie[data-value="65"]:before {
  background-color: #4CC9D8;
  transform: rotate(0.15turn);
}
.progress-pie[data-value="66"]:before {
  background-color: #4CC9D8;
  transform: rotate(0.16turn);
}
.progress-pie[data-value="67"]:before {
  background-color: #4CC9D8;
  transform: rotate(0.17turn);
}
.progress-pie[data-value="68"]:before {
  background-color: #4CC9D8;
  transform: rotate(0.18turn);
}
.progress-pie[data-value="69"]:before {
  background-color: #4CC9D8;
  transform: rotate(0.19turn);
}
.progress-pie[data-value="70"]:before {
  background-color: #4CC9D8;
  transform: rotate(0.2turn);
}
.progress-pie[data-value="71"]:before {
  background-color: #4CC9D8;
  transform: rotate(0.21turn);
}
.progress-pie[data-value="72"]:before {
  background-color: #4CC9D8;
  transform: rotate(0.22turn);
}
.progress-pie[data-value="73"]:before {
  background-color: #4CC9D8;
  transform: rotate(0.23turn);
}
.progress-pie[data-value="74"]:before {
  background-color: #4CC9D8;
  transform: rotate(0.24turn);
}
.progress-pie[data-value="75"]:before {
  background-color: #4CC9D8;
  transform: rotate(0.25turn);
}
.progress-pie[data-value="76"]:before {
  background-color: #4CC9D8;
  transform: rotate(0.26turn);
}
.progress-pie[data-value="77"]:before {
  background-color: #4CC9D8;
  transform: rotate(0.27turn);
}
.progress-pie[data-value="78"]:before {
  background-color: #4CC9D8;
  transform: rotate(0.28turn);
}
.progress-pie[data-value="79"]:before {
  background-color: #4CC9D8;
  transform: rotate(0.29turn);
}
.progress-pie[data-value="80"]:before {
  background-color: #4CC9D8;
  transform: rotate(0.3turn);
}
.progress-pie[data-value="81"]:before {
  background-color: #4CC9D8;
  transform: rotate(0.31turn);
}
.progress-pie[data-value="82"]:before {
  background-color: #4CC9D8;
  transform: rotate(0.32turn);
}
.progress-pie[data-value="83"]:before {
  background-color: #4CC9D8;
  transform: rotate(0.33turn);
}
.progress-pie[data-value="84"]:before {
  background-color: #4CC9D8;
  transform: rotate(0.34turn);
}
.progress-pie[data-value="85"]:before {
  background-color: #4CC9D8;
  transform: rotate(0.35turn);
}
.progress-pie[data-value="86"]:before {
  background-color: #4CC9D8;
  transform: rotate(0.36turn);
}
.progress-pie[data-value="87"]:before {
  background-color: #4CC9D8;
  transform: rotate(0.37turn);
}
.progress-pie[data-value="88"]:before {
  background-color: #4CC9D8;
  transform: rotate(0.38turn);
}
.progress-pie[data-value="89"]:before {
  background-color: #4CC9D8;
  transform: rotate(0.39turn);
}
.progress-pie[data-value="90"]:before {
  background-color: #4CC9D8;
  transform: rotate(0.4turn);
}
.progress-pie[data-value="91"]:before {
  background-color: #4CC9D8;
  transform: rotate(0.41turn);
}
.progress-pie[data-value="92"]:before {
  background-color: #4CC9D8;
  transform: rotate(0.42turn);
}
.progress-pie[data-value="93"]:before {
  background-color: #4CC9D8;
  transform: rotate(0.43turn);
}
.progress-pie[data-value="94"]:before {
  background-color: #4CC9D8;
  transform: rotate(0.44turn);
}
.progress-pie[data-value="95"]:before {
  background-color: #4CC9D8;
  transform: rotate(0.45turn);
}
.progress-pie[data-value="96"]:before {
  background-color: #4CC9D8;
  transform: rotate(0.46turn);
}
.progress-pie[data-value="97"]:before {
  background-color: #4CC9D8;
  transform: rotate(0.47turn);
}
.progress-pie[data-value="98"]:before {
  background-color: #4CC9D8;
  transform: rotate(0.48turn);
}
.progress-pie[data-value="99"]:before {
  background-color: #4CC9D8;
  transform: rotate(0.49turn);
}
.progress-pie[data-value="100"]:before {
  background-color: #4CC9D8;
  transform: rotate(0.5turn);
}
.os-size-observer,
.os-size-observer-listener {
  scroll-behavior: auto !important;
  direction: inherit;
  pointer-events: none;
  overflow: hidden;
  visibility: hidden;
  box-sizing: border-box;
}
.os-size-observer,
.os-size-observer-listener,
.os-size-observer-listener-item,
.os-size-observer-listener-item-final {
  writing-mode: horizontal-tb;
  position: absolute;
  left: 0;
  top: 0;
}
.os-size-observer {
  z-index: -1;
  contain: strict;
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  padding: inherit;
  border: inherit;
  box-sizing: inherit;
  margin: -133px;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  transform: scale(0.1);
}
.os-size-observer::before {
  content: "";
  flex: none;
  box-sizing: inherit;
  padding: 10px;
  width: 10px;
  height: 10px;
}
.os-size-observer-appear {
  animation: os-size-observer-appear-animation 1ms forwards;
}
.os-size-observer-listener {
  box-sizing: border-box;
  position: relative;
  flex: auto;
  padding: inherit;
  border: inherit;
  margin: -133px;
  transform: scale(10);
}
.os-size-observer-listener.ltr {
  margin-right: -266px;
  margin-left: 0;
}
.os-size-observer-listener.rtl {
  margin-left: -266px;
  margin-right: 0;
}
.os-size-observer-listener:empty::before {
  content: "";
  width: 100%;
  height: 100%;
}
.os-size-observer-listener:empty::before,
.os-size-observer-listener > .os-size-observer-listener-item {
  display: block;
  position: relative;
  padding: inherit;
  border: inherit;
  box-sizing: content-box;
  flex: auto;
}
.os-size-observer-listener-scroll {
  box-sizing: border-box;
  display: flex;
}
.os-size-observer-listener-item {
  right: 0;
  bottom: 0;
  overflow: hidden;
  direction: ltr;
  flex: none;
}
.os-size-observer-listener-item-final {
  transition: none;
}
@keyframes os-size-observer-appear-animation {
  from {
    cursor: auto;
  }
  to {
    cursor: none;
  }
}
.os-trinsic-observer {
  flex: none;
  box-sizing: border-box;
  position: relative;
  max-width: 0px;
  max-height: 1px;
  padding: 0;
  margin: 0;
  border: none;
  overflow: hidden;
  z-index: -1;
  height: 0;
  top: calc(100% + 1px);
  contain: strict;
}
.os-trinsic-observer:not(:empty) {
  height: calc(100% + 1px);
  top: -1px;
}
.os-trinsic-observer:not(:empty) > .os-size-observer {
  width: 1000%;
  height: 1000%;
  min-height: 1px;
  min-width: 1px;
}
[data-overlayscrollbars-initialize],
[data-overlayscrollbars-viewport~=scrollbarHidden] {
  scrollbar-width: none !important;
}
[data-overlayscrollbars-initialize]::-webkit-scrollbar,
[data-overlayscrollbars-initialize]::-webkit-scrollbar-corner,
[data-overlayscrollbars-viewport~=scrollbarHidden]::-webkit-scrollbar,
[data-overlayscrollbars-viewport~=scrollbarHidden]::-webkit-scrollbar-corner {
  -webkit-appearance: none !important;
  appearance: none !important;
  display: none !important;
  width: 0 !important;
  height: 0 !important;
}
[data-overlayscrollbars-initialize]:not([data-overlayscrollbars]):not(html):not(body) {
  overflow: auto;
}
html[data-overlayscrollbars-body] {
  overflow: hidden;
}
html[data-overlayscrollbars-body],
html[data-overlayscrollbars-body] > body {
  width: 100%;
  height: 100%;
  margin: 0;
}
html[data-overlayscrollbars-body] > body {
  overflow: visible;
  margin: 0;
}
[data-overlayscrollbars] {
  position: relative;
}
[data-overlayscrollbars~=host],
[data-overlayscrollbars-padding] {
  display: flex;
  align-items: stretch !important;
  flex-direction: row !important;
  flex-wrap: nowrap !important;
  scroll-behavior: auto !important;
}
[data-overlayscrollbars-padding],
[data-overlayscrollbars-viewport]:not([data-overlayscrollbars]) {
  box-sizing: inherit;
  position: relative;
  flex: auto !important;
  height: auto;
  width: 100%;
  min-width: 0;
  padding: 0;
  margin: 0;
  border: none;
  z-index: 0;
}
[data-overlayscrollbars-viewport]:not([data-overlayscrollbars]) {
  --os-vaw: 0;
  --os-vah: 0;
  outline: none;
}
[data-overlayscrollbars-viewport]:not([data-overlayscrollbars]):focus {
  outline: none;
}
[data-overlayscrollbars-viewport][data-overlayscrollbars-viewport~=arrange]::before {
  content: "";
  position: absolute;
  pointer-events: none;
  z-index: -1;
  min-width: 1px;
  min-height: 1px;
  width: var(--os-vaw);
  height: var(--os-vah);
}
[data-overlayscrollbars],
[data-overlayscrollbars-padding],
[data-overlayscrollbars-viewport] {
  overflow: hidden !important;
}
[data-overlayscrollbars~=noClipping],
[data-overlayscrollbars-padding~=noClipping] {
  overflow: visible !important;
}
[data-overlayscrollbars-viewport~=measuring] {
  overflow: hidden !important;
  scroll-behavior: auto !important;
  scroll-snap-type: none !important;
}
[data-overlayscrollbars-viewport~=overflowXVisible]:not([data-overlayscrollbars-viewport~=measuring]) {
  overflow-x: visible !important;
}
[data-overlayscrollbars-viewport~=overflowXHidden] {
  overflow-x: hidden !important;
}
[data-overlayscrollbars-viewport~=overflowXScroll] {
  overflow-x: scroll !important;
}
[data-overlayscrollbars-viewport~=overflowYVisible]:not([data-overlayscrollbars-viewport~=measuring]) {
  overflow-y: visible !important;
}
[data-overlayscrollbars-viewport~=overflowYHidden] {
  overflow-y: hidden !important;
}
[data-overlayscrollbars-viewport~=overflowYScroll] {
  overflow-y: scroll !important;
}
[data-overlayscrollbars-viewport~=noContent]:not(#osFakeId) {
  font-size: 0 !important;
  line-height: 0 !important;
}
[data-overlayscrollbars-viewport~=noContent]:not(#osFakeId)::before,
[data-overlayscrollbars-viewport~=noContent]:not(#osFakeId)::after,
[data-overlayscrollbars-viewport~=noContent]:not(#osFakeId) > * {
  display: none !important;
  position: absolute !important;
  width: 1px !important;
  height: 1px !important;
  padding: 0 !important;
  margin: -1px !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  white-space: nowrap !important;
  border-width: 0 !important;
}
[data-overlayscrollbars-viewport~=scrolling] {
  scroll-behavior: auto !important;
  scroll-snap-type: none !important;
}
[data-overlayscrollbars-content] {
  box-sizing: inherit;
}
[data-overlayscrollbars-contents]:not(#osFakeId):not([data-overlayscrollbars-padding]):not([data-overlayscrollbars-viewport]):not([data-overlayscrollbars-content]) {
  display: contents;
}
[data-overlayscrollbars-grid],
[data-overlayscrollbars-grid] [data-overlayscrollbars-padding] {
  display: grid;
  grid-template: 1fr/1fr;
}
[data-overlayscrollbars-grid] > [data-overlayscrollbars-padding],
[data-overlayscrollbars-grid] > [data-overlayscrollbars-viewport],
[data-overlayscrollbars-grid] > [data-overlayscrollbars-padding] > [data-overlayscrollbars-viewport] {
  height: auto !important;
  width: auto !important;
}
@property --os-scroll-percent { syntax: "<number>"; inherits: true; initial-value: 0; }
@property --os-viewport-percent { syntax: "<number>"; inherits: true; initial-value: 0; }
.os-scrollbar {
  --os-viewport-percent: 0;
  --os-scroll-percent: 0;
  --os-scroll-direction: 0;
  --os-scroll-percent-directional: calc( var(--os-scroll-percent) - (var(--os-scroll-percent) + (1 - var(--os-scroll-percent)) * -1) * var(--os-scroll-direction) );
}
.os-scrollbar {
  contain: size layout;
  contain: size layout style;
  transition:
    opacity 0.15s,
    visibility 0.15s,
    top 0.15s,
    right 0.15s,
    bottom 0.15s,
    left 0.15s;
  pointer-events: none;
  position: absolute;
  opacity: 0;
  visibility: hidden;
}
body > .os-scrollbar {
  position: fixed;
  z-index: 99999;
}
.os-scrollbar-transitionless {
  transition: none !important;
}
.os-scrollbar-track {
  position: relative;
  padding: 0 !important;
  border: none !important;
}
.os-scrollbar-handle {
  position: absolute;
}
.os-scrollbar-track,
.os-scrollbar-handle {
  pointer-events: none;
  width: 100%;
  height: 100%;
}
.os-scrollbar.os-scrollbar-track-interactive .os-scrollbar-track,
.os-scrollbar.os-scrollbar-handle-interactive .os-scrollbar-handle {
  pointer-events: auto;
  touch-action: none;
}
.os-scrollbar-horizontal {
  bottom: 0;
  left: 0;
}
.os-scrollbar-vertical {
  top: 0;
  right: 0;
}
.os-scrollbar-rtl.os-scrollbar-horizontal {
  right: 0;
}
.os-scrollbar-rtl.os-scrollbar-vertical {
  right: auto;
  left: 0;
}
.os-scrollbar-visible {
  opacity: 1;
  visibility: visible;
}
.os-scrollbar-auto-hide.os-scrollbar-auto-hide-hidden {
  opacity: 0;
  visibility: hidden;
}
.os-scrollbar-interaction.os-scrollbar-visible {
  opacity: 1;
  visibility: visible;
}
.os-scrollbar-unusable,
.os-scrollbar-unusable *,
.os-scrollbar-wheel,
.os-scrollbar-wheel * {
  pointer-events: none !important;
}
.os-scrollbar-unusable .os-scrollbar-handle {
  opacity: 0 !important;
  transition: none !important;
}
.os-scrollbar-horizontal .os-scrollbar-handle {
  bottom: 0;
  left: calc(var(--os-scroll-percent-directional) * 100%);
  transform: translateX(calc(var(--os-scroll-percent-directional) * -100%));
  width: calc(var(--os-viewport-percent) * 100%);
}
.os-scrollbar-vertical .os-scrollbar-handle {
  right: 0;
  top: calc(var(--os-scroll-percent-directional) * 100%);
  transform: translateY(calc(var(--os-scroll-percent-directional) * -100%));
  height: calc(var(--os-viewport-percent) * 100%);
}
@supports (container-type: size) {
  .os-scrollbar-track {
    container-type: size;
  }
  .os-scrollbar-horizontal .os-scrollbar-handle {
    left: auto;
    transform: translateX(calc(var(--os-scroll-percent-directional) * 100cqw + var(--os-scroll-percent-directional) * -100%));
  }
  .os-scrollbar-vertical .os-scrollbar-handle {
    top: auto;
    transform: translateY(calc(var(--os-scroll-percent-directional) * 100cqh + var(--os-scroll-percent-directional) * -100%));
  }
  .os-scrollbar-rtl.os-scrollbar-horizontal .os-scrollbar-handle {
    right: auto;
    left: 0;
  }
}
.os-scrollbar-rtl.os-scrollbar-vertical .os-scrollbar-handle {
  right: auto;
  left: 0;
}
.os-scrollbar.os-scrollbar-horizontal.os-scrollbar-cornerless,
.os-scrollbar.os-scrollbar-horizontal.os-scrollbar-cornerless.os-scrollbar-rtl {
  left: 0;
  right: 0;
}
.os-scrollbar.os-scrollbar-vertical.os-scrollbar-cornerless,
.os-scrollbar.os-scrollbar-vertical.os-scrollbar-cornerless.os-scrollbar-rtl {
  top: 0;
  bottom: 0;
}
@media print {
  .os-scrollbar {
    display: none;
  }
}
.os-scrollbar {
  --os-size: 0;
  --os-padding-perpendicular: 0;
  --os-padding-axis: 0;
  --os-track-border-radius: 0;
  --os-track-bg: none;
  --os-track-bg-hover: none;
  --os-track-bg-active: none;
  --os-track-border: none;
  --os-track-border-hover: none;
  --os-track-border-active: none;
  --os-handle-border-radius: 0;
  --os-handle-bg: none;
  --os-handle-bg-hover: none;
  --os-handle-bg-active: none;
  --os-handle-border: none;
  --os-handle-border-hover: none;
  --os-handle-border-active: none;
  --os-handle-min-size: 33px;
  --os-handle-max-size: none;
  --os-handle-perpendicular-size: 100%;
  --os-handle-perpendicular-size-hover: 100%;
  --os-handle-perpendicular-size-active: 100%;
  --os-handle-interactive-area-offset: 0;
}
.os-scrollbar-track {
  border: var(--os-track-border);
  border-radius: var(--os-track-border-radius);
  background: var(--os-track-bg);
  transition:
    opacity 0.15s,
    background-color 0.15s,
    border-color 0.15s;
}
.os-scrollbar-track:hover {
  border: var(--os-track-border-hover);
  background: var(--os-track-bg-hover);
}
.os-scrollbar-track:active {
  border: var(--os-track-border-active);
  background: var(--os-track-bg-active);
}
.os-scrollbar-handle {
  border: var(--os-handle-border);
  border-radius: var(--os-handle-border-radius);
  background: var(--os-handle-bg);
}
.os-scrollbar-handle:hover {
  border: var(--os-handle-border-hover);
  background: var(--os-handle-bg-hover);
}
.os-scrollbar-handle:active {
  border: var(--os-handle-border-active);
  background: var(--os-handle-bg-active);
}
.os-scrollbar-track:before,
.os-scrollbar-handle:before {
  content: "";
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  display: block;
}
.os-scrollbar-horizontal {
  padding: var(--os-padding-perpendicular) var(--os-padding-axis);
  right: var(--os-size);
  height: var(--os-size);
}
.os-scrollbar-horizontal.os-scrollbar-rtl {
  left: var(--os-size);
  right: 0;
}
.os-scrollbar-horizontal .os-scrollbar-track:before {
  top: calc(var(--os-padding-perpendicular) * -1);
  bottom: calc(var(--os-padding-perpendicular) * -1);
}
.os-scrollbar-horizontal .os-scrollbar-handle {
  min-width: var(--os-handle-min-size);
  max-width: var(--os-handle-max-size);
  height: var(--os-handle-perpendicular-size);
  transition:
    opacity 0.15s,
    background-color 0.15s,
    border-color 0.15s,
    height 0.15s;
}
.os-scrollbar-horizontal .os-scrollbar-handle:before {
  top: calc((var(--os-padding-perpendicular) + var(--os-handle-interactive-area-offset)) * -1);
  bottom: calc(var(--os-padding-perpendicular) * -1);
}
.os-scrollbar-horizontal:hover .os-scrollbar-handle {
  height: var(--os-handle-perpendicular-size-hover);
}
.os-scrollbar-horizontal:active .os-scrollbar-handle {
  height: var(--os-handle-perpendicular-size-active);
}
.os-scrollbar-vertical {
  padding: var(--os-padding-axis) var(--os-padding-perpendicular);
  bottom: var(--os-size);
  width: var(--os-size);
}
.os-scrollbar-vertical .os-scrollbar-track:before {
  left: calc(var(--os-padding-perpendicular) * -1);
  right: calc(var(--os-padding-perpendicular) * -1);
}
.os-scrollbar-vertical .os-scrollbar-handle {
  min-height: var(--os-handle-min-size);
  max-height: var(--os-handle-max-size);
  width: var(--os-handle-perpendicular-size);
  transition:
    opacity 0.15s,
    background-color 0.15s,
    border-color 0.15s,
    width 0.15s;
}
.os-scrollbar-vertical .os-scrollbar-handle:before {
  left: calc((var(--os-padding-perpendicular) + var(--os-handle-interactive-area-offset)) * -1);
  right: calc(var(--os-padding-perpendicular) * -1);
}
.os-scrollbar-vertical.os-scrollbar-rtl .os-scrollbar-handle:before {
  right: calc((var(--os-padding-perpendicular) + var(--os-handle-interactive-area-offset)) * -1);
  left: calc(var(--os-padding-perpendicular) * -1);
}
.os-scrollbar-vertical:hover .os-scrollbar-handle {
  width: var(--os-handle-perpendicular-size-hover);
}
.os-scrollbar-vertical:active .os-scrollbar-handle {
  width: var(--os-handle-perpendicular-size-active);
}
[data-overlayscrollbars-viewport~=measuring] > .os-scrollbar,
.os-theme-none.os-scrollbar {
  display: none !important;
}
.os-theme-dark,
.os-theme-light {
  box-sizing: border-box;
  --os-size: 10px;
  --os-padding-perpendicular: 2px;
  --os-padding-axis: 2px;
  --os-track-border-radius: 10px;
  --os-handle-interactive-area-offset: 4px;
  --os-handle-border-radius: 10px;
}
.os-theme-dark {
  --os-handle-bg: rgba(0, 0, 0, 0.44);
  --os-handle-bg-hover: rgba(0, 0, 0, 0.55);
  --os-handle-bg-active: rgba(0, 0, 0, 0.66);
}
.os-theme-light {
  --os-handle-bg: rgba(255, 255, 255, 0.44);
  --os-handle-bg-hover: rgba(255, 255, 255, 0.55);
  --os-handle-bg-active: rgba(255, 255, 255, 0.66);
}
@keyframes pulse {
  from {
    opacity: 0.8;
  }
  to {
    opacity: 0.2;
  }
}
.day-planner {
  position: relative;
}
.day-planner .status-bar-item-segment:hover {
  cursor: pointer;
}
.status-bar-item.plugin-obsidian-day-planner {
  display: flex;
  gap: var(--size-2-2);
}
.day-planner-progress-bar {
  overflow: hidden;
  display: flex;
  align-items: stretch;
  align-self: stretch;
  min-width: 100px;
  background-color: var(--text-faint);
  border-radius: var(--radius-s);
}
.day-planner-progress-value {
  background-color: var(--color-accent);
}
.day-planner-progress-value.green,
.day-planner .progress-pie.green::before {
  background-color: #4caf50;
}
.day-planner .progress-pie.green {
  background-image: linear-gradient(to right, transparent 50%, #4caf50 0);
}
.day-planner-status-bar-text {
  float: left;
  margin-right: 10px;
}
.day-planner-status-card {
  position: absolute;
  top: -140px;
  display: none;
  width: 300px;
  padding: 8px;
  background-color: var(--background-secondary-alt);
  border-radius: 4px;
}
.day-planner-status-card .arrow-down {
  position: absolute;
  width: 20px;
  border-top: 20px solid var(--background-secondary-alt);
  border-right: 20px solid transparent;
  border-left: 20px solid transparent;
}
.progress-pie.day-planner {
  width: 20px;
  height: 20px;
}
.progress-pie.day-planner::after {
  width: 80%;
  height: 80%;
}
.progress-pie.day-planner::after {
  font-size: 8px;
  font-weight: 900;
  line-height: 13px;
  color: transparent;
  background-color: transparent;
}
[data-type=timeline] .view-content,
[data-type=weekly] .view-content,
[data-type=timeTracker] .view-content {
  --time-ruler-width: 30px;
  --scrollbar-width: 12px;
  --shadow-color: #00000010;
  --shadow-right: 2px 0px 2px var(--shadow-color);
  --shadow-bottom: 0px 2px 2px var(--shadow-color);
  display: flex;
  flex-direction: column;
  padding: 0;
}
.os-scrollbar {
  box-sizing: border-box;
  --os-size: var(--scrollbar-width);
  --os-padding-perpendicular: 2px;
  --os-padding-axis: 2px;
  --os-track-border-radius: 10px;
  --os-handle-interactive-area-offset: 4px;
  --os-handle-border-radius: 10px;
  --os-handle-bg: var(--scrollbar-thumb-bg);
  --os-handle-bg-hover: var(--scrollbar-active-thumb-bg);
  --os-handle-bg-active: var(--scrollbar-active-thumb-bg);
}
.absolute-stretch-x {
  position: absolute;
  right: 0;
  left: 0;
}
.day-planner-release-notes-modal .modal-content {
  height: 100%;
  overflow: auto;
}
.day-planner-modal-buttons {
  display: flex;
  justify-content: flex-end;
  gap: var(--size-4-2);
}
.overlayscrollbars-svelte {
  width: 100%;
  height: 100%;
}
/*! 
 * OverlayScrollbars
 * Version: 2.9.2
 * 
 * Copyright (c) Rene Haas | KingSora.
 * https://github.com/KingSora
 * 
 * Released under the MIT license.
 */
