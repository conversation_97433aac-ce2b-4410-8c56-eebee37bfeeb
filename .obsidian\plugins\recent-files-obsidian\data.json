{"recentFiles": [{"basename": "Final Chiller Notes", "path": "Final Chiller Notes.md"}, {"basename": "Dashboard", "path": "Dashboard.canvas"}, {"basename": "Weekly Schedule", "path": "Reports/Weekly Schedule.md"}, {"basename": "Chiller Notes", "path": "Chiller Notes.md"}, {"basename": "Shop AC Action Items", "path": "Shop AC Action Items.md"}, {"basename": "Test Bench PC", "path": "Test Bench PC.md"}, {"basename": "2025-04-30 Wednesday JC", "path": "Callahan Daily Notes/2025-04-30 Wednesday JC.md"}, {"basename": "engineering_tools_detailed_diagram", "path": "engineering_tools_detailed_diagram.md"}, {"basename": "engineering_tools_diagram", "path": "engineering_tools_diagram.md"}, {"basename": "Need Printed Manual", "path": "Reports/Need Printed Manual.md"}, {"basename": "Electrical Needs Turned Out", "path": "Reports/Electrical Needs Turned Out.md"}, {"basename": "PHMC-1014", "path": "Machines/Mix Chill/PHMC-1014.md"}, {"basename": "PHMC-1015", "path": "Machines/Mix Chill/PHMC-1015.md"}, {"basename": "All Injectors", "path": "Reports/All Injectors.md"}, {"basename": "Needs Packet", "path": "Reports/Needs Packet.md"}, {"basename": "Need Manual Documents", "path": "Reports/Need Manual Documents.md"}, {"basename": "PHX-1009", "path": "Machines/Injectors/PHX-1009.md"}, {"basename": "All Chillers", "path": "Reports/All Chillers.md"}, {"basename": "PHP-052", "path": "Machines/PHP/PHP-052.md"}, {"basename": "241021", "path": "Machines/Chiller/241021.md"}, {"basename": "PHP-043", "path": "Machines/PHP/PHP-043.md"}, {"basename": "PHP-044", "path": "Machines/PHP/PHP-044.md"}, {"basename": "All Sales Orders", "path": "Reports/All Sales Orders.md"}, {"basename": "Pumped <PERSON>", "path": "Machine Types/Pumped Rechiller.md"}, {"basename": "Phoenix Heat Processor", "path": "Machine Types/Phoenix Heat Processor.md"}, {"basename": "FRESHPET", "path": "Customers/FRESHPET.md"}, {"basename": "PHP-1410-S", "path": "Machine Models/PHP-1410-S.md"}, {"basename": "PPV-128", "path": "Machines/HEX/PPV-128.md"}, {"basename": "PPV-127", "path": "Machines/HEX/PPV-127.md"}, {"basename": "241024", "path": "Machines/Chiller/241024.md"}, {"basename": "241025", "path": "Machines/Chiller/241025.md"}, {"basename": "PHX-2012", "path": "Machines/Injectors/PHX-2012.md"}, {"basename": "PHX-2016", "path": "Machines/Injectors/PHX-2016.md"}, {"basename": "PHX-2015", "path": "Machines/Injectors/PHX-2015.md"}, {"basename": "PPV-102", "path": "Machines/HEX/PPV-102.md"}, {"basename": "PPV-101", "path": "Machines/HEX/PPV-101.md"}, {"basename": "PHC-006", "path": "Machines/HEX/PHC-006.md"}, {"basename": "PPV-107", "path": "Machines/HEX/PPV-107.md"}, {"basename": "PPV-129", "path": "Machines/HEX/PPV-129.md"}, {"basename": "PPV-130", "path": "Machines/HEX/PPV-130.md"}, {"basename": "241019", "path": "Machines/Chiller/241019.md"}, {"basename": "241022", "path": "Machines/Chiller/241022.md"}, {"basename": "PHP-046", "path": "Machines/PHP/PHP-046.md"}, {"basename": "PHP-041", "path": "Machines/PHP/PHP-041.md"}, {"basename": "PHP-042", "path": "Machines/PHP/PHP-042.md"}, {"basename": "PHMC-1016", "path": "Machines/Mix Chill/PHMC-1016.md"}, {"basename": "PHX-3004", "path": "Machines/Injectors/PHX-3004.md"}, {"basename": "PHX-2014", "path": "Machines/Injectors/PHX-2014.md"}, {"basename": "PPV-108", "path": "Machines/HEX/PPV-108.md"}, {"basename": "PPV-109", "path": "Machines/HEX/PPV-109.md"}], "omittedPaths": []}