# Installation Guide for Engineering Tools Suite

This document provides detailed instructions for installing and configuring the Engineering Tools Suite.

## Prerequisites

Before installing the Engineering Tools Suite, ensure you have the following:

- **Python 3.8 or higher** installed on your system
- **Windows operating system** (Windows 10 or higher recommended)
- **Administrator privileges** for installing Python packages
- **Microsoft Word** (for document generation features, Word 2016 or higher recommended)

## Installation Steps

### 1. Clone or Download the Repository

```
git clone https://github.com/nahallac/Work-scripts.git
cd Work-scripts
```

Or download and extract the ZIP file from the repository.

### 2. Set Up a Virtual Environment (Recommended)

```
python -m venv .venv
.venv\Scripts\activate
```

### 3. Install Required Dependencies

```
pip install -r requirements.txt
```

The main dependencies include:
- customtkinter==5.2.1
- PyPDF2==3.0.1
- python-docx==1.0.1
- docx2pdf==0.1.8
- Pillow==10.1.0
- pikepdf==8.7.1
- pywin32==306
- appdirs==1.4.4

### 4. Run the Setup Application

```
python setup.py
```

This will launch the setup application where you'll need to configure:
- Template directory
- Drawings directory
- Folder to monitor (optional)
- Notification email (optional)

### 5. Launch the Engineering Tools Launcher

```
python apps_launcher.py
```

This will launch the central GUI that provides access to all tools in the `apps` directory.

### 6. Verify Installation

After completing the setup, verify that the configuration files have been created in your user data directory:

```
%APPDATA%\Local\Phoenix\EngineeringTools\
```

You should see the following files:
- models.json
- config.json

## Required Directory Structure

The Engineering Tools Suite requires a specific directory structure for templates and drawings. Below is the recommended structure that should be created before running the setup application:

```
Work-scripts/
├── applications/         # Legacy application modules
├── apps/                 # New application modules
├── lib/                  # Core library modules
├── resources/            # Configuration and resource files
│   ├── config/           # Configuration templates
│   ├── Templates/        # Document templates
│   │   ├── Chiller/
│   │   │   └── Small Chiller Manual Template.dotx
│   │   ├── Heat Exchanger/
│   │   │   └── Heat Exchanger Manual Template.dotx
│   │   ├── Pump/
│   │   │   └── Pump Manual Template.dotx
│   │   ├── Injector/
│   │   │   ├── Fire Bird Standard Manual Template.dotx
│   │   │   └── Fire Bird Food Service Manual.dotx
│   │   └── [Other Equipment Types]/
│   │       └── [Template Files].dotx
│   │
│   └── drawings/         # Engineering drawings
│       ├── Chiller/
│       │   ├── P408/
│       │   │   └── [Drawing Files]
│       │   ├── P410/
│       │   │   └── [Drawing Files]
│       │   └── [Other Models]/
│       │       └── [Drawing Files]
│       ├── Heat Exchanger/
│       │   ├── PHI-110P/
│       │   │   └── [Drawing Files]
│       │   └── [Other Models]/
│       │       └── [Drawing Files]
│       └── [Other Equipment Types]/
│           └── [Model Folders]/
│               └── [Drawing Files]
```

### Important Notes About Directory Structure:

1. **Resource Folder**: All templates and drawings are now stored in the `resources` folder within the project directory.

2. **Equipment Categories**: The top-level folders in both Templates and drawings directories should match the equipment categories (e.g., Chiller, Heat Exchanger, Pump).

3. **Template Files**: Each equipment category should have one or more Word template files (.dotx) in its Templates subdirectory.

4. **Drawing Folders**: The drawings directory should have subdirectories for each equipment category, and within those, subdirectories for each specific model.

5. **File Naming**: The template filenames and drawing folder names must match what's expected in the models.json configuration.

6. **Consistency**: Maintain consistent naming between the Templates and drawings directories to ensure proper association.

You can create this structure manually, or let the setup application help you configure it. The setup will update the models.json file to point to your specific directory locations.

## Configuration

### Template and Drawing Directories

The setup application will ask you to specify:

1. **Template Directory**: Location of document templates (the root Templates folder in the structure above)
2. **Drawings Directory**: Location of engineering drawings (the root Drawings folder in the structure above)
3. **Folder to Monitor**: Directory to watch for new files (optional)
4. **Notification Email**: Email address for notifications (optional)

### Manual Configuration

If you need to manually configure the application:

1. Create a `config.json` file in the user data directory with the following structure:
   ```json
   {
       "folder_to_monitor": "C:/path/to/monitor/folder",
       "target_email": "<EMAIL>"
   }
   ```

2. Create a `models.json` file with your model definitions. The application now uses a JSON-based model configuration instead of the Python models.py file. The format should be:

   ```json
   {
       "Chiller": {
           "P408": {
               "template_path": "resources/templates/Chiller/Small Chiller Manual Template.dotx",
               "drawings_path": "resources/drawings/Chiller/P408",
               "asme_flag": false,
               "controls_parent": "503390"
           }
       },
       "Heat Exchanger": {
           "PHI-110P": {
               "template_path": "resources/templates/Heat Exchanger/Heat Exchanger Manual Template.dotx",
               "drawings_path": "resources/drawings/Heat Exchanger/PHI-110P",
               "asme_flag": true,
               "controls_parent": "501281"
           }
       }
   }
   ```

## Running the Applications

After installation and configuration, you can run the various applications:

```
python applications/config_app.py
python applications/create_Manuals.py
python applications/Publish\ 3.py
```

Or any other application in the `applications` directory.

## Application Documentation

The Engineering Tools Suite includes several applications for different purposes. Below is detailed documentation for each application.

### Configuration Application (config_app.py)

**Purpose**: Configure the Engineering Tools suite with paths to templates, drawings, and other settings.

**Features**:
- Set template directory path
- Set drawings directory path
- Configure folder to monitor for new files
- Set notification email address
- Automatically update models.json with new paths

**Usage**:
```
python applications/config_app.py
```

**Configuration Process**:
1. Launch the application
2. Browse and select your template directory
3. Browse and select your drawings directory
4. (Optional) Set a folder to monitor for new files
5. (Optional) Enter an email address for notifications
6. Click "Save Configuration"

The application will update the models.json file with the new paths and create a config.json file with your settings.

### Manual Creator (create_Manuals.py)

**Purpose**: Create technical manuals from templates, replacing placeholders with project-specific information.

**Features**:
- Select equipment category and model
- Browse and select job folder
- Enter company, location, and serial number information
- Automatically generate a complete manual with:
  - Filled-in template document
  - Conversion to PDF
  - Inclusion of relevant drawings
  - Merged final document

**Usage**:
```
python applications/create_Manuals.py
```

**Manual Creation Process**:
1. Launch the application
2. Select the equipment category from the dropdown
3. Select the specific model from the dropdown
4. Browse and select the job folder where files will be saved
5. Enter the company name, location, and serial number
6. Click "Create"

The application will:
- Open the appropriate Word template
- Replace placeholders with the provided information
- Convert the document to PDF
- Copy relevant drawings from the drawings directory
- Merge all PDFs into a single document
- Save the final manual in the job folder

### Project Publisher (Publish 3.py)

**Purpose**: Publish engineering projects by updating title block data, exporting to PDF/DXF, and optionally creating manuals.

**Features**:
- Enter project information (GSS Parent #, Serial number, Customer, etc.)
- Select a model from available options
- Browse and select a folder for saving files
- Export project to PDF and DXF formats
- Optionally create a technical manual for the project

**Usage**:
```
python applications/Publish\ 3.py
```

**Publishing Process**:
1. Launch the application
2. Fill in the project information fields
3. Select the appropriate model
4. Browse and select a folder for saving files
5. Check "Create Manual" if you want to generate a manual
6. Click "Publish"

The application will:
- Update the title block data in the project
- Save the project with the document number and serial number
- Export the project to PDF and DXF formats
- If selected, create a technical manual using the Manual Creator

### Model Manager (add_model.py)

**Purpose**: Add new equipment models to the Engineering Tools suite configuration.

**Features**:
- Select existing equipment category or create a new one
- Add new model with associated template and drawings
- Configure ASME certification flag
- Set controls parent ID
- Update models.json with new model information

**Usage**:
```
python add_model.py
```

**Adding a Model Process**:
1. Launch the application
2. Select an existing category or create a new one
3. Enter the model name
4. Browse and select the template file (.dotx) for this model
5. Browse and select the drawings directory for this model
6. Check the ASME flag if the model is ASME certified
7. Enter the controls parent ID if applicable
8. Click "Add Model"

The application will update the models.json file with the new model information, making it available for use in the Manual Creator and Project Publisher applications.

## Creating Standalone Executables

To create standalone executables that don't require Python:

```
pip install pyinstaller
pyinstaller --onefile --windowed applications/config_app.py
```

The executable will be created in the `dist` directory.

## Troubleshooting

### Common Issues

1. **Missing Dependencies**

   If you encounter errors about missing modules, ensure all dependencies are installed:
   ```
   pip install -r requirements.txt
   ```

2. **Path Configuration Issues**

   If the application can't find templates or drawings, run the setup application again to reconfigure paths.

3. **Permission Errors**

   Ensure you have read/write permissions for the directories specified in the configuration.

4. **File Not Found Errors**

   Check that all paths in the configuration files are correct and accessible.

### Getting Help

If you encounter issues not covered in this guide, please:

1. Check the logs in the user data directory
2. Contact Jonathan Callahan: <EMAIL>

## Updating

To update the Engineering Tools Suite:

1. Pull the latest changes from the repository:
   ```
   git pull origin main
   ```

2. Install any new dependencies:
   ```
   pip install -r requirements.txt
   ```

3. Run the setup application to update configurations if needed:
   ```
   python setup.py
   ```

4. Launch the Engineering Tools Launcher to access all tools:
   ```
   python apps_launcher.py
   ```

## New Tools in the Apps Directory

### Engineering Tools Launcher (apps_launcher.py)

**Purpose**: Provide a central GUI to access all tools in the apps directory.

**Features**:
- Automatically scans the apps directory for available tools
- Displays tool names and descriptions
- Launches tools with a single click

**Usage**:
```
python apps_launcher.py
```

### MERGED Files Copy Tool (apps/copy_merged_files.py)

**Purpose**: Search all subfolders of a selected location, find files in folders named 'MERGED', and copy them to a 'manual' folder at the selected location.

**Features**:
- Select a root directory to search
- Automatically find all MERGED folders in subdirectories
- Copy all files from MERGED folders to a central 'manual' folder
- Handle duplicate filenames by adding suffixes
- Display detailed progress and results

**Usage**:
```
python apps/copy_merged_files.py
```

### Publish Tool (apps/publish_3.py)

**Purpose**: Publish engineering projects with title block updates and export to PDF/DXF.

**Features**:
- Read title block data from the project
- Update attributes in the project
- Export to PDF and DXF formats
- Optionally create manuals based on the selected model

**Usage**:
```
python apps/publish_3.py
```
