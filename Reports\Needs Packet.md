[[Heat Exchanger]]s are excluded from this list as they do not get a packet for each individual [[Heat Exchanger|HEX]]. We produce them 10 at a time.
```dataview
table without id
row["Scheduled Ship"] - date(today) as Time-Left,
Company,
Location,
row["Machine Type"] As "Machine Type",
row["Machine Model"] as "Model",
file.link as Machine,
SO as "Sales Order",
row["Scheduled Ship"] as "Scheduled Ship",
row["Machine Model"] as "Model"


from "Machines"
where row["Ship Date"] = Empty and row["Scheduled Ship"] != Empty and Packet = false and row["Machine Type"] != [[Heat Exchanger]]
sort row["Scheduled Ship"] ASC, SO asc, Machine asc
FLATTEN Time-Left
```
 