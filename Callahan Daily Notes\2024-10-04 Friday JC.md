# Past DUE
```tasks
not done
sort by priority
due before 2024-10-04
tags include #J<PERSON> 
```
# DUE TODAY
```tasks
not done
sort by priority
due on 2024-10-04
tags include #JC 
```
# DUE This Week
```tasks
not done
sort by priority
due between last friday and this friday
tags include #JC 
```
# Assigned to [[<PERSON>|<PERSON>]]
```tasks 
not done
tags include JC
```
# LOG

# Tasks
- [x] #JC make data plates for center paw heaters 📅 2024-10-04 ✅ 2024-10-31