"""
Tests for the utils module.

This module contains unit tests for the utility functions in the lib.utils module.
"""

import os
import sys
import unittest
import tempfile
import shutil
import json
from unittest.mock import patch

# Add parent directory to path to allow importing from lib
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from lib.utils import (
    get_app_dir,
    get_user_data_dir,
    get_resource_path,
    get_config_path,
    ensure_dir_exists,
    load_json_config,
    save_json_config,
    convert_to_relative_path,
    convert_to_absolute_path,
)

class TestUtils(unittest.TestCase):
    """Test case for the utils module."""

    def setUp(self):
        """Set up test fixtures."""
        # Create a temporary directory for testing
        self.temp_dir = tempfile.mkdtemp()

        # Create a mock config file
        self.config_data = {"test_key": "test_value"}
        self.config_file = os.path.join(self.temp_dir, "test_config.json")
        with open(self.config_file, "w") as f:
            json.dump(self.config_data, f)

    def tearDown(self):
        """Tear down test fixtures."""
        # Remove the temporary directory
        shutil.rmtree(self.temp_dir)

    def test_get_app_dir(self):
        """Test get_app_dir function."""
        # Test with normal Python environment
        app_dir = get_app_dir()
        self.assertTrue(os.path.exists(app_dir))

        # Test with PyInstaller environment by patching the function that checks for _MEIPASS
        with patch("lib.utils.sys") as mock_sys:
            # Set up the mock to have _MEIPASS attribute
            mock_sys._MEIPASS = self.temp_dir
            # Also mock the AttributeError that would normally be raised
            mock_sys.configure_mock(**{'__getattribute__.side_effect': lambda attr: self.temp_dir if attr == '_MEIPASS' else getattr(sys, attr)})

            app_dir = get_app_dir()
            self.assertEqual(app_dir, self.temp_dir)

    @patch("appdirs.user_data_dir")
    def test_get_user_data_dir(self, mock_user_data_dir):
        """Test get_user_data_dir function."""
        mock_user_data_dir.return_value = self.temp_dir
        user_dir = get_user_data_dir()
        self.assertEqual(user_dir, self.temp_dir)
        mock_user_data_dir.assert_called_once_with("EngineeringTools", "Phoenix")

    def test_get_resource_path(self):
        """Test get_resource_path function."""
        with patch("lib.utils.get_app_dir", return_value=self.temp_dir):
            resource_path = get_resource_path("test_file.txt")
            self.assertEqual(resource_path, os.path.join(self.temp_dir, "test_file.txt"))

    def test_get_config_path(self):
        """Test get_config_path function."""
        # Create a mock user data directory
        user_dir = os.path.join(self.temp_dir, "user_data")
        os.makedirs(user_dir)

        # Create a mock config file in the user directory
        user_config = os.path.join(user_dir, "user_config.json")
        with open(user_config, "w") as f:
            json.dump({"user": "data"}, f)

        # Create a mock app directory
        app_dir = os.path.join(self.temp_dir, "app_dir")
        os.makedirs(os.path.join(app_dir, "resources", "config"))

        # Create a mock config file in the app directory
        app_config = os.path.join(app_dir, "resources", "config", "app_config.json")
        with open(app_config, "w") as f:
            json.dump({"app": "data"}, f)

        # Test with config file in user directory
        with patch("lib.utils.get_user_data_dir", return_value=user_dir):
            with patch("lib.utils.get_app_dir", return_value=app_dir):
                config_path = get_config_path("user_config.json")
                self.assertEqual(config_path, user_config)

        # Test with config file in app directory
        with patch("lib.utils.get_user_data_dir", return_value=user_dir):
            with patch("lib.utils.get_app_dir", return_value=app_dir):
                config_path = get_config_path("app_config.json")
                self.assertEqual(config_path, app_config)

    def test_ensure_dir_exists(self):
        """Test ensure_dir_exists function."""
        # Test with existing directory
        dir_path = ensure_dir_exists(self.temp_dir)
        self.assertEqual(dir_path, self.temp_dir)
        self.assertTrue(os.path.exists(dir_path))

        # Test with new directory
        new_dir = os.path.join(self.temp_dir, "new_dir")
        dir_path = ensure_dir_exists(new_dir)
        self.assertEqual(dir_path, new_dir)
        self.assertTrue(os.path.exists(dir_path))

    def test_load_json_config(self):
        """Test load_json_config function."""
        # Test with existing config file
        with patch("lib.utils.get_config_path", return_value=self.config_file):
            config = load_json_config("test_config.json")
            self.assertEqual(config, self.config_data)

        # Test with non-existent config file
        with patch("lib.utils.get_config_path", return_value=os.path.join(self.temp_dir, "nonexistent.json")):
            config = load_json_config("nonexistent.json")
            self.assertEqual(config, {})

        # Test with invalid JSON
        invalid_json = os.path.join(self.temp_dir, "invalid.json")
        with open(invalid_json, "w") as f:
            f.write("invalid json")

        with patch("lib.utils.get_config_path", return_value=invalid_json):
            config = load_json_config("invalid.json")
            self.assertEqual(config, {})

    def test_save_json_config(self):
        """Test save_json_config function."""
        # Test saving config
        with patch("lib.utils.get_user_data_dir", return_value=self.temp_dir):
            result = save_json_config("new_config.json", {"new": "data"})
            self.assertTrue(result)

            # Check that the file was created
            new_config_path = os.path.join(self.temp_dir, "new_config.json")
            self.assertTrue(os.path.exists(new_config_path))

            # Check the content
            with open(new_config_path, "r") as f:
                data = json.load(f)
                self.assertEqual(data, {"new": "data"})

    def test_convert_to_relative_path(self):
        """Test convert_to_relative_path function."""
        # Test with path in base directory
        base_dir = os.path.join(self.temp_dir, "base")
        full_path = os.path.join(base_dir, "file.txt")
        rel_path = convert_to_relative_path(base_dir, full_path)
        self.assertEqual(rel_path, "file.txt")

        # Test with path in subdirectory
        full_path = os.path.join(base_dir, "subdir", "file.txt")
        rel_path = convert_to_relative_path(base_dir, full_path)
        self.assertEqual(rel_path, os.path.join("subdir", "file.txt"))

    def test_convert_to_absolute_path(self):
        """Test convert_to_absolute_path function."""
        # Test with relative path
        base_dir = os.path.join(self.temp_dir, "base")
        rel_path = "file.txt"
        abs_path = convert_to_absolute_path(base_dir, rel_path)
        self.assertEqual(abs_path, os.path.normpath(os.path.join(base_dir, rel_path)))

        # Test with absolute path
        abs_path_input = os.path.join(self.temp_dir, "other", "file.txt")
        abs_path_output = convert_to_absolute_path(base_dir, abs_path_input)
        self.assertEqual(abs_path_output, abs_path_input)

if __name__ == "__main__":
    unittest.main()
