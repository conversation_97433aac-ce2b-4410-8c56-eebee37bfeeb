# Python virtual environment
.venv/
venv/
env/
ENV/
__pycache__/
*.py[cod]
*$py.class

# Distribution / packaging
dist/
build/
*.egg-info/
*.egg
.eggs/
sdist/
wheels/

# Local development settings
.env
.idea/
.vscode/
*.swp
*.swo

# Windows specific
Thumbs.db
desktop.ini
ehthumbs.db

# Logs and databases
*.log
*.sqlite
*.db

# User-specific files
*.ini
*.state
last_run_state.json

# Temporary files
*.tmp
*.bak
*.backup

# Generated files
*.spec
