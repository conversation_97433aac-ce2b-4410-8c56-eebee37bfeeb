/* THIS FILE IS NOT EXPORTED WITH THE HTML FILE! */

/* Flow list used on the settings page */
.flow-list {
    contain: inline-size;
    gap: 0.2em;
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: flex-start;
    width: -webkit-fill-available;
	background-color: var(--background-secondary);
	border: 1px solid var(--divider-color);
	border-radius: 5px;
	padding: 6px;
}

.flow-item {
    display: flex;
	flex-direction: row;
    border-radius: 100px;
	border: 1px solid var(--divider-color);
    font-size: 0.9em;
    height: min-content;
    width: max-content;
    padding: 3px 8px 3px 8px;
    margin: 0.1em 0em 0.1em 0.0em;
    background-color: var(--background-primary);
	align-items: center;
}

.flow-item:has(input:checked) {
    background-color: hsla(var(--color-accent-hsl), 0.3);
}

.flow-item input[type="checkbox"] {
    padding: 0;
    margin: 0.1em;
    margin-right: 0.5em;
}

/* Progressbar used in the render progress */

.html-render-progressbar::-webkit-progress-bar {
    background-color: var(--background-secondary);
    border-radius: 500px;
}

.html-render-progressbar::-webkit-progress-value {
    background-color: currentColor;
    border-radius: 500px;
}


/*#region Tree */

.tree-container
{
	--checkbox-size: 1.2em;
    --collapse-arrow-size: 0.5em;
    --tree-horizontal-spacing: calc(var(--collapse-arrow-size) * 2);
    --tree-vertical-spacing: 0.5em;
    --sidebar-margin: 12px;

	font-size: 14px;
	font-family: var(--font-family);
}

input[type=checkbox].file-checkbox 
{
    position: absolute;
    margin-left: calc(0px - var(--collapse-arrow-size) * 2 - 0.5em - var(--checkbox-size) - 0.5em);
    z-index: 20;
}

.theme-dark .tree-item:has(.file-checkbox.checked).mod-tree-folder
{
	transition: border-radius 0.2s, background-color 0.2s;
	background-color: rgba(var(--color-blue-rgb), 0.05);
	border-radius: 3px var(--radius-l) var(--radius-l) 3px;
}

.tree-item:has(.tree-item-contents)
{
	cursor: pointer;
}

.tree-item:has(.file-checkbox).mod-tree-folder
{
	margin-top: 2px;
	margin-bottom: 2px;
}

.tree-item.mod-tree-control
{
	background-color: var(--color-base-00);
    border-radius: var(--radius-s);
	box-shadow: 0px 0px 10px 0px rgba(0,0,0,0.2);
	width: fit-content;
	margin-bottom: 1em;
}

.tree-item:has(.file-checkbox.checked).mod-tree-folder.is-collapsed
{
	border-radius: 3px;
}

.tree-item-title *
{
	padding: 0;
	margin: 0;
	overflow: visible;
	display: inline-block;
}


.tree-container .tree-item-icon *
{
	color: var(--text-muted); 
	font-family: emoji;
}

.tree-container .tree-item-icon :is(svg,img) 
{
	-webkit-mask-image-repeat: no-repeat;
	-webkit-mask-image-position: center;
	max-width: 1.3em;
	height: 100%;
}

/* Skip outer wrappers around icons */
.tree-container .tree-item-icon *:has(svg)
{
	display: contents !important;
}

.tree-container .tree-item-icon
{
	min-width: 1.6em;
	max-width: 1.6em;
	display: flex;
	align-items: center;
	justify-content: flex-start;
}

.theme-dark .tree-item:has(> .tree-link > .tree-item-contents > .file-checkbox:not(.checked)):has(.file-checkbox.checked).mod-tree-folder
{
	background-color: rgba(var(--color-pink-rgb), 0.1);
}

.theme-light .tree-item:has(.file-checkbox.checked).mod-tree-folder
{
	transition: border-radius 0.2s, background-color 0.2s;
	background-color: rgba(var(--color-blue-rgb), 0.15);
	border-radius: 3px var(--radius-l) var(--radius-l) 3px;
}

.theme-light .tree-item:has(> .tree-link > .tree-item-contents  > .file-checkbox:not(.checked)):has(.file-checkbox.checked).mod-tree-folder
{
	background-color: rgba(var(--color-pink-rgb), 0.15);
}


/* Base tree */
.tree-container 
{
	/* padding-bottom: 12px; */
	/* margin: 12px; */
	/* height: 100%; */
	/* position: relative; */
	/* display: contents; */
	position: relative;
	height: 100%;
	width: auto;
	margin: var(--sidebar-margin);
	margin-top: 3em;
    margin-bottom: 0;
}

.tree-container .tree-header 
{
	display: flex;
	flex-direction: row;
	align-items: center;
	position: absolute;
	top: -3em;
}

.tree-container .tree-header .sidebar-section-header
{
    margin: 1em;
    margin-left: 0;
}

.tree-container:has(.tree-scroll-area:empty) 
{
    display: none;
}

.tree-container .tree-scroll-area 
{
	width: 100%;
	height: 100%;
	max-height: 100%;
	overflow-y: auto;
	padding: 1em;
	padding-right: calc(1em + var(--sidebar-margin));
	padding-bottom: 3em;
	border-radius: var(--radius-m);
}

.tree-container .tree-item 
{
	transition: background-color 0.2s;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    padding: 0;
	border: none !important;
}

.tree-container .tree-item-children
{
    padding: 0;
    margin-bottom: 0;
    margin-left: 0;

    border-left: none;
    width: -webkit-fill-available;
}

.tree-container .tree-item.mod-active > .tree-link > .tree-item-contents
{
    color: var(--interactive-accent);
}

.tree-container .tree-link {
    position: relative;
    display: flex;
    flex-direction: row;
    align-items: center;
    text-decoration: none;
    color: var(--nav-item-color);
    width: -webkit-fill-available;
    margin-left: var(--tree-horizontal-spacing);
}

.tree-container .tree-link:active 
{
    color: var(--nav-item-color-active);
}

.tree-container .tree-item-contents 
{
    width: 100%;
    height: 100%;
	margin: 0 !important;
	padding: 0 !important;
	background-color: transparent !important;
    border-radius: var(--radius-s);
    padding-left: calc(var(--tree-horizontal-spacing) + var(--checkbox-size) * 2 + 1px) !important;
    padding-bottom: calc(var(--tree-vertical-spacing) / 2) !important;
    padding-top: calc(var(--tree-vertical-spacing) / 2) !important;
    color: var(--text-normal);
	display: flex !important;
	flex-direction: row !important;
	justify-content: flex-start;
    align-items: center;
}

.tree-container .tree-item-contents:has(.tree-item-icon.collapse-icon)
{
	cursor: pointer !important;
}

.tree-container .tree-item-title
{
    overflow: hidden;
    text-overflow: ellipsis;
    text-wrap: nowrap;
	white-space: nowrap;

	width: 100%;
	width: -webkit-fill-available;
	width: -moz-available;
	width: fill-available;

	position: relative;
}

.tree-container .collapse-icon {
    margin-left: calc(0px - var(--collapse-arrow-size) * 2 - 0.5em);
    position: absolute;
}

.tree-container .tree-item.mod-tree-folder > .tree-link > .collapse-icon
{
    width: 100%;
}

.tree-container .collapse-icon > svg {
    color: unset !important;
}

.tree-container .collapse-icon:hover 
{
    color: var(--nav-item-color-hover);
}

.tree-container .tree-item.is-collapsed > .tree-link > .tree-item-contents > .collapse-icon > svg
{
    transition: transform 0.1s ease-in-out;
    transform: rotate(-90deg);
}

.tree-container .tree-item-contents:hover 
{
    cursor: default;
    text-decoration: none;
}

.tree-container .tree-link:hover
{
	background-color: var(--nav-item-background-hover);
	border-radius: var(--radius-s);
}

.tree-container .tree-item-title
{
	background-color: transparent !important;
	color: var(--nav-item-color) !important;
}


/* Indentation guide */

.tree-container > .tree-scroll-area > * .tree-item
{
    margin-left: calc(var(--tree-horizontal-spacing) + var(--collapse-arrow-size) / 2 + 1px);
    border-left: var(--nav-indentation-guide-width) solid var(--nav-indentation-guide-color);
}

.tree-container .tree-scroll-area > * > * > .tree-item
{
    margin-left: calc(var(--collapse-arrow-size) / 2 + 1px);
}

.tree-container .tree-item.mod-active
{
    border-left: var(--nav-indentation-guide-width) solid var(--interactive-accent);
}


.tree-container .tree-item:hover:not(.mod-active):not(.mod-collapsible):not(:has(.tree-item:hover)) /* Hover */
{
    border-left: var(--nav-indentation-guide-width) solid var(--nav-item-color-hover);
}

.tree-container .tree-item:not(.mod-collapsible) > .tree-item-children > .tree-item,
.tree-container > .tree-scroll-area > .tree-item,
.tree-container:not(.mod-nav-indicator) .tree-item
{
    border-left: none !important;
}

.tree-container .tree-item:not(.mod-collapsible) > .tree-item-children > .tree-item > .tree-link,
.tree-container:not(.mod-nav-indicator) .tree-item .tree-link,
.tree-container > .tree-scroll-area > .tree-item > .tree-link
{
    margin-left: 0 !important;
}

/* Special */


/* AnuPpuccin rainbow indent support */
.anp-simple-rainbow-color-toggle.anp-simple-rainbow-indentation-toggle .tree-container .tree-item
{
	border-color: rgba(var(--rainbow-folder-color), 0.5);
}

.tree-container.outline-tree .tree-item[data-depth='1'] > .tree-link > .tree-item-contents
{
    font-weight: 900;
    font-size: 1.1em;
    margin-left: 0;
	padding-left: 1em;
}

.tree-container .nav-folder.mod-root .nav-folder>.nav-folder-children
{
	padding: 0 !important;
	margin: 0 !important;
	border: none !important;
}

.tree-container .nav-file
{
	border-radius: 0 !important;
}

.tree-container .nav-folder.mod-root .nav-folder > .nav-folder-children
{
	border-radius: var(--radius-s) !important;;
}

.tree-container  .nav-file-tag
{
	margin-right: 1em;
}

.tree-container .nav-file-title-content, .tree-container .nav-folder-title-content
{
    margin-top: unset !important;
    margin-bottom: unset !important;
    margin-left: unset !important;
    margin-right: unset !important;
    display: unset !important;
    border-radius: unset !important;
    cursor: unset !important;
    font-size: unset !important;
    font-weight: unset !important;
    line-height: unset !important;
	padding: unset !important;
	border: unset !important;
}

.tree-item-contents:has(.tree-item-icon) .tree-item-title::before
{
	display: none !important;
}
/*#endregion */
