.nv-homepage-interstitial {
	position: absolute;
	left: 0;
	top: 0;
	width: 100vw;
	height: 100vh;
	background: var(--background-primary);
	z-index: 9999;
	animation: 0.02s ease-in 0.5s forwards nv-interstitial-destroy;
	pointer-events: none;
}

@keyframes nv-interstitial-destroy {
  from { opacity: 1; }
  to { opacity: 0; }
}

.setting-item[nv-greyed] {
	opacity: .5; 
	pointer-events: none !important;
}

#nv-main-setting {
	flex-wrap: wrap;
	margin-bottom: 30px;
}

#nv-main-setting .setting-item-control {
	padding-top: var(--size-4-2);
	flex-basis: 100%;
	align-items: stretch;
}

#nv-main-setting .setting-item-control input, #nv-main-setting .setting-item-control select {
	font-size: var(--font-ui-medium);
	font-weight: 600;
}

#nv-main-setting .setting-item-control select {
	padding: var(--size-4-3) var(--size-4-4);
	padding-right: var(--size-4-8);
	height: auto;
}

#nv-main-setting .setting-item-control input {
	flex-grow: 1;
	padding: var(--size-4-5) var(--size-4-4);
}

#nv-main-setting .setting-item-control input[disabled] {
	opacity: 0.3;
}

#nv-main-setting #nv-desc, #nv-main-setting #nv-info {
	flex-basis: 100%;
}

#nv-main-setting #nv-desc {
	font-weight: 500;
	color: var(--text-normal);
	font-size: var(--font-ui-small);
	padding: 10px 0 0;
}

#nv-main-setting #nv-desc.mod-warning {
	color: var(--text-error);
}

#nv-main-setting #nv-desc code {
	font-family: var(--font-monospace);
	font-size: var(--font-smaller);
	border-radius: var(--radius-s);
}

#nv-main-setting #nv-desc small {
	display: block;
	font-weight: 400;
	color: var(--text-muted);
	font-size: calc(var(--font-ui-smaller) * 0.9);
	padding: 5px 0 0;
}

.nv-mobile-setting {
	flex-wrap: wrap;
	row-gap: var(--size-2-2);
}

.nv-mobile-setting .nv-mobile-info {
	font-size: var(--font-ui-smaller);
	width: 100%;
	margin-right: var(--size-4-18);
}

.nv-command-desc {
	padding: 1.2em 0 0;
	border-top: 1px solid var(--background-modifier-border);
}

.nv-command-box {
	margin: 1em 0 1.75em;
	display: flex;
	flex-wrap: wrap;
	gap: 12px;
	align-items: center;
}

.nv-command-pill {
	background-color: var(--background-secondary);
	border: 1px solid var(--background-modifier-border-hover);
	border-radius: var(--radius-s);
	font-size: var(--font-ui-small);
	padding: var(--size-2-1) var(--size-2-2) var(--size-2-1) var(--size-2-3) ;
}

.nv-command-pill.nv-command-invalid {
	color: var(--text-faint);
}

.nv-command-pill button {
	display: inline-block;
	padding: 0;
	margin: 0 0 0 3px;
	vertical-align: bottom;
}

.nv-command-pill button:first-of-type {
	margin-left: var(--size-4-2);
}

.nv-command-pill button.nv-command-selected {
	margin-left: var(--size-2-2);
	padding: 0 var(--size-2-1);
}

.nv-command-pill button.nv-command-selected span {
	color: var(--text-accent);
	display: inline-block;
	font-size: 0.9em;
	vertical-align: top;
	position: relative;
	top: -1px;
}

.nv-command-pill > .svg-icon, .nv-command-pill button .svg-icon {
	height: 1em;
	width: 1em;
}

.nv-command-pill > .svg-icon {
	vertical-align: text-bottom;
	position: relative;
	margin: 0 var(--size-2-1) 0 0;
}

.nv-command-pill.nv-dragging {
	background-color: transparent;
}

.nv-command-add-button {
	font-size: var(--font-ui-small);
	padding: var(--size-2-2) var(--size-4-2);
	height: auto;
}

#nv-main-setting + .setting-item, .nv-command-desc + .setting-item {
	padding-top: 20px; 
	border-top: none !important;
}

.nv-debug-button {
	margin: 3em 0 -0.2em;
	font-size: var(--font-ui-smaller);
	padding: 0;
	height: auto;
	float: right;
	box-shadow: none !important;
	background: none !important;
	color: var(--text-accent);
	font-weight: 600;
	cursor: pointer;
}

.nv-debug-button:hover, .nv-debug-button:active {
	text-decoration: underline;
}

.is-phone #nv-main-setting .setting-item-control {
	flex-wrap: wrap;
	justify-content: flex-start;
}

.is-phone #nv-main-setting .setting-item-control select {
	width: auto;
	max-width: auto;
}

.is-phone .nv-mobile-setting {
	row-gap: var(--size-4-2);
}

.is-phone .nv-mobile-setting .setting-item-info {
	max-width: calc(100% - 100px);
}

.is-phone .nv-mobile-setting {
	row-gap: var(--size-4-2);
}

.is-phone .nv-mobile-setting .setting-item-info {
	max-width: calc(100% - 100px);
}

.is-phone .nv-command-pill {
	width: 100%;
	border: none;
	background: none;
	padding: 0 0 var(--size-4-2);
	display: flex;
	gap: var(--size-4-4);
	align-items: baseline;
}

.is-phone .nv-command-pill .nv-command-text {
	flex-grow: 1;
	overflow: hidden;
	text-overflow: ellipsis;
}

.is-phone .nv-command-pill, .is-phone .nv-command-add-button {
	font-size: var(--font-ui-medium);
	justify-content: space-between;
}

.is-phone .nv-command-pill button {
	line-height: var(--font-ui-medium);
	height: 100%;
	margin: 0 !important;
}
