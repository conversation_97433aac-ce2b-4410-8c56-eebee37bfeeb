# Engineering Tools Suite - Detailed Data Flow Diagram

```mermaid
graph TD
    %% Main Components
    User((User))
    
    %% Configuration Files
    ModelsJSON[models.json]
    ConfigJSON[config.json]
    
    %% Core Applications
    AppLauncher[Apps Launcher]
    ConfigApp[Configuration App]
    
    %% Engineering Applications
    PublishApp[Publish Tool]
    ManualCreator[Manual Creator]
    CopyMerged[MERGED Files Copy Tool]
    PrintPDF[PDF Section Printing]
    SumMotorFLA[Motor FLA Calculator]
    CreateJobJSON[Job Data Creator]
    UpdateDocs[Documentation Status Updater]
    
    %% Libraries
    LibUtils[Utility Library]
    LibTheme[Theme Utilities]
    LibManualCreator[Manual Creator Library]
    LibPDFPrinting[PDF Printing Library]
    
    %% External Systems
    CAD[CAD System]
    Word[Microsoft Word]
    Printer[Printer]
    FileSystem[File System]
    ObsidianVault[Obsidian Vault]
    
    %% Resources
    Templates[Document Templates]
    Drawings[Engineering Drawings]
    
    %% User Interactions
    User -- "1. Launches" --> AppLauncher
    User -- "2. Configures" --> ConfigApp
    User -- "3. Selects Tool" --> AppLauncher
    
    %% Configuration Setup
    ConfigApp -- "1. Updates" --> ModelsJSON
    ConfigApp -- "2. Updates" --> ConfigJSON
    
    %% App Launcher Flow
    AppLauncher -- "Reads" --> ModelsJSON
    AppLauncher -- "Reads" --> ConfigJSON
    AppLauncher -- "Launches" --> PublishApp
    AppLauncher -- "Launches" --> ManualCreator
    AppLauncher -- "Launches" --> CopyMerged
    AppLauncher -- "Launches" --> PrintPDF
    AppLauncher -- "Launches" --> SumMotorFLA
    AppLauncher -- "Launches" --> CreateJobJSON
    AppLauncher -- "Launches" --> UpdateDocs
    
    %% Library Dependencies
    LibUtils -- "Provides utilities to" --> AppLauncher
    LibUtils -- "Provides utilities to" --> ConfigApp
    LibUtils -- "Provides utilities to" --> PublishApp
    LibUtils -- "Provides utilities to" --> ManualCreator
    LibUtils -- "Provides utilities to" --> CopyMerged
    LibUtils -- "Provides utilities to" --> PrintPDF
    
    LibTheme -- "Provides theme to" --> AppLauncher
    LibTheme -- "Provides theme to" --> ConfigApp
    LibTheme -- "Provides theme to" --> PublishApp
    LibTheme -- "Provides theme to" --> ManualCreator
    LibTheme -- "Provides theme to" --> CopyMerged
    LibTheme -- "Provides theme to" --> PrintPDF
    
    LibManualCreator -- "Provides manual creation to" --> ManualCreator
    LibManualCreator -- "Provides manual creation to" --> PublishApp
    
    LibPDFPrinting -- "Provides PDF processing to" --> PrintPDF
    
    %% Publish Tool Flow
    CAD -- "1. Project data" --> PublishApp
    PublishApp -- "2. Reads model info" --> ModelsJSON
    PublishApp -- "3. Updates attributes" --> CAD
    PublishApp -- "4. Exports PDF/DXF" --> FileSystem
    PublishApp -- "5. Can trigger" --> ManualCreator
    PublishApp -- "6. Creates" --> CreateJobJSON
    
    %% Manual Creator Flow
    ManualCreator -- "1. Reads model info" --> ModelsJSON
    ManualCreator -- "2. Loads template" --> Templates
    ManualCreator -- "3. Loads drawings" --> Drawings
    ManualCreator -- "4. Creates document" --> Word
    Word -- "5. Converts to PDF" --> FileSystem
    
    %% Copy Merged Files Flow
    CopyMerged -- "1. Searches for MERGED folders" --> FileSystem
    CopyMerged -- "2. Copies files to manual folder" --> FileSystem
    
    %% PDF Printing Flow
    PrintPDF -- "1. Reads PDF sections" --> FileSystem
    PrintPDF -- "2. Determines page types" --> LibPDFPrinting
    PrintPDF -- "3. Sends to printer" --> Printer
    
    %% Motor FLA Calculator Flow
    SumMotorFLA -- "1. Reads motor data" --> CAD
    SumMotorFLA -- "2. Calculates total FLA" --> CAD
    
    %% Job Data Creator Flow
    CreateJobJSON -- "1. Collects job data" --> FileSystem
    CreateJobJSON -- "2. Creates JSON file" --> FileSystem
    
    %% Documentation Status Updater Flow
    UpdateDocs -- "1. Searches for manuals" --> FileSystem
    UpdateDocs -- "2. Updates YAML frontmatter" --> ObsidianVault
    
    %% Styling
    classDef user fill:#f96,stroke:#333,stroke-width:2px;
    classDef app fill:#9cf,stroke:#333,stroke-width:1px;
    classDef library fill:#fc9,stroke:#333,stroke-width:1px;
    classDef system fill:#cfc,stroke:#333,stroke-width:1px;
    classDef data fill:#fcf,stroke:#333,stroke-width:1px;
    classDef resource fill:#ffc,stroke:#333,stroke-width:1px;
    
    class User user;
    class AppLauncher,ConfigApp,PublishApp,ManualCreator,CopyMerged,PrintPDF,SumMotorFLA,CreateJobJSON,UpdateDocs app;
    class LibUtils,LibTheme,LibManualCreator,LibPDFPrinting library;
    class CAD,Word,Printer,FileSystem,ObsidianVault system;
    class ModelsJSON,ConfigJSON data;
    class Templates,Drawings resource;
```

## Key Components and Their Functions

### Core Applications
- **Apps Launcher**: Central GUI that provides access to all tools in the apps directory
- **Configuration App**: Configures paths to templates, drawings, and other settings

### Engineering Applications
- **Publish Tool**: Updates title block data, exports to PDF/DXF, and optionally creates manuals
- **Manual Creator**: Creates technical manuals from templates with project-specific information
- **MERGED Files Copy Tool**: Searches for MERGED folders and copies files to a manual folder
- **PDF Section Printing**: Prints PDF sections with appropriate settings based on page size
- **Motor FLA Calculator**: Calculates total FLA for all motors in a project
- **Job Data Creator**: Creates a JSON file with all project information
- **Documentation Status Updater**: Updates documentation status in Obsidian vault

### Libraries
- **Utility Library**: Provides common utility functions for file operations and path handling
- **Theme Utilities**: Manages application themes (dark mode, red color theme)
- **Manual Creator Library**: Core functionality for creating technical manuals
- **PDF Printing Library**: Handles PDF section detection and printing

### Configuration Files
- **models.json**: Contains information about engineering models, templates, and drawing paths
- **config.json**: Contains general configuration settings

### External Systems
- **CAD System**: Engineering CAD software (e.g., E3.series)
- **Microsoft Word**: Used for document creation and editing
- **Printer**: For printing documents with specific settings
- **File System**: For storing and retrieving files
- **Obsidian Vault**: For documentation management

### Resources
- **Document Templates**: Word templates for creating manuals
- **Engineering Drawings**: PDF drawings to include in manuals
