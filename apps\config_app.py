"""
Configuration Application Module

This module provides a GUI application for configuring the Engineering Tools suite.
It allows users to set up paths for templates, drawings, and other configuration options.
"""

import os
import sys
import json
import tkinter as tk
from tkinter import filedialog, messagebox
import customtkinter as ctk
from typing import Dict, Any, Optional

# Add parent directory to path to allow importing from lib
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from lib.utils import get_app_dir, get_user_data_dir, ensure_dir_exists
from lib.config import save_config, load_config, MODELS_FILENAME, CONFIG_FILENAME
from lib.logging_config import setup_logging, get_logger

# Import theme utilities
try:
    from lib.theme_utils import apply_theme
except ImportError:
    # Define a basic theme utility if import fails
    def apply_theme(theme_name="red", appearance_mode="dark"):
        ctk.set_appearance_mode(appearance_mode)
        ctk.set_default_color_theme("blue")

# Set up logging
setup_logging("config_app")
logger = get_logger(__name__)

class ConfigApp:
    """Configuration application for the Engineering Tools suite."""

    def __init__(self, root: ctk.CTk):
        """
        Initialize the configuration application.

        Args:
            root: The root Tk window
        """
        self.root = root
        self.root.title("Engineering Tools Configuration")
        self.root.geometry("700x600")

        # Apply the red theme
        apply_theme("red", "dark")

        # Create main frame
        self.main_frame = ctk.CTkFrame(self.root)
        self.main_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # Title
        title_label = ctk.CTkLabel(
            self.main_frame,
            text="Engineering Tools Configuration",
            font=("Arial", 20, "bold")
        )
        title_label.pack(pady=10)

        # Description
        desc_label = ctk.CTkLabel(
            self.main_frame,
            text="Configure the Engineering Tools suite for your system.",
            font=("Arial", 12)
        )
        desc_label.pack(pady=10)

        # Template directory
        self.template_frame = ctk.CTkFrame(self.main_frame)
        self.template_frame.pack(fill="x", pady=10)

        template_label = ctk.CTkLabel(
            self.template_frame,
            text="Template Directory:",
            font=("Arial", 12, "bold")
        )
        template_label.pack(anchor="w", padx=10, pady=5)

        self.template_path_var = tk.StringVar()
        self.template_path_entry = ctk.CTkEntry(
            self.template_frame,
            textvariable=self.template_path_var,
            width=500
        )
        self.template_path_entry.pack(side="left", padx=10, pady=5)

        template_browse_btn = ctk.CTkButton(
            self.template_frame,
            text="Browse",
            command=self.browse_template_dir
        )
        template_browse_btn.pack(side="right", padx=10, pady=5)

        # Drawings directory
        self.drawings_frame = ctk.CTkFrame(self.main_frame)
        self.drawings_frame.pack(fill="x", pady=10)

        drawings_label = ctk.CTkLabel(
            self.drawings_frame,
            text="Drawings Directory:",
            font=("Arial", 12, "bold")
        )
        drawings_label.pack(anchor="w", padx=10, pady=5)

        self.drawings_path_var = tk.StringVar()
        self.drawings_path_entry = ctk.CTkEntry(
            self.drawings_frame,
            textvariable=self.drawings_path_var,
            width=500
        )
        self.drawings_path_entry.pack(side="left", padx=10, pady=5)

        drawings_browse_btn = ctk.CTkButton(
            self.drawings_frame,
            text="Browse",
            command=self.browse_drawings_dir
        )
        drawings_browse_btn.pack(side="right", padx=10, pady=5)

        # Monitoring directory (for nesting_file_monitor)
        self.monitor_frame = ctk.CTkFrame(self.main_frame)
        self.monitor_frame.pack(fill="x", pady=10)

        monitor_label = ctk.CTkLabel(
            self.monitor_frame,
            text="Folder to Monitor:",
            font=("Arial", 12, "bold")
        )
        monitor_label.pack(anchor="w", padx=10, pady=5)

        self.monitor_path_var = tk.StringVar()
        self.monitor_path_entry = ctk.CTkEntry(
            self.monitor_frame,
            textvariable=self.monitor_path_var,
            width=500
        )
        self.monitor_path_entry.pack(side="left", padx=10, pady=5)

        monitor_browse_btn = ctk.CTkButton(
            self.monitor_frame,
            text="Browse",
            command=self.browse_monitor_dir
        )
        monitor_browse_btn.pack(side="right", padx=10, pady=5)

        # Email for notifications
        self.email_frame = ctk.CTkFrame(self.main_frame)
        self.email_frame.pack(fill="x", pady=10)

        email_label = ctk.CTkLabel(
            self.email_frame,
            text="Notification Email:",
            font=("Arial", 12, "bold")
        )
        email_label.pack(anchor="w", padx=10, pady=5)

        self.email_var = tk.StringVar()
        self.email_entry = ctk.CTkEntry(
            self.email_frame,
            textvariable=self.email_var,
            width=500
        )
        self.email_entry.pack(padx=10, pady=5)

        # Setup button
        self.setup_btn = ctk.CTkButton(
            self.main_frame,
            text="Save Configuration",
            command=self.save_configuration,
            height=40,
            font=("Arial", 14, "bold"),
            fg_color="#C53F3F",  # Red color
            hover_color="#A02222"  # Darker red
        )
        self.setup_btn.pack(pady=20)

        # Status label
        self.status_var = tk.StringVar()
        self.status_label = ctk.CTkLabel(
            self.main_frame,
            textvariable=self.status_var,
            font=("Arial", 12),
            text_color="#C53F3F"  # Red color
        )
        self.status_label.pack(pady=10)

        # Load existing configuration if available
        self.load_existing_config()

    def browse_template_dir(self) -> None:
        """Browse for template directory."""
        directory = filedialog.askdirectory(title="Select Template Directory")
        if directory:
            self.template_path_var.set(directory)

    def browse_drawings_dir(self) -> None:
        """Browse for drawings directory."""
        directory = filedialog.askdirectory(title="Select Drawings Directory")
        if directory:
            self.drawings_path_var.set(directory)

    def browse_monitor_dir(self) -> None:
        """Browse for folder to monitor."""
        directory = filedialog.askdirectory(title="Select Folder to Monitor")
        if directory:
            self.monitor_path_var.set(directory)

    def load_existing_config(self) -> None:
        """Load existing configuration if available."""
        try:
            # Load models.json for template and drawings paths
            try:
                models_data = load_config(MODELS_FILENAME)

                # Extract template and drawings paths from the first model
                for category in models_data:
                    for model in models_data[category]:
                        model_data = models_data[category][model]
                        if isinstance(model_data, list):
                            # Old format [template_path, drawings_path, asme_flag, controls_parent]
                            template_path = os.path.dirname(model_data[0])
                            drawings_path = os.path.dirname(model_data[1])
                        else:
                            # New format with keys
                            template_path = os.path.dirname(model_data.get('template_path', ''))
                            drawings_path = os.path.dirname(model_data.get('drawings_path', ''))

                        if template_path:
                            self.template_path_var.set(template_path)
                        if drawings_path:
                            self.drawings_path_var.set(drawings_path)
                        break
                    if self.template_path_var.get():
                        break
            except Exception as e:
                logger.warning(f"Error loading models.json: {e}")

            # Load config.json for monitor path and email
            try:
                config_data = load_config(CONFIG_FILENAME)

                if 'folder_to_monitor' in config_data:
                    self.monitor_path_var.set(config_data['folder_to_monitor'])
                if 'target_email' in config_data:
                    self.email_var.set(config_data['target_email'])
            except Exception as e:
                logger.warning(f"Error loading config.json: {e}")

        except Exception as e:
            logger.error(f"Error loading configuration: {e}")
            messagebox.showerror("Error", f"Failed to load configuration: {e}")

    def save_configuration(self) -> None:
        """Save the configuration."""
        template_dir = self.template_path_var.get()
        drawings_dir = self.drawings_path_var.get()
        monitor_dir = self.monitor_path_var.get()
        email = self.email_var.get()

        # Validate inputs
        if not template_dir or not drawings_dir:
            messagebox.showerror("Error", "Template and Drawings directories are required")
            return

        try:
            # Update models.json
            self.update_models_json(template_dir, drawings_dir)

            # Update config.json
            config_data = {
                'folder_to_monitor': monitor_dir,
                'target_email': email
            }
            save_config(config_data, CONFIG_FILENAME)

            # Show success message
            self.status_var.set("Configuration saved successfully!")
            logger.info("Configuration saved successfully")
            messagebox.showinfo("Configuration Saved",
                               "The Engineering Tools suite has been configured for your system.\n\n"
                               "You can now run the applications.")
        except Exception as e:
            logger.error(f"Error saving configuration: {e}")
            messagebox.showerror("Error", f"Failed to save configuration: {e}")

    def update_models_json(self, template_dir: str, drawings_dir: str) -> None:
        """
        Update models.json with the new paths.

        Args:
            template_dir: Path to the template directory
            drawings_dir: Path to the drawings directory

        Raises:
            FileNotFoundError: If models.json cannot be found
            Exception: If there is an error updating models.json
        """
        # First, try to load existing models.json
        try:
            models_data = load_config(MODELS_FILENAME)
        except FileNotFoundError:
            # If not found, try to find it in the app directory
            app_dir = get_app_dir()
            models_path = os.path.join(app_dir, 'resources', 'config', 'models.json')

            if not os.path.exists(models_path):
                # Try to find it in the current directory
                models_path = os.path.join(app_dir, 'models.json')

            if not os.path.exists(models_path):
                logger.error("Could not find models.json in the application directory")
                raise FileNotFoundError("Could not find models.json in the application directory")

            with open(models_path, 'r') as f:
                models_data = json.load(f)

        # Update paths in models_data
        updated_models = {}
        for category, models in models_data.items():
            updated_models[category] = {}
            for model, model_data in models.items():
                if isinstance(model_data, list):
                    # Old format [template_path, drawings_path, asme_flag, controls_parent]
                    template_filename = os.path.basename(model_data[0])
                    drawings_subdir = os.path.basename(model_data[1])

                    new_template_path = os.path.join(template_dir, template_filename)
                    new_drawings_path = os.path.join(drawings_dir, category, drawings_subdir)

                    updated_models[category][model] = [
                        new_template_path,
                        new_drawings_path,
                        model_data[2],
                        model_data[3]
                    ]
                else:
                    # New format with keys
                    template_filename = os.path.basename(model_data.get('template_path', ''))
                    drawings_subdir = os.path.basename(model_data.get('drawings_path', ''))

                    new_template_path = os.path.join(template_dir, template_filename)
                    new_drawings_path = os.path.join(drawings_dir, category, drawings_subdir)

                    updated_models[category][model] = {
                        'template_path': new_template_path,
                        'drawings_path': new_drawings_path,
                        'asme_flag': model_data.get('asme_flag', False),
                        'controls_parent': model_data.get('controls_parent', '')
                    }

        # Save updated models.json
        save_config(updated_models, MODELS_FILENAME)
        logger.info("Updated models.json with new paths")

def main():
    """Run the configuration application."""
    root = ctk.CTk()
    app = ConfigApp(root)
    root.mainloop()

if __name__ == "__main__":
    main()
