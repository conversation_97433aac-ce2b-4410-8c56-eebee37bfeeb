
## First actions
Spoke with [[<PERSON>|<PERSON>]] and let him know [[<PERSON>|I]] was updating the [[Heat Exchanger|HEX]] program. He already had one machine for [[Danville, AR|Danville]] wrapped up and palletized. 
Spoke with [[<PERSON>|<PERSON>]] for a few minutes and welcomed him back. 
## Wi-Fi Weirdness
Came in to our computers being offline. [[<PERSON><PERSON>|<PERSON>al]] sent out a password change to the production network and the guest network. [[<PERSON>]] read [[<PERSON><PERSON>|Reba]]'s email and understood it to mean he should join the guest network on his laptop. He could not use any network assets such as printers.

## [[Heat Exchanger|HEX]] Program Update
Updated the [HEX with Float Column](<"%HOMEDRIVE%%HOMEPATH%\Phoenix Innovations\Phoenix Sharepoint - Documents\Callahan\Standard Programs\Heat exchanger\Heat Exchanger With Float Column L50E>") program to add trending for Temp and Pressure. Had to split them into two screens because the range is so different for pressures and temps. [[<PERSON>|I]] may have to adjust the range on each trend to more closely match the needs of the plants

There is some unclear wording on some of the property of the trends. mainly how it saves the log file. [[<PERSON>|I]] need to play with one when you get it done so [[<PERSON>|I]] can get the wording correct in the manual

- [x] 8:40 headed down to set it up.
put the trending on the machine and logging is not working like [[<PERSON> Call<PERSON>|I]] thought it should am researching now

it appears that the [[Panelview800]] datalog starts with the tag and continues until turned off. put in some logic to start it at power up. [[<PERSON> <PERSON><PERSON>|I]] updated the manuals.

- [x] 9:00 had a meeting with [[Darin <PERSON>|Darin]] [[<PERSON> <PERSON>|<PERSON>]] and [[<PERSON> Hill|Jacob]] to discuss [[Upcoming Scheduling 8-13-24 meeting|Scheduling]]

## Manuals
Updated the Manual for the medium heat exchangers. 
printed manuals for
- [[PPV-079]]
- [[PPV-080]]
- [[PPV-081]]
Handed them off to [[David Andrews|David]].


