{"globalQuery": "", "globalFilter": "", "removeGlobalFilter": false, "taskFormat": "tasksPluginEmoji", "setCreatedDate": false, "setDoneDate": true, "setCancelledDate": true, "autoSuggestInEditor": true, "autoSuggestMinMatch": 0, "autoSuggestMaxItems": 20, "provideAccessKeys": true, "useFilenameAsScheduledDate": false, "filenameAsScheduledDateFormat": "", "filenameAsDateFolders": [], "recurrenceOnNextLine": false, "statusSettings": {"coreStatuses": [{"symbol": " ", "name": "Todo", "nextStatusSymbol": "x", "availableAsCommand": true, "type": "TODO"}, {"symbol": "x", "name": "Done", "nextStatusSymbol": " ", "availableAsCommand": true, "type": "DONE"}], "customStatuses": [{"symbol": "/", "name": "In Progress", "nextStatusSymbol": "x", "availableAsCommand": true, "type": "IN_PROGRESS"}, {"symbol": "-", "name": "Cancelled", "nextStatusSymbol": " ", "availableAsCommand": true, "type": "CANCELLED"}, {"symbol": " ", "name": "to-do", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}, {"symbol": "/", "name": "incomplete", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "IN_PROGRESS"}, {"symbol": "x", "name": "done", "nextStatusSymbol": " ", "availableAsCommand": false, "type": "DONE"}, {"symbol": "-", "name": "canceled", "nextStatusSymbol": " ", "availableAsCommand": false, "type": "CANCELLED"}, {"symbol": ">", "name": "forwarded", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}, {"symbol": "<", "name": "scheduling", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}, {"symbol": "?", "name": "question", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}, {"symbol": "!", "name": "important", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}, {"symbol": "*", "name": "star", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}, {"symbol": "\"", "name": "quote", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}, {"symbol": "l", "name": "location", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}, {"symbol": "b", "name": "bookmark", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}, {"symbol": "i", "name": "information", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}, {"symbol": "S", "name": "savings", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}, {"symbol": "I", "name": "idea", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}, {"symbol": "p", "name": "pros", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}, {"symbol": "c", "name": "cons", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}, {"symbol": "f", "name": "fire", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}, {"symbol": "k", "name": "key", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}, {"symbol": "w", "name": "win", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}, {"symbol": "u", "name": "up", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}, {"symbol": "d", "name": "down", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}]}, "features": {"INTERNAL_TESTING_ENABLED_BY_DEFAULT": true}, "generalSettings": {}, "headingOpened": {"Core Statuses": true, "Custom Statuses": true}, "debugSettings": {"ignoreSortInstructions": false, "showTaskHiddenData": false, "recordTimings": false}, "loggingOptions": {"minLevels": {"": "info", "tasks": "info", "tasks.Cache": "info", "tasks.Events": "info", "tasks.File": "info", "tasks.Query": "info", "tasks.Task": "info"}}}