 This is hard to explain verbally / textually, so I wrote a program to explain this graphicly. 
 
 The original linear scale was not accurate. Below this is the math explaining how to calculate the hyperbolic scale and a plot showing the difference.
 The original PLC program calculated the minimum dwell time at max freq and max dwell time at minimum freq then scaled them linearly between the two points. This lead to some pretty big discrepancies in the middle of the dwell time range as shown below.
 The PLC program now compensates for this. 
![[Pasted image 20250212231043.png]]
## **Frequency Calculation**  
The target Motor RPM X is given by:
$$
X =  \frac{L \times 12}{T \times P} \times \left(\frac{B}{A} \times \frac{s}{S} \times G\right)
$$

where:
- N = Motor Nominal Speed (RPM)
- F = Nominal Frequency (Hz)
- L = Chiller Length (ft.)
- T = Dwell Time (min.)
- P = Auger Pitch (in.)
- A = Drive Sheave (datum)
- B = Driven Sheave (datum)
- G = Gear Box Ratio
- s = Driven Sprocket (teeth)
- S = Drive Sprocket (teeth)

The Controller computes the constant \( K \) as follows:
$$K = \frac{L \times 12}{P} \times \left(\frac{B}{A} \times \frac{s}{S} \times G\right) \times \frac{F_{\text{nom}}}{N}$$
The dwell times corresponding to the nominal and minimum frequencies are computed as:
$$T_{\text{nom}} = \frac{K}{F_{\text{nom}}} \quad \text{and} \quad T_{\text{min}} = \frac{K}{F_{\text{min}}}$$
The frequency at any dwell time $( T )$ on the new (hyperbolic) scale is given by:
$$F(T) = \frac{K}{T}$$
The "Old Scale" is defined by a linear interpolation between the nominal and minimum frequency points. 
Its value at a target dwell time $T_{\text{target}}$ is: $F_{\text{old}}(T_{\text{target}}) = F_{\text{nom}} + \frac{F_{\text{min}} - F_{\text{nom}}}{T_{\text{min}} - T_{\text{nom}}} \left( T_{\text{target}} - T_{\text{nom}} \right)$
## **Plotting:**
The script plots the hyperbolic frequency curve: $F(T) = \frac{K}{T}$ over the dwell time range $[T_{\text{nom}}, T_{\text{min}}]$
It marks the following points on the plot
- Nominal frequency: $(T_{\text{nom}}, F_{\text{nom}})$
- Minimum frequency: $(T_{\text{min}}, F_{\text{min}})$
- Target frequency on the new scale: $(T_{\text{target}}, F(T_{\text{target}}))$
- A dashed line labeled "Old Scale" is drawn connecting $(T_{\text{nom}}, F_{\text{nom}})$and $(T_{\text{min}}, F_{\text{min}})$ and the target point $\left( T_{\text{target}}, F_{\text{old}}(T_{\text{target}}) \right)$
is plotted on this line.
## **Difference Calculation:**
The difference between the target frequency on the new scale and the old scale is calculated as: $$\Delta F = F(T_{\text{target}}) - F_{\text{old}}(T_{\text{target}})$$ This difference is displayed in the results, providing insight into how the two scales compare at the target dwell time.
