# [[Phoenix Heat Processor|PHP]]s
```dataview
table
SO as "Sales Order",
row["Scheduled Ship"] as "Scheduled Ship",
row["Machine Type"] As "Machine Type",
row["Machine Options"] as "Options",
row["Machine Model"] as "Model",
Company,
Location
from "Machines"
where TurnedOut != true and row["Machine Type"] = [[Phoenix Heat Processor]]
sort SO DESC 

```
# [[Chiller]]s
```dataview
table
SO as "Sales Order",
row["Scheduled Ship"] as "Scheduled Ship",
row["Machine Type"] As "Machine Type",
row["Machine Options"] as "Options",
row["Machine Model"] as "Model",
Company,
Location
from "Machines"
where TurnedOut != true and row["Machine Type"] = [[Chiller]]
sort SO DESC 

```
# [[Heat Exchanger]]s
```dataview
table
SO as "Sales Order",
row["Scheduled Ship"] as "Scheduled Ship",
row["Machine Type"] As "Machine Type",
row["Machine Options"] as "Options",
row["Machine Model"] as "Model",
Company,
Location
from "Machines"
where TurnedOut != true and row["Machine Type"] = [[Heat Exchanger|HEX]]
sort SO DESC 

```

# [[Pumped Rechiller|Rechiller]]s
```dataview
table
SO as "Sales Order",
row["Scheduled Ship"] as "Scheduled Ship",
row["Machine Type"] As "Machine Type",
row["Machine Options"] as "Options",
row["Machine Model"] as "Model",
Company,
Location
from "Machines"
where TurnedOut != true and row["Machine Type"] = [[Pumped Rechiller]]
sort SO DESC 

```
# [[Injector]]s 
```dataview
table
SO as "Sales Order",
row["Scheduled Ship"] as "Scheduled Ship",
row["Machine Type"] As "Machine Type",
row["Machine Options"] as "Options",
row["Machine Model"] as "Model",
Company,
Location
from "Machines"
where TurnedOut != true and row["Machine Type"] = [[Injector]]
sort SO DESC 

```
# [[Mixing System|DMC]]s
```dataview
table
SO as "Sales Order",
row["Scheduled Ship"] as "Scheduled Ship",
row["Machine Type"] As "Machine Type",
row["Machine Options"] as "Options",
row["Machine Model"] as "Model",
Company,
Location
from "Machines"
where TurnedOut != true and row["Machine Type"] = [[Mixing System|DMC]]
sort SO DESC 

```
