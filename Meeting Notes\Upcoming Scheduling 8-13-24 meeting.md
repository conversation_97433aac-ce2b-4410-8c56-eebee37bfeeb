## [[Danville, AR|Danville]]
pickup [[Heat Exchanger|HEX]]s Saturday 8-17-24 at 9AM
- [ ] [[PPV-079]]
- [ ] [[PPV-080]]
- [ ] [[PPV-081]] 
## [[Siler City]]
ships this Thursday 8-15-24
Need to get with [[<PERSON>|<PERSON>]] on a shipping list

## [[Hazlehurst, MS|Hazlehurst]] 
Ships next Wednesday 8-21-24
- [ ] [[PPV-078]] Ship
- [ ] [[PHP-010]] Ship
- [ ] Pump 7.5 or 10? David asked

## [[Alatrade]] 
[[<PERSON>|<PERSON>]] asked for trailer layouts. [[<PERSON>|<PERSON>]] took this to figure out

[[<PERSON>|<PERSON>]] asked about crating the control panels and was worried about time and people. [[<PERSON>|<PERSON>]] suggested having [[<PERSON>]] build them and everyone was in agreement. [[<PERSON>|<PERSON>]] to call and arrange crates in batches of 5. To be delivered each week.

[[<PERSON>|<PERSON>]] and [[<PERSON>|<PERSON>]] to begin unloading onsite and [[<PERSON>|<PERSON>]] to arrive later. but he can only stay 1 day

~~[[<PERSON>|<PERSON>]] will need to tune the PID loop on the deicers onsite. [[<PERSON>|<PERSON>]] need to get with him and make sure he is comfortable with tuning the loops.~~
[[<PERSON>|Austin]] won't be able to start up the macnines and it will have to be [[<PERSON>|<PERSON>]] to tune PID Loops


[[<PERSON>|<PERSON>]] and [[<PERSON> Callahan|I]] need to get together on when to download the program for the deices at some point.

[[<PERSON> Callahan|I]] need to update the programs so they can handle the steam valve and create the PID loop / PID tuning pages

## [[Action Item Register|A.I.R.]] 
#### [[Darin Chancellor|Darin]]
- [ ] #Darin Check on boiler
- [ ] #Darin something [[Jonathan Callahan|I]] wasn't fast enough to write down.
#### [[Jacob Hill|Jacob]]
- [ ] #Jacob check on screens for rotary and pressure filters
- [ ] #David chase shipping on [[Alatrade]] 
 
#### [[David Andrews|David]] 
- [ ] #David check inlet piping
- [ ] #David organize extraction of hex from asme area
- [ ] #David source and setup paint of valve groups

#### [[Jonathan Callahan|Me]] 
- [x] #JC get with austin
- [x] #JC write deicer program ✅ 2024-08-22
- [x] #JC get hex documentation
