import os
import sys
import json
import logging
import datetime
import customtkinter as ctk
from tkinter import filedialog, messagebox

# Add parent directory to path to allow importing from lib and utils
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import utility functions
try:
    from utils import get_config_path, setup_logging
    from lib.theme_utils import apply_theme
except ImportError:
    try:
        from lib.utils import get_config_path, setup_logging
        from lib.theme_utils import apply_theme
    except ImportError:
        from lib.manual_creator import get_config_path
        # Define a basic theme utility if import fails
        def apply_theme(theme_name="red", appearance_mode="dark"):
            ctk.set_appearance_mode(appearance_mode)
            ctk.set_default_color_theme("blue")

# Set up logging
log_file = setup_logging("create_job_json") if 'setup_logging' in locals() else None
if not log_file:
    logging.basicConfig(
        level=logging.DEBUG,
        format="%(asctime)s [%(levelname)s] %(message)s",
        handlers=[
            logging.FileHandler("create_job_json.log"),
            logging.StreamHandler(sys.stdout)
        ]
    )

# Load the MODELS dictionary from the JSON file
json_file_path = get_config_path("models.json")
logging.info(f"Attempting to load models from: {json_file_path}")

try:
    with open(json_file_path, 'r') as json_file:
        MODELS = json.load(json_file)
    logging.info(f"Successfully loaded models. Categories: {list(MODELS.keys())}")
except Exception as e:
    logging.error(f"Failed to load models.json: {str(e)}")
    MODELS = {}

# Apply the red theme
apply_theme("red", "dark")

class JobDataApp:
    """
    An application to create a JSON file with job data.
    """
    def __init__(self, root: ctk.CTk) -> None:
        logging.info("Initializing JobDataApp")
        self.root = root
        self.root.title("Create Job Data JSON")
        self.root.geometry("600x500")

        self.fields = [
            "GSS Parent #",
            "Serial number",
            "Customer",
            "Location",
            "Title",
            "Sales order #",
        ]
        self.entries = {}

        # Add model variable and list
        self.model_var = ctk.StringVar()
        self.category_var = ctk.StringVar()
        self.available_models = []
        self.available_categories = sorted(MODELS.keys())

        # Job folder
        self.folder_selected = ""

        self.create_form()

    def get_models_for_category(self, category: str) -> list:
        """Get all models for the specified category."""
        if category in MODELS:
            return sorted(MODELS[category].keys())
        return []

    def update_model_dropdown(self, *args):
        """Update the model dropdown based on the selected category."""
        category = self.category_var.get()
        self.available_models = self.get_models_for_category(category)

        if self.available_models:
            self.model_dropdown.configure(values=self.available_models)
            self.model_var.set(self.available_models[0])
        else:
            self.model_dropdown.configure(values=["No models available"])
            self.model_var.set("No models available")

    def create_form(self) -> None:
        """
        Create the input form with fields and buttons.
        """
        frame = ctk.CTkFrame(self.root)
        frame.pack(fill="both", expand=True, padx=10, pady=10)

        # Create form fields
        for field in self.fields:
            row_frame = ctk.CTkFrame(frame)
            row_frame.pack(fill="x", padx=5, pady=5)

            label = ctk.CTkLabel(row_frame, text=field + ":", width=25)
            label.pack(side="left", padx=5)

            entry = ctk.CTkEntry(row_frame, width=200)
            entry.pack(side="right", fill="x", expand=True, padx=5)
            self.entries[field] = entry

        # Create Category dropdown
        category_frame = ctk.CTkFrame(frame)
        category_frame.pack(fill="x", padx=5, pady=5)

        category_label = ctk.CTkLabel(category_frame, text="Category:", width=25)
        category_label.pack(side="left", padx=5)

        self.category_dropdown = ctk.CTkOptionMenu(
            category_frame,
            variable=self.category_var,
            values=self.available_categories if self.available_categories else ["No categories available"],
            width=200,
            command=self.update_model_dropdown
        )
        self.category_dropdown.pack(side="right", fill="x", expand=True, padx=5)

        # Set initial category
        if self.available_categories:
            self.category_var.set(self.available_categories[0])
        else:
            self.category_var.set("No categories available")

        # Create Model dropdown
        model_frame = ctk.CTkFrame(frame)
        model_frame.pack(fill="x", padx=5, pady=5)

        model_label = ctk.CTkLabel(model_frame, text="Model:", width=25)
        model_label.pack(side="left", padx=5)

        self.model_dropdown = ctk.CTkOptionMenu(
            model_frame,
            variable=self.model_var,
            values=["Select a category first"],
            width=200
        )
        self.model_dropdown.pack(side="right", fill="x", expand=True, padx=5)

        # Update model dropdown based on initial category
        self.update_model_dropdown()

        # Create a frame for buttons
        button_frame = ctk.CTkFrame(frame)
        button_frame.pack(fill="x", padx=5, pady=5)

        browse_button = ctk.CTkButton(button_frame, text="Browse Job Folder", command=self.browse_folder)
        browse_button.pack(side="left", padx=5)

        # Create a separate frame for folder label
        folder_frame = ctk.CTkFrame(frame)
        folder_frame.pack(fill="both", expand=True, padx=5, pady=5)

        self.folder_label = ctk.CTkLabel(folder_frame, text="No folder selected", width=200, wraplength=550, justify="left")
        self.folder_label.pack(fill="both", expand=True, padx=10, pady=5)

        create_button = ctk.CTkButton(frame, text="Create JSON", command=self.create_json)
        create_button.pack(side="bottom", padx=5, pady=10)

    def browse_folder(self) -> None:
        """
        Open a folder selection dialog and update the UI with the chosen folder.
        """
        folder = filedialog.askdirectory()
        if folder:
            self.folder_selected = folder
            self.folder_label.configure(text=f"Selected folder: {folder}", wraplength=550)
            logging.info(f"Selected folder: {folder}")

    def create_json(self) -> None:
        """
        Create a JSON file with the job data.
        """
        if not self.folder_selected:
            messagebox.showerror("Error", "Please select a job folder first.")
            return

        # Get values from form fields
        gss_parent = self.entries["GSS Parent #"].get()
        serial_number = self.entries["Serial number"].get()
        customer = self.entries["Customer"].get()
        location = self.entries["Location"].get()
        title = self.entries["Title"].get()
        sales_order = self.entries["Sales order #"].get()
        model = self.model_var.get()
        category = self.category_var.get()

        # Validate required fields
        if not gss_parent or not serial_number:
            messagebox.showerror("Error", "GSS Parent # and Serial number are required.")
            return

        try:
            # Create a dictionary with all form data
            job_data = {
                "gss_parent": gss_parent,
                "serial_number": serial_number,
                "customer": customer,
                "location": location,
                "title": title,
                "sales_order": sales_order,
                "model": model,
                "category": category,
                "job_folder": self.folder_selected,
                "timestamp": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }

            # Create the JSON filename with document number and serial number
            json_file = os.path.join(self.folder_selected, f"{gss_parent} {serial_number}.json")

            # Save the data to a JSON file
            with open(json_file, 'w') as f:
                json.dump(job_data, f, indent=4)

            logging.info(f"Job data saved to {json_file}")
            messagebox.showinfo("Success", f"Job data saved to:\n{json_file}")
        except Exception as e:
            logging.error(f"Failed to save job data to JSON: {str(e)}")
            messagebox.showerror("Error", f"Failed to save job data to JSON: {str(e)}")

if __name__ == "__main__":
    try:
        logging.info("Creating main window")
        root = ctk.CTk()
        app = JobDataApp(root)
        logging.info("Starting main loop")
        root.mainloop()
    except Exception as e:
        logging.error(f"Fatal error in main loop: {str(e)}")
        sys.exit(1)

    # Add this block to handle PyInstaller's runtime environment
    if getattr(sys, 'frozen', False):
        os.environ['PATH'] = sys._MEIPASS + ";" + os.environ['PATH']
