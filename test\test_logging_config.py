"""
Tests for the logging_config module.

This module contains unit tests for the logging configuration functions in the lib.logging_config module.
"""

import os
import sys
import unittest
import tempfile
import shutil
import logging
from unittest.mock import patch

# Add parent directory to path to allow importing from lib
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from lib.logging_config import setup_logging, get_logger

class TestLoggingConfig(unittest.TestCase):
    """Test case for the logging_config module."""

    def setUp(self):
        """Set up test fixtures."""
        # Create a temporary directory for testing
        self.temp_dir = tempfile.mkdtemp()

        # Save the original logging configuration
        self.original_handlers = logging.getLogger().handlers.copy()
        self.original_level = logging.getLogger().level

    def tearDown(self):
        """Tear down test fixtures."""
        # Restore the original logging configuration and close handlers
        root_logger = logging.getLogger()
        for handler in root_logger.handlers[:]:
            handler.close()  # Close the handler to release file locks
            root_logger.removeHandler(handler)
        for handler in self.original_handlers:
            root_logger.addHandler(handler)
        root_logger.setLevel(self.original_level)

        # Remove the temporary directory
        try:
            shutil.rmtree(self.temp_dir)
        except PermissionError:
            # On Windows, sometimes the log file is still locked
            # We'll just ignore this error in tests
            pass

    @patch("lib.logging_config.get_user_data_dir")
    @patch("lib.logging_config.ensure_dir_exists")
    def test_setup_logging(self, mock_ensure_dir_exists, mock_get_user_data_dir):
        """Test setup_logging function."""
        # Set up mocks
        mock_get_user_data_dir.return_value = self.temp_dir
        log_dir = os.path.join(self.temp_dir, "logs")
        os.makedirs(log_dir)
        mock_ensure_dir_exists.return_value = log_dir

        # Test setup_logging
        log_file = setup_logging("test_app")

        # Check that the log file path is correct
        expected_log_file = os.path.join(log_dir, "test_app.log")
        self.assertEqual(log_file, expected_log_file)

        # Check that the root logger has the correct handlers
        root_logger = logging.getLogger()
        self.assertEqual(len(root_logger.handlers), 2)

        # Check that one handler is a FileHandler
        file_handlers = [h for h in root_logger.handlers if isinstance(h, logging.FileHandler)]
        self.assertEqual(len(file_handlers), 1)
        self.assertEqual(file_handlers[0].baseFilename, expected_log_file)

        # Check that one handler is a StreamHandler
        stream_handlers = [h for h in root_logger.handlers if isinstance(h, logging.StreamHandler) and not isinstance(h, logging.FileHandler)]
        self.assertEqual(len(stream_handlers), 1)

        # Test with custom log level
        log_file = setup_logging("test_app2", log_level=logging.DEBUG)
        self.assertEqual(root_logger.level, logging.DEBUG)

    def test_get_logger(self):
        """Test get_logger function."""
        # Test getting a logger
        logger = get_logger("test_logger")
        self.assertIsInstance(logger, logging.Logger)
        self.assertEqual(logger.name, "test_logger")

if __name__ == "__main__":
    unittest.main()
