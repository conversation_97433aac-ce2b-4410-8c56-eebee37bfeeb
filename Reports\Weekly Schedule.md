
`=date(today)`
```dataview
table without id
row["Scheduled Ship"] - date(today) as Time-Left,
Company,
Location,
row["Machine Type"] As "Machine Type",
file.link as Machine,
SO as "Sales Order",
row["Scheduled Ship"] as "Scheduled Ship",
row["Machine Model"] as "Model"


from "Machines"
where row["Ship Date"] = Empty and row["Scheduled Ship"] != Empty
sort row["Scheduled Ship"] ASC, SO asc, Machine asc
FLATTEN Time-Left
```

