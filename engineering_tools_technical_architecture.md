# Engineering Tools Suite - Technical Architecture

```mermaid
graph TD
    %% Main Code Structure
    subgraph CodeStructure["Code Structure"]
        RootDir["Root Directory (D:/Scripts)"]
        AppsDir["apps/ Directory"]
        ApplicationsDir["applications/ Directory (Legacy)"]
        LibDir["lib/ Directory"]
        ResourcesDir["resources/ Directory"]
        UtilsFile["utils.py"]
        AppsLauncherFile["apps_launcher.py"]
        AddModelFile["add_model.py"]
    end
    
    %% Apps Directory
    subgraph AppsDirectory["Apps Directory Contents"]
        PublishApp["publish_3.py"]
        CreateManualsApp["create_manuals.py"]
        CopyMergedApp["copy_merged_files.py"]
        PrintPDFApp["print_merged_pdf_sections.py"]
        SumMotorApp["sum_motor_fla.py"]
        CreateJobJSONApp["create_job_json.py"]
        UpdateDocsApp["update_documentation_status.py"]
        ConfigApp["config_app.py"]
    end
    
    %% Lib Directory
    subgraph LibDirectory["Library Directory Contents"]
        UtilsLib["utils.py"]
        ThemeUtils["theme_utils.py"]
        ManualCreatorLib["manual_creator.py"]
        PDFPrintingLib["pdf_printing_ui.py"]
        PDFSectionLib["pdf_section_processor.py"]
        PDFPrintingEngineLib["pdf_printing_engine.py"]
        PDFPrintingConfigLib["pdf_printing_config.py"]
        PDFPrintingLoggingLib["pdf_printing_logging.py"]
    end
    
    %% Resources Directory
    subgraph ResourcesDirectory["Resources Directory Contents"]
        ConfigDir["config/"]
        TemplatesDir["Templates/"]
        DrawingsDir["drawings/"]
        ThemesDir["themes/"]
    end
    
    %% Config Directory
    subgraph ConfigDirectory["Config Directory Contents"]
        ModelsJSON["models.json"]
        ModelsTemplateJSON["models_template.json"]
        ConfigJSON["config.json"]
    end
    
    %% Code Dependencies
    RootDir --> AppsDir
    RootDir --> ApplicationsDir
    RootDir --> LibDir
    RootDir --> ResourcesDir
    RootDir --> UtilsFile
    RootDir --> AppsLauncherFile
    RootDir --> AddModelFile
    
    AppsDir --> PublishApp
    AppsDir --> CreateManualsApp
    AppsDir --> CopyMergedApp
    AppsDir --> PrintPDFApp
    AppsDir --> SumMotorApp
    AppsDir --> CreateJobJSONApp
    AppsDir --> UpdateDocsApp
    AppsDir --> ConfigApp
    
    LibDir --> UtilsLib
    LibDir --> ThemeUtils
    LibDir --> ManualCreatorLib
    LibDir --> PDFPrintingLib
    LibDir --> PDFSectionLib
    LibDir --> PDFPrintingEngineLib
    LibDir --> PDFPrintingConfigLib
    LibDir --> PDFPrintingLoggingLib
    
    ResourcesDir --> ConfigDir
    ResourcesDir --> TemplatesDir
    ResourcesDir --> DrawingsDir
    ResourcesDir --> ThemesDir
    
    ConfigDir --> ModelsJSON
    ConfigDir --> ModelsTemplateJSON
    ConfigDir --> ConfigJSON
    
    %% Import Dependencies
    PublishApp -.-> UtilsLib
    PublishApp -.-> ThemeUtils
    PublishApp -.-> ManualCreatorLib
    
    CreateManualsApp -.-> UtilsLib
    CreateManualsApp -.-> ThemeUtils
    CreateManualsApp -.-> ManualCreatorLib
    
    CopyMergedApp -.-> UtilsLib
    CopyMergedApp -.-> ThemeUtils
    
    PrintPDFApp -.-> PDFPrintingLib
    PrintPDFApp -.-> PDFSectionLib
    PrintPDFApp -.-> PDFPrintingEngineLib
    PrintPDFApp -.-> PDFPrintingConfigLib
    PrintPDFApp -.-> PDFPrintingLoggingLib
    PrintPDFApp -.-> ThemeUtils
    
    SumMotorApp -.-> UtilsLib
    SumMotorApp -.-> ThemeUtils
    
    CreateJobJSONApp -.-> UtilsLib
    CreateJobJSONApp -.-> ThemeUtils
    
    UpdateDocsApp -.-> UtilsLib
    UpdateDocsApp -.-> ThemeUtils
    
    ConfigApp -.-> UtilsLib
    ConfigApp -.-> ThemeUtils
    
    AppsLauncherFile -.-> UtilsLib
    AppsLauncherFile -.-> ThemeUtils
    
    AddModelFile -.-> UtilsLib
    
    PDFPrintingLib -.-> PDFSectionLib
    PDFPrintingLib -.-> PDFPrintingEngineLib
    PDFPrintingLib -.-> PDFPrintingConfigLib
    PDFPrintingLib -.-> PDFPrintingLoggingLib
    PDFPrintingLib -.-> ThemeUtils
    
    ManualCreatorLib -.-> UtilsLib
    
    %% Styling
    classDef directory fill:#f96,stroke:#333,stroke-width:2px;
    classDef file fill:#9cf,stroke:#333,stroke-width:1px;
    classDef config fill:#fcf,stroke:#333,stroke-width:1px;
    classDef subgraph fill:#efefef,stroke:#333,stroke-width:1px;
    
    class RootDir,AppsDir,ApplicationsDir,LibDir,ResourcesDir,ConfigDir,TemplatesDir,DrawingsDir,ThemesDir directory;
    class UtilsFile,AppsLauncherFile,AddModelFile,PublishApp,CreateManualsApp,CopyMergedApp,PrintPDFApp,SumMotorApp,CreateJobJSONApp,UpdateDocsApp,ConfigApp,UtilsLib,ThemeUtils,ManualCreatorLib,PDFPrintingLib,PDFSectionLib,PDFPrintingEngineLib,PDFPrintingConfigLib,PDFPrintingLoggingLib file;
    class ModelsJSON,ModelsTemplateJSON,ConfigJSON config;
    class CodeStructure,AppsDirectory,LibDirectory,ResourcesDirectory,ConfigDirectory subgraph;
```

## Class Diagram for Key Components

```mermaid
classDiagram
    %% Main Classes
    class EngineeringToolsLauncher {
        +tools: Dict[str, ToolInfo]
        +categories: Dict[str, List[ToolInfo]]
        +__init__()
        +create_widgets()
        +scan_tools()
        +get_tool_name(filename: str): str
        +launch_tool(tool: ToolInfo)
        +_add_tools_to_frame(tools: List[ToolInfo], frame: ctk.CTkFrame)
        +_adjust_window_size(tool_count: int)
    }
    
    class ToolInfo {
        +name: str
        +path: str
        +description: str
        +category: str
        +__init__(name: str, path: str, description: str, category: str)
    }
    
    class ManualCreator {
        +job_folder: str
        +category: str
        +model: str
        +company: str
        +location: str
        +serial: str
        +template_path: str
        +drawings_path: str
        +asme_flag: bool
        +document_number: str
        +__init__(job_folder, category, model, company, location, serial, model_data, document_number)
        +create_manual(): bool
        +replace_placeholders(doc): bool
        +copy_drawings(): List[str]
        +merge_pdfs(docx_pdf_path, drawing_pdfs): bool
    }
    
    class PublishTool {
        +sheet_document_number: str
        +sheet_model: str
        +sheet_description: str
        +sheet_customer: str
        +sheet_location: str
        +sheet_sales_order: str
        +sheet_serial_number: str
        +folder_selected: str
        +create_manual_var: BooleanVar
        +__init__(root)
        +create_widgets()
        +browse_folder()
        +publish()
        +write_gss_parent(document_number, serial_number, app)
        +write_sheet_title_block_data(app)
        +run_reportgenerator(app)
        +export_to_pdf(app, job)
        +export_to_dxf(app, job)
        +determine_category(model): str
        +save_job_data_to_json()
    }
    
    class PDFSectionProcessor {
        +pdf_path: str
        +sections: List[PDFSection]
        +__init__(pdf_path: str)
        +analyze_pdf(): List[PDFSection]
        +get_page_type(page): str
        +identify_sections(): List[PDFSection]
        +print_sections(printer_profiles: Dict[str, PrinterProfile]): bool
    }
    
    class PDFSection {
        +start_page: int
        +end_page: int
        +page_type: str
        +__init__(start_page: int, end_page: int, page_type: str)
        +get_page_count(): int
        +__str__(): str
    }
    
    class PrinterProfile {
        +name: str
        +printer_name: str
        +paper_size: str
        +orientation: str
        +duplex: bool
        +color: bool
        +__init__(name, printer_name, paper_size, orientation, duplex, color)
        +__str__(): str
    }
    
    class ConfigApp {
        +template_path_var: StringVar
        +drawings_path_var: StringVar
        +monitor_path_var: StringVar
        +email_var: StringVar
        +status_var: StringVar
        +__init__(root)
        +create_widgets()
        +browse_template_dir()
        +browse_drawings_dir()
        +browse_monitor_dir()
        +save_configuration()
        +update_models_json(template_dir, drawings_dir)
        +load_configuration()
    }
    
    %% Relationships
    EngineeringToolsLauncher -- ToolInfo : contains
    PublishTool -- ManualCreator : uses
    PDFSectionProcessor -- PDFSection : contains
    PDFSectionProcessor -- PrinterProfile : uses
```

## Sequence Diagram for Manual Creation Process

```mermaid
sequenceDiagram
    actor User
    participant PublishApp as Publish Tool
    participant ManualCreator as Manual Creator
    participant Word as Microsoft Word
    participant FileSystem as File System
    
    User->>PublishApp: Enter project information
    User->>PublishApp: Select model
    User->>PublishApp: Check "Create Manual"
    User->>PublishApp: Click "Publish"
    
    PublishApp->>FileSystem: Save project
    PublishApp->>FileSystem: Export to PDF/DXF
    
    alt Create Manual is checked
        PublishApp->>ManualCreator: Create manual with project info
        ManualCreator->>FileSystem: Load template
        ManualCreator->>Word: Open template
        ManualCreator->>Word: Replace placeholders
        Word->>FileSystem: Save as DOCX
        Word->>FileSystem: Convert to PDF
        ManualCreator->>FileSystem: Copy drawings
        ManualCreator->>FileSystem: Merge PDFs
        ManualCreator->>User: Display success message
    end
    
    PublishApp->>User: Display completion message
```

## Sequence Diagram for PDF Section Printing Process

```mermaid
sequenceDiagram
    actor User
    participant PrintPDFApp as PDF Section Printing App
    participant PDFProcessor as PDF Section Processor
    participant Printer as Printer
    
    User->>PrintPDFApp: Select folder with PDFs
    User->>PrintPDFApp: Configure printer profiles
    User->>PrintPDFApp: Click "Print PDF Sections"
    
    PrintPDFApp->>PDFProcessor: Process PDF files
    
    loop For each PDF file
        PDFProcessor->>PDFProcessor: Analyze PDF
        PDFProcessor->>PDFProcessor: Identify sections
        
        loop For each section
            PDFProcessor->>PDFProcessor: Determine page type
            PDFProcessor->>Printer: Print with appropriate settings
        end
    end
    
    PDFProcessor->>PrintPDFApp: Return results
    PrintPDFApp->>User: Display completion message
```
