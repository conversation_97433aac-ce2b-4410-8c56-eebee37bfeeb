
# All PHPs by Company
```dataview
list rows.file.link
from "Machines"
Where row["Machine Type"] = [[Phoenix Heat Processor]]
group by Company
```
# [[Electric Temp Control|Electric Paw Heaters]] 
```dataview
list rows.file.link
from "Machines"
Where contains(row["Machine Options"], [[Electric Temp Control]])
group by Company
```
# All PHPs by Location
```dataview
list rows.file.link
from "Machines"
Where row["Machine Type"] = [[Phoenix Heat Processor]]
group by Location
```
# All PHPs by SN
```dataview
list 
from "Machines"
Where row["Machine Type"] = [[Phoenix Heat Processor]]

```