# Attendees
[[<PERSON>|<PERSON>]], [[<PERSON>|<PERSON>]], [[<PERSON><PERSON><PERSON>|<PERSON>]], [[<PERSON>]], and [[<PERSON><PERSON>]]
# Discussed 
program changes to the [[Olymel, CA]] [[Mixing System|DMC]] and [[Heat Exchanger|HEX]]. 

We discussed the matter of an email the contents of which are here
```<PERSON><PERSON>
As we previously discuss we need to make some modifications for the hmi and plc program of the brine mixer:

At the brine mixer HMI, (Password protected page) add a turn off cooling temperature setpoint, if tank one or tank 2 temperature is above that setpoint, you turn off the cooling authorisation for the brine mixer heat exchanger cooling mode. If the temperature is below that setpoint, you program a n/o contact that will be closed. This contact will be used to be connected in serial with the selector that allows cooling mode for the saddle tank heat exchanger.

Because I have to install purge valve at the lowest point of both heat exchangers, I will also need to avoid head exchangers pumps to run if this selector is activated. So I need to add a selector in the Brine mixer panel connected to the plc input for purge mode. This input when activated will have to avoid the brine mixer heat exchanger pump from running. This input will also have to open a dry contact (from one of your outputs) that I will connect in serial with the selector switch of the heat exchanger pump in the injector panel. The same input must drive another output for me to send 120v to both purge valves

At the end, you only have to send me an updated program for the brine mixer PLC and HMI. No modifications require for the other PLCs/hmi

I hope my explanations are understandable
```
