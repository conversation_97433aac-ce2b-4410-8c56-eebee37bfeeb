---
Machine Type: "[[Chiller]]"
Machine Model: TBD
Machine Options:
  - TBD
Company: "[[<PERSON><PERSON>]]"
Location: "[[Bryan, TX]]"
Nominal Voltage: 480V
Machine FLA: "61.8"
Minimum Feeder Current: 75A
Largest Motor: "40"
Largest Load: "52"
Built by: Phoenix
date: 2025-03-05
Ship Date: 
Scheduled Ship: 
SO: 
Work Orders: 
Who Installed: 
TurnedOut: false
ElecTurnedOut: false
Documentation: false
DTM: true
Packet: false
Has Pre-Chiller: true
Blower Size: 30 HP
Pump Size: 40 HP
Blower QTY: "1"
Drive Size: 1.5 HP
Unload Size: 3 HP
Controller: 5069 series
Visualization: Factory Talk Optix
Rocker Size: 10HP
---
Hardware
- Electronics
	- AB Compact Logix Controller
	- AB Circuit Protection
	- AB Optix Panel for visualization
		- One on the panel in the MCC
			- Will handle remote access for us
		- One on the plant floor
	- PowerFlex 755 VFDs for all motors above 20HP
		- ethernet control
		- torque vector control
		- handles bigger motors
	- PowerFlex 525 VFDs for all motors bellow 20HP
		- ethernet control
		- inexpensive
		- we use them everywhere
	- Keyence Safety Controller
		- Handle all safety function
		- expandable IO to handle multiple safety zones
	- Keyence Safety switches
		- We use them all the time
		- Very reliable
	- AB 440E pull cables
		- we use these on the small chillers
- Panels
	- Main PLC Panel
		- located in MCC
		- Houses the PLC and control components
		- Drives for Smaller Motors
		- Drive for Pump? does this need to be in a separate panel
	- HMI Panel
		- Remote Panel on the Plant floor
	- Blower panel
		- Located near blower??
	- Jboxes 
		- For connecting the devices to the main wiring harness
			- One on the drive end
			- One on the unload end
		- For connecting motor leads and 480 devices
			- One on drive end
			- One on unload end
			- Not sure these are needed. will confirm

# Software
- Pre-Ciller Loading/Unloading
	- currently they manually control when the unloader runs.
	- need to look at automating this
		- look at using rocker current to determine if loaded
		- use blackout periods to only allow auto breaks at certain times
		- use vision to determine if the line is till feeding
- Density
	- use vision to count the birds entering
	- use vision to count the birds exiting
	- do the math to determine pounds per foot.
	- develop a mode to control the speed of the auger to maintain a PPF instead of strait dwell time
- Alarming
	- Current warnings on all motors.
	- have a graphic pop up showing exactly what is causing the alarm
		- animated.
		- use cad data
	- be descriptive on the alarm system
		- more than "estop 1 tripped" etc.
		- i.e. Sensor such and such disconnected. 
			- Modern controllers can detect the difference between a broken sensor and a bad sensor show as much of that as possible in plain english
- PM Scheduler
	- Pop up on the screen when a PM should be done
		- use graphics to show what should be pm'd
		- animated
		- use cad data
	- Email the maintenance manager / planner that a PM is due
	- Show a parts list for each PM using Phoenix part no.
- MISC upgrades to make troubleshooting easier
	- find a way to show the ladder logic on screen.
	- display every IO card and its reading/value
	- give each drive a face plate page with as much data as we can pull out of the drives
	- If an estop is pressed pop up a graphic showing which one.
	- any alarm pop up shows the part numbers associated with it to aid in repairs
	- alarms are emailed to plant personnel with PNs associated with the parts that cause the alarm
# Machine Type

![[Large Chiller]]

![[Chiller Cable Runs.canvas|Chiller Cable Runs]]