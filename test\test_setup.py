"""
Tests for the setup.py file.

This module contains unit tests for the setup.py file.
"""

import os
import sys
import unittest
import tempfile
import shutil
import subprocess
from unittest.mock import patch, MagicMock

# Add parent directory to path to allow importing from lib
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class TestSetup(unittest.TestCase):
    """Test case for the setup.py file."""
    
    def setUp(self):
        """Set up test fixtures."""
        # Create a temporary directory for testing
        self.temp_dir = tempfile.mkdtemp()
        
        # Get the path to the setup.py file
        self.setup_py_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "setup.py")
    
    def tearDown(self):
        """Tear down test fixtures."""
        # Remove the temporary directory
        shutil.rmtree(self.temp_dir)
    
    def test_setup_py_exists(self):
        """Test that setup.py exists."""
        self.assertTrue(os.path.exists(self.setup_py_path))
    
    def test_setup_py_is_valid_python(self):
        """Test that setup.py is valid Python code."""
        try:
            # Try to compile the setup.py file
            with open(self.setup_py_path, "r") as f:
                compile(f.read(), self.setup_py_path, "exec")
            
            # If we get here, the file is valid Python code
            self.assertTrue(True)
        except SyntaxError as e:
            self.fail(f"setup.py is not valid Python code: {e}")
    
    @unittest.skip("This test requires setuptools to be installed")
    def test_setup_py_sdist(self):
        """Test that setup.py sdist works."""
        # Change to the parent directory
        original_dir = os.getcwd()
        os.chdir(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        
        try:
            # Run setup.py sdist
            subprocess.check_call([sys.executable, "setup.py", "sdist", "--dist-dir", self.temp_dir])
            
            # Check that a tarball was created
            tarballs = [f for f in os.listdir(self.temp_dir) if f.endswith(".tar.gz")]
            self.assertEqual(len(tarballs), 1)
        finally:
            # Change back to the original directory
            os.chdir(original_dir)

if __name__ == "__main__":
    unittest.main()
