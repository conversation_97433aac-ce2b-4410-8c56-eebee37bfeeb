#!/usr/bin/env python3
"""
<PERSON><PERSON>t to search for machine manuals and update Obsidian notes.

This script searches through a selected folder and its subfolders for PDF files
that might be machine manuals. When a manual is found, it updates the corresponding
machine note in the Obsidian vault to set Documentation: true in the YAML frontmatter.
"""

import os
import re
import sys
import tkinter as tk
from tkinter import filedialog, messagebox, ttk
import customtkinter as ctk
import logging
from pathlib import Path
import yaml
import frontmatter

# Set up logging
logging.basicConfig(level=logging.INFO,
                    format='%(asctime)s - %(levelname)s - %(message)s',
                    handlers=[logging.StreamHandler()])

# Add parent directory to path to allow importing from lib
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import theme utilities
try:
    from lib.theme_utils import apply_theme
except ImportError:
    # Define a basic theme utility if import fails
    def apply_theme(theme_name="red", appearance_mode="dark"):
        ctk.set_appearance_mode(appearance_mode)
        ctk.set_default_color_theme("blue")
        logging.info(f"Using fallback theme: {appearance_mode} mode with blue color theme")

# Apply the red theme
apply_theme("red", "dark")

class ObsidianDocumentationUpdater(ctk.CTk):
    """
    GUI application to search for machine manuals and update Obsidian notes.
    """
    def __init__(self):
        super().__init__()

        # Configure window
        self.title("Documentation Status Updater")
        self.geometry("800x600")

        # Initialize variables
        self.search_folder = None
        self.machines_folder = None
        self.manual_patterns = [
            r"^([A-Za-z0-9-]+)\.pdf$",              # <serial>.pdf
            r"^([A-Za-z0-9-]+) [Mm]anual\.pdf$",    # <serial> manual.pdf
            r"^([A-Za-z0-9-]+) [Mm]anual\.PDF$"     # <serial> Manual.PDF
        ]

        # Create widgets
        self.create_widgets()

        # Results tracking
        self.results = {
            "manuals_found": 0,
            "notes_updated": 0,
            "notes_already_documented": 0,
            "notes_not_found": 0
        }

    def create_widgets(self):
        """Create and arrange the GUI widgets."""
        # Main frame
        main_frame = ctk.CTkFrame(self)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Search folder selection
        search_frame = ctk.CTkFrame(main_frame)
        search_frame.pack(fill=tk.X, padx=10, pady=10)

        search_label = ctk.CTkLabel(search_frame, text="Folder to search for manuals:")
        search_label.pack(anchor=tk.W, padx=5, pady=5)

        search_button_frame = ctk.CTkFrame(search_frame)
        search_button_frame.pack(fill=tk.X, padx=5, pady=5)

        self.search_folder_label = ctk.CTkLabel(search_button_frame, text="No folder selected")
        self.search_folder_label.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)

        search_button = ctk.CTkButton(search_button_frame, text="Browse...", command=self.browse_search_folder,
                                     fg_color="#C53F3F", hover_color="#A02222")
        search_button.pack(side=tk.RIGHT, padx=5)

        # Machines folder selection
        machines_frame = ctk.CTkFrame(main_frame)
        machines_frame.pack(fill=tk.X, padx=10, pady=10)

        machines_label = ctk.CTkLabel(machines_frame, text="Obsidian Machines folder:")
        machines_label.pack(anchor=tk.W, padx=5, pady=5)

        machines_button_frame = ctk.CTkFrame(machines_frame)
        machines_button_frame.pack(fill=tk.X, padx=5, pady=5)

        self.machines_folder_label = ctk.CTkLabel(machines_button_frame, text="No folder selected")
        self.machines_folder_label.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)

        machines_button = ctk.CTkButton(machines_button_frame, text="Browse...", command=self.browse_machines_folder,
                                      fg_color="#C53F3F", hover_color="#A02222")
        machines_button.pack(side=tk.RIGHT, padx=5)

        # Options frame
        options_frame = ctk.CTkFrame(main_frame)
        options_frame.pack(fill=tk.X, padx=10, pady=10)

        # Add a checkbox for recursive search
        self.recursive_var = tk.BooleanVar(value=True)
        recursive_check = ctk.CTkCheckBox(options_frame, text="Search recursively in subfolders",
                                          variable=self.recursive_var,
                                          fg_color="#C53F3F", hover_color="#A02222",
                                          checkmark_color="#FFFFFF")
        recursive_check.pack(anchor=tk.W, padx=5, pady=5)

        # Add a checkbox for dry run
        self.dry_run_var = tk.BooleanVar(value=False)
        dry_run_check = ctk.CTkCheckBox(options_frame, text="Dry run (don't modify files)",
                                        variable=self.dry_run_var,
                                        fg_color="#C53F3F", hover_color="#A02222",
                                        checkmark_color="#FFFFFF")
        dry_run_check.pack(anchor=tk.W, padx=5, pady=5)

        # Log frame
        log_frame = ctk.CTkFrame(main_frame)
        log_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        log_label = ctk.CTkLabel(log_frame, text="Log:")
        log_label.pack(anchor=tk.W, padx=5, pady=5)

        # Create a text widget with a scrollbar for logging
        log_text_frame = ctk.CTkFrame(log_frame)
        log_text_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        self.log_text = tk.Text(log_text_frame, height=10, bg="#2b2b2b", fg="#ffffff",
                               wrap=tk.WORD, font=("Consolas", 10))
        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        scrollbar = ttk.Scrollbar(log_text_frame, command=self.log_text.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.log_text.config(yscrollcommand=scrollbar.set)

        # Button frame
        button_frame = ctk.CTkFrame(main_frame)
        button_frame.pack(fill=tk.X, padx=10, pady=10)

        run_button = ctk.CTkButton(button_frame, text="Run", command=self.run_update,
                               fg_color="#C53F3F", hover_color="#A02222")
        run_button.pack(side=tk.RIGHT, padx=5, pady=5)

        clear_button = ctk.CTkButton(button_frame, text="Clear Log", command=self.clear_log,
                                  fg_color="#C53F3F", hover_color="#A02222")
        clear_button.pack(side=tk.RIGHT, padx=5, pady=5)

    def browse_search_folder(self):
        """Open a dialog to select the folder to search for manuals."""
        folder = filedialog.askdirectory(title="Select Folder to Search for Manuals")
        if folder:
            self.search_folder = folder
            self.search_folder_label.configure(text=folder)
            self.log(f"Search folder set to: {folder}")

    def browse_machines_folder(self):
        """Open a dialog to select the Obsidian Machines folder."""
        folder = filedialog.askdirectory(title="Select Obsidian Machines Folder")
        if folder:
            self.machines_folder = folder
            self.machines_folder_label.configure(text=folder)
            self.log(f"Machines folder set to: {folder}")

    def log(self, message):
        """Add a message to the log text widget."""
        self.log_text.insert(tk.END, message + "\n")
        self.log_text.see(tk.END)
        logging.info(message)

    def clear_log(self):
        """Clear the log text widget."""
        self.log_text.delete(1.0, tk.END)

    def run_update(self):
        """Run the update process."""
        # Check if folders are selected
        if not self.search_folder:
            messagebox.showerror("Error", "Please select a folder to search for manuals.")
            return

        if not self.machines_folder:
            messagebox.showerror("Error", "Please select the Obsidian Machines folder.")
            return

        # Reset results
        self.results = {
            "manuals_found": 0,
            "notes_updated": 0,
            "notes_already_documented": 0,
            "notes_not_found": 0
        }

        # Start the update process
        self.log("Starting update process...")
        self.log(f"{'DRY RUN - ' if self.dry_run_var.get() else ''}Searching for manuals in: {self.search_folder}")

        # Find all manuals
        manuals = self.find_manuals(self.search_folder)

        if not manuals:
            self.log("No manuals found.")
            return

        self.log(f"Found {len(manuals)} potential manuals.")
        self.results["manuals_found"] = len(manuals)

        # Process each manual
        for manual_path in manuals:
            self.process_manual(manual_path)

        # Show summary
        self.log("\nSummary:")
        self.log(f"Manuals found: {self.results['manuals_found']}")
        self.log(f"Notes updated: {self.results['notes_updated']}")
        self.log(f"Notes already documented: {self.results['notes_already_documented']}")
        self.log(f"Notes not found: {self.results['notes_not_found']}")

        if not self.dry_run_var.get():
            messagebox.showinfo("Complete",
                               f"Process complete!\n\n"
                               f"Manuals found: {self.results['manuals_found']}\n"
                               f"Notes updated: {self.results['notes_updated']}\n"
                               f"Notes already documented: {self.results['notes_already_documented']}\n"
                               f"Notes not found: {self.results['notes_not_found']}")
        else:
            messagebox.showinfo("Dry Run Complete",
                               f"Dry run complete! No files were modified.\n\n"
                               f"Manuals found: {self.results['manuals_found']}\n"
                               f"Notes that would be updated: {self.results['notes_updated']}\n"
                               f"Notes already documented: {self.results['notes_already_documented']}\n"
                               f"Notes not found: {self.results['notes_not_found']}")

    def find_manuals(self, folder):
        """
        Find all PDF files that match manual patterns in the given folder.
        Specifically looks in folders named 'merged' for files matching the patterns.

        Args:
            folder (str): The folder to search in

        Returns:
            list: List of paths to potential manual files
        """
        manuals = []

        # Determine if we should search recursively
        if self.recursive_var.get():
            # Walk through all subdirectories
            for root, dirs, files in os.walk(folder):
                # Check if the current directory is named 'merged'
                if os.path.basename(root).lower() == 'merged':
                    for file in files:
                        if file.lower().endswith('.pdf'):
                            file_path = os.path.join(root, file)
                            # Check if the file matches any of the manual patterns
                            for pattern in self.manual_patterns:
                                match = re.match(pattern, file)
                                if match:
                                    manuals.append(file_path)
                                    self.log(f"Found potential manual: {file_path}")
                                    break
        else:
            # Only search in the top-level directory
            if os.path.basename(folder).lower() == 'merged':
                # If the selected folder is already named 'merged'
                for file in os.listdir(folder):
                    if file.lower().endswith('.pdf'):
                        file_path = os.path.join(folder, file)
                        # Check if the file matches any of the manual patterns
                        for pattern in self.manual_patterns:
                            match = re.match(pattern, file)
                            if match:
                                manuals.append(file_path)
                                self.log(f"Found potential manual: {file_path}")
                                break
            else:
                # Check if there's a 'merged' subfolder
                merged_folder = os.path.join(folder, 'merged')
                if os.path.exists(merged_folder) and os.path.isdir(merged_folder):
                    for file in os.listdir(merged_folder):
                        if file.lower().endswith('.pdf'):
                            file_path = os.path.join(merged_folder, file)
                            # Check if the file matches any of the manual patterns
                            for pattern in self.manual_patterns:
                                match = re.match(pattern, file)
                                if match:
                                    manuals.append(file_path)
                                    self.log(f"Found potential manual: {file_path}")
                                    break

        return manuals

    def process_manual(self, manual_path):
        """
        Process a manual file and update the corresponding machine note.

        Args:
            manual_path (str): Path to the manual file
        """
        # Extract the serial number or identifier from the manual filename
        manual_filename = os.path.basename(manual_path)

        # Try to extract a serial number or identifier
        # This assumes the manual filename starts with the serial number
        for pattern in self.manual_patterns:
            match = re.match(pattern, manual_filename)
            if match:
                identifier = match.group(1)  # Extract the serial/identifier from the first capture group
                self.log(f"Extracted identifier: {identifier} from manual: {manual_filename}")

                # Find the corresponding machine note
                machine_note = self.find_machine_note(identifier)

                if not machine_note:
                    self.log(f"No machine note found for identifier: {identifier}")
                    self.results["notes_not_found"] += 1
                    return

                # Update the machine note
                self.update_machine_note(machine_note, manual_path)
                return

        # If we get here, no pattern matched
        self.log(f"Could not extract identifier from manual: {manual_filename}")
        self.results["notes_not_found"] += 1

    def find_machine_note(self, identifier):
        """
        Find the machine note that corresponds to the given identifier.
        Specifically looks for a file named <identifier>.md

        Args:
            identifier (str): The identifier to search for

        Returns:
            str or None: Path to the machine note, or None if not found
        """
        # Search for machine notes in the Machines folder
        for root, _, files in os.walk(self.machines_folder):
            for file in files:
                if file.lower().endswith('.md'):
                    # Check if the filename exactly matches the identifier
                    if file == f"{identifier}.md":
                        self.log(f"Found matching note: {os.path.join(root, file)}")
                        return os.path.join(root, file)

        self.log(f"No exact match found for {identifier}.md, searching for partial matches...")

        # If no exact match, try a more flexible approach
        for root, _, files in os.walk(self.machines_folder):
            for file in files:
                if file.lower().endswith('.md'):
                    # Check if the identifier is in the filename
                    if identifier in file:
                        self.log(f"Found partial match: {os.path.join(root, file)}")
                        return os.path.join(root, file)

        return None

    def update_machine_note(self, note_path, manual_path):
        """
        Update the Documentation field in the YAML frontmatter of a machine note.

        Args:
            note_path (str): Path to the machine note
            manual_path (str): Path to the manual file
        """
        try:
            # Read the note file
            with open(note_path, 'r', encoding='utf-8') as f:
                note_content = f.read()

            # Parse the frontmatter
            post = frontmatter.loads(note_content)

            # Check if Documentation is already true
            if post.get('Documentation') == True:
                self.log(f"Note already has Documentation=true: {os.path.basename(note_path)}")
                self.results["notes_already_documented"] += 1
                return

            # Update the Documentation field
            post['Documentation'] = True

            # Also update the Manual field if it exists
            if 'Manual' in post:
                post['Manual'] = True

            # If this is a dry run, don't actually modify the file
            if self.dry_run_var.get():
                self.log(f"[DRY RUN] Would update Documentation to true in: {os.path.basename(note_path)}")
                self.results["notes_updated"] += 1
                return

            # Write the updated content back to the file
            with open(note_path, 'w', encoding='utf-8') as f:
                f.write(frontmatter.dumps(post))

            self.log(f"Updated Documentation to true in: {os.path.basename(note_path)}")
            self.results["notes_updated"] += 1

        except Exception as e:
            self.log(f"Error updating note {os.path.basename(note_path)}: {str(e)}")

if __name__ == "__main__":
    app = ObsidianDocumentationUpdater()
    app.mainloop()
