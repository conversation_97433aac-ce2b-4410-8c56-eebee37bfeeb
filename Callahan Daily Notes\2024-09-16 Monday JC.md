# Past DUE
```tasks
not done
sort by priority
due before 2024-09-16
tags include #<PERSON><PERSON> 
```
# DUE TODAY
```tasks
not done
sort by priority
due on 2024-09-16
tags include #JC 
```
# DUE This Week
```tasks
not done
sort by priority
due between last friday and this friday
tags include #JC 
```
# Assigned to [[<PERSON>|<PERSON>]]
```tasks 
not done
tags include JC
```
# LOG
[[Weekly Engineering meeting 2024-09-16|ENG MEETING]]

[[IPPE 25 Marination Meeting]] 

[[<PERSON> Moody|Austin]] requested that we set the max frequency of the auger drive to 70 hz. Below is our exchange.

```Austin
Stop setting the max HZ at 60 for these drives please!!!! We need these to be able to run 70 right now because of how much were tearing up the product. 
```

```Jonathan
Drive frequency max is set in the drive on p44. The default is 60 Hz. We are not setting them to that that is how they come from factory. Nobody has requested that we set them to anything else untill this text. 

In order to change p44 you will have to stop the machine and disconnect the Ethernet cable from that drive. Once the drive faults from comms loss you can change the parameters. 

To clear the comms loss fault press the red button on the drive
```

```Austin
I've asked for that 3 separate times once by text before and twice by installation report. I've got it running a faster dwell now but we have way worse issues on the unloader currently 
```

While he may have requested it through his chain of command neither [[<PERSON> <PERSON>|<PERSON>]] nor [[<PERSON> Call<PERSON>|I]] have received this request.


# Tasks
