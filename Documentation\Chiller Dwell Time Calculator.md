![[Pasted image 20250212231043.png]]
# **Instructions for Running the Application:**

1. **Receive the Package:**
   - You received a zipped folder ("Chiller Dwell Time Calculator.zip") containing the application folder 

2. **Extract the Files:**
   - Right-click on the zipped file and select "Extract All..." to unzip the folder.
   - Choose a location on your computer where you want to store the extracted files.

3. **Locate the Executable:**
   - Open the extracted folder. You should see an executable file. "Chiller Dwell Time Calculator.exe"

4. **Run the Application:**
   - Double-click the executable file ("Chiller Dwell Time Calculator.exe") to launch the application.
   - If prompted by your operating system or antivirus software, confirm that you want to run the file.

5. **Create a Shortcut (Optional):**
   - For easier future access, you can right-click on "Chiller Dwell Time Calculator.exe" and select "Create shortcut" to place a shortcut on your desktop.

6. **Troubleshooting:**
   - If the application does not start, check your antivirus settings as sometimes executables from unknown sources might be blocked.

# **How It Works**
## **Inputs and Plotting**
Enters the required values. The values that we hardly ever change are pre-filled with the default values shown below
![[Pasted image 20250212234217.png]]
Once all fields have valid data in them click the Calculate Plot button.
![[Pasted image 20250212231955.png]]
If all parameters have positive values a plot like the following will appear.
![[Pasted image 20250212231255.png]]
## **Saving the Plot**
When you are ready to save your plot click the Save Plot to PDF button.
![[Pasted image 20250212231955.png]]
Select the location you want to save it to, enter a name and click ok.
The exported plot will open automatically. Bellow the plot will be all of the data used to create it.
![[Pasted image 20250212232349.png]]

## **Frequency Calculation**  
The target Motor RPM X is given by:
$$
X =  \frac{L \times 12}{T \times P} \times \left(\frac{B}{A} \times \frac{s}{S} \times G\right)
$$

where:
- N = Motor Nominal Speed (RPM)
- F = Nominal Frequency (Hz)
- L = Chiller Length (ft)
- T = Dwell Time (min.)
- P = Auger Pitch (in.)
- A = Drive Sheave (datum)
- B = Driven Sheave (datum)
- G = Gear Box Ratio
- s = Driven Sprocket (teeth)
- S = Drive Sprocket (teeth)

The script computes the constant \( K \) as follows:
$$K = \frac{L \times 12}{P} \times \left(\frac{B}{A} \times \frac{s}{S} \times G\right) \times \frac{F_{\text{nom}}}{N}$$
The dwell times corresponding to the nominal and minimum frequencies are computed as:
$$T_{\text{nom}} = \frac{K}{F_{\text{nom}}} \quad \text{and} \quad T_{\text{min}} = \frac{K}{F_{\text{min}}}$$
The frequency at any dwell time $( T )$ on the new (hyperbolic) scale is given by:
$$F(T) = \frac{K}{T}$$
The "Old Scale" is defined by a linear interpolation between the nominal and minimum frequency points. 
Its value at a target dwell time $T_{\text{target}}$ is: $F_{\text{old}}(T_{\text{target}}) = F_{\text{nom}} + \frac{F_{\text{min}} - F_{\text{nom}}}{T_{\text{min}} - T_{\text{nom}}} \left( T_{\text{target}} - T_{\text{nom}} \right)$
## **Plotting:**
The script plots the hyperbolic frequency curve: $F(T) = \frac{K}{T}$ over the dwell time range $[T_{\text{nom}}, T_{\text{min}}]$
It marks the following points on the plot
- Nominal frequency: $(T_{\text{Max}}, F_{\text{Max}})$
- Minimum frequency: $(T_{\text{min}}, F_{\text{min}})$
- Target frequency on the new scale: $(T_{\text{target}}, F(T_{\text{target}}))$
- A dashed line labeled "Old Scale" is drawn connecting $(T_{\text{nom}}, F_{\text{nom}})$and $(T_{\text{min}}, F_{\text{min}})$ and the target point $\left( T_{\text{target}}, F_{\text{old}}(T_{\text{target}}) \right)$
is plotted on this line.
## **Difference Calculation:**
The difference between the target frequency on the new scale and the old scale is calculated as: $$\Delta F = F(T_{\text{target}}) - F_{\text{old}}(T_{\text{target}})$$
This difference is displayed in the results, providing insight into how the two scales compare at the target dwell time.
