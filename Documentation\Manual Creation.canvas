{"nodes": [{"id": "b10d37f5f3251947", "type": "group", "styleAttributes": {}, "x": 420, "y": -700, "width": 300, "height": 580, "label": "Schematics"}, {"id": "b933b00649f8b288", "type": "group", "styleAttributes": {}, "x": -200, "y": -700, "width": 320, "height": 530, "label": "Text Block"}, {"id": "06a8626530faf37e", "type": "group", "styleAttributes": {}, "x": 720, "y": -700, "width": 300, "height": 470, "label": "Mechanical"}, {"id": "2808384b92cb62b7", "type": "group", "styleAttributes": {}, "x": 120, "y": -700, "width": 300, "height": 460, "label": "Panel Layout"}, {"id": "eafd94085e23b6f6", "type": "text", "text": "Create a Serialized Copy of the standard drawing file for the layout of the electrical of the machine.", "styleAttributes": {"textAlign": "center"}, "x": 140, "y": -670, "width": 260, "height": 100}, {"id": "f0969fb12c3ffb87", "type": "text", "text": "edit the data file of the model.", "styleAttributes": {"textAlign": "center"}, "x": 140, "y": -520, "width": 260, "height": 60}, {"id": "c4e02d0aaee21b2a", "type": "text", "text": "create a pdf of the drawing", "styleAttributes": {"textAlign": "center"}, "x": 140, "y": -420, "width": 260, "height": 60}, {"id": "c959477ca002163e", "type": "text", "text": "add to / create job folder for that machine", "styleAttributes": {"textAlign": "center"}, "x": 140, "y": -320, "width": 260, "height": 60}, {"id": "ee8106e653f4326c", "type": "text", "text": "Open the word document for the machine's manual in the manual creation folder.", "styleAttributes": {"textAlign": "center"}, "x": -180, "y": -670, "width": 260, "height": 80}, {"id": "cdff3b7b3ec1ce1a", "type": "text", "text": "update the cover page.\n- company\n- location\n- model \n- serial number", "styleAttributes": {}, "x": -180, "y": -560, "width": 260, "height": 180}, {"id": "f280c8c085702e52", "type": "text", "text": "Print file to PDF", "styleAttributes": {}, "x": -180, "y": -340, "width": 260, "height": 60}, {"id": "3f9d120fcaffc637", "type": "text", "text": "add to / create job folder for that machine", "styleAttributes": {"textAlign": "center"}, "x": -180, "y": -240, "width": 260, "height": 60}, {"id": "b0ba7b6f9987e09b", "type": "text", "text": "Create a Serialized Copy of the standard drawing files for the schematic of the machine.", "styleAttributes": {"textAlign": "center"}, "x": 440, "y": -670, "width": 260, "height": 100}, {"id": "c62f8618ee4c49e9", "type": "text", "text": "add to / create job folder for that machine", "styleAttributes": {"textAlign": "center"}, "x": 440, "y": -200, "width": 260, "height": 60}, {"id": "a69a99492d78bca6", "type": "text", "text": "open project in auto-cad ", "styleAttributes": {"textAlign": "center"}, "x": 440, "y": -530, "width": 260, "height": 60}, {"id": "003cb55a87184b2b", "type": "text", "text": "edit each drawing properties \nfor the serial number of the machine", "styleAttributes": {"textAlign": "center"}, "x": 440, "y": -430, "width": 260, "height": 100}, {"id": "93f01adf2afbbec1", "type": "text", "text": "publish project to pdf", "styleAttributes": {}, "x": 440, "y": -290, "width": 260, "height": 60}, {"id": "d87803b50a8aa645", "type": "text", "text": "Run the pdf joiner python script", "styleAttributes": {"textAlign": "center"}, "x": 310, "y": 20, "width": 260, "height": 60}, {"id": "cfaab5c9e5e9a0a1", "type": "text", "text": "Gather all mechanical drawings intended for the manual of the machine. Should be located in the manuals creation folder for that machine", "styleAttributes": {"textAlign": "center"}, "x": 740, "y": -680, "width": 260, "height": 140}, {"id": "6c0caf531f43b45a", "type": "text", "text": "add to / create job folder for that machine", "styleAttributes": {"textAlign": "center"}, "x": 740, "y": -320, "width": 260, "height": 60}, {"id": "f9479fd59e14d314", "type": "text", "text": "click choose folder\nnavigate to the job folder for the machine\npress enter", "styleAttributes": {"textAlign": "center"}, "x": 310, "y": 100, "width": 260, "height": 100}, {"id": "4f674047e0f6371a", "type": "text", "text": "reorder the files so they are in the following order\n- text block\n- panel layout\n- schematic\n- mechanical ", "styleAttributes": {}, "x": 310, "y": 240, "width": 260, "height": 240}], "edges": [{"id": "6047a96d38b06e60", "styleAttributes": {}, "fromNode": "eafd94085e23b6f6", "fromSide": "bottom", "toNode": "f0969fb12c3ffb87", "toSide": "top"}, {"id": "dd2a4d3ba1bd3147", "styleAttributes": {}, "fromNode": "f0969fb12c3ffb87", "fromSide": "bottom", "toNode": "c4e02d0aaee21b2a", "toSide": "top"}, {"id": "d3aba864427f8453", "styleAttributes": {}, "fromNode": "c4e02d0aaee21b2a", "fromSide": "bottom", "toNode": "c959477ca002163e", "toSide": "top"}, {"id": "309866d63c3529d1", "styleAttributes": {}, "fromNode": "b0ba7b6f9987e09b", "fromSide": "bottom", "toNode": "a69a99492d78bca6", "toSide": "top"}, {"id": "64fac21cfda9f15e", "styleAttributes": {}, "fromNode": "a69a99492d78bca6", "fromSide": "bottom", "toNode": "003cb55a87184b2b", "toSide": "top"}, {"id": "897ce6cd471f61ca", "styleAttributes": {}, "fromNode": "003cb55a87184b2b", "fromSide": "bottom", "toNode": "93f01adf2afbbec1", "toSide": "top"}, {"id": "2218bd3501101dbd", "styleAttributes": {}, "fromNode": "93f01adf2afbbec1", "fromSide": "bottom", "toNode": "c62f8618ee4c49e9", "toSide": "top"}, {"id": "3d792031bb4bdf1c", "styleAttributes": {}, "fromNode": "ee8106e653f4326c", "fromSide": "bottom", "toNode": "cdff3b7b3ec1ce1a", "toSide": "top"}, {"id": "b87bfb33fd3360b6", "styleAttributes": {}, "fromNode": "cdff3b7b3ec1ce1a", "fromSide": "bottom", "toNode": "f280c8c085702e52", "toSide": "top"}, {"id": "7cc4190d40f59f1d", "styleAttributes": {}, "fromNode": "f280c8c085702e52", "fromSide": "bottom", "toNode": "3f9d120fcaffc637", "toSide": "top"}, {"id": "36c0da6f058e7ccb", "styleAttributes": {}, "fromNode": "cfaab5c9e5e9a0a1", "fromSide": "bottom", "toNode": "6c0caf531f43b45a", "toSide": "top"}, {"id": "098f26e5d790559b", "styleAttributes": {}, "fromNode": "3f9d120fcaffc637", "fromSide": "bottom", "toNode": "d87803b50a8aa645", "toSide": "top"}, {"id": "6e89dba5b2250428", "styleAttributes": {}, "fromNode": "c959477ca002163e", "fromSide": "bottom", "toNode": "d87803b50a8aa645", "toSide": "top"}, {"id": "5471a2b585ac9f13", "styleAttributes": {}, "fromNode": "c62f8618ee4c49e9", "fromSide": "bottom", "toNode": "d87803b50a8aa645", "toSide": "top"}, {"id": "0b87a28a0775d4f2", "styleAttributes": {}, "fromNode": "6c0caf531f43b45a", "fromSide": "bottom", "toNode": "d87803b50a8aa645", "toSide": "top"}, {"id": "a8ad7c20bc425a6c", "styleAttributes": {}, "fromNode": "d87803b50a8aa645", "fromSide": "bottom", "toNode": "f9479fd59e14d314", "toSide": "top"}, {"id": "7e1b1768d2ad03e3", "styleAttributes": {}, "fromNode": "f9479fd59e14d314", "fromSide": "bottom", "toNode": "4f674047e0f6371a", "toSide": "top"}], "metadata": {}}