# Engineering Tools

This directory contains various engineering tools and utilities.

## Available Tools

### Copy Merged Files

This application provides a GUI to search all subfolders of a selected location,
find files in folders named 'MERGED', and copy them to a 'manual' folder at the selected location.

### Publish 3

An application to publish a project by reading title block data, updating attributes, and exporting to PDF.
It can also create manuals based on the selected model.

## How to Use

You can launch these tools individually or use the Engineering Tools Launcher to access all tools from a central interface.

### Using the Engineering Tools Launcher

1. Run `apps_launcher.py` from the main directory:
   ```
   python apps_launcher.py
   ```

2. The launcher will display all available tools with descriptions.

3. Click the "Launch" button next to a tool to start it.

### Running Tools Directly

You can also run each tool directly:

```
python apps/copy_merged_files.py
```

or

```
python apps/publish_3.py
```

## Adding New Tools

To add a new tool:

1. Create a new Python file in the `apps` directory.
2. Add a docstring at the top of the file to describe the tool (this will be displayed in the launcher).
3. Make sure to add the parent directory to the Python path to allow importing from lib and utils:
   ```python
   import os
   import sys
   sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
   ```
4. The tool will automatically appear in the Engineering Tools Launcher.
